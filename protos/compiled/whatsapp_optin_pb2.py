# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: protos/proto_files/whatsapp_optin.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='protos/proto_files/whatsapp_optin.proto',
  package='whatsapp_optin',
  syntax='proto3',
  serialized_pb=_b('\n\'protos/proto_files/whatsapp_optin.proto\x12\x0ewhatsapp_optin\"9\n\x17SetWhatsappOptinRequest\x12\x0e\n\x06mobile\x18\x01 \x01(\t\x12\x0e\n\x06opt_in\x18\x02 \x01(\x08\"9\n\x18SetWhatsappOptinResponse\x12\x0e\n\x06status\x18\x01 \x01(\x08\x12\r\n\x05\x65rror\x18\x02 \x01(\t\")\n\x17GetWhatsappOptinRequest\x12\x0e\n\x06mobile\x18\x01 \x01(\t\"N\n\x19GetWhatsappOptingResponse\x12\x0e\n\x06status\x18\x01 \x01(\x08\x12\x12\n\nopt_status\x18\x02 \x01(\x08\x12\r\n\x05\x65rror\x18\x03 \x01(\tb\x06proto3')
)




_SETWHATSAPPOPTINREQUEST = _descriptor.Descriptor(
  name='SetWhatsappOptinRequest',
  full_name='whatsapp_optin.SetWhatsappOptinRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='mobile', full_name='whatsapp_optin.SetWhatsappOptinRequest.mobile', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='opt_in', full_name='whatsapp_optin.SetWhatsappOptinRequest.opt_in', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=59,
  serialized_end=116,
)


_SETWHATSAPPOPTINRESPONSE = _descriptor.Descriptor(
  name='SetWhatsappOptinResponse',
  full_name='whatsapp_optin.SetWhatsappOptinResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='whatsapp_optin.SetWhatsappOptinResponse.status', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='error', full_name='whatsapp_optin.SetWhatsappOptinResponse.error', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=118,
  serialized_end=175,
)


_GETWHATSAPPOPTINREQUEST = _descriptor.Descriptor(
  name='GetWhatsappOptinRequest',
  full_name='whatsapp_optin.GetWhatsappOptinRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='mobile', full_name='whatsapp_optin.GetWhatsappOptinRequest.mobile', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=177,
  serialized_end=218,
)


_GETWHATSAPPOPTINGRESPONSE = _descriptor.Descriptor(
  name='GetWhatsappOptingResponse',
  full_name='whatsapp_optin.GetWhatsappOptingResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='whatsapp_optin.GetWhatsappOptingResponse.status', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='opt_status', full_name='whatsapp_optin.GetWhatsappOptingResponse.opt_status', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='error', full_name='whatsapp_optin.GetWhatsappOptingResponse.error', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=220,
  serialized_end=298,
)

DESCRIPTOR.message_types_by_name['SetWhatsappOptinRequest'] = _SETWHATSAPPOPTINREQUEST
DESCRIPTOR.message_types_by_name['SetWhatsappOptinResponse'] = _SETWHATSAPPOPTINRESPONSE
DESCRIPTOR.message_types_by_name['GetWhatsappOptinRequest'] = _GETWHATSAPPOPTINREQUEST
DESCRIPTOR.message_types_by_name['GetWhatsappOptingResponse'] = _GETWHATSAPPOPTINGRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

SetWhatsappOptinRequest = _reflection.GeneratedProtocolMessageType('SetWhatsappOptinRequest', (_message.Message,), dict(
  DESCRIPTOR = _SETWHATSAPPOPTINREQUEST,
  __module__ = 'protos.proto_files.whatsapp_optin_pb2'
  # @@protoc_insertion_point(class_scope:whatsapp_optin.SetWhatsappOptinRequest)
  ))
_sym_db.RegisterMessage(SetWhatsappOptinRequest)

SetWhatsappOptinResponse = _reflection.GeneratedProtocolMessageType('SetWhatsappOptinResponse', (_message.Message,), dict(
  DESCRIPTOR = _SETWHATSAPPOPTINRESPONSE,
  __module__ = 'protos.proto_files.whatsapp_optin_pb2'
  # @@protoc_insertion_point(class_scope:whatsapp_optin.SetWhatsappOptinResponse)
  ))
_sym_db.RegisterMessage(SetWhatsappOptinResponse)

GetWhatsappOptinRequest = _reflection.GeneratedProtocolMessageType('GetWhatsappOptinRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETWHATSAPPOPTINREQUEST,
  __module__ = 'protos.proto_files.whatsapp_optin_pb2'
  # @@protoc_insertion_point(class_scope:whatsapp_optin.GetWhatsappOptinRequest)
  ))
_sym_db.RegisterMessage(GetWhatsappOptinRequest)

GetWhatsappOptingResponse = _reflection.GeneratedProtocolMessageType('GetWhatsappOptingResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETWHATSAPPOPTINGRESPONSE,
  __module__ = 'protos.proto_files.whatsapp_optin_pb2'
  # @@protoc_insertion_point(class_scope:whatsapp_optin.GetWhatsappOptingResponse)
  ))
_sym_db.RegisterMessage(GetWhatsappOptingResponse)


# @@protoc_insertion_point(module_scope)
