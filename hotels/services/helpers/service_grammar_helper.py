import traceback
from django.conf import settings
from django.core.cache import cache
from hotels.services.constant import CONFIG_KEEPER_CATEGORY_KEY, CONFIG_KEEPER_SERVICE_KEY, DEFAULT_SHORT_BASE_TEMPLATE, SHORT_<PERSON><PERSON><PERSON>AR_BASE_TEMPLATE, SHORT_GRAMMAR_BASE_TEMPLATE_CACHE_KEY, SHORT_GRAMMAR_CACHE_KEY, SHORT_GRAMMAR_DEFAULT_EXTENSION, SHORT_GRAMMAR_DEFAULT_EXTENSION_CACHE_KEY, SHORT_GRAMMAR_TEMPLATE_KEY, SHORT_GRAMMAR_TEMPLATE_MAP_KEY, SHORT_G<PERSON>MMAR_TEMPLATES_MAP
from jinja2 import Template
from common.commonhelper import update_error_identifier, update_specific_identifier
from utils.logger import Logger
from ingo_partners.config_manager.common_methods import config_keeper



api_logger = Logger(logger='inventoryAPILogger')


class ShortGrammarService(object):

    def __init__(self):
        super(ShortGrammarService, self).__init__()
        self.db = cache
        default_short_desc, config = get_short_grammar_templates()
        self.config = config
        self.default_short_desc = default_short_desc

    def get_grammar(self, grammar_context, leaf_category_id):
        if not grammar_context or not leaf_category_id:
            return ""

        custom_template_config =  self.config.get(str(leaf_category_id), {})
        custom_template_extension = custom_template_config.get(SHORT_GRAMMAR_TEMPLATE_KEY)
        if not custom_template_extension:
            custom_template_extension = self.default_short_desc
        full_template = custom_template_extension
        
        try:
            tm = Template(full_template)
            return tm.render(**grammar_context)
        except Exception as e:
            log_identifier = {}
            update_error_identifier(error_message=str(e), traceback=repr(traceback.format_exc()), log_identifier=log_identifier)
            log_msg = "Render failed for Template: {}. Context: {}".format(full_template, grammar_context)
            update_specific_identifier('remark', log_msg, log_identifier)
            api_logger.critical(log_type='ingoibibo', bucket='ShortGrammarService', stage='get_grammar', identifier='{}'.format(log_identifier))
            return ""


def get_short_grammar_templates():
    short_grammar_config = settings.SHORT_GRAMMAR_CONFIG
    cache_key = short_grammar_config.get(SHORT_GRAMMAR_CACHE_KEY)
    default_extension_cache_key = short_grammar_config.get(SHORT_GRAMMAR_DEFAULT_EXTENSION_CACHE_KEY)
    cached_config = cache.get(cache_key)
    default_extension = cache.get(default_extension_cache_key)
    if cached_config and default_extension:
        return default_extension, cached_config
    default_extension, short_grammar_template_extension = fetch_short_grammar_configs_configkeeper(short_grammar_config)
    cache.set(default_extension_cache_key, default_extension, settings.CACHE_TIMEOUT_GENERIC)
    cache.set(cache_key, short_grammar_template_extension, settings.CACHE_TIMEOUT_GENERIC)
    return default_extension, short_grammar_template_extension


def fetch_short_grammar_configs_configkeeper(short_grammar_config):
    pulled_config = config_keeper.get_config(short_grammar_config.get(CONFIG_KEEPER_SERVICE_KEY),
                                             short_grammar_config.get(CONFIG_KEEPER_CATEGORY_KEY), True)
    core_config = pulled_config.get(short_grammar_config.get(CONFIG_KEEPER_CATEGORY_KEY), {}).get(short_grammar_config.get(CONFIG_KEEPER_CATEGORY_KEY), {})
    default_extension = core_config.get(SHORT_GRAMMAR_DEFAULT_EXTENSION, '{{leaf_category_name}}')
    short_grammar_template_extension = core_config.get(SHORT_GRAMMAR_TEMPLATE_MAP_KEY, SHORT_GRAMMAR_TEMPLATES_MAP)
    return default_extension, short_grammar_template_extension
