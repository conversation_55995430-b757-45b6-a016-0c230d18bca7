<div>
    <h2>Occupancy Info</h2>
    <table>
        <tr>
            <th>Max Child Age (for free)</th>
            <th>Max Child Age (for low fare)</th>
            <th>Base Adult</th>
            <th>Base Child</th>
            <th>Max Guest</th>
            <th>Max Adult</th>
            <th>Max Children</th>
            <th><PERSON> (Max Free Child)</th>
        </tr>
        <tr>
            <td>{{odict.maxchildage1}}</td>
            <td>{{odict.maxchildage2}}</td>
            <td>{{odict.baseadult}}</td>
            <td>{{odict.basechild}}</td>
            <td>{{odict.maxguest}}</td>
            <td>{{odict.maxadult}}</td>
            <td>{{odict.maxchild}}</td>
            <td>{{odict.maxinfant}}</td>
        </tr>
    </table>
    <h2>Availability  & Rate's Info</h2>
    <table>
        <tr>
            <th>Date</th>
            <th>Available Rooms</th>
            <th>Minimum Nights</th>
            {% if property_type != 'entire' %}
                <th>Rack Rate (Single)</th>
                <th>Rack Rate (Double)</th>
                <th>Rack Rate (Triple)</th>
                <th>Net Rate (Single)</th>
                <th>Net Rate (Double)</th>
                <th>Net Rate (triple)</th>
                <th>Sell Rate (Single)</th>
                <th>Sell Rate (Double)</th>
                <th>Sell Rate (Triple)</th>
            {% else %}
                <th>Rack Rate (Total)</th>
                <th>Net Rate (Total)</th>
                <th>Sell Rate (Total)</th>
            {% endif %}
            <th>Extra Adult</th>
            <th>Extra Child (Age Range 1)</th>
            <th>Extra Child (Age Range 2)</th>
        </tr>
        {% for combinfo in comblist %}
        <tr>
            <td>{{combinfo.idate|date:"d/m/y"}}</td>
            {% if combinfo.fixed_available %}
                <td>{{combinfo.available|add:combinfo.fixed_available}}</td>
            {% else %}
                <td>{{combinfo.available}}</td>
            {% endif %}
            <td>{{combinfo.minimumnights}}</td>
            {% if property_type != 'entire' %}
                <td>{{combinfo.rrsingle}}</td>
                <td>{{combinfo.rrdouble}}</td>
                <td>{{combinfo.rrtriple}}</td>
                <td>{{combinfo.nrsingle}}</td>
                <td>{{combinfo.nrdouble}}</td>
                <td>{{combinfo.nrtriple}}</td>
                <td>{{combinfo.srsingle}}</td>
                <td>{{combinfo.srdouble}}</td>
                <td>{{combinfo.srtriple}}</td>

            {% else %}
                <td>{{combinfo.rrtotal}}</td>
                <td>{{combinfo.nrtotal}}</td>
                <td>{{combinfo.srtotal}}</td>

            {% endif %}
            <td>{{combinfo.extraadult}}</td>
            <td>{{combinfo.extrachild1}}</td>
            <td>{{combinfo.extrachild2}}</td>
        </tr>
        {% endfor %}
    </table>
</div>