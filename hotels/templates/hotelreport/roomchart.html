{%load commonfilter %}
<div style="font-size:17px;border:1px;">
    {% for x in lastpage|range %}
        {%if thispage == x %}<label>{{x|add:1}}</label>{% else %}<a href="javascript:void(0);" onclick="getChart('{{x|add:1}}','{{objecttype}}','{{city}}');"><label style="border: solid 1px; padding:3px;">{{x|add:1}}</label></a>{%endif%}
{%endfor%}
</div>
<div>
    <table>
        <tr>
            <th>Room Name (Room Type)</th>
            <th>HotelName </th>
            <th>Amenities</th><th>Images</th><th>Rateplans</th><th>Invnetory Added</th>
            <th>Overview</th>
            <th>Description</th>
            <th>Rooms</th><th>Room Size</th><th>Bed Type</th><th>Year (built)</th>
            <th>Base Adult Occupancy</th><th>Base Child Occupancy</th>
            <th>Max Adult Occupancy</th><th>Max Infant Occupancy</th>
            <th>Max Child Occupancy</th><th>Max Guest Occupancy</th>
            <th>Created On</th><th>Modified On</th><th>User</th>
        </tr>
        {%for rm in rooms%}
        <tr>
            <td><a href="{{rm.admin_url}}" target="_blank">{{rm.roomtypename}} ({{rm.get_roomtype_display}})</a></td>
            <td><a href="{{rm.hotel.admin_url}}" target="_blank">{{rm.hotel.hotelname}} ({{rm.hotel.city}})</a></td>
            <td>{% if rm.amenities.all %}<img alt="True" src="/static/admin/img/icon-yes.gif">{{rm.amenities.count}}{%else%}<img alt="True" src="/static/admin/img/icon-no.gif"> {%endif%}</td>
            <td>{% if rm.images.all %}<img alt="True" src="/static/admin/img/icon-yes.gif">{{rm.images.count}}{%else%}<img alt="True" src="/static/admin/img/icon-no.gif"> {%endif%}</td>
            <td>{% if rm.rateplans.all %}<img alt="True" src="/static/admin/img/icon-yes.gif">{{rm.rateplans.count}}{%else%}<img alt="True" src="/static/admin/img/icon-no.gif"> {%endif%}</td>
            <td>{% if rm.desc %}<img alt="True" src="/static/admin/img/icon-yes.gif">{%else%}<img alt="True" src="/static/admin/img/icon-no.gif"> {%endif%}</td>
            <td>{% if rm.description %}<img alt="True" src="/static/admin/img/icon-yes.gif">{%else%}<img alt="True" src="/static/admin/img/icon-no.gif"> {%endif%}</td>
            <td>{{rm.noofrooms}}</td><td>{{rm.roomsize}}</td><td>{{rm.bedtype}}</td><td>{{rm.get_extra_bed_display}}</td>
            <td>{{rm.base_adult_occupancy}}</td><td>{{rm.base_child_occupancy}}</td>
            <td>{{rm.max_adult_occupancy}}</td><td>{{rm.max_infant_occupancy}}</td>
            <td>{{rm.max_child_occupancy}}</td><td>{{rm.max_guest_occupancy}}</td>
            <td>{{rm.createdon.date}}</td><td>{{rm.modifiedon.date}}</td><td>{{rm.user.username}}</td>
        </tr>
        {%endfor%}
    </table>
</div>
<div style="font-size:17px;border:1px;">
    {% for x in lastpage|range %}
        {%if thispage == x %}<label>{{x|add:1}}</label>{% else %}<a href="javascript:void(0);" onclick="getChart('{{x|add:1}}','{{objecttype}}','{{city}}');"><label style="border: solid 1px; padding:3px;">{{x|add:1}}</label></a>{%endif%}
{%endfor%}
</div>