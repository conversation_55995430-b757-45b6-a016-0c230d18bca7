{% extends "admin/object_history.html" %}
{% load i18n %}

{% block content %}
<div id="content-main">
<div class="module">

{% if custom_action_list %}
    <h2> Inventory Changes</h2>
    <table id="change-history">
        <thead>
        <tr>
            <th scope="col">{% trans 'Date/time' %}</th>
            <th scope="col">{% trans 'User' %}</th>
            <th scope="col">{% trans 'Success' %}</th>
            <th scope="col">{% trans 'Changes' %}</th>
        </tr>
        </thead>
        <tbody>
        {% for action in custom_action_list %}
        <tr>
            <th scope="row">{{ action.action_time|date:"DATETIME_FORMAT" }}</th>
            {% if action.user %}
            <td>{{ action.user.username }}{% if action.user.get_full_name %} ({{ action.user.get_full_name }}){% endif %}</td>
            {% else %}
            <td> Goibibo System </th>
            {% endif %}
            <td>{{ action.success }}</td>
            <td>{{ action.change_message }}</td>
        </tr>
        {% endfor %}
        </tbody>
    </table>
{% else %}
    <p>{% trans "This object doesn't have a change history. It probably wasn't added via this admin site." %}</p>
{% endif %}
</div>


{% for rateplan in rateplans %}
    <h1> {{rateplan.module_name}} {{rateplan.object}}</h1>
    <div class="module">
    {% if rateplan.custom_action_list %}
        <h2> Rates Changes {{rateplan.object}}</h2>
        <table id="change-history">
            <thead>
            <tr>
                <th scope="col">{% trans 'Date/time' %}</th>
                <th scope="col">{% trans 'User' %}</th>
                <th scope="col">{% trans 'Success' %}</th>
                <th scope="col">{% trans 'Changes' %}</th>
            </tr>
            </thead>
            <tbody>
            {% for action in rateplan.custom_action_list %}
            <tr>
                <th scope="row">{{ action.action_time|date:"DATETIME_FORMAT" }}</th>
                {% if action.user %}
                <td>{{ action.user.username }}{% if action.user.get_full_name %} ({{ action.user.get_full_name }}){% endif %}</td>
                {% else %}
                <td> Goibibo System </th>
                {% endif %}
                <td>{{ action.success }}</td>
                <td>{{ action.change_message }}</td>
            </tr>
            {% endfor %}
            </tbody>
        </table>
    {% else %}
        <p>{% trans "This object doesn't have a change history. It probably wasn't added via this admin site." %}</p>
    {% endif %}
    </div>
{%endfor%}
</div>
{% endblock %}