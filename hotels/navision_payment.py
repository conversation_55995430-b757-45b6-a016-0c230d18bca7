import datetime
import csv
from django.db.models import Count
from django.conf import settings
from ingouser.models import User
from utils.logger import Logger
from lib.MDBStorage import MDBStorage
import mysql.connector
from django.conf import settings

from hotels.methods import HotelMethods
from hotels.tasks import send_hotel_payment_info_mail
from communication.common_comms.communications import sendMail
from hotels.hotelchoice import ADJUSTMENT_STATUS_DICT
from hotels.models import (HotelOutwardPayment, HotelBooking,
                           HotelPaymentLink, HotelDetail, AdjustmentEntry, MultiRoomBookingInfo, PaymentMultiRoom)
import traceback
import hotelchoice
hm = HotelMethods()
mdb = MDBStorage()
from bson import json_util
from django.db.models import Q
import json
inventory_logger = Logger(logger="inventoryLogger")

from hotels.bookingpayments import close_db_connection

booking_columns = ['id', 'city', 'bookingvendor', 'confirmbookingid',
                       'hotel', 'bookingamount', 'nettamount', 'promocode',
                       'mihpayid', 'gocashpaymentid', 'bookingstatus',
                       'confirmstatus', 'paymentstatus', 'payathotelflag',
                       'paymentoutdate', 'collection_amount', 'createdon',
                       'pre_buy_flag', 'rnpl_flag', 'offlinealternate', 'booking_vendor_name']

adjustment_columns = ['hotelid', 'amount', 'paymentstatus',
                      'adjustmentstatus', 'paymentdate']

error_emaillist = ["<EMAIL>", '<EMAIL>', '<EMAIL>']
update_emaillist = ["<EMAIL>", '<EMAIL>', '<EMAIL>']

words = ('PRE_BUY', 'PLB', 'Prebuy')


def update_exiting_paymentid(hop, obj, amount, user=None, payment_type = 'payment'):
    if not hop.user:
        hop.user = user
    try:
        payment_details = eval(hop.paymentdetail)
    except:
        payment_details = hop.paymentdetail
    booking_ids = []
    adjustment_ids = []
    if payment_type == 'adjustment':
        adjustment_ids = payment_details.get('adjustment', [])
        adjustment_ids.append(obj.adjustmentid)
    elif payment_type == 'payment':
        booking_ids = payment_details.get('bookings', [])
        booking_ids.append(obj.confirmbookingid)
    payment_action = 'adjustment'
    if amount > 0:
        hop.amount += amount
        obj.paymentstatus = 'processed'
        payment_action = 'payment'
    hplink = HotelPaymentLink(payment=hop, content_object=obj, amount=amount)
    hplink.user = hop.user
    if payment_type == 'payment':
        msg = 'initiated action %s of Rs. %s for booking %s' % (
            str(obj.paymentstatus), str(abs(hplink.amount)), str(obj.confirmbookingid))
        cmsg = 'initiated action %s of Rs. %s with gbpid %s' % (
            str(obj.paymentstatus), str(abs(hplink.amount)), str(hop.paymentid))
    elif payment_type == 'adjustment':
        msg = (
        'initiated adjustment entry of Rs. %s for this %s adjustment with payment action %s of adjustment id %s' %
        (hplink.amount, obj.adjustmenttype, payment_action, obj.adjustmentid))
        cmsg = ('initiated adjustment entry of Rs. %s with gbpid %s' % (hplink.amount, hop.paymentid))

    hm.updateLogMsg(hop.user, hop, msg)
    hm.updateLogMsg(hop.user, obj, cmsg)
    close_db_connection()
    if booking_ids:
        payment_details['bookings'] = booking_ids
    if adjustment_ids:
        payment_details['adjustment'] = adjustment_ids
    hop.paymentdetail = payment_details
    hop.save()
    hplink.save()


def create_multi_room_payment(multiroom_obj, user_obj, vendor):
    vendor_child_ids = []
    child_booking_ids = []
    payment_multi_room_obj = None
    for obj in multiroom_obj:
        vendor_child_ids.append(obj.vendor_child_booking_id)
        child_booking_ids.extend((obj.child_booking_ids).split(','))
    if child_booking_ids:
        child_booking_ids = list(set(child_booking_ids))
    if multiroom_obj:
        multiroom_obj = multiroom_obj[0]
        if not payment_multi_room_obj:
            payment_dict = {}
            payment_dict['parent_booking_id'] = multiroom_obj.parent_booking_id
            payment_dict['vendor_parent_booking_id'] = multiroom_obj.vendor_parent_booking_id
            payment_dict['vendor_child_booking_ids'] = ','.join(vendor_child_ids)
            payment_dict['hotel_code'] = multiroom_obj.hotel_code
            payment_dict['booking_ids'] = ','.join(child_booking_ids)
            payment_dict['user'] = user_obj
            # payment_dict['payment_date'] = hop.paymentdate
            payment_dict['booking_vendor_name'] = vendor
            try:
                booking_nett_amount = HotelBooking.objects.filter(
                    confirmbookingid__in=child_booking_ids).values(
                    'nettamount').annotate(Count('nettamount'))
                if booking_nett_amount:
                    payment_dict['nett_amount'] = booking_nett_amount[0]['nettamount']
            except Exception, e:
                payment_dict['nett_amount'] = 0
            payment_multi_room_obj = PaymentMultiRoom.objects.create(**payment_dict)
    return payment_multi_room_obj


def update_multiple_payment_id():
    multirooms = MultiRoomBookingInfo.objects.using('report-slave').all()
    for obj in multirooms:
        create_master_dict2(local_flag=True, arg_vendorbookingid=obj.vendor_parent_booking_id, vendor='MakeMyTrip', allow_mail=False, checkhop=False, tech_mail=False)


# using report-slave as required -
def create_master_dict2(start=2, end=1, checkhop=True, local_flag = False, allow_mail=True, perform_update=True, vendor='goibibo', arg_vendorbookingid = None, popup = False, arg_bank_ref = None, start_date=None, end_date=None, tech_mail=True, allow_log = False):
    if popup and not arg_vendorbookingid:
        return {}
    today = datetime.datetime.now().date()
    inventory_logger.info(
        message='Payment calculation start for %s on %s date' % (vendor, today),
        log_type='ingoibibo',
        bucket='NavisionPayment',
        stage='create_master_dict2')
    if tech_mail and not popup:
        pass
        # sendMail(subject='payment calculation start for %s (%s)' % (vendor, today),
        #          mail_data='payment calculation start for %s (%s)' % (vendor, today),
        #          emailid=error_emaillist, tempId='60.072', send_to_email=[],
        #          fromEmailId='<EMAIL>', toEmailIds=update_emaillist)
    if vendor == "goibibo":
        # CHECKUSERCHANGE - DONE
        user_obj = User.objects.get(username='navision')
    else:
        # CHECKUSERCHANGE - DONE
        user_obj = User.objects.get(username='mmt2ibibocron')
    for i in range(end, start)[::-1]:
        perform_update = perform_update
        if vendor == 'goibibo':
            db_data = get_fulldbdata(d = i, arg_vendorbookingid = arg_vendorbookingid, arg_bank_ref=arg_bank_ref, start=start_date, end=end_date, allow_log=allow_log)
        else:
            db_data = get_mmt_fulldbdata(d = i, arg_vendorbookingid = arg_vendorbookingid, arg_bank_ref=arg_bank_ref, start=start_date, end=end_date, allow_log=allow_log)
        master_data_dict = {}
        # total_number_of_records = len(db_data)
        update_file = ""
        remaining_utr = {}
        with open('/tmp/back_payment_error.txt', 'wb') as csvfile:
            spamwriter = csv.writer(csvfile, delimiter=' ',
                                    quotechar='|', quoting=csv.QUOTE_MINIMAL)
            spamwriter.writerow(['BookingNo','Date','UTR','Amount', 'VendorNo', 'HotelCode', 'vendorbookingid'])
            bank_ref_date_mapping = []
            try:
                for row in db_data:
                    try:
                        offline_booking = False
                        temp_checkhop = checkhop
                        booking_id = row.get('BookingNo')
                        adj_booking_id = row.get('adj_pnr')
                        vendorbookingid = row.get('VendorBookingId')
                        d_documenttype = row.get('dDocumentType')
                        v_documenttype = row.get('vDocumentType')
                        d_entrytype = row.get('dEntryType')
                        abookingid = row.get('aBookingID')
                        row_vendor_booking = row.get('VendorBookingId')
                        row_hotelcode = row.get('HotelCode')
                        row_vendorNo = row.get('VendorNo')
                        description = row.get('Description')
                        new_pnr = row.get('newBookingNo')
                        new_adjustment_pnr = row.get('new_adj_pnr')
                        if new_adjustment_pnr and new_adjustment_pnr.isdigit():
                            adj_booking_id = new_adjustment_pnr
                        if popup and row.get('Description'):
                            for word in words:
                                if word in row.get('Description'):
                                    description = row.get('Description')
                        if booking_id == "" and vendorbookingid == "":
                            continue
                        payment_date = row['Date']
                        bank_ref= row.get('UTR')
                        multi_room_flag = False
                        recover_multi_room_flag = False
                        obj = None
                        r_booking_obj = None
                        if (vendorbookingid and 'htc' in vendorbookingid.lower()) or (abookingid and 'htc' in abookingid.lower()):
                            booking_id = None
                        if abookingid and 'htc' in abookingid.lower():
                            adj_booking_id = None
                        if new_pnr and new_pnr.isdigit():
                            obj = HotelBooking.objects.using('report-slave').filter(id = new_pnr).select_related('hotel').first()
                        if len(vendorbookingid) > 10 and not booking_id and not obj:
                            if vendor == 'goibibo':
                                vendorbookingid = "HTL"+vendorbookingid[-7:]
                            close_db_connection()
                            obj = HotelBooking.objects.using('report-slave').filter(vendorbookingid=vendorbookingid).select_related('hotel').first()#.exclude(paymentstatus="processed")
                        if not obj and booking_id:
                            close_db_connection()
                            if vendor == 'goibibo':
                                obj = HotelBooking.objects.using('report-slave').filter(id=int(booking_id)).select_related('hotel').only(*booking_columns).first()
                            else:
                                obj = HotelBooking.objects.using('report-slave').filter(vendorbookingid=booking_id).select_related('hotel').only(
                                    *booking_columns).first()
                        # if obj and vendor == 'goibibo' and obj.alterbookingid and row_hotelcode != obj.hotel.voyagerid:     # for alternative booking case
                        #     obj = HotelBooking.objects.using('report-slave').filter(confirmbookingid=obj.alterbookingid).select_related('hotel').only(
                        #             *booking_columns).first()
                        if not obj:
                            obj = PaymentMultiRoom.objects.using('default').filter(vendor_parent_booking_id=row_vendor_booking).first()
                            if not obj:
                                multi_room_objs = MultiRoomBookingInfo.objects.filter(vendor_parent_booking_id=row_vendor_booking)
                                if multi_room_objs:
                                    obj = create_multi_room_payment(multi_room_objs, user_obj, vendor)
                                    multi_room_flag = True
                            else:
                                multi_room_flag = True
                        if vendor != 'goibibo' and not obj:
                            offline_booking = True
                        if not obj and not offline_booking and vendor == 'goibibo':
                            obj = get_bookingobj(vendor_booking = row_vendor_booking, supplier_code = row_vendorNo)
                        if not obj and not offline_booking:
                            continue #TODO mail error
                        try:
                            amount = float(row['Amount'])
                        except:
                            amount = float(0)
                        if amount > 0 and amount < 1:
                            continue
                        close_db_connection()
                        txn_type = ''
                        if d_entrytype != 2:
                            continue
                        if v_documenttype == 2 and d_documenttype == 1:
                            txn_type = 'payment'
                        elif v_documenttype == 2 and d_documenttype in (3,0):
                            txn_type = 'adjustment'
                        elif v_documenttype in (3,0) and d_documenttype in (3,0):
                            txn_type = 'recovery'
                        if txn_type in ('adjustment', 'recovery') and abookingid == row.get('VendorBookingId'):
                            continue
                        if not txn_type:
                            continue
                        if txn_type in ('adjustment', 'recovery'):
                            payment_date = row.get('dPostingDate')
                        if offline_booking:
                            if not booking_id:
                                booking_id = vendorbookingid
                            temp_amount = amount
                            if row_hotelcode and row_hotelcode.lower() != 'x' and row_hotelcode.isdigit():
                                hotel_obj = HotelDetail.objects.using('report-slave').filter(mmt_id=row_hotelcode).only('hotelcode')
                                if hotel_obj:
                                    row_hotelcode = hotel_obj[0].hotelcode
                            if txn_type == 'payment':
                                booking_dict = {'amount': temp_amount, "rbooking_id": booking_id}
                            if txn_type == 'adjustment' or txn_type == 'recovery':
                                booking_dict = {'amount': -amount, "rbooking_id": abookingid}
                                temp_amount = 0
                                if row['aSubDocumentType'] == 19 and vendor != 'goibibo':
                                    booking_dict['rbooking_id'] = booking_id
                                    booking_dict['plb'] = True
                            if booking_id not in master_data_dict:
                                master_data_dict[booking_id] = {'bookingid': booking_id, 'booking_list': [],
                                                                'hotelcode': row_hotelcode, 'recovery_dict_list': [],
                                                                'adjustment_dict_list': [],
                                                                'booking_payout': temp_amount, 'payment_date': payment_date,
                                                                'bankref': bank_ref, 'payment_ids': [],
                                                                'offline_booking_list': [booking_dict], 'advance_pay': False}
                            else:
                                master_data_dict[booking_id]['booking_payout'] += temp_amount
                                master_data_dict[booking_id]['offline_booking_list'].append(booking_dict)
                            continue

                        if row["aSubDocumentType"] == 25:
                            temp_checkhop = False

                        # obj = obj.first()
                        temp_booking_id = None
                        if row.get('aBookingID') and not 'ADT' in row['aBookingID']:
                            if adj_booking_id and adj_booking_id.isdigit():
                                r_booking_obj = HotelBooking.objects.filter(id=int(adj_booking_id)).only(*booking_columns).first()
                            elif row['aBookingID'].isdigit():
                                r_booking_obj = HotelBooking.objects.filter(id=int(row['aBookingID'])).only(*booking_columns).first()
                                temp_booking_id = r_booking_obj.vendorbookingid
                            else:
                                if vendor == 'goibibo':
                                    temp_booking_id = "HTL" + row.get('aBookingID')[-7:]
                                else:
                                    temp_booking_id = abookingid
                                r_booking_obj = HotelBooking.objects.filter(vendorbookingid=temp_booking_id).only(*booking_columns).first()
                            if not r_booking_obj:
                                r_booking_obj = PaymentMultiRoom.objects.using('default').filter(vendor_parent_booking_id=row.get('aBookingID')).first()
                                if not r_booking_obj:
                                    multi_room_objs = MultiRoomBookingInfo.objects.filter(vendor_parent_booking_id=row.get('aBookingID'))
                                    if multi_room_objs:
                                        r_booking_obj = create_multi_room_payment(multi_room_objs, user_obj, vendor)
                                        recover_multi_room_flag = True
                                else:
                                    recover_multi_room_flag = True
                        if r_booking_obj:
                            # r_booking_obj = r_booking_obj.first()
                            if recover_multi_room_flag:
                                r_booking_obj.id = r_booking_obj.vendor_parent_booking_id

                        if multi_room_flag:
                            hotelcode = obj.hotel_code
                            obj.id = obj.vendor_parent_booking_id
                            booking_id = obj.vendor_parent_booking_id
                            # recovery_hotelcode = r_booking_obj.hotelcode
                        else:
                            hotelcode = obj.hotel.hotelcode
                            # recovery_hotelcode = r_booking_obj.hotel.hotelcode
                            booking_id = obj.id

                        hop = []

                        if bank_ref:
                            close_db_connection()
                            hop = HotelOutwardPayment.objects.using('report-slave').filter(bankreference=bank_ref)

                        if not bank_ref:
                            credit_utr = ['credit_' + str(obj.id), 'adjustment_' + str(obj.id)]
                            close_db_connection()
                            hop = HotelOutwardPayment.objects.using('report-slave').filter(bankreference__in=credit_utr)

                        if not hop or not temp_checkhop:
                            if not booking_id in master_data_dict:
                                master_data_dict[booking_id] = {}
                                master_data_dict[booking_id] = {'bookingid': booking_id, 'booking_list':[], 'hotelcode': hotelcode, 'recovery_dict_list':[], 'adjustment_dict_list':[],
                                                                'booking_payout': 0, 'payment_date': payment_date, 'bankref': '', 'payment_ids': [], 'offline_booking_list': [], 'advance_pay': False}
                                if hop and popup and not multi_room_flag:
                                    payment_ids = get_paymentids(obj.payments.all())
                                    master_data_dict[booking_id]['payment_ids'] = payment_ids
                            if master_data_dict[booking_id]['hotelcode'] != hotelcode:
                                inventory_logger.info(
                                    message = 'multiple hotels have same bank ref, bank reference: %s and bank code: %s' % (str(master_data_dict[bank_ref]), str(hotelcode)),
                                    log_type = 'ingoibibo',
                                    bucket = 'NavisionPayment',
                                    stage = 'create_master_dict2')

                            if txn_type == 'payment': #["aSubDocumentType"] == 0 :
                                temp_amount = 0
                                if master_data_dict[booking_id]['bankref'] != "" and master_data_dict[booking_id]['bankref'] != bank_ref:
                                    remaining_utr[bank_ref] = {"booking_list": [
                                        {"booking_id": obj.id, "amount": amount, 'description': description}],
                                                               "booking_payout": amount, "payment_date": payment_date,
                                                               "hotelcode": hotelcode, "bookingid": obj.id}
                                    continue
                                if row['aBookingID'] and row['aBookingID'] != row.get('VendorBookingId'):
                                    if r_booking_obj:
                                        if ((row['aBookingID'] != row.get('VendorBookingId')) or (d_documenttype == 1 and v_documenttype == 2 and not bank_ref)):
                                            temp_booking_dict = {'rbooking_id': r_booking_obj.id, 'amount': -1 * abs(amount), 'description': description}
                                            temp_amount = amount
                                            master_data_dict[obj.id]['recovery_dict_list'].append(temp_booking_dict)
                                    else:
                                        # inventory_logger.debug("rbooking id missing: %s" % row)
                                        inventory_logger.info(message = ( "rbooking id missing: %s" % (row)),
                                                              log_type = 'ingoibibo',
                                                              bucket = 'NavisionPayment',
                                                              stage = 'create_master_dict2')
                                if row.get("aSubDocumentType") == 25:
                                    master_data_dict[booking_id]["advance_pay"] = not(temp_checkhop)
                                if bank_ref and r_booking_obj and r_booking_obj.id != obj.id:
                                    if r_booking_obj.id not in master_data_dict:
                                        master_data_dict[r_booking_obj.id] = {}
                                        master_data_dict[r_booking_obj.id] = {
                                            'bookingid': r_booking_obj.id, 'booking_list': [],
                                            'hotelcode': hotelcode,
                                            'recovery_dict_list': [],
                                            'adjustment_dict_list': [],
                                            'booking_payout': 0,
                                            'payment_date': payment_date, 'bankref': '',
                                            'payment_ids': [], 'offline_booking_list': [],
                                            'advance_pay': False
                                        }
                                    master_data_dict[r_booking_obj.id]['bankref'] = bank_ref
                                    booking_dict = {'amount': amount,
                                                    "booking_id": r_booking_obj.id,
                                                    'description': description}
                                    temp_date_bank_ref = {'bank_ref': bank_ref,
                                                          'date': payment_date, }
                                    if booking_dict not in master_data_dict[r_booking_obj.id]['booking_list']:
                                        master_data_dict[r_booking_obj.id]['booking_list'].append(booking_dict)
                                    if temp_date_bank_ref not in bank_ref_date_mapping:
                                        bank_ref_date_mapping.append(
                                            {'bank_ref': bank_ref, 'date': str(payment_date.date())})
                                    master_data_dict[r_booking_obj.id]["booking_payout"] += amount
                                elif bank_ref:
                                    master_data_dict[booking_id]['bankref'] = bank_ref
                                    amount -= temp_amount
                                    booking_dict = {'amount': amount, "booking_id": booking_id, 'description': description}
                                    temp_date_bank_ref = {'bank_ref': bank_ref, 'date': payment_date, }
                                    if booking_dict not in master_data_dict[booking_id]['booking_list']:
                                        master_data_dict[booking_id]['booking_list'].append(booking_dict)
                                    if temp_date_bank_ref not in bank_ref_date_mapping:
                                        bank_ref_date_mapping.append({'bank_ref': bank_ref, 'date': str(payment_date.date())})
                                    master_data_dict[booking_id]["booking_payout"] += amount
                                    master_data_dict[booking_id]["payment_date"] = payment_date
                            elif (row["aSubDocumentType"] in (17, 27) or (txn_type in ('adjustment', 'recovery') and row["aSubDocumentType"] not in range(18, 23))) and abookingid and 'ADT' not in row.get('aBookingID'):
                                # if len(row.get("aBookingID")) > 10:
                                #     if vendor == 'goibibo' and 'ADT' not in row.get('aBookingID'):
                                #         temp_booking_id = "HTL" + row.get('aBookingID')[-7:]
                                #     else:
                                #         temp_booking_id = abookingid
                                #     close_db_connection()
                                #     r_booking_obj = HotelBooking.objects.filter(vendorbookingid=temp_booking_id).only(*booking_columns).first()
                                # else:
                                #     temp_booking_id = row.get("aBookingID")
                                #     # if temp_booking_id.isdigit():
                                #     #     temp_booking_id = int(temp_booking_id)
                                #     close_db_connection()
                                #     if vendor == "goibibo":
                                #         r_booking_obj = HotelBooking.objects.filter(id=int(temp_booking_id)).only(*booking_columns).first()
                                #     else:
                                #         r_booking_obj = HotelBooking.objects.filter(vendorbookingid=temp_booking_id).only(*booking_columns).first()
                                if bank_ref and r_booking_obj:
                                    if r_booking_obj.id in master_data_dict and master_data_dict[r_booking_obj.id]["bankref"] != bank_ref:
                                        inventory_logger.error("")  # TODO mail this case
                                    if not hop:
                                        if r_booking_obj.id not in master_data_dict:
                                            master_data_dict[r_booking_obj.id] = {
                                                'bookingid': r_booking_obj.id,
                                                'booking_list': [],
                                                'hotelcode': hotelcode,
                                                'recovery_dict_list': [],
                                                'adjustment_dict_list': [],
                                                'booking_payout': 0,
                                                'payment_date': payment_date,
                                                'bankref': bank_ref,
                                                'offline_booking_list': [],
                                                'advance_pay': False
                                            }
                                        booking_dict = {'amount': amount,
                                                        "booking_id": r_booking_obj.id , 'description': description}

                                        master_data_dict[r_booking_obj.id]['bankref'] = bank_ref
                                        master_data_dict[r_booking_obj.id]["booking_payout"] += amount
                                        master_data_dict[r_booking_obj.id]['booking_list'].append(booking_dict)
                                # recovery_vendor_id = "HTL" + row.get('aBookingID')[-7:] if row.get("aBookingID") else row.get("aBookingID")
                                # revover_booking_id = HotelBooking.objects.filter(vendorbookingid=recovery_vendor_id).only(*booking_columns).first()
                                # recovery_bobj = booking_obj
                                if r_booking_obj:
                                    booking_dict = {"rbooking_id": r_booking_obj.id,"amount": -1 * abs(amount), 'description': description}
                                    master_data_dict[booking_id]['recovery_dict_list'].append(booking_dict)
                                elif not r_booking_obj and vendor != 'goibibo' and temp_booking_id:
                                        booking_dict = {"rbooking_id": temp_booking_id,
                                                        "amount": -1 * abs(amount),
                                                        'description': description}
                                        master_data_dict[booking_id]['offline_booking_list'].append(booking_dict)

                            if not r_booking_obj and 'Security' in row.get('Description') and not bank_ref and row["aSubDocumentType"] == 27:
                                booking_dict = {"rbooking_id": obj.id, "amount": -1 * abs(amount), 'description': description}
                                # master_data_dict[booking_id]['bankref'] = description
                                master_data_dict[booking_id]['booking_payout'] -= amount
                                master_data_dict[booking_id]['booking_list'].append(booking_dict)

                            elif row["aSubDocumentType"] in range(18,23):
                                # if 'ADT' not in row.get('aBookingID'):
                                    # if len(row.get("aBookingID")) > 10:
                                    #     if vendor == 'goibibo':
                                    #         temp_booking_id = "HTL" + row.get('aBookingID')[-7:]
                                    #     else:
                                    #         temp_booking_id = row.get("aBookingID")
                                    #     close_db_connection()
                                    #     r_booking_obj = HotelBooking.objects.filter(vendorbookingid=temp_booking_id).only(*booking_columns).first()
                                    # else:
                                    #     temp_booking_id = row.get("aBookingID")
                                    #     close_db_connection()
                                    #     if vendor == 'goibibo':
                                    #         r_booking_obj = HotelBooking.objects.filter(id=int(temp_booking_id)).only(*booking_columns).first()
                                    #     else:
                                    #         r_booking_obj = HotelBooking.objects.filter(vendorbookingid=temp_booking_id).only(*booking_columns).first()

                                if row['aSubDocumentType'] == 19 and 'ADT' in row['aBookingID']:
                                    adjustment_id = row['aBookingID']
                                    adjustment_obj = AdjustmentEntry.objects.filter(adjustmentid=adjustment_id)
                                    if adjustment_obj:
                                        payments = adjustment_obj.first().payments.all()
                                        adj_paid_amount = 0
                                        for pay in payments:
                                            adj_paid_amount += pay.amount
                                        if adj_paid_amount == amount:
                                            continue
                                        # if adjustment_obj[0].adjustmentstatus == 'recovery':
                                        amount = abs(amount)
                                        booking_dict = {'adjustmentid': adjustment_id, 'amount': amount, 'description': description}
                                        master_data_dict[booking_id]['adjustment_dict_list'].append(booking_dict)
                                elif row['aSubDocumentType'] == 19 and vendor != 'goibibo':
                                    booking_dict = {'rbooking_id': obj.id, 'amount': -1 * amount, 'plb': True, 'description': description}
                                    master_data_dict[booking_id]['recovery_dict_list'].append(booking_dict)
                                elif row['aSubDocumentType'] == 18 and r_booking_obj:#and obj.payathotelflag:
                                    amount = -1 * amount
                                    if r_booking_obj:
                                        booking_dict = {'rbooking_id': r_booking_obj.id, 'amount': amount, 'description': description}
                                        master_data_dict[booking_id]['recovery_dict_list'].append(booking_dict)
                                elif not r_booking_obj and vendor != 'goibibo' and temp_booking_id:
                                    booking_dict = {"rbooking_id": temp_booking_id,
                                                    "amount": -1 * abs(amount),
                                                    'description': description}
                                    master_data_dict[booking_id]['offline_booking_list'].append(booking_dict)
                                elif r_booking_obj:
                                    booking_dict = {'rbooking_id': r_booking_obj.id, 'amount': -1*amount, 'description': description}
                                    master_data_dict[booking_id]['recovery_dict_list'].append(booking_dict)
                                else:
                                    # inventory_logger.debug("payment entry error: %s" % (row))
                                    inventory_logger.info(
                                        message = ("payment entry error: %s" % (row)),
                                        log_type = 'ingoibibo',
                                        bucket = 'NavisionPayment',
                                        stage = 'create_master_dict2')
                                    # master_data_dict[booking_id]['total_pay_amount'] += float(amount)
                    except Exception, e:
                        close_db_connection()
                        # sendMail(subject='error for booking %s' % row.get('BookingNo'),
                        #          mail_data="row %s and exception %s" %(row, e),
                        #          emailid=error_emaillist, tempId='60.072', send_to_email=[],
                        #          fromEmailId='<EMAIL>', toEmailIds=error_emaillist)
                        continue
            except Exception, e:
                pass
                # sendMail(subject='error for booking',
                #          mail_data="error happen %s" % (e),
                #          emailid=error_emaillist, tempId='60.072', send_to_email=[],
                #          fromEmailId='<EMAIL>', toEmailIds=error_emaillist)

        if popup:
            if master_data_dict:
                master_data_dict[master_data_dict.keys()[0]]['bankref_list'] = bank_ref_date_mapping
            return master_data_dict
        new_master_dict = {}
        for md in master_data_dict:
            if not master_data_dict[md]["booking_list"] and not master_data_dict[md]['recovery_dict_list'] and not master_data_dict[md]["adjustment_dict_list"] and not master_data_dict[md]['booking_payout'] and not master_data_dict[md]["offline_booking_list"]:
                continue
            if (master_data_dict[md]['bankref'] and not master_data_dict[md]["booking_list"] and not master_data_dict[md]["offline_booking_list"]) or (not master_data_dict[md]["adjustment_dict_list"] and not master_data_dict[md]["booking_list"] and not master_data_dict[md]["recovery_dict_list"] and not master_data_dict[md]["offline_booking_list"]):
                continue
            if master_data_dict[md]["bankref"] == "" :
                hop = HotelOutwardPayment.objects.using('report-slave').filter(bankreference="adjustment_" + str(md))
                if not hop:
                    new_master_dict.update({"adjustment_"+str(md): master_data_dict[md]})
                    new_master_dict["adjustment_"+str(md)]['booking_list'].append({"booking_id": md, "amount": float(0)})
                elif len(hop) == 1:
                    if not master_data_dict[md]['booking_list']:
                        master_data_dict[md]['booking_list'].append({"booking_id": md, "amount": float(0)})
                    saveobj_list = []
                    hop = hop[0]
                    payment_details, saveobj_list = process_booking_payments(hop, user_obj, saveobj_list,  master_data_dict[md], data=True)
                    if saveobj_list:
                        try:
                            for obj in saveobj_list:
                                close_db_connection()
                                if isinstance(obj, HotelBooking):
                                    obj.paymentoutdate = master_data_dict[md]['payment_date']
                                    obj.save(
                                        update_fields=['paymentstatus', 'paymentoutdate'])
                                else:
                                    obj.save()
                        except:
                            close_db_connection()
                        if not hop.paymentid:
                            hop.paymentid = hm.update_code(hop.id, 12, pref='GBP')
                        if not hop.payoutdate:
                            hop.payoutdate = hop.paymentdate = master_data_dict[md]['payment_date']
                        hop.paymentdetail = payment_details
                        close_db_connection()
                        hop.save()
                        continue

            else:
                if master_data_dict[md]["bankref"] not in new_master_dict:
                    new_master_dict[master_data_dict[md]["bankref"]]= master_data_dict[md]
                else:
                    new_master_dict[master_data_dict[md]["bankref"]]["adjustment_dict_list"].extend(
                        master_data_dict[md]["adjustment_dict_list"])
                    new_master_dict[master_data_dict[md]["bankref"]]["recovery_dict_list"].extend(
                        master_data_dict[md]["recovery_dict_list"])
                    new_master_dict[master_data_dict[md]["bankref"]]["booking_list"].extend(
                        master_data_dict[md]["booking_list"])
                    new_master_dict[master_data_dict[md]["bankref"]]["offline_booking_list"].extend(
                        master_data_dict[md]["offline_booking_list"])
                    # if new_master_dict[master_data_dict[md]["bankref"]]['bookingid'] not in new_master_dict[master_data_dict[md]["bankref"]]["booking_list"]:
                    #     new_master_dict[master_data_dict[md]["bankref"]]["booking_list"].append(new_master_dict[master_data_dict[md]["bankref"]]['bookingid'])
                        # new_master_dict[master_data_dict[md]["bankref"]]["booking_list"].append()
                    new_master_dict[master_data_dict[md]["bankref"]]["booking_payout"] += master_data_dict[md]["booking_payout"]
        for bank_utr in remaining_utr:
            if new_master_dict.has_key(bank_utr):
                new_master_dict[bank_utr]['booking_payout'] += remaining_utr[bank_utr]["booking_payout"]
                new_master_dict[bank_utr]["booking_list"].extend(remaining_utr[bank_utr]["booking_list"])
            else:
                new_master_dict[bank_utr] = {'bookingid': remaining_utr[bank_utr]["bookingid"],
                                             'booking_list': remaining_utr[bank_utr]['booking_list'],
                                             'hotelcode': remaining_utr[bank_utr]["hotelcode"],
                                             'recovery_dict_list': [], 'adjustment_dict_list': [],
                                             'booking_payout': remaining_utr[bank_utr]["booking_payout"],
                                             'payment_date': remaining_utr[bank_utr]["payment_date"],
                                             'offline_booking_list': [],
                                             'advance_pay': False,
                                             'bankref': bank_utr}
        number_of_update = 0
        close_db_connection()
        if vendor.lower() == 'goibibo':
            detail_payment_info = gi_payment_detail_info(d=i)
        else:
            detail_payment_info = mmt_payment_detail_info(d=i)
        payment_count = 0
        recovery_count = 0
        adjustment_count = 0
        offline_booking_count = 0
        if not local_flag:
            from common.task import navision_payment_data
        new_master_dict_length = len(new_master_dict)
        inventory_logger.info(
            message='Total length of new master dict %s on %s date for %s vendor' % (new_master_dict_length, today, vendor),
            log_type='ingoibibo',
            bucket='NavisionPayment',
            stage='create_master_dict2')
        if perform_update and new_master_dict:
            for data in new_master_dict:
                payment_count += len(new_master_dict[data].get('booking_list', []))
                recovery_count += len(new_master_dict[data].get('recovery_dict_list', []))
                adjustment_count += len(new_master_dict[data].get('adjustment_dict_list', []))
                offline_booking_count += len(new_master_dict[data].get('offline_booking_list', []))
                number_of_update += 1
                close_db_connection()
                inventory_logger.info(
                    message='payment update for bank reference %s and payment breakup %s' % (data, new_master_dict[data]),
                    log_type='ingoibibo',
                    bucket='NavisionPayment',
                    stage = 'create_master_dict2')
                if local_flag:
                    create_paymentid2(new_master_dict[data], data, user_obj, allow_mail=allow_mail, vendor=vendor)
                else:
                    from common.task import navision_payment_data
                    navision_payment_data.apply_async(args=(new_master_dict[data], data, user_obj, allow_mail, vendor,))
        if update_file:
            update_file.close()
        if tech_mail:
            text_data = "payment = %s/%s, recovery= %s/%s, adjustment = %s/%s, offline = %s, total lenght of new master dict %s" % (detail_payment_info.get('payment')[0], payment_count,
                                                                                                      detail_payment_info.get('recovery')[0], recovery_count,
                                                                                                      detail_payment_info.get('adjustment')[0], adjustment_count,
                                                                                                      offline_booking_count, new_master_dict_length)
            # sendMail(subject='payment update done for %s (%s)' % (vendor, today),
            #          mail_data="Total number of update %s.\n total number of records in db: %s, %s" % (number_of_update, total_number_of_records, text_data),
            #          emailid=error_emaillist, tempId='60.072', send_to_email=[],
            #          fromEmailId='<EMAIL>', toEmailIds=update_emaillist)
        inventory_logger.info(
            message='Payment calculation end for %s on %s date' % (vendor, today),
            log_type='ingoibibo',
            bucket='NavisionPayment',
            stage='create_master_dict2')


def get_correct_hop(hops, booking_id, bankref, advance_pay=False):
    for hop in hops:
        paymentdetails = eval(hop.paymentdetail)
        adjustment_ids = paymentdetails.get('adjustment', [])
        booking_ids = paymentdetails.get('bookings', [])
        offline_bookings = paymentdetails.get('offline_bookings', [])
        multi_room_ids = paymentdetails.get('multiroomids', [])
        if multi_room_ids and booking_id in multi_room_ids:
            return [hop]
        elif offline_bookings:
            offline_ids = get_offline_bookings(offline_bookings)
            if booking_id in offline_ids:
                return [hop]


    if advance_pay:
        return []
    return hops

#create payment id
def create_paymentid2(pay_data, bankref, user_obj, allow_mail, vendor='goibibo'):
    try:
        close_db_connection()
        hotel_obj = HotelDetail.objects.using('report-slave').select_related('locality').filter(
            hotelcode=pay_data['hotelcode']).only('hotelcode','hotelname', 'locality', 'base_currency', 'city')
        if not hotel_obj:
            hotel_obj = HotelDetail.objects.using('report-slave').select_related('locality').filter(
                mmt_id=pay_data['hotelcode']).only('hotelcode', 'hotelname', 'locality', 'base_currency', 'city')
        # hops = HotelOutwardPayment.objects.filter(bankreference=bankref, payoutdate=pay_data['payment_date'])
        # if len(hops) > 1:
        #     hops = get_correct_hop(hops, pay_data['bookingid'], bankref, pay_data['advance_pay'])
        # if bankref and hops:
        #     saveobj_list = []
        #     hop = hops[0]
        #     payment_details, saveobj_list = process_booking_payments(hop, user_obj, saveobj_list, pay_data, data=True)
        #     if saveobj_list:
        #         try:
        #             for obj in saveobj_list:
        #                 close_db_connection()
        #                 if isinstance(obj, HotelBooking):
        #                     obj.paymentoutdate = pay_data['payment_date']
        #                     obj.save(update_fields=['paymentstatus', 'paymentoutdate'])
        #                 else:
        #                     obj.save()
        #         except:
        #             close_db_connection()
        #         if not hop.paymentid:
        #             hop.paymentid = hm.update_code(hop.id, 12, pref='GBP')
        #         if not hop.payoutdate:
        #             hop.payoutdate = pay_data['payment_date']
        #
        #         hop.paymentdetail = payment_details
        #         close_db_connection()
        #         hop.save()
        #         # print hop
        #         # return None
        #
        # elif hotel_obj:
        if hotel_obj:
            hotel_obj = hotel_obj[0]
            hop = HotelOutwardPayment(amount=pay_data['booking_payout'], hotel=hotel_obj.hotelcode,
                                      paymentstatus='processed',bankreference=bankref,
                                      base_currency=hotel_obj.base_currency, paymode='NEFT')
            hotelname = repr(hotel_obj.hotelname)
            if hotel_obj.locality and hotel_obj.locality.localityname:
                hotelname = hotelname + "," + str(hotel_obj.locality.localityname)
            hotelname = hotelname + "," + str(hotel_obj.city_id.cityname)
            hop.hotelname = hotelname
            if vendor == 'goibibo':
                accounts_list = hotel_obj.get_vendor_accounts().filter(isactive=True)
                # if not accounts_list:
                #     accounts_list = hotel_obj.accounts.filter(isactive=True, contract_source='goibibo')
            else:
                accounts_list = hotel_obj.get_vendor_accounts(vendor='MakeMyTrip').filter(isactive=True)
                # if not accounts_list:
                #     accounts_list = hotel_obj.accounts.filter(isactive=True, contract_source='mmt')
            if not accounts_list:
                accounts_list = hotel_obj.accounts.filter(isactive=True)
            if accounts_list:
                account = accounts_list[0]
                hop.ifsc = account.ifsc
                hop.accname = account.accname
                hop.accno = account.accno
                hop.bankcode = account.bankcode
                hop.bankname = account.bankname
                hop.branchcode = account.branchcode
                hop.branchname = account.branchname
            close_db_connection()
            hop.save()
            hop.paymentid = hm.update_code(hop.id, 12, pref='GBP')
            hop.user = user_obj
            hop.balanceamount = 0
            hop.payoutdate = hop.paymentdate = pay_data['payment_date']
            saveobj_list = []
            # import pdb; pdb.set_trace()
            paymentdetails, saveobj_list = process_booking_payments(hop, user_obj, saveobj_list, pay_data)
            hop.paymentdetail = paymentdetails
            close_db_connection()
            hop.save()
            try:
                for obj in saveobj_list:
                    close_db_connection()
                    if isinstance(obj, HotelBooking):
                        obj.paymentoutdate = pay_data['payment_date']
                        obj.save(update_fields=['paymentstatus', 'paymentoutdate'])
                    else:
                        obj.save()
            except:
                close_db_connection()
            # if allow_mail:
            #     send_hotel_payment_info_mail.delay(hop=hop)
        else:
            # inventory_logger.info("wrong hotel code in payment data %s" % (pay_data['hotelcode']))
            inventory_logger.critical(
                message = 'wrong hotel code in payment data %s' % (pay_data['hotelcode']),
                log_type='ingoibibo',
                bucket='NavisionPayment',
                stage = 'create_paymentid2')
    except Exception,e:
        # inventory_logger.error(e)
        inventory_logger.critical(
            message = 'error occour while creating payment id data = %s' % (pay_data),
            log_type='ingoibibo',
            bucket='NavisionPayment', stage = 'create_paymentid2')

def get_offline_bookings(offline_bookings):
    booking_ids = []
    for dict_data in offline_bookings:
        booking_ids.append(dict_data['rbooking_id'])
    return booking_ids


def remove_offline_bookings(offline_bookings, multi_room_ids):
    new_offline_bookings = []
    for dict_data in offline_bookings:
        if dict_data['rbooking_id'] not in multi_room_ids:
            new_offline_bookings.append(dict_data)
    return new_offline_bookings


def process_booking_payments(hop, user_obj, saveobj_list, pay_data, data=False):
    paymentdetails = {}
    plb_ids = []
    adjustment_ids = []
    booking_ids = []
    multi_room_ids = []
    offline_ids = []
    offline_bookings = []
    adjustment_list = pay_data.get('adjustment_dict_list', [])
    rbooking_list = pay_data.get('recovery_dict_list', [])
    booking_list = pay_data.get('booking_list', [])
    offline_list = pay_data.get('offline_booking_list', [])
    payment_multi_obj = []
    for booking_dict in booking_list:
        if booking_dict.get('booking_id'):
            rbooking_list.append({"rbooking_id": booking_dict.get('booking_id'), "amount": booking_dict.get("amount"), 'pay': True})
    if data:
        paymentdetails = eval(hop.paymentdetail)
        adjustment_ids = paymentdetails.get('adjustment', [])
        booking_ids = paymentdetails.get('bookings', [])
        present_offline_bookings = paymentdetails.get('offline_bookings', [])
        for dict_data in present_offline_bookings:
            if dict_data.get('booking_id'):
                offline_bookings.append({"rbooking_id": dict_data.get('booking_id'), "amount": dict_data.get("amount")})
            elif dict_data.get('rbooking_id'):
                offline_bookings.append(dict_data)
        multi_room_ids = paymentdetails.get('multiroomids', [])
        plb_ids = paymentdetails.get('plb_bookings', [])
        if offline_bookings:
            offline_ids = get_offline_bookings(offline_bookings)
    if adjustment_list:
        for adj_dict in adjustment_list:
            amount = adj_dict['amount']
            close_db_connection()
            adt_obj = AdjustmentEntry.objects.filter(adjustmentid=adj_dict['adjustmentid']).only(*adjustment_columns).first()
            # paymentstatus='processed'
            if adt_obj.adjustmentstatus == 'recovery':
                action_multiplier = -1
            else:
                action_multiplier = 1
            hplink = HotelPaymentLink(payment=hop, content_object=adt_obj, amount=action_multiplier*amount)
            adt_obj.paymentdate = pay_data['payment_date']
            payment_action = ADJUSTMENT_STATUS_DICT[adt_obj.adjustmentstatus][1]
            hplink.user = user_obj
            adt_obj.paymentstatus= 'processed'
            adt_obj.paymentdate = pay_data['payment_date']
            msg = ('initiated adjustment entry of Rs. %s for this %s adjustment '
                   'with payment action %s of adjustment id %s' %
                   (hplink.amount, adt_obj.adjustmenttype, payment_action,
                    adt_obj.adjustmentid))
            cmsg = ('initiated adjustment entry of Rs. %s with gbpid %s' %
                    (hplink.amount, hop.paymentid))
            hm.updateLogMsg(user_obj, hop, msg)
            hm.updateLogMsg(user_obj, adt_obj, cmsg)
            adjustment_ids.append(adt_obj.adjustmentid)
            saveobj_list.append(adt_obj)
            saveobj_list.append(hplink)
            #bulk create hplinks
        paymentdetails['adjustment'] = adjustment_ids
    if rbooking_list:
        for dict_data in rbooking_list:
            close_db_connection()
            multi_payment = False
            multi_payment_link = False
            bkngs = None
            if str(dict_data['rbooking_id']).isdigit():
                bkngs = HotelBooking.objects.filter(id=dict_data['rbooking_id']).only('payathotelflag', 'confirmbookingid', 'paymentstatus', 'paymentoutdate').only(*booking_columns)
                if (bkngs and bkngs[0].confirmbookingid in booking_ids):
                    if bkngs[0].payments.all():
                        if bkngs[0] == 'pending':
                            bkngs[0].paymentstatus = 'processed'
                            bkngs[0].save()
                    continue
            else:
                if (dict_data['rbooking_id'] in multi_room_ids and 'plb' not in dict_data) or (dict_data['rbooking_id'] in plb_ids and 'plb' in dict_data):
                    # offline_list = remove_offline_bookings(offline_bookings, vendor_parent_booking_id)
                    continue
                payment_multi_obj = PaymentMultiRoom.objects.using('default').filter(vendor_parent_booking_id=dict_data['rbooking_id']).first()
                if payment_multi_obj and payment_multi_obj.booking_ids:
                    bkngs = HotelBooking.objects.filter(confirmbookingid__in=payment_multi_obj.booking_ids.split(','))
                    if bkngs:
                        multi_payment = True
                        multi_payment_link = True
            if not bkngs:
                if dict_data['rbooking_id'] not in offline_ids:
                    offline_list.append(dict_data)
                if dict_data['rbooking_id'] in offline_ids:
                    for offline_dict in offline_bookings:
                        if offline_dict['rbooking_id'] == dict_data['rbooking_id'] and offline_dict['amount'] != dict_data['amount']:
                            offline_dict['amount'] = dict_data['amount']
                continue
            # else:
            #     bkng = bkngs[0]
            for bkng in bkngs:
                if not bkng.paymentoutdate:
                    bkng.paymentoutdate = pay_data['payment_date']
                if bkngs and bkngs[0].confirmbookingid in booking_ids:
                    continue
                if multi_payment:
                    if multi_payment_link:
                        vendor_parent_booking_id = payment_multi_obj.vendor_parent_booking_id
                        if data and dict_data['amount'] > 0 and not vendor_parent_booking_id in multi_room_ids and not vendor_parent_booking_id in offline_ids:
                            hop.amount += dict_data['amount']
                        if 'plb' not in dict_data and not vendor_parent_booking_id in multi_room_ids:
                            multi_room_ids.append(payment_multi_obj.vendor_parent_booking_id)
                        payment_multi_obj.payment_status = 'processed'
                        payment_multi_obj.paymentdate = pay_data['payment_date']
                        hplink = HotelPaymentLink(payment=hop, content_object=payment_multi_obj, amount=dict_data['amount'])
                        saveobj_list.append(payment_multi_obj)
                        multi_payment_link = False
                        if offline_ids:
                            offline_list = remove_offline_bookings(offline_bookings, multi_room_ids)
                            if not offline_list and paymentdetails.get('offline_bookings'):
                                paymentdetails.pop('offline_bookings')
                else:
                    if data and dict_data['amount'] > 0 and not bkng.confirmbookingid in booking_ids:
                        hop.amount += dict_data['amount']
                    hplink = HotelPaymentLink(payment=hop, content_object=bkng, amount=dict_data['amount'])
                hplink.user = user_obj
                if 'pay' in dict_data and not bkng.payathotelflag:
                    bkng.paymentstatus = 'processed'
                else:
                    bkng.paymentstatus = 'adjusted'
                if 'plb' not in dict_data:
                    msg = 'initiated action %s of Rs. %s for booking %s' % (str(bkng.paymentstatus), str(abs(hplink.amount)), str(bkng.confirmbookingid))
                    bkmsg = 'initiated action %s of Rs. %s with gbpid %s' % (str(bkng.paymentstatus), str(abs(hplink.amount)), str(hop.paymentid))
                    if not multi_payment:
                        booking_ids.append(bkng.confirmbookingid)
                else:
                    msg = 'PLB adjustment %s of Rs. %s for booking %s' % (str(bkng.paymentstatus), str(abs(hplink.amount)), str(bkng.confirmbookingid))
                    bkmsg = 'PLB adjustment %s of Rs. %s with gbpid %s' % (str(bkng.paymentstatus), str(abs(hplink.amount)), str(hop.paymentid))
                    if multi_payment:
                        if payment_multi_obj and payment_multi_obj.vendor_parent_booking_id not in plb_ids:
                            plb_ids.append(payment_multi_obj.vendor_parent_booking_id)
                    else:
                        plb_ids.append(bkng.confirmbookingid)
                hm.updateLogMsg(user_obj, hop, msg)
                hm.updateLogMsg(user_obj, bkng, bkmsg)
                saveobj_list.append(bkng)
                saveobj_list.append(hplink)
        if booking_ids:
            paymentdetails['bookings'] = booking_ids
        if plb_ids:
            paymentdetails['plb_bookings'] = plb_ids
        if multi_room_ids:
            paymentdetails['multiroomids'] = multi_room_ids
    if offline_list:
        for dict_data in offline_list:
            if dict_data['rbooking_id'] not in offline_ids:
                offline_bookings.append(dict_data)
        paymentdetails['offline_bookings'] = offline_bookings
        saveobj_list.append(hop)
    return paymentdetails, saveobj_list


def get_fulldbdata(start, end, d = 1, arg_vendorbookingid = None, arg_bank_ref = None, allow_log=False, vendor_code=None):
    try:
        config = {
            'user': 'payment',
            'password': settings.NAVISION_MYSQL_DATA_PASSWORD,
            'host': settings.DATABASES["ingo-analytics-slave"]["HOST"],
            'database': 'back_payment'
        }
        end_date = datetime.datetime.now().date()
        start_date = (datetime.datetime.now() - datetime.timedelta(d)).date()
        cnx = mysql.connector.connect(**config)
        cur = cnx.cursor(buffered=True)
        query = "SELECT d.VendorLedgerEntryNo, MIN(vle.VendorNo) as vVendorNo, MIN(vle.BookingNo) as vBookingID, MIN(vle.DocumentType) as vDocumentType, \
                 vle.EntryNo as vEntryNo, MIN(d.EntryType) as dEntryType, MIN(d.DocumentType) as dDocumentType, MIN(d.PostingDate) as dPostingDate, MIN(d.Amount) as dAmount, \
                 MIN(avle.BookingNo) as aBookingID ,avle.SubDocumentType as aSubDocumentType, MIN(avle.ChequeNo) as aChequeNo,MIN(vle.HotelCode) as HotelCode, MIN(vle.CustName) as CustName, \
                 MIN(vle.CheckIn) as CheckIn, MIN(vle.CheckOut) as CheckOut, MIN(vle.BookingDate) as BookingDate, avle.PNRNo as adj_pnr, vle.PNRNo as BookingNo, avle.NewPNRNo as new_adj_pnr, vle.NewPNRNo as new_BookingNo, avle.PostingDate as Date, vle.Description, avle.Description, \
                 vle.BookingNo as VendorBookingid, vle.VendorNo, vle.HotelCode as HotelCode FROM htl_DetailedVendorLedgerEntry as d inner join htl_VendorLedgerEntryFull AS vle on d.VendorLedgerEntryNo=vle.EntryNo and \
                 vle.DocumentType in (0, 2, 3) and d.DocumentType in (1, 3, 0) and d.EntryType in (2) LEFT JOIN htl_VendorLedgerEntryFull AS avle ON avle.EntryNo = d.AppliedVendLedgerEntryNo where d.UploadDate between %s and %s GROUP BY vle.EntryNo, d.EntryNo;"
        params = (str(start_date), str(end_date))
        if arg_vendorbookingid:
            query = "SELECT d.VendorLedgerEntryNo, MIN(vle.VendorNo) as vVendorNo, MIN(vle.BookingNo) as vBookingID, MIN(vle.DocumentType) as vDocumentType, \
                     vle.EntryNo as vEntryNo, MIN(d.EntryType) as dEntryType, MIN(d.DocumentType) as dDocumentType, MIN(d.PostingDate) as dPostingDate, MIN(d.Amount) as dAmount, \
                     MIN(avle.BookingNo) as aBookingID ,avle.SubDocumentType as aSubDocumentType, MIN(avle.ChequeNo) as aChequeNo,MIN(vle.HotelCode) as HotelCode, MIN(vle.CustName) as CustName, \
                     MIN(vle.CheckIn) as CheckIn, MIN(vle.CheckOut) as CheckOut, MIN(vle.BookingDate) as BookingDate, avle.PNRNo as adj_pnr, vle.PNRNo as BookingNo, avle.NewPNRNo as new_adj_pnr, vle.NewPNRNo as new_BookingNo, avle.PostingDate as Date, vle.Description, avle.Description, \
                     vle.BookingNo as VendorBookingid, vle.VendorNo, vle.HotelCode as HotelCode FROM htl_DetailedVendorLedgerEntry as d inner join htl_VendorLedgerEntryFull AS vle on d.VendorLedgerEntryNo=vle.EntryNo and \
                     vle.DocumentType in (0, 2, 3) and d.DocumentType in (1, 3, 0) and d.EntryType in (2) LEFT JOIN htl_VendorLedgerEntryFull AS avle ON avle.EntryNo = d.AppliedVendLedgerEntryNo where vle.BookingNo = %s GROUP BY vle.EntryNo, d.EntryNo;"
            params = (arg_vendorbookingid,)
        if arg_vendorbookingid and vendor_code:
            query = "SELECT d.VendorLedgerEntryNo, MIN(vle.VendorNo) as vVendorNo, MIN(vle.BookingNo) as vBookingID, MIN(vle.DocumentType) as vDocumentType, \
                     vle.EntryNo as vEntryNo, MIN(d.EntryType) as dEntryType, MIN(d.DocumentType) as dDocumentType, MIN(d.PostingDate) as dPostingDate, MIN(d.Amount) as dAmount, \
                     MIN(avle.BookingNo) as aBookingID ,avle.SubDocumentType as aSubDocumentType, MIN(avle.ChequeNo) as aChequeNo,MIN(vle.HotelCode) as HotelCode, MIN(vle.CustName) as CustName, \
                     MIN(vle.CheckIn) as CheckIn, MIN(vle.CheckOut) as CheckOut, MIN(vle.BookingDate) as BookingDate, avle.PNRNo as adj_pnr, vle.PNRNo as BookingNo, avle.NewPNRNo as new_adj_pnr, vle.NewPNRNo as new_BookingNo, avle.PostingDate as Date, vle.Description, avle.Description, \
                     vle.BookingNo as VendorBookingid, vle.VendorNo, vle.HotelCode as HotelCode FROM htl_DetailedVendorLedgerEntry as d inner join htl_VendorLedgerEntryFull AS vle on d.VendorLedgerEntryNo=vle.EntryNo and \
                     vle.DocumentType in (0, 2, 3) and d.DocumentType in (1, 3, 0) and d.EntryType in (2) LEFT JOIN htl_VendorLedgerEntryFull AS avle ON avle.EntryNo = d.AppliedVendLedgerEntryNo where vle.BookingNo = %s and vle.VendorNo = %s GROUP BY vle.EntryNo, d.EntryNo;"
            params = (arg_vendorbookingid, vendor_code)
        if arg_bank_ref:
            query = "SELECT d.VendorLedgerEntryNo, MIN(vle.VendorNo) as vVendorNo, MIN(vle.BookingNo) as vBookingID, MIN(vle.DocumentType) as vDocumentType, \
                    vle.EntryNo as vEntryNo, MIN(d.EntryType) as dEntryType, MIN(d.DocumentType) as dDocumentType, MIN(d.PostingDate) as dPostingDate, MIN(d.Amount) as dAmount, \
                    MIN(avle.BookingNo) as aBookingID ,avle.SubDocumentType as aSubDocumentType, MIN(avle.ChequeNo) as aChequeNo,MIN(vle.HotelCode) as HotelCode, MIN(vle.CustName) as CustName, \
                    MIN(vle.CheckIn) as CheckIn, MIN(vle.CheckOut) as CheckOut, MIN(vle.BookingDate) as BookingDate, avle.PNRNo as adj_pnr, vle.PNRNo as BookingNo, avle.NewPNRNo as new_adj_pnr, vle.NewPNRNo as new_BookingNo, avle.PostingDate as Date, vle.Description, avle.Description, \
                    vle.BookingNo as VendorBookingid, vle.VendorNo, vle.HotelCode as HotelCode FROM htl_DetailedVendorLedgerEntry as d inner join htl_VendorLedgerEntryFull AS vle on d.VendorLedgerEntryNo=vle.EntryNo and \
                    vle.DocumentType in (0, 2, 3) and d.DocumentType in (1, 3, 0) and d.EntryType in (1, 2) LEFT JOIN htl_VendorLedgerEntryFull AS avle ON avle.EntryNo = d.AppliedVendLedgerEntryNo where avle.ChequeNo = %s and d.PostingDate between SUBDATE(NOW(),%s) and SUBDATE(NOW(),%s) GROUP BY vle.EntryNo, d.EntryNo;"
            params = (arg_bank_ref, start, end)
        cur.execute(query, params)
        ###sql_injection id: 9 996-1006 R
        data = []
        for ( VendorLedgerEntryNo, vVendorNo, vBookingID, vDocumentType, vEntryNo, dEntryType, dDocumentType, dPostingDate,
              dAmount, aBookingID, aSubDocumentType, aChequeNo, HotelCode, CustName, CheckIn, CheckOut, BookingDate, adj_pnr,
              BookingNo, new_adj_pnr, NewBookingNo, Date, Description, Description, VendorBookingid, VendorNo, HotelCode) in cur:
                    data.append({
                        "VendorLedgerEntryNo": VendorLedgerEntryNo,
                        "HotelCode": HotelCode,
                        "BookingNo": BookingNo,
                        "adj_pnr": adj_pnr,
                        "UTR": aChequeNo,
                        "Date": Date,
                        "Amount": float(dAmount),
                        "VendorNo": VendorNo,
                        "VendorBookingId": VendorBookingid,
                        "vDocumentType": vDocumentType,
                        "dEntryType": dEntryType,
                        "dDocumentType": dDocumentType,
                        "dPostingDate": dPostingDate,
                        "aBookingID": aBookingID,
                        "aSubDocumentType": aSubDocumentType,
                        "HotelCode": HotelCode,
                        'Description': Description,
                        'new_adj_pnr': new_adj_pnr,
                        'newBookingNo': NewBookingNo,
                        'customer_name': CustName,
                        'checkin': CheckIn,
                        'checkout': CheckOut,
                        'booking_date': BookingDate
                    })
        if allow_log:
            with open('/tmp/payment_%s_%s.csv' % (start_date, 'gi'), 'wb') as csvfile:
                spamwriter = csv.writer(csvfile, delimiter=',')
                spamwriter.writerow(["Entry Number", "HotelCode", "VendorBookingid",
                                     "UTR", "Date", "Amount", "VendorNo",
                                     "VendorBookingId", "vDocumentType",
                                     "dEntryType", "dDocumentType", "dPostingDate",
                                     "aBookingID", "aSubDocumentType",
                                     "Description", "new_adj_pnr", "newBookingNo"])
                for d in data:
                    spamwriter.writerow([d['VendorLedgerEntryNo'], d['HotelCode'], d['BookingNo'],
                                         d['UTR'], d['Date'], d['Amount'],
                                         d['VendorNo'], d['VendorBookingId'], d['vDocumentType'],
                                         d['dPostingDate'], d['aBookingID'], d['aSubDocumentType'],
                                         d['Description'], d['new_adj_pnr'], d['newBookingNo']])
        return data
    except Exception, e:
        inventory_logger.info(message='Unable to process payment data Exception %s traceback %s' %
                                      (e, repr(traceback.format_exc())), log_type='ingoibibo', bucket='NavisionPayment',
                              stage='get_fulldbdata')
        return []


def get_mmt_fulldbdata(start, end, d=1, arg_vendorbookingid=None, arg_bank_ref=None, allow_log=False, vendor_code = None):
    try:
        config = {
            'user': 'payment',
            'password': settings.NAVISION_MYSQL_DATA_PASSWORD,
            'host': settings.DATABASES["ingo-analytics-slave"]["HOST"],
            'database': 'htl_reports'
        }
        end_date = datetime.datetime.now().date()
        start_date = (datetime.datetime.now() - datetime.timedelta(d)).date()
        cnx = mysql.connector.connect(**config)
        cur = cnx.cursor(buffered=True)
        query = "SELECT d.VendorLedgerEntryNo, MIN(vle.VendorNo) as vVendorNo, MIN(vle.BookingNo) as vBookingID, MIN(vle.DocumentType) as vDocumentType, \
                 vle.EntryNo as vEntryNo, MIN(d.EntryType) as dEntryType, MIN(d.DocumentType) as dDocumentType, MIN(d.PostingDate) as dPostingDate, MIN(d.Amount) as dAmount, \
                 MIN(avle.BookingNo) as aBookingID ,avle.SubDocumentType as aSubDocumentType, MIN(avle.ChequeNo) as aChequeNo,MIN(vle.HotelCode) as HotelCode, MIN(vle.CustName) as CustName, \
                 MIN(vle.CheckIn) as CheckIn, MIN(vle.CheckOut) as CheckOut, MIN(vle.BookingDate) as BookingDate, avle.PostingDate as Date, vle.Description, avle.Description, avle.NewPNRNo as new_adj_pnr, vle.NewPNRNo as new_BookingNo , \
                 vle.BookingNo as VendorBookingid, vle.VendorNo, vle.HotelCode as HotelCode FROM htl_DetailedVendorLedgerEntry as d inner join htl_VendorLedgerEntryFull AS vle on d.VendorLedgerEntryNo=vle.EntryNo and \
                 vle.DocumentType in (0, 2, 3) and d.DocumentType in (1, 3, 0) and d.EntryType in (2) LEFT JOIN htl_VendorLedgerEntryFull AS avle ON avle.EntryNo = d.AppliedVendLedgerEntryNo where d.UploadDate between %s and %s and vle.VendorPostingGroup in ('HOTDOM', 'LANDDOM', 'HOTDOMON') GROUP BY vle.EntryNo, d.EntryNo;"
        params = (str(start_date), str(end_date))
        if arg_vendorbookingid:
            query = "SELECT d.VendorLedgerEntryNo, MIN(vle.VendorNo) as vVendorNo, MIN(vle.BookingNo) as vBookingID, MIN(vle.DocumentType) as vDocumentType, \
                     vle.EntryNo as vEntryNo, MIN(d.EntryType) as dEntryType, MIN(d.DocumentType) as dDocumentType, MIN(d.PostingDate) as dPostingDate, MIN(d.Amount) as dAmount, \
                     MIN(avle.BookingNo) as aBookingID ,avle.SubDocumentType as aSubDocumentType, MIN(avle.ChequeNo) as aChequeNo,MIN(vle.HotelCode) as HotelCode, MIN(vle.CustName) as CustName, \
                     MIN(vle.CheckIn) as CheckIn, MIN(vle.CheckOut) as CheckOut, MIN(vle.BookingDate) as BookingDate, avle.PostingDate as Date, vle.Description, avle.Description, avle.NewPNRNo as new_adj_pnr, vle.NewPNRNo as new_BookingNo , \
                     vle.BookingNo as VendorBookingid, vle.VendorNo, vle.HotelCode as HotelCode FROM htl_DetailedVendorLedgerEntry as d inner join htl_VendorLedgerEntryFull AS vle on d.VendorLedgerEntryNo=vle.EntryNo and \
                     vle.DocumentType in (0, 2, 3) and d.DocumentType in (1, 3, 0) and d.EntryType in (2) LEFT JOIN htl_VendorLedgerEntryFull AS avle ON avle.EntryNo = d.AppliedVendLedgerEntryNo where vle.BookingNo = %s GROUP BY vle.EntryNo, d.EntryNo;"
            params = (arg_vendorbookingid,)
        if arg_vendorbookingid and vendor_code:
            query = "SELECT d.VendorLedgerEntryNo, MIN(vle.VendorNo) as vVendorNo, MIN(vle.BookingNo) as vBookingID, MIN(vle.DocumentType) as vDocumentType, \
                    vle.EntryNo as vEntryNo, MIN(d.EntryType) as dEntryType, MIN(d.DocumentType) as dDocumentType, MIN(d.PostingDate) as dPostingDate, MIN(d.Amount) as dAmount, \
                    MIN(avle.BookingNo) as aBookingID ,avle.SubDocumentType as aSubDocumentType, MIN(avle.ChequeNo) as aChequeNo,MIN(vle.HotelCode) as HotelCode, MIN(vle.CustName) as CustName, \
                    MIN(vle.CheckIn) as CheckIn, MIN(vle.CheckOut) as CheckOut, MIN(vle.BookingDate) as BookingDate, avle.PostingDate as Date, vle.Description, avle.Description, avle.NewPNRNo as new_adj_pnr, vle.NewPNRNo as new_BookingNo , \
                    vle.BookingNo as VendorBookingid, vle.VendorNo, vle.HotelCode as HotelCode FROM htl_DetailedVendorLedgerEntry as d inner join htl_VendorLedgerEntryFull AS vle on d.VendorLedgerEntryNo=vle.EntryNo and \
                    vle.DocumentType in (0, 2, 3) and d.DocumentType in (1, 3, 0) and d.EntryType in (2) LEFT JOIN htl_VendorLedgerEntryFull AS avle ON avle.EntryNo = d.AppliedVendLedgerEntryNo where vle.BookingNo = %s and vle.VendorNo = %s GROUP BY vle.EntryNo, d.EntryNo;"
            params = (arg_vendorbookingid, vendor_code)
        if arg_bank_ref:
            query = "SELECT d.VendorLedgerEntryNo, MIN(vle.VendorNo) as vVendorNo, MIN(vle.BookingNo) as vBookingID, MIN(vle.DocumentType) as vDocumentType, \
                    vle.EntryNo as vEntryNo, MIN(d.EntryType) as dEntryType, MIN(d.DocumentType) as dDocumentType, MIN(d.PostingDate) as dPostingDate, MIN(d.Amount) as dAmount, \
                    MIN(avle.BookingNo) as aBookingID ,avle.SubDocumentType as aSubDocumentType, MIN(avle.ChequeNo) as aChequeNo,MIN(vle.HotelCode) as HotelCode, MIN(vle.CustName) as CustName, \
                    MIN(vle.CheckIn) as CheckIn, MIN(vle.CheckOut) as CheckOut, MIN(vle.BookingDate) as BookingDate, avle.PostingDate as Date, vle.Description, avle.Description, avle.NewPNRNo as new_adj_pnr, vle.NewPNRNo as new_BookingNo , \
                    vle.BookingNo as VendorBookingid, vle.VendorNo, vle.HotelCode as HotelCode FROM htl_DetailedVendorLedgerEntry as d inner join htl_VendorLedgerEntryFull AS vle on d.VendorLedgerEntryNo=vle.EntryNo and \
                    vle.DocumentType in (0, 2, 3) and d.DocumentType in (1, 3, 0) and d.EntryType in (1, 2) LEFT JOIN htl_VendorLedgerEntryFull AS avle ON avle.EntryNo = d.AppliedVendLedgerEntryNo where avle.ChequeNo = %s and d.PostingDate between SUBDATE(NOW(),%s) and SUBDATE(NOW(),%s) and vle.VendorPostingGroup in ('HOTDOM', 'LANDDOM', 'HOTDOMON') GROUP BY vle.EntryNo, d.EntryNo;"
            params = (arg_bank_ref, start, end)
        cur.execute(query, params)
        ##sql_injection id: 8 1091-1101 R
        data = []
        for ( VendorLedgerEntryNo, vVendorNo, vBookingID, vDocumentType, vEntryNo, dEntryType, dDocumentType, dPostingDate, dAmount, aBookingID, aSubDocumentType, aChequeNo, HotelCode, CustName, CheckIn, CheckOut,
                BookingDate, Date, Description, Description, new_adj_pnr, NewBookingNo, VendorBookingid, VendorNo, HotelCode) in cur:
                    data.append({
                        "VendorLedgerEntryNo": VendorLedgerEntryNo,
                        "HotelCode": HotelCode,
                        "BookingNo": VendorBookingid,
                        "UTR": aChequeNo,
                        "Date": Date,
                        "Amount": float(dAmount),
                        "VendorNo": VendorNo,
                        "VendorBookingId": VendorBookingid,
                        "vDocumentType": vDocumentType,
                        "dEntryType": dEntryType,
                        "dDocumentType": dDocumentType,
                        "dPostingDate": dPostingDate,
                        "aBookingID": aBookingID,
                        "aSubDocumentType": aSubDocumentType,
                        "HotelCode": HotelCode,
                        'Description': Description,
                        'new_adj_pnr': new_adj_pnr,
                        'newBookingNo': NewBookingNo,
                        'customer_name': CustName,
                        'checkin': CheckIn,
                        'checkout': CheckOut,
                        'booking_date': BookingDate
                    })
        if allow_log:
            with open('/tmp/payment_%s_%s.csv' % (start_date, 'mmt'), 'wb') as csvfile:
                spamwriter = csv.writer(csvfile, delimiter=',')
                spamwriter.writerow(["Entry Number", "HotelCode", "VendorBookingid",
                                     "UTR", "Date", "Amount", "VendorNo",
                                     "VendorBookingId", "vDocumentType",
                                     "dEntryType", "dDocumentType",
                                     "dPostingDate","aBookingID", "aSubDocumentType",
                                     "Description", "new_adj_pnr", "newBookingNo"])
            for d in data:
                spamwriter.writerow([d['VendorLedgerEntryNo'], d['HotelCode'], d['BookingNo'],
                                     d['UTR'], d['Date'], d['Amount'], d['VendorNo'],
                                     d['VendorBookingId'], d['vDocumentType'],
                                     d['dEntryType'], d['dDocumentType'], d['dPostingDate'],
                                     d['aBookingID'], d['aSubDocumentType'], d['Description'],
                                     d['new_adj_pnr'], d['newBookingNo']])
                inventory_logger.info(
                    message='Total Number of transections: %s' % (len(data)),
                    log_type='ingoibibo',
                    bucket='NavisionPayment',
                    stage = 'get_mmt_fulldbdata')
        return data
    except Exception as e:
        inventory_logger.error(
            message='error while processing query: %s | Traceback: %s' % (e, repr(traceback.format_exc())),
            log_type='ingoibibo',
            bucket='NavisionPayment',
            stage='get_mmt_fulldbdata')


def get_paymentids(payments = None):
    paymentids_list = set()
    for payment in payments:
        paymentids_list.add(payment.payment.paymentid)
    return list(paymentids_list)


def mmt_payment_detail_info(d=1):
    detail_dict = {
        'payment': (0,),
        'recovery': (0,),
        'adjustment': (0,)
    }
    config = {
        'user': 'payment',
        'password': settings.NAVISION_MYSQL_DATA_PASSWORD,
        'host': settings.DATABASES["ingo-analytics-slave"]["HOST"],
    }
    end_date = datetime.datetime.now().date()
    start_date = (datetime.datetime.now() - datetime.timedelta(d)).date()
    payment_query = "SELECT count(*) FROM htl_DetailedVendorLedgerEntry as d inner join htl_VendorLedgerEntryFull AS vle on d.VendorLedgerEntryNo=vle.EntryNo and \
                     vle.DocumentType = 2 and d.DocumentType = 1 and d.EntryType in (2) LEFT JOIN htl_VendorLedgerEntryFull AS avle ON avle.EntryNo = d.AppliedVendLedgerEntryNo where d.UploadDate between %s and %s and vle.VendorPostingGroup in %s;"
    adjustment_query = "SELECT count(*) FROM htl_DetailedVendorLedgerEntry as d inner join htl_VendorLedgerEntryFull AS vle on d.VendorLedgerEntryNo=vle.EntryNo and \
                         vle.DocumentType = 2 and d.DocumentType in (3, 0) and d.EntryType in (2) LEFT JOIN htl_VendorLedgerEntryFull AS avle ON avle.EntryNo = d.AppliedVendLedgerEntryNo where d.UploadDate between %s and %s and vle.VendorPostingGroup in %s and vle.BookingNo != avle.BookingNo;"
    recovery_query = "SELECT count(*) FROM htl_DetailedVendorLedgerEntry as d inner join htl_VendorLedgerEntryFull AS vle on d.VendorLedgerEntryNo=vle.EntryNo and \
                         vle.DocumentType in (0, 3) and d.DocumentType in (0, 3) and d.EntryType in (2) LEFT JOIN htl_VendorLedgerEntryFull AS avle ON avle.EntryNo = d.AppliedVendLedgerEntryNo where d.UploadDate between %s and %s and vle.VendorPostingGroup in %s and vle.BookingNo != avle.BookingNo;"
    params = (str(start_date), str(end_date), hotelchoice.PAYMENT_VENDOR_POSTING_GROUP)
    cnx = mysql.connector.connect(**config)
    cur = cnx.cursor(buffered=True)
    cur.execute(payment_query, params)
    ##sql_injection id: 7 1180-1190 R
    for (count,) in cur:
        detail_dict['payment'] = count
    ##sql_injection id: 6 1183-1193 R
    cur.execute(adjustment_query, params)
    for (count,) in cur:
        detail_dict['recovery'] = count
    ##sql_injection id: 5 1186-1196 R
    cur.execute(recovery_query, params)
    for (count,) in cur:
        detail_dict['adjustment'] = count
    return detail_dict


def gi_payment_detail_info(d = 1):
    detail_dict = {
        'payment': (0,),
        'recovery': (0,),
        'adjustment': (0,)
    }
    config = {
        'user': 'payment',
        'password': settings.NAVISION_MYSQL_DATA_PASSWORD,
        'host': settings.DATABASES["ingo-analytics-slave"]["HOST"],
        'database': 'back_payment'
    }
    end_date = datetime.datetime.now().date()
    start_date = (datetime.datetime.now() - datetime.timedelta(d)).date()
    payment_query = "SELECT count(*) FROM htl_DetailedVendorLedgerEntry as d inner join htl_VendorLedgerEntryFull AS vle on d.VendorLedgerEntryNo=vle.EntryNo and \
                         vle.DocumentType = 2 and d.DocumentType = 1 and d.EntryType in (2) LEFT JOIN htl_VendorLedgerEntryFull AS avle ON avle.EntryNo = d.AppliedVendLedgerEntryNo where d.UploadDate between %s and %s;"
    adjustment_query = "SELECT count(*) FROM htl_DetailedVendorLedgerEntry as d inner join htl_VendorLedgerEntryFull AS vle on d.VendorLedgerEntryNo=vle.EntryNo and \
                              vle.DocumentType = 2 and d.DocumentType in (3, 0) and d.EntryType in (2) LEFT JOIN htl_VendorLedgerEntryFull AS avle ON avle.EntryNo = d.AppliedVendLedgerEntryNo where d.UploadDate between %s and %s and vle.BookingNo != avle.BookingNo;"
    recovery_query = "SELECT count(*) FROM htl_DetailedVendorLedgerEntry as d inner join htl_VendorLedgerEntryFull AS vle on d.VendorLedgerEntryNo=vle.EntryNo and \
                              vle.DocumentType in (0, 3) and d.DocumentType in (0, 3) and d.EntryType in (2) LEFT JOIN htl_VendorLedgerEntryFull AS avle ON avle.EntryNo = d.AppliedVendLedgerEntryNo where d.UploadDate between %s and %s and vle.BookingNo != avle.BookingNo;"
    params = (str(start_date), str(end_date))
    cnx = mysql.connector.connect(**config)
    cur = cnx.cursor(buffered=True)
    cur.execute(payment_query, params)
    ##sql_injection id: 4 1217-1227 R
    for (count,) in cur:
        detail_dict['payment'] = count
    ##sql_injection id: 3 1220-1230 R
    cur.execute(adjustment_query, params)
    for (count,) in cur:
        detail_dict['recovery'] = count
    ##sql_injection id: 2 1223-1233 R
    cur.execute(recovery_query, params)
    for (count,) in cur:
        detail_dict['adjustment'] = count
    return detail_dict
