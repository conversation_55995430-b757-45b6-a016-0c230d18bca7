from hotels.models import HotelMetaData
from django.contrib import admin
from django.forms.models import BaseInlineFormSet


class HotelMetaDataFormset(BaseInlineFormSet):
    def clean(self):
        hotel = self.cleaned_data[0].get('hotel')
        property_category = hotel.property_category
        property_type_meta = self.cleaned_data[0].get('property_type_meta')
        if property_category and property_category == 'homestay' and not property_type_meta:
            self._errors[0]['property_type_meta'] = self.error_class(['Required'])
        if property_category and property_category == 'hotel' and property_type_meta == 'entire':
            self._errors[0]['property_type_meta'] = self.error_class(['hotel cannot be sold as entire'])

class HotelMetaDataInline(admin.StackedInline):
    model = HotelMetaData
    min_num = 1
    max_num = 1
    fields = ('property_type_meta',),
    formset = HotelMetaDataFormset

