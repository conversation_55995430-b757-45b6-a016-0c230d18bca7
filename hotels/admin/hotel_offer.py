from django import forms
from django.core.exceptions import ValidationError
from django.db.models import Q
from utils.logger import Logger
api_logger = Logger(logger='inventoryAPILogger')

__author__ = 'prerna_arya'

from django.contrib import admin, messages
from django.contrib.admin.views.main import ChangeList
from django.contrib.contenttypes.models import ContentType
from common.admin import NotesInline
from hotels.admin_forms.hotel_offer import HotelOfferValueForm, HotelOfferConditionForm
from hotels.models import HotelDetail, HotelOfferCondition, HotelOfferValue, RoomDetail, RatePlan
from hotels.models import OfferTimeSlots
from lib.newmodeladmin import HotelDetailRelatedFieldAdmin, GenericReturnListAdmin,ApproxCountQuerySet
from hotels.forms import OfferValueFormset, OfferTimeSlotFormset
from hotels.hotelchoice import HotelOfferBasisList

from hotels.models.helper import get_code_from_id
from hotels.models import configuration as HotelConf

class HotelOfferValueInline(admin.StackedInline):
    model = HotelOfferValue
    form = HotelOfferValueForm
    formset = OfferValueFormset
    extra = 0
    fieldsets = [
        ('Admin', {'fields': [('user', 'createdon', 'modifiedon'), ], 'classes': ['collapse']}),
        ('Description: ', {'fields': [('description')], 'classes': ['collapse']}),
        ('Offer Values:', {'fields': [('isactive', 'segment', 'channel',
                                               'offer_basis', 'offer_type', 'offer_value', 'approver')]}),
        ('Text Based Offer:', {'fields' : [('offer_textual_value')]})
    ]
    can_delete = False
    template = 'admin/hotels/inven_stacked.html'

    readonly_fields = ['user', 'offer_textual_value', 'createdon', 'modifiedon']
    all_fields = HotelOfferValue._meta.get_all_field_names()
    ops_readonly = all_fields
    finance_readonly = all_fields
    contract_readonly = readonly_fields
    content_readonly = readonly_fields
    content_outsource_readonly = readonly_fields

    def save_model(self, request, obj, form, change):
        try:
            if form.has_changed():
                form.save(request=request)
            else:
                obj.save()
        except ValidationError as e:
            raise forms.ValidationError(e.message)
        except Exception as e:
            raise forms.ValidationError(e)

    def get_queryset(self, request):
        qs = super(HotelOfferValueInline, self).get_queryset(request)
        return qs.filter(offer_basis__in=HotelOfferBasisList)

    def get_formset(self, request, obj=None, **kwargs):
        formset = super(HotelOfferValueInline, self).get_formset(request, obj, **kwargs)
        formset.request = request
        return formset


class HotelOfferValueAdmin(HotelDetailRelatedFieldAdmin):
    model = HotelOfferValue
    form = HotelOfferValueForm
    raw_id_fields = ['offer_condition']
    fk_field = 'offer_condition'
    fk_model = HotelOfferCondition
    change_form_template = 'admin/hotels/hoteloffervalue/change_form.html'

    fieldsets = [
        ('Admin', {'fields': [('user', 'createdon', 'modifiedon'), ], 'classes': ['collapse']}),
        (None, {'fields': [('isactive', 'non_refundable'),
                           'description',]}),
        ('Offer Applicable for:', {'fields': [('channel', 'segment', 'offer_basis')]}),
        ('Offer Type And Value:', {'fields': [('offer_type', 'offer_value')]}),
    ]
    can_delete = False
    # template = 'admin/hotels/inven_stacked.html'
    readonly_fields = ['user', 'createdon', 'modifiedon', 'offer_value', 'offer_type']
    all_fields = HotelOfferValue._meta.get_all_field_names()
    ops_readonly = all_fields
    finance_readonly = all_fields
    content_readonly = all_fields
    content_outsource_readonly = all_fields

    def get_queryset(self, request):
        qs = super(HotelOfferValueAdmin, self).get_queryset(request)
        return qs.filter(offer_basis__in=HotelOfferBasisList)


class OfferTimeSlotInline(admin.TabularInline):
    model = OfferTimeSlots
    formset = OfferTimeSlotFormset
    extra = 0
    fields = ['start_time', 'end_time', 'is_active']
    readonly_fields = ['createdon', 'modifiedon']
    can_delete = False


class HotelOfferConditionAdmin(GenericReturnListAdmin):
    inlines = [HotelOfferValueInline, NotesInline]  # OfferTimeSlotInline
    save_as = True
    add_error_url = '/admin/hotels/hoteldetail/'
    change_form_template = 'admin/hotels/hoteloffercondition/change_form.html'
    form = HotelOfferConditionForm

    list_display = ['isactive', 'showtop', 'nonrefundable', 'offercode', 'related_to', 'description',
                     'content_type', 'bookingdatestart', 'bookingdateend']
    readonly_fields = ['user', 'modifiedon', 'createdon', 'bookingblackoutdates', 'checkinblackoutdates',
                       'offermulticonditions', 'offer_source', 'applicable_window', 'hotelcode']
    list_display_links = ['offercode']
    fieldsets = [
        ('Admin', {'fields': [('user', 'createdon', 'modifiedon'), ('content_type',
                                                                    'object_id', 'hotelcode')], 'classes': ['collapse']}),
        (None, {'fields': [('isactive', 'showtop', 'nonrefundable', 'apply_on_linked_rp'), ]}),
        ('Description: ', {'fields': [('description')], 'classes': ['collapse']}),
        ('Offer Info:', {'fields': [('offercategory', 'offer_source')]}),
        ('Offer Condition:', {'fields': [('bookingdatestart', 'bookingdateend'),
                                         ('start_time_based', 'end_time_based'),
                                         ('bookingblackoutdates',),  # 'is_time_based_promo'),
                                         ('checkindatestart', 'checkoutdateend', 'checkinblackoutdates'),
                                         ('checkinweekday', 'bookingweekday'),
                                         ('earlybirdmin', 'earlybirdmax', 'applicable_window'),
                                         ('minnights', 'max_los')]
                              }),
        ('Offer Applicability:',
         {'fields': [('offercondition', 'night'), ('offermulticonditions', 'pah_applicable', 'only_pah_applicable',
                                                   'advantage_program')]}),
    ]

    all_fields = HotelOfferCondition._meta.get_all_field_names()
    ops_readonly = all_fields
    finance_readonly = all_fields
    content_readonly = all_fields
    content_outsource_readonly = all_fields
    on_change_fields = {'isactive'}

    def get_changelist(self, request):
        request.GET._mutable = True
        HotelOfferChangeList.offer_type = 0
        HotelOfferChangeList.isactive = 0
        offer_type = int(request.GET.get('offer_type', 0))
        isactive = int(request.GET.get('is_active', 0))
        if offer_type:
            request.GET.pop('offer_type')
            HotelOfferChangeList.offer_type = offer_type
        if isactive:
            request.GET.pop('is_active')
            HotelOfferChangeList.isactive = isactive

        return HotelOfferChangeList

    def get_queryset(self, request):
        qs = super(HotelOfferConditionAdmin, self).get_queryset(request)
        return qs._clone(klass=ApproxCountQuerySet)

    def save_model(self, request, obj, form, change):
        self.add_notification(obj, change, form)
        # TODO: Uncomment this after advantage program release
        # setattr(obj, 'only_pah_applicable', form.cleaned_data['only_pah_applicable'])
        try:
            if not form.has_changed():
                obj.save(hashChangeFunctionCall=False)
            else:
                obj.save()
        except Exception, e:
            messages.error(request, str(e))

    def save_related(self, request, form, formsets, change):
        # super(HotelOfferConditionAdmin, self).save_related(request, form, formsets, change)
        for formset in formsets:
            if isinstance(formset, OfferValueFormset):
                inline_instance = self.inlines[0](self.model, self.admin_site)
                formset.save(commit=False)
                for inline_form in formset.forms:
                    inline_instance.save_model(request, inline_form.instance, inline_form, change)
            else:
                super(HotelOfferConditionAdmin, self).save_related(request, form, [formset], change)

class HotelOfferChangeList(ChangeList):

    def get_results(self, *args, **kwargs):
        object_id = args[0].GET.get('object_id', 0)
        if args[0].GET.get('content_type') and object_id:
            content_type = int(args[0].GET.get('content_type', 0))
            hotel_detail_content_type = ContentType.objects.get_for_model(HotelDetail)
            room_detail_content_type = ContentType.objects.get_for_model(RoomDetail)
            rateplan_content_type = ContentType.objects.get_for_model(RatePlan)
            request_content_type = ContentType.objects.get_for_id(content_type)
            if object_id and request_content_type.id == hotel_detail_content_type.id:
                hotelcode = get_code_from_id(object_id, HotelConf.HotelCodeLength,HotelConf.HotelCodePrefix)
            
                qs = HotelOfferCondition.objects.filter(hotelcode=hotelcode)

                if self.isactive:
                    qs = qs.filter(isactive=True)

                if self.offer_type:
                    if self.offer_type == 2:
                        self.queryset = qs.extra(where=["offer_type_flags & (1<<4) > 0"])
                    elif self.offer_type == 3:
                        self.queryset = qs.extra(where=["offer_type_flags & (1<<10) > 0"])
                    else:
                        self.queryset = qs.extra(where=[
                            "offer_type_flags & (1<<0) > 0 or offer_type_flags & (1<<1) > 0 or offer_type_flags & (1<<2) > 0 or offer_type_flags & (1<<3) > 0"])

                else:
                    self.queryset = qs

            elif room_detail_content_type.id == content_type:
                all_room_ids = [object_id]
                all_rateplan_ids = list(RatePlan.objects.filter(roomtype_id__in=all_room_ids).values_list('id', flat=True))
                qs = HotelOfferCondition.objects.filter(
                    Q(content_type=rateplan_content_type, object_id__in=all_rateplan_ids)
                    | Q(content_type=room_detail_content_type, object_id__in=all_room_ids))

                self.queryset = qs

            elif rateplan_content_type.id == content_type:
                all_rateplan_ids = [object_id]
                qs = HotelOfferCondition.objects.filter(content_type=rateplan_content_type,
                                                        object_id__in=all_rateplan_ids)
                self.queryset = qs

        super(HotelOfferChangeList, self).get_results(*args, **kwargs)

    def get_query_string(self, new_params=None, remove=None):
        if self.offer_type:
            new_params.update({"offer_type": self.offer_type})
        if self.isactive:
            new_params.update({"is_active": self.isactive})
        return super(HotelOfferChangeList, self).get_query_string(new_params, remove)
