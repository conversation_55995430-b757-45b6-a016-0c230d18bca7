
$(document).ready(function() {
        if($('#flow').val() != 'post_booking'){
            return false;
        }
        var bookingId = $('#booking_id').val();
        if (!bookingId){
            return false;
        }
        $.ajax({
            type: 'GET',
            url: '/api/v1/booking/' + bookingId + '/get-add-ons/',
            success: function(response){
                var data = response['message'];
                if (data){
                    var addOnTemplate = _.template($('#add_on_template').html());
                    var addOnHtml = addOnTemplate({'add_on_list' : data})
                    $('#add_ons').html(addOnHtml)
                }else{
                    $('#add_ons').html("No Add Ons Found")

                }


            }
        })
    });

function get_rateplan_related_add_ons(rateplan_code, checkin, checkout){
    $('#add_on_form').hide();
    $('#add_ons').html('');
    $.ajax({
            type: 'GET',
            url: '/api/v1/add_on/' + rateplan_code + '/get-add-ons/?checkin='+checkin+'&checkout='+checkout,
            success: function(response){
                var data = response['message'];
                if (data.length){
                    var addOnTemplate = _.template($('#add_on_template').html());
                    var addOnHtml = addOnTemplate({'add_on_list' : data})
                    $('#add_ons').html(addOnHtml);
                    $('#add_on_form').show();
                }else{
                    $('#add_ons').html("No Add Ons Found");
                    $('#add_on_form').hide();
                }
            }
        })
}

function collectAddOn(){
    var totalAmount = 0;
    var addOnList = []
    var response = {};
    $('#add_ons input[type="checkbox"]:checked').each(function() {
        var addOnDict = {}
        var addOnId = $(this).val();
        var addOnUnit = $('#units_'+addOnId).val();
        addOnDict['add_on'] = addOnId;
        addOnDict['units'] = addOnUnit;
        addOnList.push(addOnDict);
        var sell_amount = $('#sell_amount_val_'+addOnId).val();
        var confirmation_type = $('#confirmation_type_val_'+addOnId).val();
        if (confirmation_type != 'false'){
            totalAmount += sell_amount*addOnUnit;
        }
    });
    $('#total_amount').val(totalAmount);
    response['add_on_list'] = addOnList;
    response['total_amount'] = totalAmount;
    response['booking_id'] = $('#booking_id').val();
    return response;
}

function bookAddOns(){
    var paramDict = collectAddOn();
    paramDict['mih_pay_id'] = $('#mih_pay_id').val();
    $('#submit_add_on').prop("disabled",true);

    $.ajax({
            type: 'POST',
            data: JSON.stringify(paramDict),
            headers: {
                'X-CSRFToken': $('input[name="csrfmiddlewaretoken"]').val(),
                'Content-Type': 'application/json'
            },
            url: '/api/v1/addonbooking/',
            success: function(response){
                alert(response['message']);
            },
            error: function(jqXHR, textStatus, errorThrown) {
                if (jqXHR.status === 403) {
                    alert('Permission Denied');
                }
                if (jqXHR.status === 400 || jqXHR.status === 500) {
                    if(jqXHR.responseText){
                       alert(jqXHR.responseText);
                    }
                }
            }

        });
}