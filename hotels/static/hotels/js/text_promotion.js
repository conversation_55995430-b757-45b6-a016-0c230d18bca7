function showRelatedData(value){
        var templateType = value.split(",")[0];
        var templateId = value.split(",")[1];

	    $('#charges_div').hide();
        $('#meal_div').hide();
        $('#value_div').hide();
        $('#transfer_div').hide();
        $('#offer_charges_div').hide();
        //Type : ADULT_CHILD_INFANT_CHARGE
	    if ( $.inArray(templateType, ['5']) > -1) {
	        $('#charges_div').show();
	    }
	    //Type : MEAL
	    else if ($.inArray(templateType, ['2']) > -1){
	        $('#meal_div').show();
	    }
	    //Type : PERCENT
	    else if($.inArray(templateType, ['3']) > -1) {
	        $('#value_div').show();
	        if(value == '18'){
	            $('#offer_value_type').hide();
	        } else {
	            $('#offer_value_type').show();
	        }
	    }
	    //Type : TRANSFER
	    else if($.inArray(templateType, ['1']) > -1) {
	        $('#transfer_div').show();
	    }
	    //Type : FIXED CHARGE
	    else if($.inArray(templateType, ['4']) > -1){
            $('#offer_charges_div').show();
	    }
	    //Type : NO BOX
	};

function onSubmitButton(){
    var formData = _.object($("#text_promotion_form").serializeArray().map(function(v) {return [v.name, v.value];} ));
    var validationFlag = validateData(formData);
    if (validationFlag){
        submitTextPromotion(formData);


    }

}

function submitTextPromotion(formData){
    $.ajax({
            type: 'POST',
            url: '/admin/hotels/create-text-promotion/',
            data: formData,
            success: showMessage
        });
}

function showMessage(response){
    response = JSON.parse(response);
    $('#resp_message').html(response['message']);
    if(response['success']){
        $('#go_to_promotion').attr('href', '/admin/hotels/hoteloffercondition/'+response['offer_id']+'/');
        $('#href_div').show();
     }
    $('#text_promotion_form').hide();

}

function validateData(formData){
    if(formData['info_template'] == ''){
        alert('Info template is mandatory');
        return false;
    }
    var today = new Date();
    today = new Date(today.getFullYear(),today.getMonth(),today.getDate());
    var bookingSDate = $("#booking_start_date").val();
    var bookingEDate = $("#booking_end_date").val();

    var checkinSDate = $("#checkin_start_date").val();
    var checkinEDate = $("#checkin_end_date").val();


    if (bookingEDate == ''){
        alert ("End Date mandatory.");
        return false;
    }
    if (checkinEDate == ''){
        checkinEDate = bookingEDate
    }
    var bookingStartDate = new Date(bookingSDate.split("-")[0],parseInt(bookingSDate.split("-")[1],10)-1, bookingSDate.split("-")[2]);
    var bookingEndDate = new Date(bookingEDate.split("-")[0],parseInt(bookingEDate.split("-")[1],10)-1, bookingEDate.split("-")[2]);

    var checkinStartDate = new Date(checkinSDate.split("-")[0],parseInt(checkinSDate.split("-")[1],10)-1, checkinSDate.split("-")[2]);
    var checkinEndDate = new Date(checkinEDate.split("-")[0],parseInt(checkinEDate.split("-")[1],10)-1, checkinEDate.split("-")[2]);

    if (validateDate (bookingSDate) != true){
        alert ("Invalid Booking Start Date.");
        return false;
    }else if (validateDate (bookingEDate) != true){
        alert ("Invalid Booking End Date.");
        return false
    }else if (bookingStartDate < today){
        alert("Booking Start date must be greater than or equal to today.");
        return false;
    }else if(bookingStartDate > bookingEndDate){
        alert ("Booking End date must be greater than Booking Start date");
        return false;
    }if (validateDate (checkinSDate) != true){
        alert ("Invalid Checkin Start Date.");
        return false;
    }else if (validateDate (checkinEDate) != true){
        alert ("Invalid Checkin End Date.");
        return false;
    }else if (checkinStartDate < today){
        alert("Checkin Start date must be greater than or equal to today.");
        return false;
    }else if(checkinStartDate > checkinEndDate){
        alert ("Checkin End date must be greater than Checkin Start date");
        return false;
    }else if(checkinStartDate < bookingStartDate){
        alert ("Checkin Start date must be greater than or equal to Booking Start date");
        return false;
    }else if(bookingEndDate > checkinEndDate){
        alert ("Booking End date must be greater than or equal to Checkin end date");
        return false;
    }else if ($.inArray(formData['info_template'], ['5' ]) > -1 && $('#adult_charges').val() == '')  {
        alert('Adult charges are mandatory');
        return false;
    }else if ($.inArray(formData['info_template'], ['2']) > -1 && $('#meal').val() == ''){
         alert('Meal is mandatory');
         return false;

    }else if($.inArray(formData['info_template'], ['3']) > -1 && $('#offer_value').val() == ''){
        alert('Offer Value is mandatory');
        return false;
    }else if($.inArray(formData['info_template'], ['1']) > -1 && $('#transfer').val() == '') {
        alert('Transfer type is mandatory');
        return false;
    }else if($.inArray(formData['info_template'], ['4']) > -1 && $('#offer_charges_value').val() == '') {
        alert('Charge value is mandatory');
        return false;
    }
    return true
}
