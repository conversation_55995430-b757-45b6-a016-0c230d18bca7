import base64
import os
import shutil
import traceback

import datetime
import json

import requests
from django.conf import settings
from django.db.utils import OperationalError
from django.db import close_old_connections
from django.contrib.contenttypes.models import ContentType
from kafka import KafkaConsumer

from common.constants import EVENT_NAME
from common.threads import ContentPipelineEventHandler, PTCPIngoEvents
from common.commonchoice import SPACE_CHOICES
from common.commonhelper import populate_common_fields
from hotels.picassoService.picasso_image_parser import PicassoImageParser
from hotels.models import RoomDetail
from hotels.models.hoteldetail import SpaceDetail, push_hotel_static_content
from api.v2.videos.resources.resources import get_video_meta_data, remove_temp_files, validate_video, \
    create_temp_file_from_url
from api.v2.media.resources.resources import move_space_to_approved
from common.features_helper import skip_media_moderation
from common.models import Image, Video, push_image_to_auto_moderation
from hotels.push_video_service import push_video_to_processing_service
from hotels.push_moderation_data_to_kafka import push_video_to_moderation
from common.task import crop_image_to_different_sizes
from hotels.models import HotelDetail, ListYourHotel
from proto_utils.dict_to_proto3_to_dict import protobuf_to_dict

from hotels.onboarding_state_machine import OnboardingStateMachine
from protos.compiled.hotel_detail_acknowledgement_pb2 import AcknowledgementNode
from utils.logger import Logger

api_logger = Logger(logger="inventoryAPILogger")


def consumer_picasso_video_change(video_object, is_newly_created, skip_gmp_push):
    try:
        # Fetch the video from the provided URL
        if is_newly_created:
            push_video_to_processing_service('video_processing_service', video_object)
            if skip_media_moderation(video_object):
                api_logger.info(message="skipped video moderation in picasso consumer for video {}".format(video_object.id))
            else:
                if not skip_gmp_push:
                    push_video_to_moderation(video_object, is_newly_created)

        video_object.save()
        api_logger.info(message="picasso video change packet successfully consumed - {}".format(video_object.__dict__))
    except Exception as e:
        api_logger.error(message="An exception occurred while consuming video packet: {}".format(e))


def consumer_picasso_image_change(image_object, is_newly_created, is_on_boarding, source, hotel_code, skip_am_push):
    if is_on_boarding:
        ContentPipelineEventHandler().add_attribute(EVENT_NAME, PTCPIngoEvents.onboarding)

    image_object.save(using='default')

    # trigger the post save methods
    if is_newly_created:
        if skip_media_moderation(image_object):
            api_logger.info(message="skipping image moderation in picasso consumer for image {}".format(image_object.id))
        else:
            if not skip_am_push:
                push_image_to_auto_moderation(image_object, is_newly_created)

            else:
                api_logger.info(
                    message="skipping image automoderation push for image {}".format(image_object.id))

        crop_image_to_different_sizes.apply_async(args=(image_object.id, 'create_image_rec_sqr_thumbnails',),
                                                  countdown=10)

        # trigger the on-boarding state machine
        if is_on_boarding and source == settings.HOST_APP_SETTING['INGO_WEB_HEADER']:
            draft_hotel_qs = ListYourHotel.objects.using('default').filter(hotelcode=hotel_code)
            if draft_hotel_qs.exists():
                OnboardingStateMachine.process(draft_hotel_qs.first())

    if is_on_boarding:
        ContentPipelineEventHandler().reset_args()

def bulk_media_map_space_and_room_handling(picasso_packet, hotel_obj):
    filter_request = [{
        'filterName': 'moderationStatus',
        'filterValue': 'APPROVED'
    }]
    if picasso_packet["media_type"] == "image":
        if len(picasso_packet["space_ids"]) > 0:
            object_ids = picasso_packet["space_ids"]
            space_objects = SpaceDetail.objects.filter(id__in=object_ids)

            picasso_image_parser_obj = PicassoImageParser(hotel_obj.hotelcode, filter_request)
            space_id_images = picasso_image_parser_obj.fetch_space_images()

            content_type_id = ContentType.objects.get_for_model(SpaceDetail).id

            for space_obj in space_objects:
                if space_obj.id in space_id_images and space_obj.status == SPACE_CHOICES.SPACE_STATUS_DRAFT:
                    move_space_to_approved(space_obj, content_type_id)

def consume_picasso_image_packet(data):
    picasso_packet = json.loads(data)

    # Checking if the data received is for bulk map/unmap
    if picasso_packet.has_key("action"):
        hotel_code = picasso_packet["hotel_code"]
        hotel_obj = HotelDetail.objects.get(hotelcode=int(hotel_code))
        if picasso_packet["action"] == "bulk_media_map":
            bulk_media_map_space_and_room_handling(picasso_packet, hotel_obj)

        # Both Bulk Media Map and Unmap are pushed to the content pipeline
        hotel_obj.request_data={}
        hotel_obj.source="ingo"
        kwargs = {
            'instance': hotel_obj
        }
        push_hotel_static_content("picasso_consumer", **kwargs)
    # using master to avoid issues related to replication lag
    elif picasso_packet.has_key("media_type") and picasso_packet["media_type"] == "video":
        video_object = Video.objects.using('default').get(id=picasso_packet['id'])
        is_newly_created = picasso_packet['is_newly_added']
        skip_gmp_push = picasso_packet.get('skip_gmp_push', False)
        consumer_picasso_video_change(video_object, is_newly_created, skip_gmp_push)
    else:
        image_object = Image.objects.using('default').get(id=picasso_packet['id'])
        is_newly_created = picasso_packet['is_newly_added']
        is_on_boarding = picasso_packet.get('is_on_boarding', False)
        source = picasso_packet.get('source', '')
        hotel_code = picasso_packet.get('hotel_code', '')
        skip_am_push = picasso_packet.get('skip_am_push', False)
        """
        Change to trigger set for image cropping dimensions in the image_object
        """
        consumer_picasso_image_change(image_object, is_newly_created, is_on_boarding, source, hotel_code, skip_am_push)
    api_logger.info(message="picasso packet received from kafka {packet}".format(packet=picasso_packet))


def picasso_image_packet_consumer():
    config = settings.PICASSO_IMAGE_PACKET_PIPELINE_KAFKA_SERVER_CONF['picasso_image_packet_server']
    topic = config['TOPIC']
    group = config['GROUP']

    enable_debug_logs = config['enable_debug_logs'] if ('enable_debug_logs' in config) else False
    if enable_debug_logs:
        import logging
        kafka_logger = logging.getLogger("kafka")
        kafka_logger.setLevel(logging.DEBUG)

    consumer_config = config['consumer_config'] if ('consumer_config' in config) else dict()
    consumer_config['group_id'] = group
    consumer_config['bootstrap_servers'] = config['HOST']

    consumer = KafkaConsumer(topic, **consumer_config)

    for message in consumer:
        try:
            api_logger.info(message='Message Received from Picasso:{}\t{}'.format(message, message.key),
                            log_type='ingoibibo', bucket='hotels',
                            stage='picasso_image_packet_consumer.picasso_image_packet_consumer')

            try:
                consume_picasso_image_packet(message.value)
            except OperationalError:
                # recover db connections from MySQL have gone away error
                close_old_connections()
                consume_picasso_image_packet(message.value)
        except Exception, e:
            api_logger.critical(message="error while processing picasso image packet: {data}, with {error},"
                                        "Traceback: {trace}".format(data=message.value, error=str(e),
                                                                    trace=repr(traceback.format_exc())),
                                bucket="hotels", stage="picasso_image_packet_consumer.consume_picasso_image_packet")