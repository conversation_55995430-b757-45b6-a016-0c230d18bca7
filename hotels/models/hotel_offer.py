__author__ = 'prerna_arya'

import datetime
import json
import logging
import traceback

from ingouser.models import User
from django.contrib.contenttypes.fields import GenericRelation, GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.db import models
from django.core.urlresolvers import reverse
from django.db.models import Q
from django.utils.translation import ugettext_lazy as _

from api.v2.cug.resources.cug_constants import CUG_OFFER_TYPE, CUG_OFFER_TYPE_PERCENTAGE, CUG_SEGMENTS, CUG_SEGMENT_CUG1
from api.v2.cug.resources.affiliate_cug_constants import AFFILIATES_SEGMENTS, AFFILIATES_SEGMENTS_LIST
from common.models import TemplateMapping
from hotels import hotelchoice
from hotels.models import helper as HotelHelper
from hotels.models.basic_models import HotelCancellationRule
from hotels.models import configuration as HotelConf
from hotels.models.hotel_flag_configuration import HOTEL_OFFER_FLAG_DICT
from hotels.models.helper import round_off_time_stamp_to_hours
from lib.current_user.models import CurrentUserField
from lib.fields import MultiSelectField
from common.object_guardian import check_permission
from common.constants import DISCOUNT_COUPON, AAJ_KA_BHAO, CUG_OFFER
from lib.models import Model as INGOModel
from lib.fields import FlagBitField
from hotels.constants.constants import OFFER_AAJ_KA_BHAO

logger = logging.getLogger("inventoryLogger")


class TextOfferTemplate(models.Model):
    is_active = models.BooleanField(_('Activation'), default=False)
    template_text = models.CharField(_('Template Text'), max_length=1000)
    type = models.CharField(_('Template Type'), max_length=30, choices=hotelchoice.TEXTUAL_OFFER_TYPE)
    mandatory_status = models.IntegerField(_('Mandatory Status'), choices=hotelchoice.TEXTUAL_OFFER_MANDATORY_STATUS)
    applicability_type = models.IntegerField(_('Applicability Type'), choices=hotelchoice.TEXTUAL_OFFER_APPLICABILITY,
                                             default=1)
    createdon = models.DateTimeField(auto_now_add=True, db_index=True)
    modifiedon = models.DateTimeField(auto_now=True, db_index=True)

    class Meta:
        app_label = "hotels"
        verbose_name = _('Hotel Offer Template')
        verbose_name_plural = _('Hotel Offer Templates')

    def admin_url(self):
        return reverse('admin:hotels_offertemplate_change', args=(self.id,))


class HotelOfferCondition(INGOModel):
    offercode = models.CharField(_('Offer Code'), max_length=20, null=True, blank=True,
                                 db_index=True, unique=True)
    description = models.CharField(max_length=1000, null=True)
    offer_name = models.CharField(max_length=120, null=True, blank=True)
    hotelcode = models.CharField(max_length=15, null=True, blank=True)
    bookingdatestart = models.DateTimeField('Booking starts on', null=True, db_index=True,
                                            help_text="<span style='margin-left:-106px !important'><b>Time entered will be"
                                                      " rounded off to the next multiple of 3 hours.</b></span>")
    bookingdateend = models.DateTimeField('Booking ends on', null=True, db_index=True,
                                          help_text="<span style='margin-left:-106px !important'><b>Time entered will be"
                                                    " rounded off to the previous multiple of 3 hours.</b></span>")
    bookingblackoutdates = models.CharField('Booking Blackout Dates', max_length=1000, null=True, blank=True)
    checkindatestart = models.DateField('Checkin Starts on', null=True, blank=True)
    checkoutdateend = models.DateField('Checkout Ends on', null=True, blank=True)
    checkinblackoutdates = models.CharField('Checkin Blackout Dates', max_length=1000, null=True, blank=True)
    earlybirdmin = models.PositiveSmallIntegerField('Early Bird (Min)',
                                                    null=True, blank=True)  # , default=-1)TODO
    earlybirdmax = models.PositiveSmallIntegerField('Last Minute Deal/Early Bird (Max)',
                                                    null=True, blank=True)  # , default=9999)TODO
    minnights = models.PositiveSmallIntegerField('Minimum Nights', default=1, null=True, blank=True)
    noofrooms = models.PositiveSmallIntegerField('Minimum Rooms', null=True, blank=True, default=1)
    checkinweekday = MultiSelectField(_('Check-in Specific Days'), max_length=100,
                                      null=True, blank=True, choices=hotelchoice.checkinWeekDay,
                                      default="0,1,2,3,4,5,6")
    bookingweekday = MultiSelectField(_('Booking on Specific Days'), max_length=100,
                                      null=True, blank=True, choices=hotelchoice.bookingWeekDay,
                                      default="0,1,2,3,4,5,6")
    offercondition = models.CharField(_('Offer Conditions'), max_length=50, null=True, blank=True,
                                      choices=hotelchoice.HotelOfferCondition, default='all')
    offermulticonditions = models.CharField(_('Offers Multiple conditions'), max_length=500, null=True, blank=True)
    night = models.CommaSeparatedIntegerField(_('Apply on nights/rooms'),
                                              max_length=50, null=True, blank=True)
    offercategory = models.CharField(max_length=50, null=True, choices=hotelchoice.HotelOfferCategory,
                                     help_text='Early Bird/Last Minute')
    content_type = models.ForeignKey(ContentType, db_index=True)
    object_id = models.PositiveIntegerField(db_index=True)
    content_object = GenericForeignKey('content_type', 'object_id')
    isactive = models.BooleanField(default=False)
    push_to_hotelstore_timestamp = models.DateTimeField(
        'Last pushed to hotelstore timestamp', null=True, blank=True)
    showtop = models.BooleanField(default=False)
    apply_on_linked_rp = models.BooleanField(_('Apply on Linked Rateplans'), default=True)
    offercancellationrules = GenericRelation(HotelCancellationRule, related_query_name='hotel_cancellation_rule')
    nonrefundable = models.BooleanField(_('Non Refundable'), default=False)

    low_inventory_thr = models.IntegerField(_('Low Inventory Threshold'), null=True, blank=True)
    max_inventory_thr = models.IntegerField(_('Max Inventory Threshold'), null=True, blank=True)
    offer_source = models.PositiveSmallIntegerField(_('Offer Created Through'), db_index=True,
                                                    choices=hotelchoice.HOTEL_OFFER_SOURCE_TYPE, null=True, )
    user = CurrentUserField(verbose_name=_('Last Modified By'))
    createdon = models.DateTimeField(auto_now_add=True, db_index=True)
    modifiedon = models.DateTimeField(auto_now=True, db_index=True)
    mmt_promoseqid = models.CharField(_('MMT Promo Seq Id'), max_length=40, null=True, blank=True,
                                      db_index=True)
    pah_applicable = models.BooleanField(_('PAH Applicable'),default=False)
    is_time_based_promo = models.BooleanField(_('Time Based Promotion'), default=False)

    parent_group_id = models.CharField(_('Parent Group Id'), max_length=20, null=True, blank=True)
    applicable_window = models.PositiveSmallIntegerField('Applicable Window', default=None, null=True, blank=True)
    max_los = models.PositiveSmallIntegerField('Maximum Length of stay', default=None, null=True, blank=True)
    offer_type_flags = FlagBitField(choices=hotelchoice.OFFER_FLAG_DICT, bits=32)
    start_time = models.PositiveSmallIntegerField('Start Time',
                                                    null=True, blank=True)
    end_time = models.PositiveSmallIntegerField('End Time',
                                                    null=True, blank=True)
    template_mapping = GenericRelation(TemplateMapping)
    init_call_done = False

    def __init__(self, *args, **kwargs):
        self.init_call_done = False
        super(HotelOfferCondition, self).__init__(*args, **kwargs)
        if args or 'offer_type_flags' in kwargs:
            for flag_name in HOTEL_OFFER_FLAG_DICT:
                self.offer_type_flags[flag_name] = self.offer_type_flags.get(flag_name, False)
        self.init_call_done = True

    def __setattr__(self, key, value):
        if self.init_call_done:
            if key in list(hotelchoice.OFFER_FLAG_DICT.values()):
                self.offer_type_flags[key] = value
                super(HotelOfferCondition, self).__setattr__('offer_type_flags', self.offer_type_flags)
        super(HotelOfferCondition, self).__setattr__(key, value)

    @property
    def inventory_thr_applicable(self):
        return self.low_inventory_thr or self.max_inventory_thr

    class Meta:
        app_label = "hotels"
        verbose_name = _('Hotel Offer Condition')
        verbose_name_plural = _('Hotel Offer Conditions')

    def admin_url(self):
        return reverse('admin:hotels_hoteloffercondition_change', args=(self.id,))

    def update_offer_type_flags(self):
        self.offer_type_flags['customised'] = True if self.offercategory == hotelchoice.CUSTOMISED else False
        self.offer_type_flags['early_bird'] = True if self.offercategory == hotelchoice.EARLY_BIRD else False
        self.offer_type_flags['last_minute'] = True if self.offercategory == hotelchoice.LAST_MINUTE else False
        self.offer_type_flags['time_based_promo'] = self.is_time_based_promo
        self.offer_type_flags['advance_window'] = self.applicable_window
        self.offer_type_flags['first_booking_limit'] = True if self.offercategory == hotelchoice.FIRST_BOOKING_LIMIT else False

    # Checks edit permission for the object before saving
    # Warning: Use at your own risk
    def save_for_user(self, user, *args, **kwargs):
        if user and isinstance(user, User):
            permission = 'edit_hoteloffer'
            if self.content_type.model == 'hoteldetail':
                if check_permission(user, self.content_object, permission):
                    return self.save(*args, **kwargs)
            if self.content_type.model == 'roomdetail':
                if check_permission(user, self.content_object.hotel, permission):
                    return self.save(*args, **kwargs)
            if self.content_type.model == 'rateplan':
                if check_permission(user, self.content_object.hotelname(), permission):
                    return self.save(*args, **kwargs)

    def save(self, *args, **kwargs):
        db_obj = None
        hash_change_function_call = kwargs.pop('hashChangeFunctionCall', True)
        update_hotel_store_flag = kwargs.pop('update_hotel_store_flag', True)
        force_update_to_hotelstore = kwargs.pop('force_push_to_hotelstore', False)
        # self.description = self.description.encode("ascii", "ignore")
        self.update_offer_type_flags()
        if self.id:
            try:
                db_obj = HotelOfferCondition.objects.using('default').get(id=self.id)
            except Exception, e:
                logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % \
                                ('hotels.models', 'hoteloffer.HotelOffer', 'save',
                                 '', '', str(e)))
        # if not db_obj or (db_obj and
        #                           self.bookingdatestart != db_obj.bookingdatestart):
        #     self.bookingdatestart = round_off_time_stamp_to_hours(
        #         self.bookingdatestart, mode="ceil")
        # if not db_obj or (db_obj and
        #                           self.bookingdateend != db_obj.bookingdateend):
        #     self.bookingdateend = round_off_time_stamp_to_hours(
        #         self.bookingdateend, mode="floor")

        if not self.offercode:
            super(HotelOfferCondition, self).save(*args, **kwargs)
            self.offercode = HotelHelper.update_code(self.id,
                                                     HotelConf.OffercodeLength, HotelConf.OfferCodePrefix)

        if not self.parent_group_id and self.offercode and self.description and self.description == AAJ_KA_BHAO:
            self.parent_group_id = self.offercode

        if not self.hotelcode or (db_obj and (db_obj.content_type_id != self.content_type_id or
                                  db_obj.object_id != self.object_id)):
            self.hotelcode = self.related_to_hotelcode()
        if db_obj and self.isactive != db_obj.isactive:
            if self.parent_group_id == self.offercode and self.parent_group_id != "" and self.parent_group_id != None:
                offer_obj_list = HotelOfferCondition.objects.filter(hotelcode=db_obj.hotelcode).filter(Q(parent_group_id=self.parent_group_id) | Q(offercode=self.parent_group_id))
                for offer_obj in offer_obj_list:
                    offer_obj.isactive = self.isactive
                    super(HotelOfferCondition, offer_obj).save(*args, **kwargs)

        super(HotelOfferCondition, self).save(*args, **kwargs)

        hotelstore_updated = False
        '''if (hash_change_function_call and update_hotel_store_flag) or force_update_to_hotelstore:
            hotelstore_updated = self.update_hotelstore(
                db_obj, force_update_to_hotelstore)
        if hotelstore_updated:
            self.push_to_hotelstore_timestamp = datetime.datetime.now()
            super(HotelOfferCondition, self).save(*args, **kwargs)'''

    # def inv_cache_update_type(self, update_type='regular', db_obj=None):
    #     change_type = 'no_inv'
    #     cur_inv_thr_app_active = self.inventory_thr_applicable and self.isactive
    #     old_inv_thr_app_active = db_obj and db_obj.inventory_thr_applicable and db_obj.isactive
    #
    #     if not db_obj and cur_inv_thr_app_active:
    #         # A new offer is created with inv_thr_app
    #         change_type = 'update'
    #
    #     elif db_obj and not old_inv_thr_app_active \
    #             and cur_inv_thr_app_active:
    #         # An old offer has changed itself to inventory based offer
    #         change_type = 'update'
    #
    #     elif old_inv_thr_app_active \
    #             and (not cur_inv_thr_app_active
    #                  or (self.low_inventory_thr != db_obj.low_inventory_thr
    #                      or self.max_inventory_thr != db_obj.max_inventory_thr)):
    #         # An old offers inv_thr has changes or it's been deactivated
    #         change_type = 'sync'
    #
    #     return change_type

    '''def update_hotelstore(self, force_update_to_hotelstore=False,event_data = None):
        """TODO : Add code to compare two offer to be same."""
        # force_update_to_hotelstore will override other checks and
        # ensure offer is pushed to hotelstore

        flag = settings.HOTELSTORE_FLAG and (self.isactive or (
            db_obj and db_obj.isactive != self.isactive))

        should_update_hotelstore = (self.is_valid_today()
                                    ) or force_update_to_hotelstore
        if should_update_hotelstore:
            # What if offer is not applicable today but is for sometime in next 90days?
            # ans: there is a daily cron which execute update_hotel_store_with_offers_daily()
            # HOTELSTORE: update to offer trigger update to hotelstore
            from hotel_store.update_store import UpdateHotelStore

            uhs = UpdateHotelStore()
            uhs.update_hotel_store_offer(self,event_data)
            try:
                hotelcode = self.related_to_hotelcode()
                HotelHelper.hotelstore_update_log_format(hotelcode, 'offer')
            except:
                logger.critical('ERROR: OFFER SAVE: related_to_hotelcode: %s' % (str(self.id)))
        return should_update_hotelstore'''

    def related_to_hotelcode(self):
        hotelcode = ''
        if self.content_type.model == 'hoteldetail':
            hotelcode = (self.content_object).hotelcode
        elif self.content_type.model == 'roomdetail':
            hotelcode = (self.content_object).hotel.hotelcode
        elif self.content_type.model == 'rateplan':
            hotelcode = (self.content_object).roomtype.hotel.hotelcode
        return hotelcode

    def related_to(self):
        if self.content_type.model == 'hoteldetail':
            related_name = '%s, %s' % ((self.content_object).hotelname, (self.content_object).city_id.cityname)
        elif self.content_type.model == 'roomdetail':
            related_name = (self.content_object).roomtypename
        elif self.content_type.model == 'rateplan':
            related_name = '%s - %s' % ((self.content_object).rateplanname, (self.content_object).roomtype.roomtypename)
        else:
            related_name = "%s: %s" % (str(self.content_type), str(self.content_object))
        return related_name

    related_to.short_description = 'Related To'
    related_to.admin_order_field = 'content_type'

    def get_hotel_object(self):
        # this will hit database to fetch content_object
        hotel = None
        if self.content_type.model == 'hoteldetail':
            hotel = self.content_object
        elif self.content_type.model == 'roomdetail':
            hotel = self.content_object.hotel
        elif self.content_type.model == 'rateplan':
            hotel = self.content_object.roomtype.hotel
        return hotel

    def getHotelCode(self):
        # this will hit database to fetch content_object
        hotel = self.get_hotel_object()
        return hotel.hotelcode if hotel else None

    def is_early_bird_offer(self):
        return self.earlybirdmax is not None or self.earlybirdmin is not None

    def is_valid_today(self):
        now = datetime.datetime.now()
        if self.bookingdateend and self.checkoutdateend:
            return (now <= self.bookingdateend and now.date() <= self.checkoutdateend)
        elif self.bookingdateend:
            return now <= self.bookingdateend
        elif self.checkoutdateend:
            return now.date() <= self.checkoutdateend
        return True

    def get_booking_blackout_dates(self):
        return HotelHelper.create_blackout_date_list(self.bookingblackoutdates)

    def get_checkin_blackout_dates(self):
        return HotelHelper.create_blackout_date_list(self.checkinblackoutdates)


class HotelOfferValue(INGOModel):
    offer_condition = models.ForeignKey(HotelOfferCondition, db_index=True,
                                        verbose_name=_("OfferCondition"), related_name='offer_values')
    offer_basis = models.CharField(_('Offer Basis'), max_length=10)

    offer_type = models.CharField(max_length=100, choices=hotelchoice.HotelOfferType, default='percentage')
    channel = models.CharField(max_length=10, choices=hotelchoice.CHANNEL_TYPE_BACKEND, default='all')
    offer_value = models.FloatField(_('Offer Value'), default=0)
    offer_textual_value = models.CharField(max_length=300, null=True, blank=True)
    description = models.CharField(max_length=1000, null=True)
    apply_com_based_offer = models.BooleanField(_('Apply Commission Based Offers with this Offer.'), default=True)
    segment = models.CharField( max_length=10, db_index=True, choices=hotelchoice.ALLOWED_PROMOTION_SEGMENT_FOR_UPDATE_ON_ADMIN.union(AFFILIATES_SEGMENTS), default='b2c')
    non_refundable = models.BooleanField(_('Non Refundable'), default=False)
    isactive = models.BooleanField(_('Activation'), default=False)

    user = CurrentUserField(verbose_name=_('Last Modified By'))
    createdon = models.DateTimeField(auto_now_add=True, db_index=True)
    modifiedon = models.DateTimeField(auto_now=True, db_index=True)

    class Meta:
        app_label = "hotels"
        verbose_name = _('Hotel Offer Value')
        verbose_name_plural = _('Hotel Offer Values')
        unique_together = ('offer_condition', 'offer_basis', 'channel', 'segment')

    def __unicode__(self):
        return self.description

    def admin_url(self):
        return reverse('admin:hotels_hoteloffervalue_change', args=(self.id,))

    def update_offer_type_flags(self):
        offer_type_flags = self.offer_condition.offer_type_flags
        offer_type_flags['cug'] = True if self.offer_basis == 'cug' else False
        offer_type_flags['coupon'] = True if self.offer_basis == 'coupon' else False
        offer_type_flags['commission'] = True if self.offer_basis == 'commission' else False
        offer_type_flags['textual'] = True if self.offer_type == 'textual' else False
        offer_type_flags['zcr'] = True if (self.offer_textual_value and OFFER_AAJ_KA_BHAO in self.offer_textual_value) else False
        offer_type_flags['discount'] =\
            True if self.offer_basis == 'discount' and not (self.offer_textual_value and OFFER_AAJ_KA_BHAO in self.offer_textual_value)\
                    and self.offer_type != 'textual' else False
        self.offer_condition.save()

    @staticmethod
    def create_new_affiliate_offer(offer_value_obj, user):
        from api.v2.cug.resources.affiliate_cug_helper import AffiliateCUGHelper
        affiliate_cug_helper = AffiliateCUGHelper()
        cug_segments = [offer_value_obj.segment]
        offer_values = {"offer_value": offer_value_obj.offer_value, "is_active": offer_value_obj.isactive}
        cug_segment_offer_value_map = {offer_value_obj.segment: offer_values}
        affiliate_cug_contracts = affiliate_cug_helper.get_affiliates_contract(AFFILIATES_SEGMENTS_LIST, cug_segments)

        affiliate_cug_helper.create_affiliates_offer_values(user, offer_value_obj.offer_condition,
                                                            affiliate_cug_contracts, cug_segment_offer_value_map)

    @staticmethod
    def handle_hotel_offer_value_changed(offer_value_obj, changed_parameter_list):
        from api.v2.cug.views.cug_helper import update_affiliate_offers
        from hotels.models.helper import get_code_from_id
        cug_segment = offer_value_obj.segment
        offer_value_id, offer_condition_id = offer_value_obj.id, offer_value_obj.offer_condition_id
        if cug_segment in AFFILIATES_SEGMENTS_LIST:
            logger.info("Invalid cug segment for %s task, offer_value_id: %s, offer_condition_id: %s" %
                        ("hotel_offer_value_changed", offer_value_id, offer_condition_id))
            return
        hotel_offer_obj = HotelOfferCondition.objects.filter(id=offer_condition_id).values("hotelcode")
        hotel_code = hotel_offer_obj[0]["hotelcode"]
        try:
            logger.info('%s\t%s\t%s\t%s\t%s\t%s' % (
                'hotels', 'tasks', 'update_affiliate_offers_value',
                offer_value_id, offer_condition_id, 'Update affiliates offer value'))
            offer_code = get_code_from_id(offer_condition_id, HotelConf.OffercodeLength, HotelConf.OfferCodePrefix)
            existing_offer_value_list = CugOfferValue.objects.filter(offer_condition__hotelcode=hotel_code,
                                                                     offer_condition__offercode=offer_code)
            offer_value_map = {cug_segment: changed_parameter_list}
            update_affiliate_offers(hotel_code, offer_value_map, existing_offer_value_list, cug_segments=[cug_segment])
        except Exception, e:
            logger.critical('%s\t%s\t%s\t%s\t%s\t%s\t%s' % (
                'hotels', 'tasks', 'update_affiliate_offers_value',
                offer_value_id, offer_condition_id, repr(e), repr(traceback.format_exc())))

    def save(self, *args, **kwargs):
        db_obj = None
        if not self.channel:
            self.channel = 'all'
        self.create_description()
        self.update_offer_type_flags()
        if self.id:
            try:
                db_obj = HotelOfferValue.objects.using('default').get(id=self.id)

            except Exception, e:
                logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % \
                                ('hotels.models', 'hoteloffer.HotelOfferValue', 'save',
                                 '', '', str(e)))
        super(HotelOfferValue, self).save(*args, **kwargs)
        if not db_obj:
            if str(self.offer_basis) == CUG_OFFER:
                self.create_new_affiliate_offer(self, self.user)
        else:
            if str(db_obj.offer_basis) == CUG_OFFER:
                changed_parameter_list = []
                if db_obj.offer_value != self.offer_value:
                    changed_param_dict = {"offer_value": self.offer_value}
                    changed_parameter_list.append(changed_param_dict)
                if db_obj.isactive != self.isactive:
                    changed_param_dict = {"is_active": self.isactive}
                    changed_parameter_list.append(changed_param_dict)
                self.handle_hotel_offer_value_changed(db_obj, changed_parameter_list)

    def create_description(self):
        currency = str(self.offer_condition.get_hotel_object().base_currency)
        if self.offer_type == 'textual':
            offer_textual_value = {'value' : ''}
            if self.offer_textual_value:
                offer_textual_value = eval(self.offer_textual_value)
            offer_basis = str(self.offer_basis)
            offer_template = TextOfferTemplate.objects.get(id=int(offer_basis))
            description = offer_template.template_text
            template_tpye = str(offer_template.type)
            textual_offer_type = dict(hotelchoice.TEXTUAL_OFFER_TYPE)
            if textual_offer_type.get(template_tpye) == 'TRANSFER':
                description = '{} - {}'.format(description, offer_textual_value['value'])
            elif textual_offer_type.get(template_tpye) == 'PERCENT':
                description = '{} - {} %'.format(description, self.offer_value)
            elif textual_offer_type.get(template_tpye) == 'ADULT_CHILD_INFANT_CHARGE':
                if offer_textual_value.get('ppa', '') and offer_textual_value['ppa'] > 0:
                    description = '{} {} : {} {}, '.format(description, 'price per adult',
                                                      offer_textual_value['ppa'], currency)
                if offer_textual_value.get('ppc', '') and offer_textual_value['ppc'] > 0:
                    description = '{} {} : {} {}, '.format(description, 'price per child',
                                                      offer_textual_value['ppc'], currency)
                if offer_textual_value.get('ppi', '') and offer_textual_value['ppi'] > 0:
                    description = '{} {} : {} {}, '.format(description, 'price per infant',
                                                      offer_textual_value['ppi'], currency)
                description = description.rstrip(', ')
            elif textual_offer_type.get(template_tpye) == 'FIXED_CHARGE':
                description = '{} - {} {}'.format(description, self.offer_value, currency)

            self.description = description
            self.offer_condition.description = self.description
            self.offer_condition.save()
        else:
            description = '%s %s %s for %s segment .' % (self.offer_basis, self.offer_value, self.offer_type ,  self.segment)
            self.description = description
            
    @property
    def validity(self):
        check_in_validity =  self.check_in_validity
        booking_validity= self.booking_validity
        validity = booking_validity + ', ' + check_in_validity \
                    if check_in_validity else booking_validity
        return validity

    @property
    def booking_validity(self):
        return 'Booking Validity: {start_date} to {end_date}'.format(
            start_date=self.offer_condition.bookingdatestart,
            end_date=self.offer_condition.bookingdateend)

    @property
    def check_in_validity(self):
        if self.offer_condition.checkindatestart:
            return 'Check-in Validity: {start_date} to {end_date}'.format(
                        start_date=self.offer_condition.checkindatestart,
                        end_date=self.offer_condition.checkoutdateend)

class OfferTimeSlots(INGOModel):

    start_time = models.TimeField(null=False, db_index=True)
    end_time = models.TimeField(null=False, db_index=True)
    hotel_offer = models.ForeignKey(HotelOfferCondition, verbose_name=_('HotelOfferCondition Id'), db_index=True,
                                    null=False)
    is_active = models.BooleanField(db_index=True, default=True)
    createdon = models.DateTimeField(auto_now_add=True, db_index=True)
    modifiedon = models.DateTimeField(auto_now=True, db_index=True)

    def save(self, *args, **kwargs):

        if self.is_active:
            # check start time < end time for all times
            if self.start_time >= self.end_time:
                raise Exception('Invalid Timings')

            # check that overlapping times should not be saved
            query1 = (Q(start_time__lte=self.start_time) & Q(end_time__gt=self.start_time)) | (
                Q(start_time__lt=self.end_time) & Q(end_time__gte=self.end_time))

            offers = OfferTimeSlots.objects.filter(hotel_offer=self.hotel_offer, is_active=True).filter(query1)
            if self.id:
                offers = offers.exclude(id=self.id)

            if offers and self.is_active:
                raise Exception("overlapping time slots")

        super(OfferTimeSlots, self).save(*args, **kwargs)

    class Meta:

        app_label = "hotels"
        verbose_name = _('Hotel Offer Timings')
        verbose_name_plural = _('Hotel Offer Timings')


def send_offer_alert(sender, **kwargs):
    offer = kwargs.get('instance')
    from communication.notifications import send_action_required_mailer
    trigger_mail = True
    if kwargs.get('created', False) and offer.isactive and offer.offer_basis == 'commission':
        textual_value = offer.offer_textual_value
        if textual_value:
            textual_value = json.loads(textual_value)
            if textual_value.get('promo_type', '') == DISCOUNT_COUPON:
                trigger_mail = False
        if trigger_mail:
            send_action_required_mailer(offer, 'activated')


models.signals.post_save.connect(send_offer_alert, HotelOfferValue)

class CugOfferCondition(HotelOfferCondition):

    class Meta:
        proxy = True

class CugOfferValue(INGOModel):
    offer_condition = models.ForeignKey(CugOfferCondition, db_index=True,
                                        verbose_name=_('CugOfferCondition'), related_name='offer_values_cug')
    offer_basis = models.CharField(_('Offer Basis'), max_length=10)
    offer_type = models.CharField(max_length=100, choices=CUG_OFFER_TYPE, default=CUG_OFFER_TYPE_PERCENTAGE)
    channel = models.CharField(max_length=10, choices=hotelchoice.CHANNEL_TYPE_BACKEND, default=hotelchoice.CHANNEL_TYPE_BACKEND_all)
    offer_value = models.FloatField(_('Offer Value'), default=0)
    offer_textual_value = models.CharField(max_length=300, null=True, blank=True)
    description = models.CharField(max_length=1000, null=True)
    apply_com_based_offer = models.BooleanField(_('Apply Commission Based Offers with this Offer.'), default=True)
    segment = models.CharField( max_length=10, db_index=True, choices=(CUG_SEGMENTS.union(AFFILIATES_SEGMENTS)), default=CUG_SEGMENT_CUG1)
    non_refundable = models.BooleanField(_('Non Refundable'), default=False)
    isactive = models.BooleanField(_('Activation'), default=False)
    user = CurrentUserField(verbose_name=_('Last Modified By'))
    createdon = models.DateTimeField(auto_now_add=True, db_index=True)
    modifiedon = models.DateTimeField(auto_now=True, db_index=True)

    class Meta:
        db_table = "hotels_hoteloffervalue"
        app_label = "hotels"
        verbose_name = _('Cug Offer Value')
        verbose_name_plural = _('Cug Offer Values')
        unique_together = ('offer_condition', 'offer_basis', 'channel', 'segment')

