syntax = "proto3";

option go_package = "pkg/generated_proto_stubs/mediaPush";

service MediaPushService {
  rpc PushMediaToGMP(PushMediaToGMPRequest) returns (PushMediaToGMPResponse);
  rpc PushForAutoModeration(PushForAutoModerationRequest) returns (PushForAutoModerationResponse);
}

message PushMediaToGMPRequest {
  int64 entityId = 1;
  bool pushImage = 2;
  bool pushVideo = 3;
  string entityType = 4;
  string propertyName = 5;
}

message PushMediaToGMPResponse {
  bool success = 1;
  string message = 2;
  repeated PushMediaData pushImageData = 3;
  repeated PushMediaData pushVideoData = 4;
  MediaPushErrorDetail errorDetail = 5;
}

message MediaPushErrorDetail {
  string errorCode = 1;
  string errorMessage = 2;
  string errorType = 3;
  string displayMessage=4;
}

message PushMediaData {
  int64 id = 1;
  ModerationStatus status = 2;
  int64 objectId = 3;
  string contentTypeModel = 4;
  string mediaType = 5;
}

enum ModerationStatus {
  APPROVED = 0;
  REJECTED = 1;
  PENDING = 2;
  AUTO_MODERATED = 3;
}

message PushForAutoModerationRequest {
  repeated EntityImages entityImages = 1;
}

message EntityImages {
  int64 objectId = 1;
  string entityType = 2;
  repeated int64 imageIds = 3;
}

message PushForAutoModerationResponse {
  repeated MediaPushStatus entityStatuses = 1;
}

message MediaPushStatus {
  int64 objectId = 1;
  repeated ImagePushStatus imageStatuses = 2;
}

message ImagePushStatus {
  int64 imageId = 1;
  string status = 2;
  string message = 3;
}