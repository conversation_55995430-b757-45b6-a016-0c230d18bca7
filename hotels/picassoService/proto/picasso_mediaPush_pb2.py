# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: picasso_mediaPush.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='picasso_mediaPush.proto',
  package='',
  syntax='proto3',
  serialized_pb=_b('\n\x17picasso_mediaPush.proto\"y\n\x15PushMediaToGMPRequest\x12\x10\n\x08\x65ntityId\x18\x01 \x01(\x03\x12\x11\n\tpushImage\x18\x02 \x01(\x08\x12\x11\n\tpushVideo\x18\x03 \x01(\x08\x12\x12\n\nentityType\x18\x04 \x01(\t\x12\x14\n\x0cpropertyName\x18\x05 \x01(\t\"\xb4\x01\n\x16PushMediaToGMPResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12%\n\rpushImageData\x18\x03 \x03(\x0b\x32\x0e.PushMediaData\x12%\n\rpushVideoData\x18\x04 \x03(\x0b\x32\x0e.PushMediaData\x12*\n\x0b\x65rrorDetail\x18\x05 \x01(\x0b\x32\x15.MediaPushErrorDetail\"j\n\x14MediaPushErrorDetail\x12\x11\n\terrorCode\x18\x01 \x01(\t\x12\x14\n\x0c\x65rrorMessage\x18\x02 \x01(\t\x12\x11\n\terrorType\x18\x03 \x01(\t\x12\x16\n\x0e\x64isplayMessage\x18\x04 \x01(\t\"}\n\rPushMediaData\x12\n\n\x02id\x18\x01 \x01(\x03\x12!\n\x06status\x18\x02 \x01(\x0e\x32\x11.ModerationStatus\x12\x10\n\x08objectId\x18\x03 \x01(\x03\x12\x18\n\x10\x63ontentTypeModel\x18\x04 \x01(\t\x12\x11\n\tmediaType\x18\x05 \x01(\t\"C\n\x1cPushForAutoModerationRequest\x12#\n\x0c\x65ntityImages\x18\x01 \x03(\x0b\x32\r.EntityImages\"F\n\x0c\x45ntityImages\x12\x10\n\x08objectId\x18\x01 \x01(\x03\x12\x12\n\nentityType\x18\x02 \x01(\t\x12\x10\n\x08imageIds\x18\x03 \x03(\x03\"I\n\x1dPushForAutoModerationResponse\x12(\n\x0e\x65ntityStatuses\x18\x01 \x03(\x0b\x32\x10.MediaPushStatus\"L\n\x0fMediaPushStatus\x12\x10\n\x08objectId\x18\x01 \x01(\x03\x12\'\n\rimageStatuses\x18\x02 \x03(\x0b\x32\x10.ImagePushStatus\"C\n\x0fImagePushStatus\x12\x0f\n\x07imageId\x18\x01 \x01(\x03\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x0f\n\x07message\x18\x03 \x01(\t*O\n\x10ModerationStatus\x12\x0c\n\x08\x41PPROVED\x10\x00\x12\x0c\n\x08REJECTED\x10\x01\x12\x0b\n\x07PENDING\x10\x02\x12\x12\n\x0e\x41UTO_MODERATED\x10\x03\x32\xad\x01\n\x10MediaPushService\x12\x41\n\x0ePushMediaToGMP\x12\x16.PushMediaToGMPRequest\x1a\x17.PushMediaToGMPResponse\x12V\n\x15PushForAutoModeration\x12\x1d.PushForAutoModerationRequest\x1a\x1e.PushForAutoModerationResponseB%Z#pkg/generated_proto_stubs/mediaPushb\x06proto3')
)

_MODERATIONSTATUS = _descriptor.EnumDescriptor(
  name='ModerationStatus',
  full_name='ModerationStatus',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='APPROVED', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='REJECTED', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PENDING', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='AUTO_MODERATED', index=3, number=3,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=931,
  serialized_end=1010,
)
_sym_db.RegisterEnumDescriptor(_MODERATIONSTATUS)

ModerationStatus = enum_type_wrapper.EnumTypeWrapper(_MODERATIONSTATUS)
APPROVED = 0
REJECTED = 1
PENDING = 2
AUTO_MODERATED = 3



_PUSHMEDIATOGMPREQUEST = _descriptor.Descriptor(
  name='PushMediaToGMPRequest',
  full_name='PushMediaToGMPRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='entityId', full_name='PushMediaToGMPRequest.entityId', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pushImage', full_name='PushMediaToGMPRequest.pushImage', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pushVideo', full_name='PushMediaToGMPRequest.pushVideo', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='entityType', full_name='PushMediaToGMPRequest.entityType', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='propertyName', full_name='PushMediaToGMPRequest.propertyName', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=27,
  serialized_end=148,
)


_PUSHMEDIATOGMPRESPONSE = _descriptor.Descriptor(
  name='PushMediaToGMPResponse',
  full_name='PushMediaToGMPResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='PushMediaToGMPResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='message', full_name='PushMediaToGMPResponse.message', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pushImageData', full_name='PushMediaToGMPResponse.pushImageData', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='pushVideoData', full_name='PushMediaToGMPResponse.pushVideoData', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='errorDetail', full_name='PushMediaToGMPResponse.errorDetail', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=151,
  serialized_end=331,
)


_MEDIAPUSHERRORDETAIL = _descriptor.Descriptor(
  name='MediaPushErrorDetail',
  full_name='MediaPushErrorDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='errorCode', full_name='MediaPushErrorDetail.errorCode', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='errorMessage', full_name='MediaPushErrorDetail.errorMessage', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='errorType', full_name='MediaPushErrorDetail.errorType', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='displayMessage', full_name='MediaPushErrorDetail.displayMessage', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=333,
  serialized_end=439,
)


_PUSHMEDIADATA = _descriptor.Descriptor(
  name='PushMediaData',
  full_name='PushMediaData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='PushMediaData.id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='PushMediaData.status', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='objectId', full_name='PushMediaData.objectId', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='contentTypeModel', full_name='PushMediaData.contentTypeModel', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mediaType', full_name='PushMediaData.mediaType', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=441,
  serialized_end=566,
)


_PUSHFORAUTOMODERATIONREQUEST = _descriptor.Descriptor(
  name='PushForAutoModerationRequest',
  full_name='PushForAutoModerationRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='entityImages', full_name='PushForAutoModerationRequest.entityImages', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=568,
  serialized_end=635,
)


_ENTITYIMAGES = _descriptor.Descriptor(
  name='EntityImages',
  full_name='EntityImages',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='objectId', full_name='EntityImages.objectId', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='entityType', full_name='EntityImages.entityType', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='imageIds', full_name='EntityImages.imageIds', index=2,
      number=3, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=637,
  serialized_end=707,
)


_PUSHFORAUTOMODERATIONRESPONSE = _descriptor.Descriptor(
  name='PushForAutoModerationResponse',
  full_name='PushForAutoModerationResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='entityStatuses', full_name='PushForAutoModerationResponse.entityStatuses', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=709,
  serialized_end=782,
)


_MEDIAPUSHSTATUS = _descriptor.Descriptor(
  name='MediaPushStatus',
  full_name='MediaPushStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='objectId', full_name='MediaPushStatus.objectId', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='imageStatuses', full_name='MediaPushStatus.imageStatuses', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=784,
  serialized_end=860,
)


_IMAGEPUSHSTATUS = _descriptor.Descriptor(
  name='ImagePushStatus',
  full_name='ImagePushStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='imageId', full_name='ImagePushStatus.imageId', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='status', full_name='ImagePushStatus.status', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='message', full_name='ImagePushStatus.message', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=862,
  serialized_end=929,
)

_PUSHMEDIATOGMPRESPONSE.fields_by_name['pushImageData'].message_type = _PUSHMEDIADATA
_PUSHMEDIATOGMPRESPONSE.fields_by_name['pushVideoData'].message_type = _PUSHMEDIADATA
_PUSHMEDIATOGMPRESPONSE.fields_by_name['errorDetail'].message_type = _MEDIAPUSHERRORDETAIL
_PUSHMEDIADATA.fields_by_name['status'].enum_type = _MODERATIONSTATUS
_PUSHFORAUTOMODERATIONREQUEST.fields_by_name['entityImages'].message_type = _ENTITYIMAGES
_PUSHFORAUTOMODERATIONRESPONSE.fields_by_name['entityStatuses'].message_type = _MEDIAPUSHSTATUS
_MEDIAPUSHSTATUS.fields_by_name['imageStatuses'].message_type = _IMAGEPUSHSTATUS
DESCRIPTOR.message_types_by_name['PushMediaToGMPRequest'] = _PUSHMEDIATOGMPREQUEST
DESCRIPTOR.message_types_by_name['PushMediaToGMPResponse'] = _PUSHMEDIATOGMPRESPONSE
DESCRIPTOR.message_types_by_name['MediaPushErrorDetail'] = _MEDIAPUSHERRORDETAIL
DESCRIPTOR.message_types_by_name['PushMediaData'] = _PUSHMEDIADATA
DESCRIPTOR.message_types_by_name['PushForAutoModerationRequest'] = _PUSHFORAUTOMODERATIONREQUEST
DESCRIPTOR.message_types_by_name['EntityImages'] = _ENTITYIMAGES
DESCRIPTOR.message_types_by_name['PushForAutoModerationResponse'] = _PUSHFORAUTOMODERATIONRESPONSE
DESCRIPTOR.message_types_by_name['MediaPushStatus'] = _MEDIAPUSHSTATUS
DESCRIPTOR.message_types_by_name['ImagePushStatus'] = _IMAGEPUSHSTATUS
DESCRIPTOR.enum_types_by_name['ModerationStatus'] = _MODERATIONSTATUS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

PushMediaToGMPRequest = _reflection.GeneratedProtocolMessageType('PushMediaToGMPRequest', (_message.Message,), dict(
  DESCRIPTOR = _PUSHMEDIATOGMPREQUEST,
  __module__ = 'picasso_mediaPush_pb2'
  # @@protoc_insertion_point(class_scope:PushMediaToGMPRequest)
  ))
_sym_db.RegisterMessage(PushMediaToGMPRequest)

PushMediaToGMPResponse = _reflection.GeneratedProtocolMessageType('PushMediaToGMPResponse', (_message.Message,), dict(
  DESCRIPTOR = _PUSHMEDIATOGMPRESPONSE,
  __module__ = 'picasso_mediaPush_pb2'
  # @@protoc_insertion_point(class_scope:PushMediaToGMPResponse)
  ))
_sym_db.RegisterMessage(PushMediaToGMPResponse)

MediaPushErrorDetail = _reflection.GeneratedProtocolMessageType('MediaPushErrorDetail', (_message.Message,), dict(
  DESCRIPTOR = _MEDIAPUSHERRORDETAIL,
  __module__ = 'picasso_mediaPush_pb2'
  # @@protoc_insertion_point(class_scope:MediaPushErrorDetail)
  ))
_sym_db.RegisterMessage(MediaPushErrorDetail)

PushMediaData = _reflection.GeneratedProtocolMessageType('PushMediaData', (_message.Message,), dict(
  DESCRIPTOR = _PUSHMEDIADATA,
  __module__ = 'picasso_mediaPush_pb2'
  # @@protoc_insertion_point(class_scope:PushMediaData)
  ))
_sym_db.RegisterMessage(PushMediaData)

PushForAutoModerationRequest = _reflection.GeneratedProtocolMessageType('PushForAutoModerationRequest', (_message.Message,), dict(
  DESCRIPTOR = _PUSHFORAUTOMODERATIONREQUEST,
  __module__ = 'picasso_mediaPush_pb2'
  # @@protoc_insertion_point(class_scope:PushForAutoModerationRequest)
  ))
_sym_db.RegisterMessage(PushForAutoModerationRequest)

EntityImages = _reflection.GeneratedProtocolMessageType('EntityImages', (_message.Message,), dict(
  DESCRIPTOR = _ENTITYIMAGES,
  __module__ = 'picasso_mediaPush_pb2'
  # @@protoc_insertion_point(class_scope:EntityImages)
  ))
_sym_db.RegisterMessage(EntityImages)

PushForAutoModerationResponse = _reflection.GeneratedProtocolMessageType('PushForAutoModerationResponse', (_message.Message,), dict(
  DESCRIPTOR = _PUSHFORAUTOMODERATIONRESPONSE,
  __module__ = 'picasso_mediaPush_pb2'
  # @@protoc_insertion_point(class_scope:PushForAutoModerationResponse)
  ))
_sym_db.RegisterMessage(PushForAutoModerationResponse)

MediaPushStatus = _reflection.GeneratedProtocolMessageType('MediaPushStatus', (_message.Message,), dict(
  DESCRIPTOR = _MEDIAPUSHSTATUS,
  __module__ = 'picasso_mediaPush_pb2'
  # @@protoc_insertion_point(class_scope:MediaPushStatus)
  ))
_sym_db.RegisterMessage(MediaPushStatus)

ImagePushStatus = _reflection.GeneratedProtocolMessageType('ImagePushStatus', (_message.Message,), dict(
  DESCRIPTOR = _IMAGEPUSHSTATUS,
  __module__ = 'picasso_mediaPush_pb2'
  # @@protoc_insertion_point(class_scope:ImagePushStatus)
  ))
_sym_db.RegisterMessage(ImagePushStatus)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('Z#pkg/generated_proto_stubs/mediaPush'))

_MEDIAPUSHSERVICE = _descriptor.ServiceDescriptor(
  name='MediaPushService',
  full_name='MediaPushService',
  file=DESCRIPTOR,
  index=0,
  options=None,
  serialized_start=1013,
  serialized_end=1186,
  methods=[
  _descriptor.MethodDescriptor(
    name='PushMediaToGMP',
    full_name='MediaPushService.PushMediaToGMP',
    index=0,
    containing_service=None,
    input_type=_PUSHMEDIATOGMPREQUEST,
    output_type=_PUSHMEDIATOGMPRESPONSE,
    options=None,
  ),
  _descriptor.MethodDescriptor(
    name='PushForAutoModeration',
    full_name='MediaPushService.PushForAutoModeration',
    index=1,
    containing_service=None,
    input_type=_PUSHFORAUTOMODERATIONREQUEST,
    output_type=_PUSHFORAUTOMODERATIONRESPONSE,
    options=None,
  ),
])
_sym_db.RegisterServiceDescriptor(_MEDIAPUSHSERVICE)

DESCRIPTOR.services_by_name['MediaPushService'] = _MEDIAPUSHSERVICE