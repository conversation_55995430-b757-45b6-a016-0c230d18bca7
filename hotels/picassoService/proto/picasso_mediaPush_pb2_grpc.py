# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

import picasso_mediaPush_pb2 as picasso__mediaPush__pb2


class MediaPushServiceStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.PushMediaToGMP = channel.unary_unary(
        '/MediaPushService/PushMediaToGMP',
        request_serializer=picasso__mediaPush__pb2.PushMediaToGMPRequest.SerializeToString,
        response_deserializer=picasso__mediaPush__pb2.PushMediaToGMPResponse.FromString,
        )
    self.PushForAutoModeration = channel.unary_unary(
        '/MediaPushService/PushForAutoModeration',
        request_serializer=picasso__mediaPush__pb2.PushForAutoModerationRequest.SerializeToString,
        response_deserializer=picasso__mediaPush__pb2.PushForAutoModerationResponse.FromString,
        )


class MediaPushServiceServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def PushMediaToGMP(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def PushForAutoModeration(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_MediaPushServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'PushMediaToGMP': grpc.unary_unary_rpc_method_handler(
          servicer.PushMediaToGMP,
          request_deserializer=picasso__mediaPush__pb2.PushMediaToGMPRequest.FromString,
          response_serializer=picasso__mediaPush__pb2.PushMediaToGMPResponse.SerializeToString,
      ),
      'PushForAutoModeration': grpc.unary_unary_rpc_method_handler(
          servicer.PushForAutoModeration,
          request_deserializer=picasso__mediaPush__pb2.PushForAutoModerationRequest.FromString,
          response_serializer=picasso__mediaPush__pb2.PushForAutoModerationResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'MediaPushService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))
