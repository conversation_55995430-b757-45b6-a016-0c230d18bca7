# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: picassoservice.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import wrappers_pb2 as google_dot_protobuf_dot_wrappers__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='picassoservice.proto',
  package='media',
  syntax='proto3',
  serialized_pb=_b('\n\x14picassoservice.proto\x12\x05media\x1a\x1egoogle/protobuf/wrappers.proto\"a\n\x0b\x45rrorDetail\x12\x11\n\terrorCode\x18\x01 \x01(\t\x12\x14\n\x0c\x65rrorMessage\x18\x02 \x01(\t\x12\x11\n\terrorType\x18\x03 \x01(\t\x12\x16\n\x0e\x64isplayMessage\x18\x04 \x01(\t\"\x97\x01\n\x11MediaResponseData\x12#\n\timageData\x18\x01 \x03(\x0b\x32\x10.media.ImageData\x12#\n\tvideoData\x18\x02 \x03(\x0b\x32\x10.media.VideoData\x12\x0f\n\x07message\x18\x03 \x01(\t\x12\'\n\x0b\x65rrorDetail\x18\x04 \x01(\x0b\x32\x12.media.ErrorDetail\"\x8d\x01\n\rMediaResponse\x12\x0f\n\x07message\x18\x01 \x01(\t\x12 \n\x06images\x18\x02 \x03(\x0b\x32\x10.media.ImageData\x12 \n\x06videos\x18\x03 \x03(\x0b\x32\x10.media.VideoData\x12\'\n\x0b\x65rrorDetail\x18\x04 \x01(\x0b\x32\x12.media.ErrorDetail\"j\n\x0eVideosResponse\x12\x0f\n\x07message\x18\x01 \x01(\t\x12\x1e\n\x04\x64\x61ta\x18\x02 \x03(\x0b\x32\x10.media.VideoData\x12\'\n\x0b\x65rrorDetail\x18\x03 \x01(\x0b\x32\x12.media.ErrorDetail\"b\n\x11TagsDetailRequest\x12,\n\x07hotelId\x18\x01 \x01(\x0b\x32\x1b.google.protobuf.Int64Value\x12\x11\n\thotelCode\x18\x02 \x01(\t\x12\x0c\n\x04tags\x18\x03 \x03(\t\"u\n\x12TagsDetailResponse\x12\x0f\n\x07message\x18\x01 \x01(\t\x12\x12\n\nimagesTags\x18\x02 \x01(\t\x12\x11\n\tvideoTags\x18\x03 \x01(\t\x12\'\n\x0b\x65rrorDetail\x18\x04 \x01(\x0b\x32\x12.media.ErrorDetail\"\xb4\x01\n\x16SpaceRoomDetailRequest\x12,\n\x07hotelId\x18\x01 \x01(\x0b\x32\x1b.google.protobuf.Int64Value\x12+\n\x06roomId\x18\x02 \x01(\x0b\x32\x1b.google.protobuf.Int64Value\x12,\n\x07spaceId\x18\x03 \x01(\x0b\x32\x1b.google.protobuf.Int64Value\x12\x11\n\thotelCode\x18\x04 \x01(\t\"\x99\x01\n\x17SpaceRoomDetailResponse\x12\x0f\n\x07message\x18\x01 \x01(\t\x12 \n\x05rooms\x18\x02 \x03(\x0b\x32\x11.media.RoomDetail\x12\"\n\x06spaces\x18\x03 \x03(\x0b\x32\x12.media.SpaceDetail\x12\'\n\x0b\x65rrorDetail\x18\x04 \x01(\x0b\x32\x12.media.ErrorDetail\"\xda\x01\n\x14ImageSequenceRequest\x12,\n\x07hotelId\x18\x01 \x01(\x0b\x32\x1b.google.protobuf.Int64Value\x12+\n\x06roomId\x18\x02 \x01(\x0b\x32\x1b.google.protobuf.Int64Value\x12,\n\x07spaceId\x18\x03 \x01(\x0b\x32\x1b.google.protobuf.Int64Value\x12\x11\n\thotelCode\x18\x04 \x01(\t\x12&\n\x04\x64\x61ta\x18\x05 \x03(\x0b\x32\x18.media.NewImagesSequence\"6\n\x11NewImagesSequence\x12\x0f\n\x07imageId\x18\x01 \x01(\x03\x12\x10\n\x08sequence\x18\x02 \x01(\x05\"J\n\x15\x46ilteredImagesRequest\x12\x11\n\thotelCode\x18\x01 \x01(\t\x12\x1e\n\x07\x66ilters\x18\x02 \x03(\x0b\x32\r.media.Filter\"J\n\x15\x46ilteredVideosRequest\x12\x11\n\thotelCode\x18\x01 \x01(\t\x12\x1e\n\x07\x66ilters\x18\x02 \x03(\x0b\x32\r.media.Filter\"1\n\x06\x46ilter\x12\x12\n\nfilterName\x18\x01 \x01(\t\x12\x13\n\x0b\x66ilterValue\x18\x02 \x01(\t\"j\n\x0eImagesResponse\x12\x0f\n\x07message\x18\x01 \x01(\t\x12\x1e\n\x04\x64\x61ta\x18\x02 \x03(\x0b\x32\x10.media.ImageData\x12\'\n\x0b\x65rrorDetail\x18\x03 \x01(\x0b\x32\x12.media.ErrorDetail\"\xed\x05\n\tImageData\x12\n\n\x02id\x18\x01 \x01(\x03\x12\r\n\x05image\x18\x02 \x01(\t\x12\x0c\n\x04user\x18\x03 \x01(\x03\x12\x13\n\x0bvendorImgId\x18\x04 \x01(\x03\x12\x10\n\x08sequence\x18\x05 \x01(\x05\x12\x11\n\thotelCode\x18\x06 \x01(\t\x12\x0e\n\x06\x63\x64nUrl\x18\x07 \x01(\t\x12\x14\n\x0crejectReason\x18\x08 \x03(\t\x12\x14\n\x0cselectedTags\x18\t \x03(\t\x12\x1c\n\x06mlTags\x18\n \x03(\x0b\x32\x0c.media.Mltag\x12\x11\n\thotelName\x18\x0b \x01(\t\x12\r\n\x05width\x18\x0c \x01(\x05\x12\x0e\n\x06height\x18\r \x01(\x05\x12\x0f\n\x07\x63\x61ption\x18\x0e \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x0f \x01(\t\x12\x31\n\x0bimageStatus\x18\x10 \x01(\x0e\x32\x1c.media.ImageData.ImageStatus\x12;\n\x10moderationStatus\x18\x11 \x01(\x0e\x32!.media.ImageData.ModerationStatus\x12\x31\n\x0borientation\x18\x12 \x01(\x0e\x32\x1c.media.ImageData.Orientation\x12\x16\n\x0esupplierSource\x18\x13 \x01(\t\x12\x1e\n\x05rooms\x18\x14 \x03(\x0b\x32\x0f.media.RoomInfo\x12 \n\x06spaces\x18\x15 \x03(\x0b\x32\x10.media.SpaceInfo\x12\x11\n\tcreatedOn\x18\x16 \x01(\t\"(\n\x0bImageStatus\x12\n\n\x06\x41\x43TIVE\x10\x00\x12\r\n\tIN_ACTIVE\x10\x01\"O\n\x10ModerationStatus\x12\x0c\n\x08\x41PPROVED\x10\x00\x12\x0c\n\x08REJECTED\x10\x01\x12\x0b\n\x07PENDING\x10\x02\x12\x12\n\x0e\x41UTO_MODERATED\x10\x03\"@\n\x0bOrientation\x12\x08\n\x04NONE\x10\x00\x12\r\n\tLANDSCAPE\x10\x01\x12\n\n\x06SQUARE\x10\x02\x12\x0c\n\x08PORTRAIT\x10\x03\"\x07\n\x05Mltag\">\n\x08RoomInfo\x12\x0e\n\x06roomId\x18\x01 \x01(\x03\x12\x10\n\x08roomName\x18\x02 \x01(\t\x12\x10\n\x08sequence\x18\x03 \x01(\x05\"@\n\nRoomDetail\x12\x0e\n\x06roomId\x18\x01 \x01(\x03\x12\x10\n\x08roomName\x18\x02 \x01(\t\x12\x10\n\x08roomType\x18\x03 \x01(\t\"A\n\tSpaceInfo\x12\x0f\n\x07spaceId\x18\x01 \x01(\x03\x12\x11\n\tspaceName\x18\x02 \x01(\t\x12\x10\n\x08sequence\x18\x03 \x01(\x05\"D\n\x0bSpaceDetail\x12\x0f\n\x07spaceId\x18\x01 \x01(\x03\x12\x11\n\tspaceName\x18\x02 \x01(\t\x12\x11\n\tspaceType\x18\x03 \x01(\t\"K\n\x16UpdateHotelMappingData\x12\x0f\n\x07videoId\x18\x01 \x03(\x03\x12\x0f\n\x07imageId\x18\x02 \x03(\x03\x12\x0f\n\x07hotelId\x18\x03 \x01(\x03\"L\n\x16UpdateSpaceMappingData\x12\x0f\n\x07videoId\x18\x01 \x03(\x03\x12\x0f\n\x07imageId\x18\x02 \x03(\x03\x12\x10\n\x08spaceIds\x18\x03 \x03(\x03\"J\n\x15UpdateRoomMappingData\x12\x0f\n\x07videoId\x18\x01 \x03(\x03\x12\x0f\n\x07imageId\x18\x02 \x03(\x03\x12\x0f\n\x07roomIds\x18\x03 \x03(\x03\"\xbe\x02\n\x1d\x42ulkMediaMappingUpdateRequest\x12\x11\n\thotelCode\x18\x01 \x01(\t\x12\x0f\n\x07hotelId\x18\x02 \x01(\x03\x12\x31\n\nunMapHotel\x18\x04 \x01(\x0b\x32\x1d.media.UpdateHotelMappingData\x12.\n\x08mapRooms\x18\x05 \x01(\x0b\x32\x1c.media.UpdateRoomMappingData\x12\x30\n\nunMapRooms\x18\x06 \x01(\x0b\x32\x1c.media.UpdateRoomMappingData\x12\x30\n\tmapSpaces\x18\x07 \x01(\x0b\x32\x1d.media.UpdateSpaceMappingData\x12\x32\n\x0bunMapSpaces\x18\x08 \x01(\x0b\x32\x1d.media.UpdateSpaceMappingData\"l\n\x12\x44\x65leteMediaRequest\x12\x0f\n\x07videoId\x18\x01 \x01(\x03\x12\x0f\n\x07imageId\x18\x02 \x01(\x03\x12\x11\n\thotelCode\x18\x03 \x01(\t\x12\x0f\n\x07roomIds\x18\x04 \x03(\x03\x12\x10\n\x08spaceIds\x18\x05 \x03(\x03\"\x8e\x01\n\x12UpdateMediaRequest\x12\x11\n\thotelCode\x18\x01 \x01(\t\x12\x0f\n\x07hotelId\x18\x02 \x01(\x03\x12\x0f\n\x07imageId\x18\x03 \x01(\x03\x12\x0f\n\x07videoId\x18\x04 \x01(\x03\x12\x0c\n\x04tags\x18\x05 \x03(\t\x12\x13\n\x0b\x64\x65scription\x18\x06 \x01(\t\x12\x0f\n\x07\x63\x61ption\x18\x07 \x01(\t\"Y\n\x12UploadVideoRequest\x12\x1e\n\x04info\x18\x01 \x01(\x0b\x32\x10.media.VideoInfo\x12\x11\n\tchunkData\x18\x02 \x01(\x0c\x12\x10\n\x08videoUrl\x18\x03 \x01(\t\"\xeb\x01\n\tVideoInfo\x12\x11\n\thotelCode\x18\x01 \x01(\t\x12\x0f\n\x07hotelId\x18\x02 \x01(\x03\x12\x10\n\x08spaceIds\x18\x03 \x03(\x03\x12\x0f\n\x07roomIds\x18\x04 \x03(\x03\x12\x18\n\x10videoContentType\x18\x05 \x01(\t\x12\x10\n\x08\x66ileSize\x18\x06 \x01(\x03\x12\x10\n\x08\x66ileName\x18\x07 \x01(\t\x12\x10\n\x08sequence\x18\x08 \x01(\x03\x12\x13\n\x0bvideoSource\x18\t \x01(\t\x12\x0f\n\x07\x63\x61ption\x18\n \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x0b \x01(\t\x12\x0c\n\x04tags\x18\x0c \x03(\t\"Y\n\x12UploadImageRequest\x12\x1e\n\x04info\x18\x01 \x01(\x0b\x32\x10.media.ImageInfo\x12\x11\n\tchunkData\x18\x02 \x01(\x0c\x12\x10\n\x08imageUrl\x18\x03 \x01(\t\"\xd5\x02\n\tImageInfo\x12\x11\n\thotelCode\x18\x01 \x01(\t\x12\x0f\n\x07hotelId\x18\x02 \x01(\x03\x12\x10\n\x08spaceIds\x18\x03 \x03(\x03\x12\x0f\n\x07roomIds\x18\x04 \x03(\x03\x12\x10\n\x08imageUrl\x18\x05 \x01(\t\x12\x18\n\x10imageContentType\x18\x06 \x01(\t\x12\x10\n\x08\x66ileSize\x18\x07 \x01(\x03\x12\x10\n\x08\x66ileName\x18\x08 \x01(\t\x12\x10\n\x08sequence\x18\t \x01(\x03\x12\x0f\n\x07\x63\x61ption\x18\x0b \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x0c \x01(\t\x12\x0c\n\x04tags\x18\r \x03(\t\x12\x18\n\x10sourceConfigName\x18\x0e \x01(\t\x12\x15\n\rvendorImageId\x18\x0f \x01(\x03\x12\x0e\n\x06imgSrc\x18\x10 \x01(\t\x12\x14\n\x0cisOnBoarding\x18\x11 \x01(\t\x12\x14\n\x0csourceHeader\x18\x12 \x01(\t\"\xba\x05\n\tVideoData\x12\n\n\x02id\x18\x01 \x01(\x03\x12\r\n\x05video\x18\x02 \x01(\t\x12\x11\n\tthumbNail\x18\x03 \x01(\t\x12\x0c\n\x04user\x18\x04 \x01(\x03\x12\x15\n\rvendorVideoId\x18\x05 \x01(\x03\x12\x10\n\x08sequence\x18\x06 \x01(\x05\x12\x11\n\thotelCode\x18\x07 \x01(\t\x12\x13\n\x0bvideoCdnUrl\x18\x08 \x01(\t\x12\x17\n\x0fthumbnailCdnUrl\x18\t \x01(\t\x12\x14\n\x0crejectReason\x18\n \x03(\t\x12\x14\n\x0cselectedTags\x18\x0b \x03(\t\x12\x1c\n\x06mlTags\x18\x0c \x03(\x0b\x32\x0c.media.Mltag\x12\x11\n\thotelName\x18\r \x01(\t\x12\r\n\x05width\x18\x0e \x01(\x05\x12\x0e\n\x06height\x18\x0f \x01(\x05\x12\x10\n\x08\x64uration\x18\x10 \x01(\x02\x12\x0f\n\x07\x63\x61ption\x18\x11 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x12 \x01(\t\x12\x31\n\x0bvideoStatus\x18\x13 \x01(\x0e\x32\x1c.media.VideoData.VideoStatus\x12;\n\x10moderationStatus\x18\x14 \x01(\x0e\x32!.media.VideoData.ModerationStatus\x12\x16\n\x0esupplierSource\x18\x15 \x01(\t\x12\x1e\n\x05rooms\x18\x16 \x03(\x0b\x32\x0f.media.RoomInfo\x12 \n\x06spaces\x18\x17 \x03(\x0b\x32\x10.media.SpaceInfo\x12\x0e\n\x06\x66ormat\x18\x18 \x01(\t\"(\n\x0bVideoStatus\x12\n\n\x06\x41\x43TIVE\x10\x00\x12\r\n\tIN_ACTIVE\x10\x01\"O\n\x10ModerationStatus\x12\x0c\n\x08\x41PPROVED\x10\x00\x12\x0c\n\x08REJECTED\x10\x01\x12\x0b\n\x07PENDING\x10\x02\x12\x12\n\x0e\x41UTO_MODERATED\x10\x03\x32\x90\x05\n\x0cMediaService\x12J\n\x14UpdateImagesSequence\x12\x1b.media.ImageSequenceRequest\x1a\x15.media.ImagesResponse\x12\x45\n\x0e\x46ilteredImages\x12\x1c.media.FilteredImagesRequest\x1a\x15.media.ImagesResponse\x12N\n\x10\x42ulkMediaMapping\x12$.media.BulkMediaMappingUpdateRequest\x1a\x14.media.MediaResponse\x12>\n\x0bUpdateMedia\x12\x19.media.UpdateMediaRequest\x1a\x14.media.MediaResponse\x12\x41\n\nFetchVideo\x12\x1c.media.FilteredVideosRequest\x1a\x15.media.VideosResponse\x12\x41\n\x0bUploadImage\x12\x19.media.UploadImageRequest\x1a\x15.media.ImagesResponse(\x01\x12\x41\n\x0bUploadVideo\x12\x19.media.UploadVideoRequest\x1a\x15.media.VideosResponse(\x01\x12>\n\x07GetTags\x12\x18.media.TagsDetailRequest\x1a\x19.media.TagsDetailResponse\x12T\n\x13GetSpaceRoomDetails\x12\x1d.media.SpaceRoomDetailRequest\x1a\x1e.media.SpaceRoomDetailResponseB!Z\x1fpkg/generated_proto_stubs/mediab\x06proto3')
  ,
  dependencies=[google_dot_protobuf_dot_wrappers__pb2.DESCRIPTOR,])



_IMAGEDATA_IMAGESTATUS = _descriptor.EnumDescriptor(
  name='ImageStatus',
  full_name='media.ImageData.ImageStatus',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ACTIVE', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='IN_ACTIVE', index=1, number=1,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=2277,
  serialized_end=2317,
)
_sym_db.RegisterEnumDescriptor(_IMAGEDATA_IMAGESTATUS)

_IMAGEDATA_MODERATIONSTATUS = _descriptor.EnumDescriptor(
  name='ModerationStatus',
  full_name='media.ImageData.ModerationStatus',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='APPROVED', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='REJECTED', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PENDING', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='AUTO_MODERATED', index=3, number=3,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=2319,
  serialized_end=2398,
)
_sym_db.RegisterEnumDescriptor(_IMAGEDATA_MODERATIONSTATUS)

_IMAGEDATA_ORIENTATION = _descriptor.EnumDescriptor(
  name='Orientation',
  full_name='media.ImageData.Orientation',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='LANDSCAPE', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='SQUARE', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PORTRAIT', index=3, number=3,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=2400,
  serialized_end=2464,
)
_sym_db.RegisterEnumDescriptor(_IMAGEDATA_ORIENTATION)

_VIDEODATA_VIDEOSTATUS = _descriptor.EnumDescriptor(
  name='VideoStatus',
  full_name='media.VideoData.VideoStatus',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ACTIVE', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='IN_ACTIVE', index=1, number=1,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=4891,
  serialized_end=4931,
)
_sym_db.RegisterEnumDescriptor(_VIDEODATA_VIDEOSTATUS)

_VIDEODATA_MODERATIONSTATUS = _descriptor.EnumDescriptor(
  name='ModerationStatus',
  full_name='media.VideoData.ModerationStatus',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='APPROVED', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='REJECTED', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='PENDING', index=2, number=2,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='AUTO_MODERATED', index=3, number=3,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=2319,
  serialized_end=2398,
)
_sym_db.RegisterEnumDescriptor(_VIDEODATA_MODERATIONSTATUS)


_ERRORDETAIL = _descriptor.Descriptor(
  name='ErrorDetail',
  full_name='media.ErrorDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='errorCode', full_name='media.ErrorDetail.errorCode', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='errorMessage', full_name='media.ErrorDetail.errorMessage', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='errorType', full_name='media.ErrorDetail.errorType', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='displayMessage', full_name='media.ErrorDetail.displayMessage', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=63,
  serialized_end=160,
)


_MEDIARESPONSEDATA = _descriptor.Descriptor(
  name='MediaResponseData',
  full_name='media.MediaResponseData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='imageData', full_name='media.MediaResponseData.imageData', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='videoData', full_name='media.MediaResponseData.videoData', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='message', full_name='media.MediaResponseData.message', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='errorDetail', full_name='media.MediaResponseData.errorDetail', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=163,
  serialized_end=314,
)


_MEDIARESPONSE = _descriptor.Descriptor(
  name='MediaResponse',
  full_name='media.MediaResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='message', full_name='media.MediaResponse.message', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='images', full_name='media.MediaResponse.images', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='videos', full_name='media.MediaResponse.videos', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='errorDetail', full_name='media.MediaResponse.errorDetail', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=317,
  serialized_end=458,
)


_VIDEOSRESPONSE = _descriptor.Descriptor(
  name='VideosResponse',
  full_name='media.VideosResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='message', full_name='media.VideosResponse.message', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='data', full_name='media.VideosResponse.data', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='errorDetail', full_name='media.VideosResponse.errorDetail', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=460,
  serialized_end=566,
)


_TAGSDETAILREQUEST = _descriptor.Descriptor(
  name='TagsDetailRequest',
  full_name='media.TagsDetailRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='hotelId', full_name='media.TagsDetailRequest.hotelId', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hotelCode', full_name='media.TagsDetailRequest.hotelCode', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tags', full_name='media.TagsDetailRequest.tags', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=568,
  serialized_end=666,
)


_TAGSDETAILRESPONSE = _descriptor.Descriptor(
  name='TagsDetailResponse',
  full_name='media.TagsDetailResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='message', full_name='media.TagsDetailResponse.message', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='imagesTags', full_name='media.TagsDetailResponse.imagesTags', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='videoTags', full_name='media.TagsDetailResponse.videoTags', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='errorDetail', full_name='media.TagsDetailResponse.errorDetail', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=668,
  serialized_end=785,
)


_SPACEROOMDETAILREQUEST = _descriptor.Descriptor(
  name='SpaceRoomDetailRequest',
  full_name='media.SpaceRoomDetailRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='hotelId', full_name='media.SpaceRoomDetailRequest.hotelId', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='roomId', full_name='media.SpaceRoomDetailRequest.roomId', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spaceId', full_name='media.SpaceRoomDetailRequest.spaceId', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hotelCode', full_name='media.SpaceRoomDetailRequest.hotelCode', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=788,
  serialized_end=968,
)


_SPACEROOMDETAILRESPONSE = _descriptor.Descriptor(
  name='SpaceRoomDetailResponse',
  full_name='media.SpaceRoomDetailResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='message', full_name='media.SpaceRoomDetailResponse.message', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rooms', full_name='media.SpaceRoomDetailResponse.rooms', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spaces', full_name='media.SpaceRoomDetailResponse.spaces', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='errorDetail', full_name='media.SpaceRoomDetailResponse.errorDetail', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=971,
  serialized_end=1124,
)


_IMAGESEQUENCEREQUEST = _descriptor.Descriptor(
  name='ImageSequenceRequest',
  full_name='media.ImageSequenceRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='hotelId', full_name='media.ImageSequenceRequest.hotelId', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='roomId', full_name='media.ImageSequenceRequest.roomId', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spaceId', full_name='media.ImageSequenceRequest.spaceId', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hotelCode', full_name='media.ImageSequenceRequest.hotelCode', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='data', full_name='media.ImageSequenceRequest.data', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1127,
  serialized_end=1345,
)


_NEWIMAGESSEQUENCE = _descriptor.Descriptor(
  name='NewImagesSequence',
  full_name='media.NewImagesSequence',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='imageId', full_name='media.NewImagesSequence.imageId', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sequence', full_name='media.NewImagesSequence.sequence', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1347,
  serialized_end=1401,
)


_FILTEREDIMAGESREQUEST = _descriptor.Descriptor(
  name='FilteredImagesRequest',
  full_name='media.FilteredImagesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='hotelCode', full_name='media.FilteredImagesRequest.hotelCode', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='media.FilteredImagesRequest.filters', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1403,
  serialized_end=1477,
)


_FILTEREDVIDEOSREQUEST = _descriptor.Descriptor(
  name='FilteredVideosRequest',
  full_name='media.FilteredVideosRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='hotelCode', full_name='media.FilteredVideosRequest.hotelCode', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filters', full_name='media.FilteredVideosRequest.filters', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1479,
  serialized_end=1553,
)


_FILTER = _descriptor.Descriptor(
  name='Filter',
  full_name='media.Filter',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='filterName', full_name='media.Filter.filterName', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='filterValue', full_name='media.Filter.filterValue', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1555,
  serialized_end=1604,
)


_IMAGESRESPONSE = _descriptor.Descriptor(
  name='ImagesResponse',
  full_name='media.ImagesResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='message', full_name='media.ImagesResponse.message', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='data', full_name='media.ImagesResponse.data', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='errorDetail', full_name='media.ImagesResponse.errorDetail', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1606,
  serialized_end=1712,
)


_IMAGEDATA = _descriptor.Descriptor(
  name='ImageData',
  full_name='media.ImageData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='media.ImageData.id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='image', full_name='media.ImageData.image', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user', full_name='media.ImageData.user', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vendorImgId', full_name='media.ImageData.vendorImgId', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sequence', full_name='media.ImageData.sequence', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hotelCode', full_name='media.ImageData.hotelCode', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cdnUrl', full_name='media.ImageData.cdnUrl', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rejectReason', full_name='media.ImageData.rejectReason', index=7,
      number=8, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='selectedTags', full_name='media.ImageData.selectedTags', index=8,
      number=9, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mlTags', full_name='media.ImageData.mlTags', index=9,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hotelName', full_name='media.ImageData.hotelName', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='width', full_name='media.ImageData.width', index=11,
      number=12, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='height', full_name='media.ImageData.height', index=12,
      number=13, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='caption', full_name='media.ImageData.caption', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='media.ImageData.description', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='imageStatus', full_name='media.ImageData.imageStatus', index=15,
      number=16, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='moderationStatus', full_name='media.ImageData.moderationStatus', index=16,
      number=17, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orientation', full_name='media.ImageData.orientation', index=17,
      number=18, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplierSource', full_name='media.ImageData.supplierSource', index=18,
      number=19, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rooms', full_name='media.ImageData.rooms', index=19,
      number=20, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spaces', full_name='media.ImageData.spaces', index=20,
      number=21, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='createdOn', full_name='media.ImageData.createdOn', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _IMAGEDATA_IMAGESTATUS,
    _IMAGEDATA_MODERATIONSTATUS,
    _IMAGEDATA_ORIENTATION,
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1715,
  serialized_end=2464,
)


_MLTAG = _descriptor.Descriptor(
  name='Mltag',
  full_name='media.Mltag',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2466,
  serialized_end=2473,
)


_ROOMINFO = _descriptor.Descriptor(
  name='RoomInfo',
  full_name='media.RoomInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomId', full_name='media.RoomInfo.roomId', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='roomName', full_name='media.RoomInfo.roomName', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sequence', full_name='media.RoomInfo.sequence', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2475,
  serialized_end=2537,
)


_ROOMDETAIL = _descriptor.Descriptor(
  name='RoomDetail',
  full_name='media.RoomDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='roomId', full_name='media.RoomDetail.roomId', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='roomName', full_name='media.RoomDetail.roomName', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='roomType', full_name='media.RoomDetail.roomType', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2539,
  serialized_end=2603,
)


_SPACEINFO = _descriptor.Descriptor(
  name='SpaceInfo',
  full_name='media.SpaceInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='spaceId', full_name='media.SpaceInfo.spaceId', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spaceName', full_name='media.SpaceInfo.spaceName', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sequence', full_name='media.SpaceInfo.sequence', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2605,
  serialized_end=2670,
)


_SPACEDETAIL = _descriptor.Descriptor(
  name='SpaceDetail',
  full_name='media.SpaceDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='spaceId', full_name='media.SpaceDetail.spaceId', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spaceName', full_name='media.SpaceDetail.spaceName', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spaceType', full_name='media.SpaceDetail.spaceType', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2672,
  serialized_end=2740,
)


_UPDATEHOTELMAPPINGDATA = _descriptor.Descriptor(
  name='UpdateHotelMappingData',
  full_name='media.UpdateHotelMappingData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='videoId', full_name='media.UpdateHotelMappingData.videoId', index=0,
      number=1, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='imageId', full_name='media.UpdateHotelMappingData.imageId', index=1,
      number=2, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hotelId', full_name='media.UpdateHotelMappingData.hotelId', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2742,
  serialized_end=2817,
)


_UPDATESPACEMAPPINGDATA = _descriptor.Descriptor(
  name='UpdateSpaceMappingData',
  full_name='media.UpdateSpaceMappingData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='videoId', full_name='media.UpdateSpaceMappingData.videoId', index=0,
      number=1, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='imageId', full_name='media.UpdateSpaceMappingData.imageId', index=1,
      number=2, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spaceIds', full_name='media.UpdateSpaceMappingData.spaceIds', index=2,
      number=3, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2819,
  serialized_end=2895,
)


_UPDATEROOMMAPPINGDATA = _descriptor.Descriptor(
  name='UpdateRoomMappingData',
  full_name='media.UpdateRoomMappingData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='videoId', full_name='media.UpdateRoomMappingData.videoId', index=0,
      number=1, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='imageId', full_name='media.UpdateRoomMappingData.imageId', index=1,
      number=2, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='roomIds', full_name='media.UpdateRoomMappingData.roomIds', index=2,
      number=3, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2897,
  serialized_end=2971,
)


_BULKMEDIAMAPPINGUPDATEREQUEST = _descriptor.Descriptor(
  name='BulkMediaMappingUpdateRequest',
  full_name='media.BulkMediaMappingUpdateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='hotelCode', full_name='media.BulkMediaMappingUpdateRequest.hotelCode', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hotelId', full_name='media.BulkMediaMappingUpdateRequest.hotelId', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unMapHotel', full_name='media.BulkMediaMappingUpdateRequest.unMapHotel', index=2,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mapRooms', full_name='media.BulkMediaMappingUpdateRequest.mapRooms', index=3,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unMapRooms', full_name='media.BulkMediaMappingUpdateRequest.unMapRooms', index=4,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mapSpaces', full_name='media.BulkMediaMappingUpdateRequest.mapSpaces', index=5,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='unMapSpaces', full_name='media.BulkMediaMappingUpdateRequest.unMapSpaces', index=6,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2974,
  serialized_end=3292,
)


_DELETEMEDIAREQUEST = _descriptor.Descriptor(
  name='DeleteMediaRequest',
  full_name='media.DeleteMediaRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='videoId', full_name='media.DeleteMediaRequest.videoId', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='imageId', full_name='media.DeleteMediaRequest.imageId', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hotelCode', full_name='media.DeleteMediaRequest.hotelCode', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='roomIds', full_name='media.DeleteMediaRequest.roomIds', index=3,
      number=4, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spaceIds', full_name='media.DeleteMediaRequest.spaceIds', index=4,
      number=5, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3294,
  serialized_end=3402,
)


_UPDATEMEDIAREQUEST = _descriptor.Descriptor(
  name='UpdateMediaRequest',
  full_name='media.UpdateMediaRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='hotelCode', full_name='media.UpdateMediaRequest.hotelCode', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hotelId', full_name='media.UpdateMediaRequest.hotelId', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='imageId', full_name='media.UpdateMediaRequest.imageId', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='videoId', full_name='media.UpdateMediaRequest.videoId', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tags', full_name='media.UpdateMediaRequest.tags', index=4,
      number=5, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='media.UpdateMediaRequest.description', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='caption', full_name='media.UpdateMediaRequest.caption', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3405,
  serialized_end=3547,
)


_UPLOADVIDEOREQUEST = _descriptor.Descriptor(
  name='UploadVideoRequest',
  full_name='media.UploadVideoRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='info', full_name='media.UploadVideoRequest.info', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='chunkData', full_name='media.UploadVideoRequest.chunkData', index=1,
      number=2, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='videoUrl', full_name='media.UploadVideoRequest.videoUrl', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3549,
  serialized_end=3638,
)


_VIDEOINFO = _descriptor.Descriptor(
  name='VideoInfo',
  full_name='media.VideoInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='hotelCode', full_name='media.VideoInfo.hotelCode', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hotelId', full_name='media.VideoInfo.hotelId', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spaceIds', full_name='media.VideoInfo.spaceIds', index=2,
      number=3, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='roomIds', full_name='media.VideoInfo.roomIds', index=3,
      number=4, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='videoContentType', full_name='media.VideoInfo.videoContentType', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fileSize', full_name='media.VideoInfo.fileSize', index=5,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fileName', full_name='media.VideoInfo.fileName', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sequence', full_name='media.VideoInfo.sequence', index=7,
      number=8, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='videoSource', full_name='media.VideoInfo.videoSource', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='caption', full_name='media.VideoInfo.caption', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='media.VideoInfo.description', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tags', full_name='media.VideoInfo.tags', index=11,
      number=12, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3641,
  serialized_end=3876,
)


_UPLOADIMAGEREQUEST = _descriptor.Descriptor(
  name='UploadImageRequest',
  full_name='media.UploadImageRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='info', full_name='media.UploadImageRequest.info', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='chunkData', full_name='media.UploadImageRequest.chunkData', index=1,
      number=2, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=_b(""),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='imageUrl', full_name='media.UploadImageRequest.imageUrl', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3878,
  serialized_end=3967,
)


_IMAGEINFO = _descriptor.Descriptor(
  name='ImageInfo',
  full_name='media.ImageInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='hotelCode', full_name='media.ImageInfo.hotelCode', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hotelId', full_name='media.ImageInfo.hotelId', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spaceIds', full_name='media.ImageInfo.spaceIds', index=2,
      number=3, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='roomIds', full_name='media.ImageInfo.roomIds', index=3,
      number=4, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='imageUrl', full_name='media.ImageInfo.imageUrl', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='imageContentType', full_name='media.ImageInfo.imageContentType', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fileSize', full_name='media.ImageInfo.fileSize', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='fileName', full_name='media.ImageInfo.fileName', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sequence', full_name='media.ImageInfo.sequence', index=8,
      number=9, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='caption', full_name='media.ImageInfo.caption', index=9,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='media.ImageInfo.description', index=10,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='tags', full_name='media.ImageInfo.tags', index=11,
      number=13, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sourceConfigName', full_name='media.ImageInfo.sourceConfigName', index=12,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vendorImageId', full_name='media.ImageInfo.vendorImageId', index=13,
      number=15, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='imgSrc', full_name='media.ImageInfo.imgSrc', index=14,
      number=16, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='isOnBoarding', full_name='media.ImageInfo.isOnBoarding', index=15,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sourceHeader', full_name='media.ImageInfo.sourceHeader', index=16,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3970,
  serialized_end=4311,
)


_VIDEODATA = _descriptor.Descriptor(
  name='VideoData',
  full_name='media.VideoData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='media.VideoData.id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='video', full_name='media.VideoData.video', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='thumbNail', full_name='media.VideoData.thumbNail', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user', full_name='media.VideoData.user', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='vendorVideoId', full_name='media.VideoData.vendorVideoId', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='sequence', full_name='media.VideoData.sequence', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hotelCode', full_name='media.VideoData.hotelCode', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='videoCdnUrl', full_name='media.VideoData.videoCdnUrl', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='thumbnailCdnUrl', full_name='media.VideoData.thumbnailCdnUrl', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rejectReason', full_name='media.VideoData.rejectReason', index=9,
      number=10, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='selectedTags', full_name='media.VideoData.selectedTags', index=10,
      number=11, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='mlTags', full_name='media.VideoData.mlTags', index=11,
      number=12, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hotelName', full_name='media.VideoData.hotelName', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='width', full_name='media.VideoData.width', index=13,
      number=14, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='height', full_name='media.VideoData.height', index=14,
      number=15, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='duration', full_name='media.VideoData.duration', index=15,
      number=16, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='caption', full_name='media.VideoData.caption', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='description', full_name='media.VideoData.description', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='videoStatus', full_name='media.VideoData.videoStatus', index=18,
      number=19, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='moderationStatus', full_name='media.VideoData.moderationStatus', index=19,
      number=20, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='supplierSource', full_name='media.VideoData.supplierSource', index=20,
      number=21, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rooms', full_name='media.VideoData.rooms', index=21,
      number=22, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='spaces', full_name='media.VideoData.spaces', index=22,
      number=23, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='format', full_name='media.VideoData.format', index=23,
      number=24, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
    _VIDEODATA_VIDEOSTATUS,
    _VIDEODATA_MODERATIONSTATUS,
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4314,
  serialized_end=5012,
)

_MEDIARESPONSEDATA.fields_by_name['imageData'].message_type = _IMAGEDATA
_MEDIARESPONSEDATA.fields_by_name['videoData'].message_type = _VIDEODATA
_MEDIARESPONSEDATA.fields_by_name['errorDetail'].message_type = _ERRORDETAIL
_MEDIARESPONSE.fields_by_name['images'].message_type = _IMAGEDATA
_MEDIARESPONSE.fields_by_name['videos'].message_type = _VIDEODATA
_MEDIARESPONSE.fields_by_name['errorDetail'].message_type = _ERRORDETAIL
_VIDEOSRESPONSE.fields_by_name['data'].message_type = _VIDEODATA
_VIDEOSRESPONSE.fields_by_name['errorDetail'].message_type = _ERRORDETAIL
_TAGSDETAILREQUEST.fields_by_name['hotelId'].message_type = google_dot_protobuf_dot_wrappers__pb2._INT64VALUE
_TAGSDETAILRESPONSE.fields_by_name['errorDetail'].message_type = _ERRORDETAIL
_SPACEROOMDETAILREQUEST.fields_by_name['hotelId'].message_type = google_dot_protobuf_dot_wrappers__pb2._INT64VALUE
_SPACEROOMDETAILREQUEST.fields_by_name['roomId'].message_type = google_dot_protobuf_dot_wrappers__pb2._INT64VALUE
_SPACEROOMDETAILREQUEST.fields_by_name['spaceId'].message_type = google_dot_protobuf_dot_wrappers__pb2._INT64VALUE
_SPACEROOMDETAILRESPONSE.fields_by_name['rooms'].message_type = _ROOMDETAIL
_SPACEROOMDETAILRESPONSE.fields_by_name['spaces'].message_type = _SPACEDETAIL
_SPACEROOMDETAILRESPONSE.fields_by_name['errorDetail'].message_type = _ERRORDETAIL
_IMAGESEQUENCEREQUEST.fields_by_name['hotelId'].message_type = google_dot_protobuf_dot_wrappers__pb2._INT64VALUE
_IMAGESEQUENCEREQUEST.fields_by_name['roomId'].message_type = google_dot_protobuf_dot_wrappers__pb2._INT64VALUE
_IMAGESEQUENCEREQUEST.fields_by_name['spaceId'].message_type = google_dot_protobuf_dot_wrappers__pb2._INT64VALUE
_IMAGESEQUENCEREQUEST.fields_by_name['data'].message_type = _NEWIMAGESSEQUENCE
_FILTEREDIMAGESREQUEST.fields_by_name['filters'].message_type = _FILTER
_FILTEREDVIDEOSREQUEST.fields_by_name['filters'].message_type = _FILTER
_IMAGESRESPONSE.fields_by_name['data'].message_type = _IMAGEDATA
_IMAGESRESPONSE.fields_by_name['errorDetail'].message_type = _ERRORDETAIL
_IMAGEDATA.fields_by_name['mlTags'].message_type = _MLTAG
_IMAGEDATA.fields_by_name['imageStatus'].enum_type = _IMAGEDATA_IMAGESTATUS
_IMAGEDATA.fields_by_name['moderationStatus'].enum_type = _IMAGEDATA_MODERATIONSTATUS
_IMAGEDATA.fields_by_name['orientation'].enum_type = _IMAGEDATA_ORIENTATION
_IMAGEDATA.fields_by_name['rooms'].message_type = _ROOMINFO
_IMAGEDATA.fields_by_name['spaces'].message_type = _SPACEINFO
_IMAGEDATA_IMAGESTATUS.containing_type = _IMAGEDATA
_IMAGEDATA_MODERATIONSTATUS.containing_type = _IMAGEDATA
_IMAGEDATA_ORIENTATION.containing_type = _IMAGEDATA
_BULKMEDIAMAPPINGUPDATEREQUEST.fields_by_name['unMapHotel'].message_type = _UPDATEHOTELMAPPINGDATA
_BULKMEDIAMAPPINGUPDATEREQUEST.fields_by_name['mapRooms'].message_type = _UPDATEROOMMAPPINGDATA
_BULKMEDIAMAPPINGUPDATEREQUEST.fields_by_name['unMapRooms'].message_type = _UPDATEROOMMAPPINGDATA
_BULKMEDIAMAPPINGUPDATEREQUEST.fields_by_name['mapSpaces'].message_type = _UPDATESPACEMAPPINGDATA
_BULKMEDIAMAPPINGUPDATEREQUEST.fields_by_name['unMapSpaces'].message_type = _UPDATESPACEMAPPINGDATA
_UPLOADVIDEOREQUEST.fields_by_name['info'].message_type = _VIDEOINFO
_UPLOADIMAGEREQUEST.fields_by_name['info'].message_type = _IMAGEINFO
_VIDEODATA.fields_by_name['mlTags'].message_type = _MLTAG
_VIDEODATA.fields_by_name['videoStatus'].enum_type = _VIDEODATA_VIDEOSTATUS
_VIDEODATA.fields_by_name['moderationStatus'].enum_type = _VIDEODATA_MODERATIONSTATUS
_VIDEODATA.fields_by_name['rooms'].message_type = _ROOMINFO
_VIDEODATA.fields_by_name['spaces'].message_type = _SPACEINFO
_VIDEODATA_VIDEOSTATUS.containing_type = _VIDEODATA
_VIDEODATA_MODERATIONSTATUS.containing_type = _VIDEODATA
DESCRIPTOR.message_types_by_name['ErrorDetail'] = _ERRORDETAIL
DESCRIPTOR.message_types_by_name['MediaResponseData'] = _MEDIARESPONSEDATA
DESCRIPTOR.message_types_by_name['MediaResponse'] = _MEDIARESPONSE
DESCRIPTOR.message_types_by_name['VideosResponse'] = _VIDEOSRESPONSE
DESCRIPTOR.message_types_by_name['TagsDetailRequest'] = _TAGSDETAILREQUEST
DESCRIPTOR.message_types_by_name['TagsDetailResponse'] = _TAGSDETAILRESPONSE
DESCRIPTOR.message_types_by_name['SpaceRoomDetailRequest'] = _SPACEROOMDETAILREQUEST
DESCRIPTOR.message_types_by_name['SpaceRoomDetailResponse'] = _SPACEROOMDETAILRESPONSE
DESCRIPTOR.message_types_by_name['ImageSequenceRequest'] = _IMAGESEQUENCEREQUEST
DESCRIPTOR.message_types_by_name['NewImagesSequence'] = _NEWIMAGESSEQUENCE
DESCRIPTOR.message_types_by_name['FilteredImagesRequest'] = _FILTEREDIMAGESREQUEST
DESCRIPTOR.message_types_by_name['FilteredVideosRequest'] = _FILTEREDVIDEOSREQUEST
DESCRIPTOR.message_types_by_name['Filter'] = _FILTER
DESCRIPTOR.message_types_by_name['ImagesResponse'] = _IMAGESRESPONSE
DESCRIPTOR.message_types_by_name['ImageData'] = _IMAGEDATA
DESCRIPTOR.message_types_by_name['Mltag'] = _MLTAG
DESCRIPTOR.message_types_by_name['RoomInfo'] = _ROOMINFO
DESCRIPTOR.message_types_by_name['RoomDetail'] = _ROOMDETAIL
DESCRIPTOR.message_types_by_name['SpaceInfo'] = _SPACEINFO
DESCRIPTOR.message_types_by_name['SpaceDetail'] = _SPACEDETAIL
DESCRIPTOR.message_types_by_name['UpdateHotelMappingData'] = _UPDATEHOTELMAPPINGDATA
DESCRIPTOR.message_types_by_name['UpdateSpaceMappingData'] = _UPDATESPACEMAPPINGDATA
DESCRIPTOR.message_types_by_name['UpdateRoomMappingData'] = _UPDATEROOMMAPPINGDATA
DESCRIPTOR.message_types_by_name['BulkMediaMappingUpdateRequest'] = _BULKMEDIAMAPPINGUPDATEREQUEST
DESCRIPTOR.message_types_by_name['DeleteMediaRequest'] = _DELETEMEDIAREQUEST
DESCRIPTOR.message_types_by_name['UpdateMediaRequest'] = _UPDATEMEDIAREQUEST
DESCRIPTOR.message_types_by_name['UploadVideoRequest'] = _UPLOADVIDEOREQUEST
DESCRIPTOR.message_types_by_name['VideoInfo'] = _VIDEOINFO
DESCRIPTOR.message_types_by_name['UploadImageRequest'] = _UPLOADIMAGEREQUEST
DESCRIPTOR.message_types_by_name['ImageInfo'] = _IMAGEINFO
DESCRIPTOR.message_types_by_name['VideoData'] = _VIDEODATA
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ErrorDetail = _reflection.GeneratedProtocolMessageType('ErrorDetail', (_message.Message,), dict(
  DESCRIPTOR = _ERRORDETAIL,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.ErrorDetail)
  ))
_sym_db.RegisterMessage(ErrorDetail)

MediaResponseData = _reflection.GeneratedProtocolMessageType('MediaResponseData', (_message.Message,), dict(
  DESCRIPTOR = _MEDIARESPONSEDATA,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.MediaResponseData)
  ))
_sym_db.RegisterMessage(MediaResponseData)

MediaResponse = _reflection.GeneratedProtocolMessageType('MediaResponse', (_message.Message,), dict(
  DESCRIPTOR = _MEDIARESPONSE,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.MediaResponse)
  ))
_sym_db.RegisterMessage(MediaResponse)

VideosResponse = _reflection.GeneratedProtocolMessageType('VideosResponse', (_message.Message,), dict(
  DESCRIPTOR = _VIDEOSRESPONSE,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.VideosResponse)
  ))
_sym_db.RegisterMessage(VideosResponse)

TagsDetailRequest = _reflection.GeneratedProtocolMessageType('TagsDetailRequest', (_message.Message,), dict(
  DESCRIPTOR = _TAGSDETAILREQUEST,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.TagsDetailRequest)
  ))
_sym_db.RegisterMessage(TagsDetailRequest)

TagsDetailResponse = _reflection.GeneratedProtocolMessageType('TagsDetailResponse', (_message.Message,), dict(
  DESCRIPTOR = _TAGSDETAILRESPONSE,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.TagsDetailResponse)
  ))
_sym_db.RegisterMessage(TagsDetailResponse)

SpaceRoomDetailRequest = _reflection.GeneratedProtocolMessageType('SpaceRoomDetailRequest', (_message.Message,), dict(
  DESCRIPTOR = _SPACEROOMDETAILREQUEST,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.SpaceRoomDetailRequest)
  ))
_sym_db.RegisterMessage(SpaceRoomDetailRequest)

SpaceRoomDetailResponse = _reflection.GeneratedProtocolMessageType('SpaceRoomDetailResponse', (_message.Message,), dict(
  DESCRIPTOR = _SPACEROOMDETAILRESPONSE,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.SpaceRoomDetailResponse)
  ))
_sym_db.RegisterMessage(SpaceRoomDetailResponse)

ImageSequenceRequest = _reflection.GeneratedProtocolMessageType('ImageSequenceRequest', (_message.Message,), dict(
  DESCRIPTOR = _IMAGESEQUENCEREQUEST,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.ImageSequenceRequest)
  ))
_sym_db.RegisterMessage(ImageSequenceRequest)

NewImagesSequence = _reflection.GeneratedProtocolMessageType('NewImagesSequence', (_message.Message,), dict(
  DESCRIPTOR = _NEWIMAGESSEQUENCE,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.NewImagesSequence)
  ))
_sym_db.RegisterMessage(NewImagesSequence)

FilteredImagesRequest = _reflection.GeneratedProtocolMessageType('FilteredImagesRequest', (_message.Message,), dict(
  DESCRIPTOR = _FILTEREDIMAGESREQUEST,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.FilteredImagesRequest)
  ))
_sym_db.RegisterMessage(FilteredImagesRequest)

FilteredVideosRequest = _reflection.GeneratedProtocolMessageType('FilteredVideosRequest', (_message.Message,), dict(
  DESCRIPTOR = _FILTEREDVIDEOSREQUEST,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.FilteredVideosRequest)
  ))
_sym_db.RegisterMessage(FilteredVideosRequest)

Filter = _reflection.GeneratedProtocolMessageType('Filter', (_message.Message,), dict(
  DESCRIPTOR = _FILTER,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.Filter)
  ))
_sym_db.RegisterMessage(Filter)

ImagesResponse = _reflection.GeneratedProtocolMessageType('ImagesResponse', (_message.Message,), dict(
  DESCRIPTOR = _IMAGESRESPONSE,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.ImagesResponse)
  ))
_sym_db.RegisterMessage(ImagesResponse)

ImageData = _reflection.GeneratedProtocolMessageType('ImageData', (_message.Message,), dict(
  DESCRIPTOR = _IMAGEDATA,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.ImageData)
  ))
_sym_db.RegisterMessage(ImageData)

Mltag = _reflection.GeneratedProtocolMessageType('Mltag', (_message.Message,), dict(
  DESCRIPTOR = _MLTAG,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.Mltag)
  ))
_sym_db.RegisterMessage(Mltag)

RoomInfo = _reflection.GeneratedProtocolMessageType('RoomInfo', (_message.Message,), dict(
  DESCRIPTOR = _ROOMINFO,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.RoomInfo)
  ))
_sym_db.RegisterMessage(RoomInfo)

RoomDetail = _reflection.GeneratedProtocolMessageType('RoomDetail', (_message.Message,), dict(
  DESCRIPTOR = _ROOMDETAIL,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.RoomDetail)
  ))
_sym_db.RegisterMessage(RoomDetail)

SpaceInfo = _reflection.GeneratedProtocolMessageType('SpaceInfo', (_message.Message,), dict(
  DESCRIPTOR = _SPACEINFO,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.SpaceInfo)
  ))
_sym_db.RegisterMessage(SpaceInfo)

SpaceDetail = _reflection.GeneratedProtocolMessageType('SpaceDetail', (_message.Message,), dict(
  DESCRIPTOR = _SPACEDETAIL,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.SpaceDetail)
  ))
_sym_db.RegisterMessage(SpaceDetail)

UpdateHotelMappingData = _reflection.GeneratedProtocolMessageType('UpdateHotelMappingData', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEHOTELMAPPINGDATA,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.UpdateHotelMappingData)
  ))
_sym_db.RegisterMessage(UpdateHotelMappingData)

UpdateSpaceMappingData = _reflection.GeneratedProtocolMessageType('UpdateSpaceMappingData', (_message.Message,), dict(
  DESCRIPTOR = _UPDATESPACEMAPPINGDATA,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.UpdateSpaceMappingData)
  ))
_sym_db.RegisterMessage(UpdateSpaceMappingData)

UpdateRoomMappingData = _reflection.GeneratedProtocolMessageType('UpdateRoomMappingData', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEROOMMAPPINGDATA,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.UpdateRoomMappingData)
  ))
_sym_db.RegisterMessage(UpdateRoomMappingData)

BulkMediaMappingUpdateRequest = _reflection.GeneratedProtocolMessageType('BulkMediaMappingUpdateRequest', (_message.Message,), dict(
  DESCRIPTOR = _BULKMEDIAMAPPINGUPDATEREQUEST,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.BulkMediaMappingUpdateRequest)
  ))
_sym_db.RegisterMessage(BulkMediaMappingUpdateRequest)

DeleteMediaRequest = _reflection.GeneratedProtocolMessageType('DeleteMediaRequest', (_message.Message,), dict(
  DESCRIPTOR = _DELETEMEDIAREQUEST,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.DeleteMediaRequest)
  ))
_sym_db.RegisterMessage(DeleteMediaRequest)

UpdateMediaRequest = _reflection.GeneratedProtocolMessageType('UpdateMediaRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEMEDIAREQUEST,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.UpdateMediaRequest)
  ))
_sym_db.RegisterMessage(UpdateMediaRequest)

UploadVideoRequest = _reflection.GeneratedProtocolMessageType('UploadVideoRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPLOADVIDEOREQUEST,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.UploadVideoRequest)
  ))
_sym_db.RegisterMessage(UploadVideoRequest)

VideoInfo = _reflection.GeneratedProtocolMessageType('VideoInfo', (_message.Message,), dict(
  DESCRIPTOR = _VIDEOINFO,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.VideoInfo)
  ))
_sym_db.RegisterMessage(VideoInfo)

UploadImageRequest = _reflection.GeneratedProtocolMessageType('UploadImageRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPLOADIMAGEREQUEST,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.UploadImageRequest)
  ))
_sym_db.RegisterMessage(UploadImageRequest)

ImageInfo = _reflection.GeneratedProtocolMessageType('ImageInfo', (_message.Message,), dict(
  DESCRIPTOR = _IMAGEINFO,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.ImageInfo)
  ))
_sym_db.RegisterMessage(ImageInfo)

VideoData = _reflection.GeneratedProtocolMessageType('VideoData', (_message.Message,), dict(
  DESCRIPTOR = _VIDEODATA,
  __module__ = 'picassoservice_pb2'
  # @@protoc_insertion_point(class_scope:media.VideoData)
  ))
_sym_db.RegisterMessage(VideoData)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('Z\037pkg/generated_proto_stubs/media'))

_MEDIASERVICE = _descriptor.ServiceDescriptor(
  name='MediaService',
  full_name='media.MediaService',
  file=DESCRIPTOR,
  index=0,
  options=None,
  serialized_start=5015,
  serialized_end=5671,
  methods=[
  _descriptor.MethodDescriptor(
    name='UpdateImagesSequence',
    full_name='media.MediaService.UpdateImagesSequence',
    index=0,
    containing_service=None,
    input_type=_IMAGESEQUENCEREQUEST,
    output_type=_IMAGESRESPONSE,
    options=None,
  ),
  _descriptor.MethodDescriptor(
    name='FilteredImages',
    full_name='media.MediaService.FilteredImages',
    index=1,
    containing_service=None,
    input_type=_FILTEREDIMAGESREQUEST,
    output_type=_IMAGESRESPONSE,
    options=None,
  ),
  _descriptor.MethodDescriptor(
    name='BulkMediaMapping',
    full_name='media.MediaService.BulkMediaMapping',
    index=2,
    containing_service=None,
    input_type=_BULKMEDIAMAPPINGUPDATEREQUEST,
    output_type=_MEDIARESPONSE,
    options=None,
  ),
  _descriptor.MethodDescriptor(
    name='UpdateMedia',
    full_name='media.MediaService.UpdateMedia',
    index=3,
    containing_service=None,
    input_type=_UPDATEMEDIAREQUEST,
    output_type=_MEDIARESPONSE,
    options=None,
  ),
  _descriptor.MethodDescriptor(
    name='FetchVideo',
    full_name='media.MediaService.FetchVideo',
    index=4,
    containing_service=None,
    input_type=_FILTEREDVIDEOSREQUEST,
    output_type=_VIDEOSRESPONSE,
    options=None,
  ),
  _descriptor.MethodDescriptor(
    name='UploadImage',
    full_name='media.MediaService.UploadImage',
    index=5,
    containing_service=None,
    input_type=_UPLOADIMAGEREQUEST,
    output_type=_IMAGESRESPONSE,
    options=None,
  ),
  _descriptor.MethodDescriptor(
    name='UploadVideo',
    full_name='media.MediaService.UploadVideo',
    index=6,
    containing_service=None,
    input_type=_UPLOADVIDEOREQUEST,
    output_type=_VIDEOSRESPONSE,
    options=None,
  ),
  _descriptor.MethodDescriptor(
    name='GetTags',
    full_name='media.MediaService.GetTags',
    index=7,
    containing_service=None,
    input_type=_TAGSDETAILREQUEST,
    output_type=_TAGSDETAILRESPONSE,
    options=None,
  ),
  _descriptor.MethodDescriptor(
    name='GetSpaceRoomDetails',
    full_name='media.MediaService.GetSpaceRoomDetails',
    index=8,
    containing_service=None,
    input_type=_SPACEROOMDETAILREQUEST,
    output_type=_SPACEROOMDETAILRESPONSE,
    options=None,
  ),
])
_sym_db.RegisterServiceDescriptor(_MEDIASERVICE)

DESCRIPTOR.services_by_name['MediaService'] = _MEDIASERVICE

# @@protoc_insertion_point(module_scope)