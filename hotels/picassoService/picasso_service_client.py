import time
import traceback
import grpc
from goibibo_inventory.settings.base import PICASSO_HOST, PICASSO_PORT
from proto_utils.dict_to_proto3_to_dict import protobuf_to_dict

from hotels.methods import get_property_type_meta
from hotels.picassoService.proto.picasso_mediaPush_pb2_grpc import MediaPushServiceStub
from hotels.picassoService.proto.picassoservice_pb2 import FilteredImagesRequest
from hotels.picassoService.proto.picassoservice_pb2 import FilteredVideosRequest
from hotels.picassoService.proto.picasso_mediaPush_pb2 import PushForAutoModerationRequest, EntityImages, \
    PushMediaToGMPRequest
from hotels.picassoService.proto.picassoservice_pb2_grpc import MediaServiceStub
from utils.logger import Logger
from google.protobuf.json_format import json
from google.protobuf.json_format import Parse
from django.conf import settings
from django.core.exceptions import ValidationError

api_logger = Logger(logger='inventoryAPILogger')
inventory_logger = Logger(logger='inventoryLogger')

certificate_file = settings.PROJECT_PATH + '/goibibo_inventory/settings/san-aws-ecs-mmt.crt'

class PicassoServiceClient(object):
    """
        Client for accessing Picasso gRPC Services
    """

    def __init__(self):
        # configure the host and the port
        self.host = PICASSO_HOST
        self.server_port = PICASSO_PORT

        # reading certificate file
        with open(certificate_file, 'rb') as f:
            trusted_certs = f.read()

        # create credentials
        credentials = grpc.ssl_channel_credentials(root_certificates=trusted_certs)

        # instantiate a secure communication channel
        self.channel = grpc.secure_channel(
            '{}:{}'.format(self.host, self.server_port), credentials=credentials,
            options=(('grpc.enable_http_proxy', 0),
                     ('grpc.client_idle_timeout_ms', 5000)))

        self.media_service_stub = MediaServiceStub(self.channel)
        self.media_push_stub = MediaPushServiceStub(self.channel)
        inventory_logger.info(
            message="PicassoServiceClient initialized successfully",
            log_type='ingoibibo', bucket='picassoService', stage='initialization'
        )

    def fetch_filtered_images(self, formatted_request):
        response = {}
        max_retry_count = 2
        is_success = False

        try:
            retry_count = 0
            json_request = json.dumps(formatted_request)
            while not is_success and retry_count <= max_retry_count:
                try:
                    response = {'message': '', 'error': ''}
                    request_to_filtered_image_proto = Parse(json_request, FilteredImagesRequest())

                    resp_proto = self.media_service_stub.FilteredImages(
                        request_to_filtered_image_proto)
                    response = protobuf_to_dict(resp_proto)
                    is_success = True
                    inventory_logger.info(
                        message='filtered image request: grpc request %s '%(str(formatted_request)),
                        log_type='ingoibibo', bucket='picassoService', stage='filtered_images')
                except grpc.RpcError as e:
                    response['message'] = str(e)
                    inventory_logger.critical(message='filtered image request: %s error %s %s error_count:%s' % (
                        str(formatted_request), str(self.host), str(e), str(retry_count + 1)),
                                                log_type='ingoibibo', bucket='picassoService',
                                                stage='filtered_images')
                    time.sleep(1)

                    retry_count += 1

        except Exception as ex:
            inventory_logger.critical(message='filtered image request: %s error %s Traceback: %s' % (
                str(formatted_request), str(ex), repr(traceback.format_exc())),
                                      log_type='ingoibibo', bucket='picassoService',
                                      stage='filtered_images')

        return response

    def fetch_filtered_videos(self, formatted_request):
        response = {}
        max_retry_count = 2
        is_success = False

        try:
            retry_count = 0
            json_request = json.dumps(formatted_request)
            while not is_success and retry_count <= max_retry_count:
                try:
                    response = {'message': '', 'error': ''}
                    request_to_filtered_video_proto = Parse(json_request, FilteredVideosRequest())

                    resp_proto = self.media_service_stub.FetchVideo(
                        request_to_filtered_video_proto)
                    response = protobuf_to_dict(resp_proto)
                    is_success = True
                    inventory_logger.info(
                        message='filtered video request: grpc request %s ' % (str(formatted_request)),
                        log_type='ingoibibo', bucket='picassoService', stage='fetch_video')
                except grpc.RpcError as e:
                    response['message'] = str(e)
                    inventory_logger.critical(message='filtered video request: %s error %s %s error_count:%s' % (
                        str(formatted_request), str(self.host), str(e), str(retry_count + 1)),
                                              log_type='ingoibibo', bucket='picassoService',
                                              stage='fetch_video')
                    time.sleep(1)

                    retry_count += 1

        except Exception as ex:
            inventory_logger.critical(message='filtered video request: %s error %s Traceback: %s' % (
                str(formatted_request), str(ex), repr(traceback.format_exc())),
                                      log_type='ingoibibo', bucket='picassoService',
                                      stage='fetch_video')

        return response

    def push_images_to_picasso_for_auto_moderation(self, moderation_data):
        log_data = {
            'api_specific_identifiers': {
                'hotel_ids': list(moderation_data.keys()),
                'image_ids': [image_id for image_ids in moderation_data.values() for image_id in image_ids],
                'message': 'Request data being sent to Picasso'
            },
            'error': {},
            'request_id': ''
        }
        try:
            if 'error' in moderation_data and moderation_data['error']:
                return {'message': 'No data to push', 'error': 'error'}

            entity_images_list = []
            HOTEL_ENTITY_TYPE = 'hotel'
            for hotel_id, image_ids in moderation_data.iteritems():
                entity_images = EntityImages(
                    objectId=hotel_id,
                    entityType=HOTEL_ENTITY_TYPE,  # Assuming the entity type is 'hotel'
                    imageIds=image_ids
                )
                entity_images_list.append(entity_images)
                inventory_logger.info(
                    message='entity images: %s' % (str(entity_images)),
                    log_type='ingoibibo', bucket='picassoService', stage='initialization', identifier="%s" % log_data
                )

            request = PushForAutoModerationRequest(
                entityImages=entity_images_list
            )

            response = self.media_push_stub.PushForAutoModeration(request)

            inventory_logger.info(
                message='pushed images to picasso for moderation: %s' % (str(response)),
                log_type='ingoibibo', bucket='picassoService', stage='initialization', identifier="%s" % log_data
            )

            return protobuf_to_dict(response)

        except grpc.RpcError as e:
            inventory_logger.critical(message='push_image_to_auto_moderation: %s error %s' % (
                str(self.host), str(e)),
                                      log_type='ingoibibo', bucket='picassoService',
                                      stage='push_image_to_auto_moderation', identifier="%s" % log_data)
            return {'message': str(e), 'error': 'error'}
        except Exception as ex:
            inventory_logger.critical(message='push_image_to_auto_moderation: %s error %s Traceback: %s' % (
                str(self.host), str(ex), repr(traceback.format_exc())),
                                      log_type='ingoibibo', bucket='picassoService',
                                      stage='push_image_to_auto_moderation', identifier="%s" % log_data)
            return {'message': str(ex), 'error': 'error'}

    def push_videos_to_picasso_for_gmp(self, request_data):
        VIDEO_ENTITY_TYPE = "VIDEO"
        response_data = {}

        for hotel_id, video_ids in request_data.items():
            try:
                request = PushMediaToGMPRequest(
                    entityId=hotel_id,
                    pushImage=False,
                    pushVideo=True,
                    entityType=VIDEO_ENTITY_TYPE,
                    propertyName=""
                )

                inventory_logger.info(
                    message='PushMediaToGMPRequest: %s' % (str(request)),
                    log_type='ingoibibo', bucket='picassoService', stage='push_videos_to_picasso_for_gmp'
                )

                response = self.media_push_stub.PushMediaToGMP(request)

                inventory_logger.info(
                    message='PushMediaToGMPResponse: %s' % (str(response)),
                    log_type='ingoibibo', bucket='picassoService', stage='push_videos_to_picasso_for_gmp'
                )

                response_data[hotel_id] = protobuf_to_dict(response)

            except grpc.RpcError as e:
                inventory_logger.critical(
                    message='PushMediaToGMPRequest failed for hotel_id: %s, error: %s' % (hotel_id, str(e)),
                    log_type='ingoibibo', bucket='picassoService', stage='push_videos_to_picasso_for_gmp'
                )
                response_data[hotel_id] = {'error': str(e)}

            except Exception as ex:
                inventory_logger.critical(
                    message='Unexpected error for hotel_id: %s, error: %s, Traceback: %s' % (
                        hotel_id, str(ex), repr(traceback.format_exc())
                    ),
                    log_type='ingoibibo', bucket='picassoService', stage='push_videos_to_picasso_for_gmp'
                )
                response_data[hotel_id] = {'error': str(ex)}

        return response_data
