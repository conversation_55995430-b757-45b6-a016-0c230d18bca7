from django.conf import settings

from utils.logger import Logger

from common.threads import ContentPipelineEventHandler
from hotels.DuplicateShell.decorators import skip_post_save_signal
from goibibo_inventory.settings import app

inventory_logger = Logger(logger="inventoryLogger")

CONTENT_PIPELINE_PRIORITY_QUEUE = "content_pipeline_priority_queue"
REQUEST_DATA_INDEX = 4
CUSTOM_ARGS_INDEX = 7

class ContentPipelineDict(dict):
    def __getattr__(self, key):
        return self[key]

@skip_post_save_signal
def push_static_content(sender, **kwargs):
    base_obj = kwargs.get('instance')
    request_data = kwargs.get('request_data', None)
    is_created = kwargs.get('created', False)
    caller = "post_save_connector.push_static_content"
    hotel_codes = base_obj.get_hotel_ids_by_object()

    from hotels.models import HotelDetail
    hotels = HotelDetail.objects.filter(hotelcode__in=hotel_codes)

    for hotel_obj in hotels:
        inventory_logger.info(message='Pushing hotel to kafka as hotel is saved . %s' % (str(hotel_obj.hotelcode)),
                              log_type='ingoibibo', bucket='ClientAPI',
                              stage='post_save_connector.push_static_content')

        trigger_content_pipeline_push(hotel_obj.id, hotel_obj.hotelcode, base_obj.__class__.__name__, base_obj.id,
                              is_created, request_data, "ingo", caller)


def trigger_content_pipeline_push(hotel_id, hotel_code, model_name, model_id, is_created, request_data, source, caller):
    if not settings.NEW_STATIC_CONTENT_PIPELINE_FLAG:
        return

    operation_name = "create" if is_created else "update"
    event_data = {'model_name': model_name, 'operation_name': operation_name}

    from common.threads import ContentPipelineEventHandler
    ptcp_args = ContentPipelineEventHandler().get_args()
    if ptcp_args:
        event_data['custom_args'] = ptcp_args

    content_pipeline_args = (hotel_id, hotel_code, model_name, model_id, request_data, source, caller, event_data)
    log_data = {'api_specific_identifiers': {}, 'error': {}, 'request_id': ''}
    log_data["api_specific_identifiers"]["hotel_code"] = hotel_code
    log_data["api_specific_identifiers"]["event_data"] = event_data
    log_data["api_specific_identifiers"]["content_pipeline_args"] = content_pipeline_args
    log_data["api_specific_identifiers"]["message"] = "Event data for the hotel code - postsaveconnector"
    inventory_logger.info(message = 'Event data for the hotel code {}: {}'.format(hotel_code, event_data),
                          log_type = 'ingoibibo', bucket='ClientAPI',
                          stage='post_save_connector.trigger_content_pipeline_push',
                          identifier = '{}'.format(log_data))
    if settings.DEBUG:
        requests = [ContentPipelineDict({'args': content_pipeline_args})]

        from hotels.tasks import push_static_hotel_data
        push_static_hotel_data(requests)
    else:
        schedule_content_pipeline_push(hotel_code, content_pipeline_args)


def schedule_content_pipeline_push(hotel_code, content_pipeline_args, is_scheduler=False):
    from hotels.helper import get_or_set_redis_key_for_content_pipeline, add_hotel_to_set
    from api.v2.users.hosts.helper import get_hotel_id_from_code
    from hotels.models import HotelDetail
    from hotels.hotelchoice import SELF_SIGNUP_REFERENCE_MAP, CONTENT_PIPELINE_SIGNUP_REFERENCE_PRIORITY_SET
    hotel_id = get_hotel_id_from_code(hotel_code)
    content_pipeline_configs = settings.FEATURE_FLAGS.get('content_pipeline_handling', {})
    enable_priority_queue_flow = content_pipeline_configs.get('enable_priority_queue_flow', False)
    try:
        # Use "default" database to avoid master-slave replication lag when fetching recently saved hotel
        hotel_obj = HotelDetail.objects.using("default").get(id=hotel_id)
        is_test_property = hotel_obj.is_test_hotel
    except Exception as e:
        log_data = {'api_specific_identifiers': {}, 'error': {}, 'request_id': ''}
        log_data["api_specific_identifiers"]["message"] = "Error fetching hotel object for particular hotel_id"
        log_data["api_specific_identifiers"]["hotel_id"] = hotel_id
        log_data["api_specific_identifiers"]["error"] = str(e)
        inventory_logger.error(
            message='Error fetching hotel object for hotel_id: {}, error: {}'.format(hotel_id, str(e)),
            log_type='ingoibibo',
            bucket='ClientAPI',
            stage='post_save_connector.schedule_content_pipeline_push',
            identifier=str(log_data)
        )
        # Default to non-test property if hotel fetch fails
        is_test_property = False
    log_data = {'api_specific_identifiers': {}, 'error': {}, 'request_id': ''}
    if hotel_obj.datasource_id in CONTENT_PIPELINE_SIGNUP_REFERENCE_PRIORITY_SET and not is_scheduler and enable_priority_queue_flow:
        content_pipeline_args = alter_content_pipeline_args(content_pipeline_args)
        is_hotel_key_set_success = add_hotel_to_set(CONTENT_PIPELINE_PRIORITY_QUEUE, hotel_code, content_pipeline_args)
        log_data["api_specific_identifiers"]["message"] = "Adding Hotel To Priority Set"
        log_data["api_specific_identifiers"]["hotel_code"] = hotel_code
        log_data["api_specific_identifiers"]["is_hotel_key_set_success"] = is_hotel_key_set_success
        log_data["api_specific_identifiers"]["is_test_property"] = is_test_property
        log_data["api_specific_identifiers"]["data_source_value"] = SELF_SIGNUP_REFERENCE_MAP[hotel_obj.datasource_id]
        inventory_logger.info(
            message='Adding Hotel To Priority Set',
            log_type='ingoibibo', bucket='ClientAPI',
            stage='post_save_connector.schedule_content_pipeline_push',
            identifier='{}'.format(log_data))
    else:
        is_hotel_key_set_success = get_or_set_redis_key_for_content_pipeline(hotel_code, is_test_property)
        # ContentPipelineEventHandler().reset_args()
        log_data["api_specific_identifiers"]["hotel_code"] = hotel_code
        log_data["api_specific_identifiers"]["is_hotel_key_set_success"] = is_hotel_key_set_success
        log_data["api_specific_identifiers"]["is_test_property"] = is_test_property
        log_data["api_specific_identifiers"]["message"] = "Pushing static content to kafka"
        inventory_logger.info(
            message='Static Content: Pushing hotel: %s to kafka as hotel is not present in redis, is_hotel_key_set_success from redis: %s' % (
                str(hotel_code), str(is_hotel_key_set_success)),
            log_type='ingoibibo', bucket='ClientAPI',
            stage='post_save_connector.schedule_content_pipeline_push',
            identifier='{}'.format(log_data))
        if is_hotel_key_set_success:
            inventory_logger.info(
                message='Static Content: Pushing hotel: %s to kafka as hotel is not present in redis, is_hotel_key_set_success from redis: %s' % (
                    str(hotel_code), str(is_hotel_key_set_success)),
                log_type='ingoibibo', bucket='ClientAPI',
                stage='post_save_connector.schedule_content_pipeline_push')
            delay = 60 * settings.ETL_DELAY if is_test_property else settings.ETL_DELAY
            from hotels.tasks import push_static_hotel_data
            push_static_hotel_data.apply_async(
                args=content_pipeline_args,
                countdown=delay)
        else:
            inventory_logger.info(
                message='Static Content: Not Pushing hotel: %s to kafka as hotel is present in redis, is_hotel_key_set_success from redis: %s' % (
                    str(hotel_code), str(is_hotel_key_set_success)),
                log_type='ingoibibo', bucket='ClientAPI',
                stage='post_save_connector.schedule_content_pipeline_push')

def alter_content_pipeline_args(content_pipeline_args):
    content_pipeline_args = list(content_pipeline_args)
    if content_pipeline_args[CUSTOM_ARGS_INDEX].get('custom_args', None) is not None:
        content_pipeline_args[CUSTOM_ARGS_INDEX]['custom_args']['event_name'] = content_pipeline_args[CUSTOM_ARGS_INDEX]['custom_args']['event_name'].name
    if content_pipeline_args[REQUEST_DATA_INDEX] is not None:
        user_id = content_pipeline_args[REQUEST_DATA_INDEX].get('user', {}).get('user', {}).get('id', None)
        content_pipeline_args[REQUEST_DATA_INDEX] = {'user': {'id': user_id}}
    content_pipeline_args = tuple(content_pipeline_args)
    return content_pipeline_args

@app.task(name="ingoibibo.push_priority_hotels_to_content_pipeline")
def push_priority_hotels_to_content_pipeline():
    from hotels.helper import get_all_hotels_from_set, delete_hotel_from_set
    from common.threads import PTCPIngoEvents
    from django.db import close_old_connections
    hotels = get_all_hotels_from_set(CONTENT_PIPELINE_PRIORITY_QUEUE)
    log_data = {'api_specific_identifiers': {}, 'error': {}, 'request_id': ''}
    log_data["api_specific_identifiers"]["hotel_ids_count"] = len(hotels)
    log_data["api_specific_identifiers"]["message"] = "Pushing priority hotels to content pipeline"
    inventory_logger.info(message="Pushing priority hotels to content pipeline",
                                  log_type="ingoibibo",
                                  bucket="StaticHotelDataAPI",
                                  stage='post_save_connector.push_priority_hotels_to_content_pipeline',
                                  identifier='{}'.format(log_data))
    try:
        if hotels == {}:
            return
        for key, value in hotels.items():
            try:
                content_pipeline_args = eval(value)
                if content_pipeline_args[7].get('custom_args', None) is not None:
                    content_pipeline_args[7]['custom_args']['event_name'] = PTCPIngoEvents[content_pipeline_args[7]['custom_args']['event_name']]
            except Exception as e:
                log_data = {'api_specific_identifiers': {}, 'error': {}, 'request_id': ''}
                log_data["api_specific_identifiers"]["error"] = str(e)
                log_data["api_specific_identifiers"]["hotel_code"] = str(key)
                log_data["api_specific_identifiers"]["value"] = str(value)
                log_data["api_specific_identifiers"]["message"] = "Error while evaluating content pipeline args for a hotel"
                inventory_logger.critical(message="Error while evaluating content pipeline args for a hotel",
                                  log_type="ingoibibo",
                                  bucket="StaticHotelDataAPI",
                                  stage='post_save_connector.push_priority_hotels_to_content_pipeline',
                                  identifier='{}'.format(log_data))
                continue
            schedule_content_pipeline_push(str(key), content_pipeline_args, is_scheduler=True)
            delete_hotel_from_set(CONTENT_PIPELINE_PRIORITY_QUEUE, str(key))
    except Exception as e:
        close_old_connections()
        log_data = {'api_specific_identifiers': {}, 'error': {}, 'request_id': ''}
        log_data["api_specific_identifiers"]["error"] = str(e)
        log_data["api_specific_identifiers"]["message"] = "Error while pushing priority hotels to content pipeline"
        inventory_logger.critical(message="Error while pushing priority hotels to content pipeline",
                                  log_type="ingoibibo",
                                  bucket="StaticHotelDataAPI",
                                  stage='post_save_connector.push_priority_hotels_to_content_pipeline',
                                  identifier='{}'.format(log_data))
        push_priority_hotels_to_content_pipeline()