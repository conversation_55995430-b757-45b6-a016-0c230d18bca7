import operator
import random
import csv
import traceback
import uuid
from datetime import date, timedelta, datetime

import requests
import boto3
import ujson
from time import sleep, time
import os
import string
import copy

from celery.contrib.batches import Batches
from kombu.exceptions import ConnectionLimitExceeded
from django.core.exceptions import ObjectDoesNotExist
from django.contrib.auth.models import Group
from django.conf import settings
from django.core.exceptions import ValidationError
from django.db.models import Q, F
from django.contrib.admin.models import LogEntry, CHANGE, ADDITION
from django.utils.encoding import force_unicode
from django.template.loader import render_to_string
from django.forms import model_to_dict
from elasticsearch_dsl import Search, Q as ESQ

from api.v1.change_requests.resources.common_helper import validate_and_fetch_approver_obj_for_change_request, \
    fetch_requester_approver_ldap_details, create_change_request_data
from api.v1.change_requests.resources.push_change_request_data import push_change_request_packet_to_dynamic_hub
from api.v1.common_resources import validate_bool
from api.v1.group_bookings.resources.commission_helper import update_program_commission
from api.v1.inventory.resources import get_formatted_inventory_list
from api.v1.rooms.resources.room_resources import update_room_inventory
from api.v1.offers.resources.offer_resources import create_hotel_offer_util
from api.v1.event_detail.resources.resources import save_event_image_to_common_image, save_image_id_to_event_history
from api.v1.chat_box.resources.chat_meta import is_dch
from api.v1.secret_deal.resources.resources import send_secret_deal_notification
from api.v1.slot_booking.resources.resources import optimize_dayuse_inventory
from api.v2.cancellation_rules.resources.constants import priority_map, SPECIAL_POLICY_PRIORITY
from api.v2.cug.promotion_v2.create_cug_promotionv2 import create_cug_promotionv2,create_cug_enigma_call
from bulk_uploader.common_helpers import change_request_validator, change_request_commission_validator, upload_to_s3
from dynamic_hub_service.create_change_request_pb2 import GOSTAY_PROGRAM_CHANGE, ADVANTAGE_PROGRAM_CHANGE, \
    COMMISSION_BASED_OFFER_CHANGE, ListValue
from hotels.models.offline_bookings import PriceDerivative
from bulk_uploader.common_helpers import change_request_validator, change_request_commission_validator, upload_to_s3
from dynamic_hub_service.create_change_request_pb2 import GOSTAY_PROGRAM_CHANGE, ADVANTAGE_PROGRAM_CHANGE
from hotels.models.offline_bookings import PriceDerivative
from promotion_v2.get_offers import get_offersv2
from api.v1.offers.promotion_v2.create_coupon_promotionv2 import create_coupon_promotionv2
from api.v2.cug.resources.affiliate_cug_helper import AffiliateCUGHelper
from api.v2.cug.resources.cug_resource import populate_request_data
from api.v2.cug.resources.cug_constants import CUG_BLACK_LIST, CUG_SELECT_LIST, CUG_PROMOTIONV2_KEY_MAPPING, \
    CUG_SEGMENT_AF1, CUG_SEGMENT_CUG2, CUG_SEGMENT_CUG4,CREATE_MOBILE_ERROR_CODE , UPDATE_MOBILE_ERROR_CODE, \
    CREATE_MEMBER_ERROR_CODE ,UPDATE_MEMBER_ERROR_CODE,BLACK_SUBTEXT 
from api.v2.cug.views import validate_request, create_cug_util
from api.v2.hotelsShortLinks.content_service_client import ContentServiceClient
from api.v2.services.resources.resource import get_mandatory_template_ids, make_default_inclusion, \
    refresh_service_metadata
from api.v2.guest_chat.resources.resources import create_ingo_scheduler, close_conversation, \
    check_guest_chat_task_duplicacy
from common.constants import ProgramType, PID_TASK_IDLE_TIME_THRESHOLD, PLATFORM_FULLDAY, PLATFORM_HOTEL_TRAVEL, \
    PLATFORM_DAYUSE, MY_BIZ_CAMEL_CASE, ContentTypeIdForHotel
from common.models import Templates, GenericImageMapping
from api.v2.services.resources.resource import get_mandatory_template_ids, make_default_inclusion
from api.v2.amenities.resources.amenities_resources import deactivate_spa_inclusions_for_inactive_amenity
from hotels.bookingpayments import close_db_connection
from hotels.constants.constants import SHOW_IN_HOMESTAY_FUNNEL_TAG, MMT_SPECIAL, MOJO_PUSH_FLAGBITS, queues, \
    task_flush_count, task_flush_interval, NAVISION_PUSH_ASYNC_COUNTDOWN
from hotels.services.constant import LEAF_PRIORITY_MULTIPLIER
from hotels.services.helpers.priority_service import ServicePriorityService, ServicesManager
from hotels.services.hotel_service import HotelService
from hotels.hotelchoice import RTB_COMMUNICATION_EVENT, HOTEL_WAIVER_STATUS, VENDOR_COMPANY_NAMES, MMT_BLACK,CUG_SEGMENT_CUG5,CUG_SEGMENT_CUG6
from hotels.ical_services import sync_all_calendars, update_ingo_sync_details, create_communication_events
from hotels.models.booking_models import BookingFlagsInfo
from hotels.models.cancellation_rules import CancellationBlackOutDates, CancellationRules
from hotels.models.hoteldetail import SpaceDetail
from hotels.models.gst_detail import GSTNAssurance
from hotels.models.user_management import HostProfile
from inventory_service.grpc_inventory_client import InventoryClient
from hotels.models.hotel_flag_configuration import GST_WITH_PENALTY, GST_WITHOUT_PENALTY

from hotels.pushingtovoyager import push_city_bucket_info_to_voyager_kafka, update_ingo_from_voyager_kinesis_response
from hotels.rate_capping import get_rate_mean_db, convert_dict_to_array_tuple
from lib.MDBStorage import MDBStorage
from lib.AWSS3Upload import S3Upload, BucketMapper, DOCUMENT_OBJECT_TYPE
from common.task import crop_image_to_different_sizes
import newrelic.agent
import celery, json, socket, ast
from celery.signals import task_prerun, worker_process_init, worker_process_shutdown
import math
from pytz import timezone
from guardian.shortcuts import assign_perm, remove_perm, get_perms
from communication.hotelMailsSMS import sendSoldOutInventoryMail, send_promocode_task_notification, \
    send_delist_task_notification, action_mails, send_mail_task_notification, \
    send_vcc_plus_booking_voucher, send_secret_deal_expiry_mail
from communication.SMSTemplates import HotelSMSTemplates
from communication.common_comms.communications import sendMail, sendMessage
from common.models import Vendor, ItemNote, User
from django.db import connection, transaction
from hotels.models import (HotelCancellationRule, RatePlan, LinkedRateRule,
                           PerformanceLinkRecords, AdjustmentEntry, HotelOfferValue,
                           RoomDetail, HotelChain, HotelOfferCondition, SecretDealBounced,
                           HotelProBookingMetaInfo, helper, VendorRMMapping, VendorMapping,
                           configuration as HotelConf, HotelAgreementMapping, PerformanceLinkBonus,
                           HotelChatTemplate, HotelDetail, DefaultData, HotelMetaData, Services)
from hotels_defaultdata.hotels_defaultdata import push_rateplan_default_data_to_mongo_non_ingo_partner, \
    parse_rateplan_default_data_to_rateplan_obj
from common.models import Image, Templates
from common.commonhelper import push_message_to_woof, push_to_kafka, get_chunk_list, update_specific_identifier, \
    update_error_identifier
from PIL import Image as PILImage
from StringIO import StringIO
from io import BytesIO

from hotels.methods import HotelMethods, get_day_suffix
from hotels.models.usp_detail import USPTag, USPDetail
from hotels.goibibosync import goibibo_reconfirm_queue
from goibibo_inventory.settings import app, non_txn_celery_app
from celery.utils.log import get_task_logger
from goibibo_inventory.settings.base import CDN_DOMAIN, EVENT_IMAGE_MIN_HEIGHT, EVENT_IMAGE_MIN_WIDTH

from proto_utils.dict_to_proto3_to_dict import dict_to_protobuf

from lib.aes_encryption.helpers import decrypt_column_data
from lib.payment_cache import get_payment_cache_server

from lib.redis_cache_backend import RedisCache
from protos.compiled.price_cache_event_data_pb2 import PriceCacheEventData
from protos.compiled.price_cache_event_data_new_pb2 import PriceCacheEventDataNew
from protos.compiled.admin_logs_pb2 import AdminLogs

from reports.mailalerts import *
from hotels.hoteldataporting import port_bankdetails_to_ingo
from extranet.commonmethods import MakeMyTripSoaConnector
from extranet.extranetvariables import test_cm_ids
from django.core.cache import cache
from populate_mandatory_fees import pull_and_update_mandatory_fees
from scalyr_reporting.constants import SCALYR_REPORTS, BOOKING_REPORT_KEY
from scripts.hostapp_migration.migration_settings import allowed_hw_migration_hotel_types
from utils.logger import Logger, AESEncryptor
from hotels.models.add_ons import AddOn
from api.v1.add_ons.serializers.add_ons_serializers import AddOnSerializer
from django.db import close_old_connections
from db_cache.content_type_cache import get_content_id_for_model
from communication.common_comms.communications import update_whatsapp_optin_status
from hotels.constants import constants
from kafka.producer import KafkaProducer
from hotels.models.helper import get_unix_time
from rates_service.grpc_rates_client import RatesClient
from hotels.methods import create_date_list_from_start_end_date
from api.v1.bookings.proto.booking_kafka_message_pb2 import BookingKafkaMessage
from google.protobuf.json_format import *
from hotels.hotelchoice import VAT_REQUIRED_COUNTRIES_ISO2, VAT_BUS_POSTING_GROUP , MMT_SELECT, PROPERTY_STATES
from pricing_service.api_client import PricingAPIClient, NexusPartnerAPIClient
from api.v1.offers.resources.offer_resources import create_hotel_offer_util
from api.v1.offers.resources.coupon_resources import validate_coupon_request, populate_coupon_request_data
from hotels.hotelchoice import OfferSources
from hotels.ack_flow import AckFlow
from api.v2.common.resources.helper import rateplan_associated_with_non_ingo_partners
from ingo_partners.resources.configure import get_config_data
from hotel_store.debezium_common_methods import delete_soldout_cache_keys,delete_soldout_cache_keys_with_prefix
from api.v2.images.resources.resources import timer
from django.contrib.admin.models import LogEntry
from django.contrib.contenttypes.models import ContentType
from hotels.models.helper import get_id_from_code
from hotels.models.configuration import RatePlanCodePrefix, RatePlanCodeLength
from hotels.stateMachineAbTester import Trigger_state_machine_for_content_service


config_data = get_config_data(value_key=True)
logger = logging.getLogger("inventoryLogger")
api_logger = Logger(logger='inventoryAPILogger')
celery_logger = get_task_logger(__name__)
inventory_logger = Logger(logger='inventoryLogger')

hostname = socket.gethostname()
hm = HotelMethods()
rates_client = RatesClient()
affiliate_cug_helper = AffiliateCUGHelper()
CONFIRM_BOOKING_STATUS = ['confirmed', 'reconfirmed', 'amended', 'pending']
AUTO_BANK_APPROVAL_USER = "<EMAIL>"
hotelSMSObj = HotelSMSTemplates()
api_pricing_client = PricingAPIClient()
inv_client = InventoryClient()
soa_connector = MakeMyTripSoaConnector()

booking_content_type = ContentType.objects.get_for_model(HotelBooking)
cancellation_content_type = ContentType.objects.get_for_model(HotelCancellation)
payment_content_type = ContentType.objects.get_for_model(HotelOutwardPayment)
hotel_content_type = ContentType.objects.get_for_model(HotelDetail)
chain_content_type = ContentType.objects.get_for_model(HotelChain)
mdb_storage = MDBStorage()
redis_conn = None
db_encryptor = None
s3 = S3Upload()
grpc_rates_client = RatesClient()
grpc_content_service_client = ContentServiceClient()

class MockRequest:
    def __init__(self, user_obj, hotel_obj=None):
        self.user = user_obj
        self.data = {}
        self.META = {}
        self.FILES = {}
        self.hotel = hotel_obj


@task_prerun.connect()
def task_prerun(signal=None, sender=None, task_id=None, task=None, args=None, kwargs=None):
    try:
        close_db_connection()
        log_data = {'api_specific_identifiers': {}, 'error': {}, 'request_id': ''}
        log_data['api_specific_identifiers']['task_id'] = task_id
        log_data['api_specific_identifiers']['task_name'] = task.name
        log_data['api_specific_identifiers']['sender'] = sender
        log_data['api_specific_identifiers']['args'] = repr(args)
        inventory_logger.info(
            message="Going to trigger task name {0} :: task id {1} :: sender {2} args :: {3} kwargs :: {4}".format(
                task.name, task_id, sender, args, kwargs), bucket="hotels.tasks", stage="logger_task",
        identifier='{}'.format(log_data))
    except Exception as e:
        inventory_logger.critical(
            message="Task trigger failed :: task id {0} :: sender {1} args :: {2} kwargs :: {3}".format(task_id, sender, args, kwargs), bucket="hotels.tasks", stage="logger_task")
'''db_conn = None
@worker_process_init.connect(sender="<EMAIL>")
def init_worker(**kwargs):
    global db_conn
    inventory_logger.info('Initializing database connection for worker.')
    r_serv = RedisCache(server="redis-ingo-celery.mmt.mmt:6379",
                        params={'OPTIONS': {'DB': 4}})
    db_conn = r_serv._client
    # app.conf['my_object'] = MyObject()

@worker_process_shutdown.connect(sender="<EMAIL>")
def shutdown_worker(**kwargs):
    global db_conn
    if db_conn:
        inventory_logger.info('Closing database connectionn for worker.')
        db_conn.close()'''


'''class CeleryRedisConnection():
    #Issue with this approach is that all workers will create redis conection wheeas its required just for 1
    def __init__(self):
        inventory_logger.info("Creating new redis connection")
        self.r_serv = RedisCache(server=settings.CELERY_REDIS_CONNECTION['HOST'],
                                 params={'OPTIONS': settings.CELERY_REDIS_CONNECTION['OPTIONS']})
        self.r_cli = self.r_serv._client

celery_redis_conn = CeleryRedisConnection()'''


@app.task(name="ingoibibo.push_queued_tasks_count")
def queued_task():
    '''i = app.control.inspect()
    reserved_tasks = i.scheduled()
    for worker, tasks in reserved_tasks.items():
        inventory_logger.info(message="Worker : {}, queue_size : {}".format(worker, len(tasks)), bucket="hotels.tasks", stage="queued_task")'''
    queues_configured = settings.REDIS_QUEUES.get('queues', [])
    if not queues_configured:
        queues_configured = queues

    for q in queues_configured:
        try:
            with app.pool.acquire(block=False) as conn:
                tasks = conn.default_channel.client.llen(q)
                inventory_logger.info(
                    message="Queue : {}, queue_size : {}".format(q, tasks),
                    bucket="hotels.tasks", stage="queued_task")
        except ConnectionLimitExceeded as e:
            inventory_logger.critical(message="Connection pool limit exceeded : {}".format(repr(traceback.format_exc())),
                                      bucket="hotels.tasks", stage="queued_task")


'''

Will use this once we are getting logs for proper deletion

usage :  for key in batcher(app_client.scan_iter("*reply.celery.pidbox*"),500):
                app_client.delete(*keybatch)
def batcher(iterable, n):
    from itertools import izip_longest
    args = [iter(iterable)] * n
    return izip_longest(*args)

'''


@app.task(name="cleanup_pid_keys_tasks")
def cleanup_pidbox_keys():
    try:
        total_memory = 0
        with app.pool.acquire(block=False) as conn:
            app_client = conn.default_channel.client
        for key in app_client.scan_iter("*reply.celery.pidbox*", 1000):
            if key.decode().startswith('_kombu'):
                continue
            idletime = app_client.object("idletime", key)
            # Check keys which are idle since 30 mins and delete those
            if idletime > PID_TASK_IDLE_TIME_THRESHOLD:
                # Calculation of memory used by the key which we are about to delete
                memory = float(app_client.execute_command("MEMORY USAGE", key)) / 1024
                #memory = math.ceil(app_client.memory_usage(key) / 1024 / 1024)
                total_memory += memory
                app_client.delete(key)
                inventory_logger.info(message="Deleted pid key {} and freed memory {}".format(key, memory),
                                      bucket="hotels.tasks",
                                      stage="cleanup_pidbox_keys")
        inventory_logger.info(message="Total memory freed {} by deleting pid keys".format(total_memory),
                              bucket="hotels.tasks",
                              stage="cleanup_pidbox_keys")
    except Exception as e:
        inventory_logger.critical(message="Issue while cleaning pid tasks : {}".format(repr(traceback.format_exc())),
                                  bucket="hotels.tasks", stage="cleanup_pidbox_keys")


@app.task(name="ingoibibo.test_celery_task")
def test_task():
    inventory_logger.info(message="I m a test task.", bucket="hotels.tasks", stage="test_task")


ack_flow = AckFlow()


@app.task(name="ingoibibo.port_bankdetails_to_ingo_async")
def port_bankdetails_to_ingo_async(ingo_hotelcode, mmt_hotelcode):
    try:
        port_bankdetails_to_ingo(ingo_hotelcode, mmt_hotelcode)
        celery_logger.info('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'port_bankdetails_to_ingo_async', ingo_hotelcode, mmt_hotelcode, ''))
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'port_bankdetails_to_ingo_async', ingo_hotelcode, str(e), repr(traceback.format_exc())))


# TODO: uncomment this task after production celery task failure issue resolved.
# @app.task(name="ingoibibo.booking_inentory_update")
# def bookingInventoryUpdation(pbobj, inventoryAction='booking', uname='BOOKING'):
#     try:
#         from hotels.hotelbooking import booking_inventory_updation
#
#         booking_inventory_updation(pbobj, inventoryAction, uname, level='POST_BOOKING')
#         try:
#             proBookingId = pbobj.provisionbookingid
#         except:
#             # Here pbobj contains of alterbooking obj or booking obj
#             proBookingId = pbobj.probookingid
#         celery_logger.info('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'bookingInventoryUpdation', ' ',
#                                                        'Successful Provisional booking id', str(proBookingId) +
#                                                        'confirm booking id is ' + str(
#             pbobj.confirmbookingid)))
#     except Exception as e:
#         celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
#             'hotels', 'tasks', 'bookingInventoryUpdation', ' ', str(e), repr(traceback.format_exc())))
#     return pbobj


# @app.task(name="ingoibibo.send_mail_task")
# def send_mail_task(subject, mail_content, hostname, to_email):
#     try:
#         send_mail(subject, mail_content, 'alerts_ingoibibo@'+hostname, to_email, fail_silently=True)
#     except Exception as e:
#         celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
#             'hotels', 'tasks', 'send_mail_task', ' ', str(e), repr(traceback.format_exc())))


@app.task(name="ingoibibo.send_pay@hotel_payment_invoice")
def sendPayAtHotelPaymentInvoice():
    try:
        from hotels.bookingsoperations import payathotel_payment_invoice

        payathotel_payment_invoice()
        celery_logger.info(
            '%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'sendPayAtHotelPaymentInvoice', '', ' ', 'Task Completed'))
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'sendPayAtHotelPaymentInvoice', '', str(e), repr(traceback.format_exc())))


# TODO: uncomment this task after production celery task failure issue resolved.
# @app.task(name="ingoibibo.send_pay@hotel_reminder")
# def sendPayAtHotelReminder():
#     try:
#         from hotels.bookingsoperations import payathotel_reminders
#
#         payathotel_reminders()
#         celery_logger.info(
#             '%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'sendPayAtHotelReminder', '', ' ', 'task completed'))
#     except Exception as e:
#         celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
#             'hotels', 'tasks', 'sendPayAtHotelReminder', '', str(e), repr(traceback.format_exc())))


# TODO: uncomment this task after production celery task failure issue resolved.
# @app.task(name="ingoibibo.auto_expire_payathotel_booking")
# def autoExpirePayAtHotelBooking():
#     try:
#         from hotels.hotelcancellation import expire_pay_at_hotel_booking
#
#         expire_pay_at_hotel_booking()
#     except Exception as e:
#         celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
#             'hotels', 'tasks', 'autoExpirePayAtHotelBooking', '', str(e), repr(traceback.format_exc())))


@app.task(name="ingoibibo.send_hotel_booking_sms_task")
def send_hotel_booking_sms_task(bobj):
    try:
        # to get hotel timezone
        if settings.HOST in settings.PROD_HOSTS:
            if bobj.hotel.timezone:
                time_zone = timezone(bobj.hotel.timezone.zone)
            else:
                time_zone = timezone('Asia/Calcutta')
            current_time = datetime.datetime.now(time_zone)
            current_hour = current_time.time().hour
            if current_hour > 7 and current_hour < 21:
                from bookingsoperations import send_hotel_booking_sms
                send_hotel_booking_sms(bobj)
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'send_hotel_booking_sms_task', '', str(e), repr(traceback.format_exc())))


@app.task(name="ingoibibo.send_hotel_reconfirmation")
def sendHotelReconfirmation():
    try:
        from bookingsoperations import reconfirmation_mailers

        reconfirmation_mailers()
        celery_logger.info(
            '%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'sendHotelReconfirmation', '', ' ', 'task completed'))
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'sendHotelReconfirmation', '', str(e), repr(traceback.format_exc())))


@app.task(name="ingoibibo.send_hotel_reconfirmation_sub_task")
def send_hotel_reconfirmation_sub_task(booking_id, sms_for_bdo):
    try:
        from bookingsoperations import sending_reconfirm_booking_vouchers
        sending_reconfirm_booking_vouchers(booking_id, sms_for_bdo)
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'send_hotel_reconfirmation_sub_task', booking_id, str(e),
            repr(traceback.format_exc())))


@app.task(name="ingoibibo.copy_scape_content_check")
def CopyScapeContentCheck():
    from copyscape.checker import check_texts

    try:
        hList = HotelDetail.objects.filter(copyscapestatus='to_check')
        for hObj in hList:
            celery_logger.info('%s\t%s\t%s\t%s\t%s\t%s' % (
                'hotels', 'tasks', 'CopyScapeContentCheck', '', hObj.hotelname, repr(hObj.hotelcode)))
            if hObj.description:
                result = check_texts([hObj.description],
                                     {'force_api': False, 'result_type': 'processed', 'omit_text': True, 'limit': 5})
                if result:
                    result = result[0]
                    if result.get('success', False) and 'data' in result:
                        copyscapestatus = 'passed'
                        if len(result['data']):
                            if 'percentmatched' in result['data'][0] and int(result['data'][0]['percentmatched']) > 10:
                                copyscapestatus = 'failed'
                        HotelDetail.objects.filter(hotelcode=hObj.hotelcode).update(copyscapestatus=copyscapestatus)
                    else:
                        celery_logger.info(
                            '%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'CopyScapeContentCheck', '', 'else', result))
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'CopyScapeContentCheck', '', str(e), repr(traceback.format_exc())))


@app.task(name="ingoibibo.chain_hotel_push_on_voyager")
def chain_hotel_push_on_voyager(chain_id):
    try:
        from hotels.pushingtovoyager import pushHoteltoVoyagerbyObject
        htl_list = HotelDetail.objects.filter(chainname__id=chain_id)
        for htl_obj in htl_list:
            status, message = pushHoteltoVoyagerbyObject(htl_obj, caller="chain_hotel_push_on_voyager")
            celery_logger.info('%s\t%s\t%s\t%s\t%s\t%s' % (
                'hotels', 'tasks', 'chain_hotel_push_on_voyager', str(chain_id), 'else', message))
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'chain_hotel_push_on_voyager', str(chain_id), str(e), repr(traceback.format_exc())))


@app.task(name="ingoibibo.push_hotel_to_voyager")
def push_hotel_to_voyager(hotelObj, caller="post_save"):
    from hotels.pushingtovoyager import pushHoteltoVoyagerbyObject
    status, message = False, ''
    try:
        status, message = pushHoteltoVoyagerbyObject(hotelObj, caller=caller)
        celery_logger.info('%s\t%s\t%s\tCaller: %s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'push_hotel_to_voyager', caller, '', 'else', message))
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'push_hotel_to_voyager', '', str(e), repr(traceback.format_exc())))
    return status, message


@newrelic.agent.background_task()
@app.task(name="ingoibibo.push_static_hotel_data", base=Batches, flush_every=task_flush_count, flush_interval=task_flush_interval)
def push_static_hotel_data(requests):
    from hotels.push_hotel_static_data import push_static_data_to_kafka
    status, message = False, ''
    unique_hotels = set()
    try:
        for req in requests:
            inventory_logger.info(message="args for batching: {}".format(req.args), log_type='ingoibibo', bucket='hotels.tasks',
                               stage='push_static_hotel_data')
            if req.args[0] in unique_hotels:
                continue
            inventory_logger.info(message="pushing for hotel: {}".format(req.args[0]), log_type='ingoibibo', bucket='hotels.tasks',
                               stage='push_static_hotel_data')

            status, message = push_static_data_to_kafka(req.args[0], req.args[1], req.args[2], req.args[3], req.args[4],
                                                        req.args[5], req.args[7])
            unique_hotels.add(req.args[0])
            caller = req.args[6] if len(req.args)>6 else "post_save"
            celery_logger.info('%s\t%s\t%s\tCaller: %s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'push_static_data_to_kafka', caller, '', 'else', message))
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'push_static_data_to_kafka', '', str(e), repr(traceback.format_exc())))
    return status, message


@app.task(name="ingoibibo.push_service_for_scrutiny")
def push_service_for_scrutiny(service_id, hotel_code, changed_model, object_id, source='ingo', caller="post_save"):
    status, message = False, ''
    try:
        from hotels.push_moderation_data_to_kafka import push_service_data_to_kafka
        status, message = push_service_data_to_kafka(service_id, hotel_code, changed_model, object_id, source)
        celery_logger.info('%s\t%s\t%s\tCaller: %s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'push_service_for_scrutiny', caller, '', 'else', message))
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'push_service_for_scrutiny', '', str(e), repr(traceback.format_exc())))
    return status, message


@app.task(name="ingoibibo.pulltadata_from_voyager")
def pullTADataFromVoyagerTask():
    try:
        from hotels.pushingtovoyager import pullReviewsFromVoyger

        message = pullReviewsFromVoyger()
        celery_logger.info(
            '%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'pullTADataFromVoyagerTask', '', 'else', message))
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'pullTADataFromVoyagerTask', '', str(e),
                                                           repr(traceback.format_exc())))


@app.task(name="ingoibibo.whatsapp_images_handler")
def WhatsAppImageHandlerTask():
    try:
        from lib.getimage import fetch_watsapp_image

        message = fetch_watsapp_image()
        celery_logger.info(
            '%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'WhatsAppImageHandlerTask', '', 'else', message))
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'WhatsAppImageHandlerTask', '', str(e),
                                                           repr(traceback.format_exc())))


@app.task(name="ingoibibo.push_to_goibibo")
def pushToGoibiboTask(hotelObj):
    try:
        from hotels.pushingtovoyager import pushHoteltoGoibibo

        message = pushHoteltoGoibibo(hotelObj.id)
        celery_logger.info('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'pushToGoibiboTask', '', 'else', message))
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'pushToGoibiboTask', '', str(e), repr(traceback.format_exc())))


@app.task(name="ingoibibo.calculate_review_score")
def calculate_review_score():
    try:
        import requests
        from hotels.models import HotelReviews

        all_hotel_reviews_obj = HotelReviews.objects.all()
        hotels = HotelDetail.objects.filter(isactive=True, voyagerid__isnull=False).only(*['voyagerid', 'id'])
        reviews_dict = {}
        for hotel_reviews in all_hotel_reviews_obj:
            reviews_dict[hotel_reviews.hotel_id] = hotel_reviews
        for hotel in hotels:
            old_hotel_rating = 0
            old_hotel_review_counts = 0
            if hotel.id in reviews_dict:
                hotel_reviews = reviews_dict[hotel.id]
                old_hotel_rating = float(hotel_reviews.goibiborating)
                if hotel_reviews.goibiboreviewcounts:
                    old_hotel_review_counts = hotel_reviews.goibiboreviewcounts
            else:
                hotel_reviews = HotelReviews(hotel=hotel)
            try:
                hotelvoyager_id = hotel.voyagerid
                domain_name = settings.GOIBIBO_RATING_URL
                if hotelvoyager_id:
                    save = False
                    relative_url = 'hotels/staticdata/?vhid=%s' % hotelvoyager_id
                    url = '{0}/{1}'.format(domain_name, relative_url)
                    res = requests.get(url)
                    if res.status_code >= 200 and res.status_code <= 299:
                        hotel_static_data = json.loads(requests.get(url).content)
                        rating_data = hotel_static_data[0].get('gir_data', '')
                        if rating_data:
                            goibiborating = float("%.1f" % float(rating_data.get('hotel_rating', 0)))
                            goibiboreviewcount = int(rating_data.get('review_count', 0))

                            if goibiborating and not goibiborating == old_hotel_rating:
                                hotel_reviews.goibiborating = goibiborating
                                save = True

                            if goibiboreviewcount and not goibiboreviewcount == old_hotel_review_counts:
                                hotel_reviews.goibiboreviewcounts = goibiboreviewcount
                                save = True

                            if save:
                                hotel_reviews.save()
            except Exception as ex:
                celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' %
                                       ('hotels', 'tasks', 'calculate_review_score', '', str(ex),
                                        repr(traceback.format_exc())))
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' %
                               ('hotels', 'tasks', 'calculate_review_score', '', str(e),
                                repr(traceback.format_exc())))


@app.task(name="ingoibibo.user_activity")
def updateUserActivityTask():
    from reports.dailyreports import DailyReports

    dr = DailyReports()
    try:
        dr.calculateUserActivity()
        celery_logger.info(
            '%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'updateUserActivityTask', '', 'else', 'completed'))
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'updateUserActivityTask', '', str(e),
                                                           repr(traceback.format_exc())))


@app.task(name="ingoibibo.calculate_hotel_content_score")
def calculateHotelContentScoreTask(hotelIds=None):
    try:
        close_old_connections()
        if not hotelIds:
            hotelIds = HotelDetail.objects.filter(isactive=True).values_list('id', flat=True)
        n = settings.CONTENT_SCORE_TASK_SPLIT
        hotelIdsList = [hotelIds[i:i + n] for i in range(0, len(hotelIds), n)]
        for hotelIds in hotelIdsList:
            try:
                calculateHotelContentScoreSubTask.apply_async(args=(hotelIds,))
            except Exception as e:
                inventory_logger.critical(
                    message='Content score task failure for hotelIds in range {}:{} with exception {} {}'
                        .format(hotelIds[0], hotelIds[-1], e, traceback.format_exc()), log_type='ingoibibo',
                    bucket='hotels.tasks',
                    stage='calculateHotelContentScoreTask')
        celery_logger.info(
            '%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'calculateHotelContentScoreTask', '', 'else', 'completed'))
    except Exception as e:
        celery_logger.critical(
            '%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'calculateHotelContentScoreTask', '', str(e),
                                        repr(traceback.format_exc())))

        sendMail('', '', "{0}\n\n{1}".format(str(e), repr(traceback.format_exc())),
                 "Celery Job calculateHotelContentScoreTask failed", '', settings.TECH_STATS_EMAIL, {}, [])


@app.task(name="ingoibibo.calculate_hotel_content_score_sub_task")
def calculateHotelContentScoreSubTask(hotelIds=None):
    from hotels.contentscore import calculateHotelContentScore
    inventory_logger.info(
        message='calculate_hotel_content_score_sub_task started for hotelids {}:{}'.format(hotelIds[0], hotelIds[-1]),
        log_type='ingoibibo', bucket='hotels.tasks',
        stage='calculateHotelContentScoreSubTask')
    calculateHotelContentScore(hotelIds)
    inventory_logger.info(
        message='calculate_hotel_content_score_sub_task succeeded for hotelids {}:{}'.format(hotelIds[0], hotelIds[-1]),
        log_type='ingoibibo', bucket='hotels.tasks',
        stage='calculateHotelContentScoreSubTask')


@app.task(name="ingoibibo.update_hotel_specific_field_on_voyager")
def updateHotelSpecificFieldOnVoyagerTask(hotel_vid, field, value, country='INDIA'):
    try:
        from hotels.pushingtovoyager import pushHotelSpecificFieldToVoyager

        pushHotelSpecificFieldToVoyager(hotel_vid, field, value, country)
        celery_logger.info('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'updateHotelSpecificFieldOnVoyagerTask', '', 'else', 'completed'))
    except Exception as e:
        celery_logger.critical(
            '%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'updateHotelSpecificFieldOnVoyagerTask', '', str(e),
                                        repr(traceback.format_exc())))


@app.task(name="ingoibibo.push_hotel_tag_to_voyager_task")
def push_hotel_tag_to_voyager_task(hotel_vid, value_list):
    try:
        from hotels.pushingtovoyager import push_hotel_tag_to_voyager
        push_hotel_tag_to_voyager(hotel_vid, value_list)

        celery_logger.info('%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'push_hotel_tag_to_voyager_task', 'completed'))
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks',
                                                           'push_hotel_tag_to_voyager_task', 'failed', str(e),
                                                           repr(traceback.format_exc())))


@app.task(name="call_url_task")
def call_url_task(url, method, data):
    try:
        import urllib, urllib2
        if method == 'GET':
            request = urllib2.Request(url)
        else:
            request = urllib2.Request(url, data=urllib.urlencode(data))
        response_data = urllib2.urlopen(request).read()
        celery_logger.info('%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'call_url_task', 'completed'))
        return response_data
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks',
                                                           'call_url_task', 'failed', str(e),
                                                           repr(url)))


@app.task(name="ingoibibo.hotels_with_free_cancellation")
def getHotelsWithFreeCancellationTask():
    try:
        today = datetime.date.today()
        enddate = today + datetime.timedelta(days=200)
        cancellation_fields = ['object_id', 'startday']
        hotelcprules = HotelCancellationRule.objects.filter(
            isactive=True, chargesvalue=0, startday__lt=7,
            content_type__model='hoteldetail',
            staystart__lte=today, stayend__gte=enddate
        ).only(*cancellation_fields)
        hotel_dict = {}
        for rule in hotelcprules:
            hid = rule.object_id
            sd = rule.startday
            if hid not in hotel_dict:
                hotel_dict[hid] = sd
            elif sd < hotel_dict[hid]:
                hotel_dict[hid] = sd

        hotelids = set(hotelcprules.values_list('object_id', flat=True))
        hotel_fields = ['id', 'voyagerid', 'country']
        hotels = HotelDetail.objects.all().only(*hotel_fields)
        field = 'extra.orig_vendor_data.ing.free_cancel'
        for hotel in hotels:
            hvid = hotel.hotelcode if hotel.country.lower() != 'india' else hotel.voyagerid
            if not hvid:
                continue
            if hotel.id in hotel_dict:
                value = int(hotel_dict[hotel.id])
                updateHotelSpecificFieldOnVoyagerTask.apply_async(args=(str(hvid), field, value, hotel.country,))
            else:
                updateHotelSpecificFieldOnVoyagerTask.apply_async(args=(str(hvid), field, None, hotel.country,))

        celery_logger.info('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'getHotelsWithFreeCancellationTask', '', 'else', 'completed'))
    except Exception as e:
        celery_logger.critical(
            '%s\t%s\t%s\t%s\t%s\t%s' % ('hotel', 'tasks', 'getHotelsWithFreeCancellationTask', '', str(e),
                                        repr(traceback.format_exc())))


@app.task(name="ingoibibo.hotels_with_last_minute_offer")
def getHotelsWithLastMinuteOfferTask():
    try:
        today = datetime.date.today()
        tomorrow = today + datetime.timedelta(days=1)
        one_week = today + datetime.timedelta(days=6)
        offer_fields = ['object_id', 'content_type', 'earlybirdmax']
        offers = HotelOfferCondition.objects.filter(isactive=True, offercategory='lastminute',
                                                    earlybirdmax__lte=7, bookingdatestart__lte=today,
                                                    checkindatestart__lte=today,
                                                    bookingdateend__gte=one_week,
                                                    checkoutdateend__gte=one_week).only(*offer_fields)

        rateplan_ids = list()
        hotel_ids = list()
        offer_dict = {}
        for offer in offers:
            if offer.earlybirdmax:
                if offer.content_type.model == 'hoteldetail':
                    hotel_ids.append(offer.object_id)
                    offer_dict['h' + str(offer.object_id)] = offer.earlybirdmax
                elif offer.content_type.model == 'rateplan':
                    rateplan_ids.append(offer.object_id)
                    offer_dict['r' + str(offer.object_id)] = offer.earlybirdmax

        all_hotels = HotelDetail.objects.all().only(*['id', 'voyagerid', 'country'])
        rate_plan_fields = ['id', 'roomtype__hotel__id']
        rateplans = RatePlan.objects.filter(id__in=rateplan_ids).only(*rate_plan_fields)

        field = 'extra.orig_vendor_data.default.lmd'
        for rp in rateplans:
            hid = 'h' + str(rp.roomtype.hotel.id)
            rid = 'r' + str(rp.id)
            if rid in offer_dict:
                if hid not in offer_dict or offer_dict[hid] < offer_dict[rid]:
                    offer_dict[hid] = offer_dict[rid]

        for hotel in all_hotels:
            hvid = hotel.hotelcode if hotel.country.lower() != 'india' else hotel.voyagerid
            if not hvid:
                continue
            hid = 'h' + str(hotel.id)
            if hid in offer_dict:
                lmd = offer_dict[hid]
                updateHotelSpecificFieldOnVoyagerTask.apply_async(args=(str(hvid), field, lmd, hotel.country))
            else:
                updateHotelSpecificFieldOnVoyagerTask.apply_async(args=(str(hvid), field, 0, hotel.country))

        celery_logger.info(
            '%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'getHotelsWithLastMinuteOfferTask', '', 'else', 'completed'))
    except Exception as e:
        celery_logger.critical(
            '%s\t%s\t%s\t%s\t%s\t%s' % ('hotel', 'tasks', 'getHotelsWithLastMinuteOfferTask', '', str(e),
                                        repr(traceback.format_exc())))


@app.task(name="ingoibibo.customer_rating_handler")
def pushCustomerRatingTask():
    try:
        from hotels.premiumcustomer import iterateOverBookingData

        iterateOverBookingData()
        celery_logger.info(
            '%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'pushCustomerRatingTask', '', '', 'completed'))
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'pushCustomerRatingTask', '', str(e),
                                                           repr(traceback.format_exc())))


# TODO: uncomment this task after production celery task failure issue resolved.
# @app.task(name="ingoibibo.push_booking_status_to_goibibo")
# def push_bookingstatus_to_goibibo(bid=''):
#     try:
#         goibibo_reconfirm_queue(bid)
#     except Exception as e:
#         celery_logger.critical(
#             '%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'push_bookingstatus_to_goibibo', '', str(e),
#                                         repr(traceback.format_exc())))


@app.task(name="ingoibibo.update_payout_data_task")
def update_payout_data_task(filename, username):
    try:
        from hotels.admin.views import update_payout_data
        update_payout_data(filename, username)
        celery_logger.info('%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'update_payout_data_task',
            'update payout data into system'))
        # sendHotelPaymentInfoMail(hop)
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'update_payout_data_task', '', str(e),
                                                           repr(traceback.format_exc())))


@app.task(name="ingoibibo.update_payout_data_task_v2")
def update_payout_data_task_v2(filename, username):
    try:
        from hotels.admin.views import update_payout_data_v2
        update_payout_data_v2(filename, username)
        celery_logger.info('%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'update_payout_data_task_v2',
            'new update payout data into system'))
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'update_payout_data_task_v2', '', str(e),
                                                           repr(traceback.format_exc())))


@app.task(name="ingoibibo.update_checkin_status_task")
def update_checkin_status_task():
    try:
        from api.v1.bookings.resources.booking_resource import update_checkin_status_post_checkin

        update_checkin_status_post_checkin()
    except Exception as e:
        celery_logger.critical(
            '%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'update_checkin_status_post_checkin', '', str(e),
                                        repr(traceback.format_exc())))


@app.task(name="ingoibibo.notification_hotel_task")
def notification_hotel_alert_task():
    try:
        hid_list = HotelDetail.objects.filter(isactive=True).values_list('id', flat=True)
        for hid in hid_list:
            if settings.DEBUG:
                notification_hotel_sub_task(hid)
            else:
                notification_hotel_sub_task.apply_async(args=([hid, ]), )
    except Exception as e:
        celery_logger.critical(
            '%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'notification_hotel_alert_task', '', str(e),
                                        repr(traceback.format_exc())))


@app.task
def notification_hotel_sub_task(hid):
    try:
        from notifications import notify_hotel

        notify_hotel(hid)
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'notification_hotel_sub_task', '', str(e),
                                                           repr(traceback.format_exc())))


@app.task(name="ingoibibo.payout_booking_task")
def payout_booking_task(user, start_date, end_date, query_based, payment_for=None):
    try:
        from hotels.bookingpayments import processBookingPayments
        processBookingPayments(user, start_date, end_date, query_based, payment_for)
    except Exception as e:
        sendMail('', '', str(e) + str(start_date) + str(end_date) + str(query_based), 'payout_booking_task', '',
                 settings.INGOIBIBO_ERROR_ALERT, {}, [])
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'payout_booking_task', '', str(e),
                                                           repr(traceback.format_exc())))


@celery.task(name="ingoibibo.push_hotel_on_voyager_task")
def push_hotel_on_voyager_task(start, end, pushed_hotel_id):
    """
        push hotel data on voyager
    """
    celery_logger = push_hotel_on_voyager_task.get_logger()
    message = ''
    try:
        from hotels.pushingtovoyager import pushHoteltoVoyagerbyObject

        htl_list = HotelDetail.objects.filter(
            id__gte=pushed_hotel_id, voyagerid__isnull=False
        )[start:end]
        for htl in htl_list:
            message = 'Pushing hotel %s failed' % htl.hotelcode
            status, message = pushHoteltoVoyagerbyObject(htl, caller="push_hotel_on_voyager_task")
            celery_logger.info('%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'push_hotel_on_voyager_task', message))
    except Exception as e:
        celery_logger.critical(
            '%s\t%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks',
                                            'push_hotel_on_voyager_task', '', str(e),
                                            repr(traceback.format_exc()), message)
        )


@celery.task(name="ingoibibo.get_query_data_task")
def get_query_data_task():
    try:
        from analytics_data.ga_data import populate_all_data
        populate_all_data(get_data_for_all_devices=True)
    except Exception as e:

        sendMail('', '', "{0}\n\n{1}".format(str(e), repr(traceback.format_exc())),
                 "Celery Job get_query_data_task failed", '',
                 settings.CITYRANKING_ALERT, {}, [])
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'get_query_data_task', 'Updation Failed ', str(e),
            repr(traceback.format_exc())))


@celery.task(name="ingoibibo.sync_hotel_on_voyager_task")
def sync_hotel_on_voyager_task():
    """
        Syncronization hotel data day wise on voyager
    """
    celery_logger = sync_hotel_on_voyager_task.get_logger()
    try:
        daynumber = int(datetime.datetime.now().strftime("%w"))
        pushed_last_htl_id = cache.get('lasthtlid', 1)
        htl_count = HotelDetail.objects.filter(
            voyagerid__isnull=False, id__gte=pushed_last_htl_id
        ).count()
        divideby = 7 - daynumber
        total_htl_push = int(htl_count / divideby)
        loop_count = int(math.ceil(total_htl_push / 1000.0))
        htl_pushed = total_htl_push
        for index in range(loop_count):
            start = index * 1000
            end = start + (1000 if htl_pushed > 1000 else htl_pushed)
            htl_pushed -= 1000
            push_hotel_on_voyager_task.apply_async(
                args=(start, end, pushed_last_htl_id,))
        last_htl_id = HotelDetail.objects.filter(
            id__gte=pushed_last_htl_id, voyagerid__isnull=False
        ).values('id').order_by('-id')[0:total_htl_push][0]['id']
        cache.set('lasthtlid', last_htl_id, 7 * 60 * 60 * 24)
    except Exception as e:
        celery_logger.critical(
            '%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks',
                                        'sync_hotel_on_voyager_task', '', str(e),
                                        repr(traceback.format_exc()))
        )


@app.task(name="ingoibibo.booking_commission_type_field_update_task")
def booking_commission_type_field_update_task(rateplan_code, confirm_booking_id):
    log_identifier = {}
    try:
        update_specific_identifier('booking_id', confirm_booking_id, log_identifier)
        from hotels.bookingsoperations import update_booking_table_commission_type_field

        update_booking_table_commission_type_field(rateplan_code, confirm_booking_id)
        update_specific_identifier('remark', 'Successfully Done', log_identifier)
        inventory_logger.info(log_type='ingoibibo', bucket='hotels.tasks', stage='booking_commission_type_field_update',
                              identifier='{}'.format(log_identifier))
    except Exception as e:
        update_specific_identifier('remark', 'Failed', log_identifier)
        update_error_identifier(error_message=str(e), traceback=repr(traceback.format_exc()),
                                log_identifier=log_identifier)
        inventory_logger.critical(log_type='ingoibibo', bucket='hotels.tasks', stage='booking_commission_type_field_update',
                              identifier='{}'.format(log_identifier))


@app.task(name="ingoibibo.execute_plb_cycle")
def execute_plb_cycle():
    try:
        from hotels.performlinkbonus import plb_statement_cycle
        plb_statement_cycle()
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'execute_plb_cycle',
                                                       'execute plb cycle job Failed', repr(traceback.format_exc())))
        sendMail('', '', str(e), 'Celery Job for execute_plb_cycle failed', '', settings.INGOIBIBO_ERROR_ALERT, {}, [])


@app.task(name="ingoibibo.execute_vdi_cycle")
def execute_vdi_cycle():
    try:
        from hotels.performlinkbonus import vdi_statement_cycle
        vdi_statement_cycle()
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'execute_vdi_cycle',
                                                       'execute vdi cycle job Failed', repr(traceback.format_exc())))
        sendMail('', '', str(e), 'Celery Job for execute_vdi_cycle failed', '', settings.INGOIBIBO_ERROR_ALERT, {}, [])


@app.task(name="ingoibibo.execute_vdi_statement")
def execute_vdi_statement(vdi_obj):
    try:
        from hotels.performlinkbonus import create_vdi_statement
        create_vdi_statement(vdi_obj)
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'execute_vdi_statement',
                                                       'execute vdi statement job Failed',
                                                       repr(traceback.format_exc())))
        sendMail('', '', str(e), 'Celery Job for execute_vdi_statement failed', '', settings.INGOIBIBO_ERROR_ALERT, {},
                 [])


@app.task(name="ingoibibo.execute_plb_statement")
def execute_plb_statement(plb_id):
    try:
        from hotels.performlinkbonus import create_plb_statement
        plb_obj = PerformanceLinkBonus.objects.get(id=plb_id)
        create_plb_statement(plb_obj)
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'execute_plb_statement',
                                                       'execute plb statement job Failed',
                                                       repr(traceback.format_exc())))
        sendMail('', '', str(e), 'Celery Job for execute_plb_statement failed', '', settings.INGOIBIBO_ERROR_ALERT, {},
                 [])


@app.task(name="ingoibibo.free_room_nights_plb")
def free_room_nights_plb():
    try:
        from hotels.performlinkbonus import check_booking_of_frn
        check_booking_of_frn()
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'free_room_nights_plb', 'free room nights plb job Failed', repr(traceback.format_exc())))
        sendMail('', '', str(e), 'Celery Job for free_room_nights_plb failed', '', settings.INGOIBIBO_ERROR_ALERT, {},
                 [])


@app.task(name="ingoibibo.upload_mapping_csv")
def upload_mapping_csv(filename, email):
    from lib.MDBStorage import MDBStorage
    from reports.upload_reports import hotel_user_mapping
    mdb = MDBStorage()
    mapping_csv = mdb.open(filename)
    hotel_user_mapping(mapping_csv, email)


@app.task(name="ingoibibo.child_upload_hotel_fields_csv")
def child_upload_hotel_fields_csv(csv_rows, user):
    from reports.upload_reports import hotel_level_field_updation
    hotel_level_field_updation(csv_rows, user)


@app.task(name="ingoibibo.parent_upload_hotel_fields_task")
def parent_upload_hotel_fields_task(csv_file_name, user):
    from itertools import islice
    import logging

    from lib.MDBStorage import MDBStorage

    logger = logging.getLogger("inventoryLogger")
    mdb = MDBStorage()
    csv_file = mdb.open(csv_file_name)
    file_reader = csv.DictReader(csv_file)
    try:
        while 1:
            small_chunk = []
            for row in islice(file_reader, 0, 200):
                small_chunk.append(row)
            if not settings.DEBUG:
                child_upload_hotel_fields_csv.apply_async(args=(small_chunk, user,))
                logger.info('%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'parent_upload_hotel_fields_task',
                                                'Spawned child task for child_upload_hotel_fields_task with %s of rows' % len(
                                                    small_chunk)))
            else:
                child_upload_hotel_fields_csv(small_chunk, user)

            if not small_chunk:
                break
    except Exception as e:
        logger.critical('%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'parent_upload_hotel_fields_task', e))


# @app.task(name="ingoibibo.update_rates_to_mysql")
# def update_rates_to_mysql(hotel_code=None, extrabed_commissionable_flag=False):
#     if hotel_code:
#         from hotels.hotelrates import update_rates_to_mysql
#         hotel_obj = HotelDetail.objects.get(hotelcode=hotel_code)
#         room_list = hotel_obj.rooms.all()
#         for room in room_list:
#             rateplan_list = room.rateplans.all()
#             for rateplan in rateplan_list:
#                 update_rates_to_mysql(rateplan, action='extrabed_commissionable_flag',
#                                       extrabed_commissionable_flag=extrabed_commissionable_flag)
#                 celery_logger.info('%s\t%s\t%s\t%s\t%s' % (
#                     'hotels', 'tasks', 'update_rates_to_mysql', rateplan.rateplancode,
#                     'Updation of Rates on changing extrabed flag'))
#     else:
#         celery_logger.critical('%s\t%s\t%s\t%s' % (
#             'hotels', 'tasks', 'update_rates_to_mysql',
#             'Updation of Rates on changing extrabed flag because hotel_obj is None'))


@app.task(name="ingoibibo.plb_supplier_report")
def send_plb_supplier_report():
    try:
        from reports.plbreports import plb_supplier_report
        plb_supplier_report()
        celery_logger.info('%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'send_plb_supplier_report',
            'Sending of PLB Supplier report file'))
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'send_plb_supplier_report',
            'execute plb supplier report job Failed',
            repr(traceback.format_exc())))
        sendMail('', '', str(e), 'Celery Job for send_plb_supplier_report failed', '', settings.INGOIBIBO_ERROR_ALERT,
                 {}, [])


@app.task(name="ingoibibo.update_oracle_payout_data_task")
def update_oracle_payout_data_task(filename, user_obj):
    try:
        from hotels.admin.views import update_oracle_payout_data
        update_oracle_payout_data(filename, user_obj)
        celery_logger.info('%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'update_oracle_payout_data_task',
            'updateing payment info into the system'))
    except Exception as e:
        celery_logger.critical(
            '%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'update_oracle_payout_data_task', '', str(e),
                                        repr(traceback.format_exc())))


@app.task(name="ingoibibo.update_last_login_task")
def update_last_login_task(user):
    try:
        from extranet.views import update_last_login
        update_last_login(user)
        celery_logger.info('%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'update_last_login_task',
            'updating last login in system'))
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'update_last_login_task', '', str(e),
                                                           repr(traceback.format_exc())))


@app.task(name="ingoibibo.retry_mail_service")
def retry_mail_service():
    try:
        from common.models import MailRecords
        from scripts.bookingdata import mail_booking_related_voucher
        from reports.plbreports import send_plb_statment_report
        from hotels.voucher_manager import VoucherManager

        date_filter = (datetime.datetime.now() - datetime.timedelta(days=3)).date()
        mailobjs = MailRecords.objects.filter(
            Q(tempid__in=['60.001', '60.005', '60.002', '60.014', '60.040', '60.018', '60.053'],
              createdon__gte=date_filter), (Q(status__in=['Error', 'waiting']) |
                                            Q(pigeonid__icontains='error') | Q(stresponse='connection closed'))). \
            exclude(status='Retried').only(*['object_id'])
        booking_ids = mailobjs.filter(content_type__model='hotelbooking'). \
            values_list('object_id', flat=True)
        cancel_ids = mailobjs.filter(content_type__model='hotelcancellation'). \
            values_list('object_id', flat=True)
        payment_ids = mailobjs.filter(content_type__model='hoteloutwardpayment'). \
            values_list('object_id', flat=True)
        perform_link_record_ids = mailobjs.filter(content_type__model='performancelinkrecords'). \
            values_list('object_id', flat=True)
        if booking_ids:
            bobj_list = HotelBooking.objects.filter(id__in=booking_ids, checkin__gte=datetime.date.today())
            for booking_obj in bobj_list:
                VoucherManager.process_voucher(booking_obj, False, True, False, "Resending: ", "")
        if cancel_ids:
            mail_booking_related_voucher(cancel_ids, 'hotelcancellation')
        if payment_ids:
            mail_booking_related_voucher(payment_ids, 'hotelpayment')
        if perform_link_record_ids:
            plr_obj_list = PerformanceLinkRecords.objects.filter(id__in=perform_link_record_ids)
            send_plb_statment_report(plr_obj_list, False, False)
        if mailobjs:
            mailobjs.update(status='Retried')
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'retry_mail_service', '', str(e),
            repr(traceback.format_exc())))


@app.task(name="ingoibibo.assign_permission_task")
def assign_permission_task(user, hotel_ids_list, permission_list=[], email_receiver=[], task_identifier=None):
    # TODO: As of now logging complete hotel_ids. Will remove post permission issue fix
    inventory_logger.info(
        message="assign_permission_task_STARTED | Task Identifier: %s | User: %s | Total Hotel IDs: %s | Permission List: %s | Time: %s" % (
            task_identifier, user.username, hotel_ids_list, permission_list,
            datetime.datetime.now()), log_type="ingoibibo",
        bucket="Permission Task", stage="assign_permission_task")
    if not permission_list:
        hotel_permissions = HotelDetail._meta.permissions
        permission_list = [hotel_permission[0] for hotel_permission in hotel_permissions]

    if not isinstance(hotel_ids_list, list):
        hotel_ids_list = list(hotel_ids_list)
    hotel_obj_list = HotelDetail.objects.filter(id__in=hotel_ids_list)
    html_message = ''
    sender = 'alerts_ingoibibo@%s' % hostname
    receiver = email_receiver + settings.GUARDIAN_TEAM_ALERT
    for hotel_obj in hotel_obj_list:
        successfully_assigned = []
        for permission in permission_list:
            try:
                assign_perm(permission, user, hotel_obj)
                logger.info('%s\t%s\t%s\t%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks',
                                                                    'assign_permission_task',
                                                                    user.username,
                                                                    hotel_obj.hotelcode, 'PERMISSION ASSIGNED',
                                                                    permission, '', ''))
                successfully_assigned.append(unicode(permission))
            except Exception as e:
                logger.critical('%s\t%s\t%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks',
                                                                    'assign_permission_task_ERROR',
                                                                    user.username,
                                                                    hotel_obj.hotelcode, permission, str(e),
                                                                    repr(traceback.format_exc())))
                if not settings.DEBUG:
                    sendMail('', '', 'Permission Failed due to %s for username %s '
                                     'for hotel %s for permission %s' % (str(e),
                                                                         user.username, hotel_obj.hotelcode,
                                                                         permission),
                             'assign_permission_task_ERROR', '', receiver, {}, [])

        html_message += '<b>%s</b> ----- <b>%s</b><br>' % (hotel_obj.hotelcode, hotel_obj.hotelname)
        cache_key = "guardian_%s_%s_v2" % (user.username, hotel_obj.hotelcode)
        cache.set(cache_key, successfully_assigned, timeout=0)

    subject = 'Assign Permission Task Completion mail'
    message = 'Assign Permission task for the below hotels has been completed'
    if not settings.DEBUG:
        sendMail('', '', html_message, subject, '', receiver, {}, [])
    inventory_logger.info(
        message="assign_permission_task_ENDED | Task Identifier: %s | User: %s | Total Hotel IDs: %s | Permission List: %s | Time: %s" % (
            task_identifier, user.username, hotel_ids_list, permission_list,
            datetime.datetime.now()), log_type="ingoibibo",
        bucket="Permission Task", stage="assign_permission_task")


@app.task(name="ingoibibo.remove_permission_task")
def remove_permission_task(user, hotel_ids_list, permission_list=[], task_identifier=None):
    # TODO: As of now logging complete hotel_ids. Will remove post permission issue fix
    inventory_logger.info(
        message="remove_permission_task_STARTED | Task Identifier: %s | User: %s | Total Hotel IDs: %s | Permission List: %s | Time: %s" % (
            task_identifier, user.username, hotel_ids_list, permission_list,
            datetime.datetime.now()), log_type="ingoibibo",
        bucket="Permission Task", stage="remove_permission_task")

    if not permission_list:
        hotel_permissions = HotelDetail._meta.permissions
        permission_list = [hotel_permission[0] for hotel_permission in hotel_permissions]

    hotel_obj_list = HotelDetail.objects.filter(id__in=hotel_ids_list)
    sender = 'alerts_ingoibibo@%s' % hostname
    receiver = settings.GUARDIAN_TEAM_ALERT
    for hotel_obj in hotel_obj_list:
        removed_permissions = []
        existing_permissions = get_perms(user, hotel_obj)

        for permission in permission_list:
            try:
                remove_perm(permission, user, hotel_obj)
                removed_permissions.append(unicode(permission))
                logger.info('%s\t%s\t%s\t%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks',
                                                                    'remove_permission_task',
                                                                    user.username,
                                                                    hotel_obj, permission,
                                                                    'PERMISSION REMOVED', '', ''))
            except Exception as e:
                logger.critical('%s\t%s\t%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks',
                                                                    'remove_permission_task', user.username,
                                                                    hotel_obj, permission, str(e),
                                                                    repr(traceback.format_exc())))
                if not settings.DEBUG:
                    sendMail('', '', 'Permission Failed due to %s for username %s '
                                     'for hotel %s for permission %s' % (
                                 str(e), user.username, hotel_obj.hotelcode, permission),
                             'remove_permission_task_ERROR', '', receiver, {}, [])
        cache_key = "guardian_%s_%s_v2" % (user.username, hotel_obj.hotelcode)
        cache.set(cache_key, list(set(existing_permissions).difference(set(removed_permissions))), timeout=0)

    inventory_logger.info(
        message="remove_permission_task_ENDED | Task Identifier: %s | User: %s | Total Hotel IDs: %s | Permission List: %s | Time: %s" % (
            task_identifier, user.username, hotel_ids_list, permission_list,
            datetime.datetime.now()), log_type="ingoibibo",
        bucket="Permission Task", stage="remove_permission_task")


@app.task(name="ingoibibo.remove_all_permission_task")
def remove_all_permission_task(user_id, username):
    task_identifier = random.random()
    celery_logger.info('%s %s %s %s' % (task_identifier, username, datetime.datetime.now(),
                                        'remove_all_permission_task_STARTED'))
    if not user_id or not username:
        return

    try:
        import redis
        from ingouser.models import User
        query = 'DELETE FROM guardian_userobjectpermission WHERE user_id=%s'
        cursor = connection.cursor()
        cursor.execute(query, [user_id])
        ## sql_injection id: 1 1366-1376 R
        all_keys = cache.fetch_keys('guardian_%s_*_v2' % username)
        all_keys = [all_key.split(':')[-1] for all_key in all_keys]
        cache.delete_many(all_keys)
    except Exception as e:
        celery_logger.critical('%s %s %s %s' % ('hotels', 'tasks', 'remove_all_permission_task', str(e)))
    celery_logger.info('%s %s %s %s' % (task_identifier, username, datetime.datetime.now(),
                                        'remove_all_permission_task_ENDED'))


# @app.task(name="ingoibibo.send_dm_booking")
# def send_mo_booking_push_failure_count_mail():
#     try:
#         resp, attachment = fetch_mo_booking_push_failure_count()
#         start_date = (date.today() - timedelta(days=5)).strftime('%e-%b-%g')
#         end_date = (date.today() - timedelta(days=1)).strftime('%e-%b-%g')
#         mail_sub = 'Booking Failure Count for SubType Booking_MO_Push from ' + str(start_date) + ' to ' + str(end_date)
#         render_html_page = 'hotelmails/dm_bookingCount.html'
#         mail_content = render_to_string(render_html_page, {'response': resp})
#         if len(resp) > 0 and attachment:
#             sendMail('', '', mail_content, mail_sub, '', settings.BOOKING_MO_PUSH_FAIL_ALERT, attached_file=attachment,
#                      bcc_emails=[])
#         celery_logger.info('%s\t%s\t%s\t%s\t%s' % ('ingoibibo', 'tasks', 'ingoibibo.send_dm_booking',
#                                                    'response data ', str(resp)))
#
#     except Exception as e:
#         celery_logger.critical('%s\t%s\t%s\t%s' % ('Exception occured', 'ingoibibo.send_dm_booking',
#                                                    str(e), repr(traceback.format_exc())))


# @app.task(name="ingoibibo.send_ingo_health_status")
# def send_ingo_health_status():
#     try:
#         response = ingo_health_status()
#         mail_subject = 'INGO Health Status Report as of date : ' + str(datetime.datetime.today().strftime('%d-%m-%Y'))
#         render_html = 'hotelmails/ingo_booking_statistic.html'
#         mail_data = render_to_string(render_html, {'response': response})
#         if settings.HOST in settings.PROD_HOSTS and len(response) > 0:
#             sendMail('', '', mail_data, mail_subject, '', ['<EMAIL>'], {}, [])
#             # mail template consist stastistic of channel manager api, new relic count
#         celery_logger.info('%s\t%s\t%s\t%s\t%s' % ('ingoibibo', 'tasks', 'ingoibibo.send_ingo_health_status',
#                                                    'response data ', str(response)))
#     except Exception as e:
#         celery_logger.critical('%s\t%s\t%s\t%s' % ('Exception occured', 'ingoibibo.send_ingo_health_status',
#                                                    str(e), repr(traceback.format_exc())))


@app.task(name="ingoibibo.send_bulk_booking_mailer")
def send_bulk_booking_mailtask(hotels, postDict):
    from communication.hotelMailsSMS import getSendToEmailAndSubject, sendMail
    tempId = '60.123'
    mail_subject = 'Bulk Booking Query.'
    if settings.HOST not in settings.PROD_HOSTS:
        hotels = hotels[:1]
    for hotel in hotels:
        postDict['hotelname'] = hotel.hotelname
        renderHtml = 'hotelmails/bulk_bookingmail.html'
        mail_content = render_to_string(renderHtml, postDict)
        emailid, send_to_email, subject = getSendToEmailAndSubject(hotel, mail_subject)
        sendMail(emailid, settings.TRANSACTION_EMAIL_SENDER, mail_content, subject, tempId, send_to_email, {}, [],
                 content_obj=hotel)
        postDict['checkin'] = postDict['checkin'].split()[0]
        postDict['checkout'] = postDict['checkout'].split()[0]
        smsContent = hotelSMSObj.getSmsText('bulkbooking', postDict)
        hotelmobile = hotel.hotelmobile
        if settings.HOST not in settings.PROD_HOSTS:
            hotelmobile = settings.TEST_SMS
        isDomestic = True if hotel.country.strip().lower() == 'india' else False
        sendMessage(hotelmobile, smsContent, pnr=hotel.hotelcode, isDomestic=isDomestic)

    sendMail('', '', '%s hotels have been mailed in %s with params: %s' %
             (hotels.count(), postDict['cityname'], postDict), 'Bulk mailer task completed', '',
             settings.TECH_EMAIL, {}, [])


@app.task(name="ingoibibo.update_rates_for_linked_rateplan_task")
def update_rates_for_linked_rateplan_task(linked_raterule_id):
    from hotels.hotelrates import update_linked_rateplan_rates
    try:
        linked_rule_obj = LinkedRateRule.objects.get(id=linked_raterule_id)
    except LinkedRateRule.DoesNotExist:
        linked_rule_obj = LinkedRateRule.objects.using('default').get(id=linked_raterule_id)

    try:
        celery_logger.info('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'update_rates_for_linked_rateplan_task',
            linked_rule_obj.linked_rateplan.rateplancode, 'Updation of Linked Rates'))

        if linked_rule_obj.linked_rateplan and linked_rule_obj.linked_rateplan.isactive:
            update_linked_rateplan_rates(linked_rule_obj.linked_rateplan)
        else:
            celery_logger.info('%s\t%s\t%s\t%s' % (
                'hotels', 'tasks', 'update_rates_for_linked_rateplan_task',
                'Inactive Linked RatePlan cannot be updated'))

    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'update_rates_for_linked_rateplan_task',
            linked_rule_obj.linked_rateplan.rateplancode, repr(e)))

def update_linked_rates(room_type_id, slot_hours):
    if slot_hours != 540:
        rateplan_objects = RatePlan.objects.values('id','isactive','rateplancode').filter(roomtype_id = room_type_id, slot_duration = slot_hours, isactive=True)
    else:
        rateplan_objects = RatePlan.objects.values('id','isactive','rateplancode').filter(roomtype_id = room_type_id, slot_duration = slot_hours)
    if rateplan_objects:
        rp_object = rateplan_objects[0]
        linked_rule = LinkedRateRule.objects.only('id').filter(linked_rateplan_id = rp_object["id"])
        if linked_rule:
            update_rates_for_linked_rateplan_task(linked_rule[0].id)
        else:
            inventory_logger.info(message="Rateplan: %s is not linked." % (str(rp_object["rateplancode"])), bucket="hotels.tasks", stage="update_linked_rates")

@app.task(name="ingoibibo.remove_contracttype_rates_for_rateplan_task")
def remove_contracttype_rates_for_rateplan_task(rateplancode, contracttypes):
    from hotels.hotelrates import remove_contracttype_rates
    try:
        celery_logger.info('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'remove_contracttype_rates_for_rateplan_task',
            rateplancode, 'Remove of contract type rates'))
        remove_contracttype_rates(rateplancode, contracttypes)

    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'remove_contracttype_rates_for_rateplan_task',
            rateplancode, repr(e)))


@app.task(name="ingoibibo.update_isactive_mark_false")
def update_isactive_mark_false(hotel_list):
    from hotels.models import GSTDetail
    from django.db import close_old_connections
    no_of_hotels = len(hotel_list)
    success_case = 0
    count_of_hotels = 0
    for hotel in hotel_list:
        record_list = GSTDetail.objects.filter(hotel_id=hotel, isactive=1).order_by('-modifiedon')[1:]
        for record in record_list:
            try:
                record.isactive = False
                record.user_id = 759483
                record.save()
                success_case += 1
                count_of_hotels += 1
            except:
                api_logger.error("Getting error for %s hotel in %s id ." % (str(hotel), str(record.id)),
                                 log_type='ingoibibo',
                                 bucket='ExceptionHandler', stage='api.exception_handler.build_error_data')
        if count_of_hotels % 500 == 0:
            close_old_connections()
            sleep(30)
    api_logger.info("total hotels executed %s / %s ." % (str(success_case), str(no_of_hotels - success_case)),
                    log_type='ingoibibo',
                    bucket='ExceptionHandler', stage='api.exception_handler.build_error_data')

@app.task(name="ingoibibo.update_vcc_currency")
def update_vcc_currency(hotel_list):
    success_count = 0
    hotel_obj = HotelDetail.objects.filter(hotelcode__in=hotel_list, isactive=1)
    for hotel in hotel_obj:
        try:
            actual_vcc_currency = hotel.base_currency
            hotel.vcc_currency = actual_vcc_currency
            hotel.user_id = 759483
            hotel.save()
            success_count+=1
        except:
            api_logger.error("Getting error for %s hotel." % str(hotel.hotelcode),
                                log_type='ingoibibo',
                                bucket='ExceptionHandler', stage='api.exception_handler.build_error_data')
    api_logger.info("VCC currency update successfully for %s/%s hotels." % (str(success_count),str(len(hotel_list))),
                                log_type='ingoibibo',
                                bucket='hotels.tasks', stage='update_vcc_currency')


@app.task(name="ingoibibo.rates_update_on_parent_id_change_task")
def rates_update_on_parent_id_change_task(linked_rateplan_id, rateplan_reactivated):
    from hotels.hotelrates import update_linked_rateplan_rates
    linked_rateplan = RatePlan.objects.get(id=linked_rateplan_id)
    try:
        celery_logger.info('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'rates_update_on_parent_id_change_task',
            linked_rateplan.rateplancode, 'Updation of Linked Rates'))

        if not linked_rateplan.isactive:
            logger.info(message='Inactive Linked Rate Plan %s cannot be updated' % linked_rateplan.rateplancode,
                        log_type='ingoibibo', bucket='rates_update_on_parent_id_change_task')
            return

        update_linked_rateplan_rates(linked_rateplan, rateplan_reactivated)

    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'rates_update_on_parent_id_change_task',
            linked_rateplan.rateplancode, repr(e)))


@app.task(name="ingoibibo.static_copy_ari_deactivation_on_optout")
def static_copy_ari_deactivation_on_optout(hotel_code):
    try:
        celery_logger.info('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'static_copy_ari_deactivation_on_optout',
            hotel_code, 'Deactivation of static/copy data'))

        update_static_copy_ari_request = dict()
        data = [{
            'code_list': [hotel_code],
            'level': 'hotel',
            'is_active': False
        }]
        update_static_copy_ari_request['hotel_code'] = hotel_code
        update_static_copy_ari_request['data'] = data
        grpc_rates_client.update_static_copy_ari(update_static_copy_ari_request)

    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'static_copy_ari_deactivation_on_optout',
            hotel_code, repr(e)))


@app.task(name='ingoibibo.change_promocode_task')
def change_promocode_task(hotelcodes_mail_type, promocode_status, to_be_notified, actor=None):
    unintimidated_hotels = []
    update_failed_hotels = []
    exec_cancel_hotels = []
    EXECUTION_STATUS_MAP = {'TO_BE_EXECUTED': 1, 'EXECUTION_CANCELLED': 2}
    try:
        hotels = HotelDetail.objects.filter(hotelcode__in=hotelcodes_mail_type.keys())
        for hotel in hotels:
            execution_status = hotelcodes_mail_type[hotel.hotelcode].get('execution_status', None)
            if execution_status == EXECUTION_STATUS_MAP.get('EXECUTION_CANCELLED'):
                exec_cancel_hotels.append(hotel.hotelcode)
                continue
            mail_type = hotelcodes_mail_type[hotel.hotelcode].get('mail_type', None)
            actual_notes = 'Play Book Action Implemented. Do Not Change Promocode Status.'
            custom_notes = hotelcodes_mail_type[hotel.hotelcode].get('notes', '')
            requested_by = hotelcodes_mail_type[hotel.hotelcode].get('requested_by', '')
            if requested_by:
                requested_by = '%s : %s' % ('Requested by', requested_by)
            notes = '%s  %s  %s' % (actual_notes, custom_notes, requested_by)
            hotelname = '%s, %s' % (hotel.hotelname, hotel.city_id.cityname)
            if mail_type:
                reciepents = dict()
                reciepents['primary'] = hotel.hotelemail[0]
                reciepents['cc'] = hotel.createSendToEmailList(email_filter=['bdo', 'mm', 'non-staff'])

                if settings.DEBUG:
                    reciepents['primary'] = '<EMAIL>'
                    reciepents['cc'] = ['<EMAIL>']

                if not action_mails(reciepents, mail_type, hotelname):
                    unintimidated_hotels.append(hotel.hotelcode)
                    celery_logger.critical('%s\t%s\t%s\t' % ('change_promocode_task',
                                                             'Error While sending mail for enabling promocode',
                                                             hotel.hotelcode))
                    continue
            hotel.user = actor
            hotel.promo_code_discount = promocode_status
            hotel.save(update_fields=['promo_code_discount'])
            ItemNote(
                notes=notes,
                # currently giving user id of request user, in future it will be changed to approver
                user=actor,
                content_object=hotel
            ).save()


    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t' % ('change_promocode_task', str(e), repr(traceback.format_exc())))
        update_failed_hotels = list(set(hotelcodes_mail_type.keys()) - set(unintimidated_hotels))

    if to_be_notified:
        send_promocode_task_notification(hotelcodes_mail_type, promocode_status, unintimidated_hotels,
                                         update_failed_hotels, exec_cancel_hotels, to_be_notified)


@app.task(name='ingoibibo.change_hotel_status_task')
def change_hotel_status_task(hotelcodes_mail_type, active_status, to_be_notified, actor=None):
    unintimidated_hotels = []
    update_failed_hotels = []
    exec_cancel_hotels = []
    EXECUTION_STATUS_MAP = {'TO_BE_EXECUTED': 1, 'EXECUTION_CANCELLED': 2}

    try:
        hotels = HotelDetail.objects.filter(hotelcode__in=hotelcodes_mail_type.keys())
        for hotel in hotels:
            execution_status = hotelcodes_mail_type[hotel.hotelcode].get('execution_status', None)
            if execution_status == EXECUTION_STATUS_MAP.get('EXECUTION_CANCELLED'):
                exec_cancel_hotels.append(hotel.hotelcode)
                continue
            mail_type = hotelcodes_mail_type[hotel.hotelcode].get('mail_type', None)
            actual_notes = 'Play Book Action Implemented. Do Not Change Hotel Status.'
            custom_notes = hotelcodes_mail_type[hotel.hotelcode].get('notes', '')
            requested_by = hotelcodes_mail_type[hotel.hotelcode].get('requested_by', '')
            if requested_by:
                requested_by = '%s : %s' % ('Requested by', requested_by)
            notes = '%s  %s  %s' % (actual_notes, custom_notes, requested_by)
            hotelname = '%s, %s' % (hotel.hotelname, hotel.city_id.cityname)
            if mail_type:
                reciepents = dict()
                reciepents['primary'] = hotel.hotelemail[0]
                reciepents['cc'] = hotel.createSendToEmailList(email_filter=['bdo', 'mm', 'non-staff'])

                if settings.DEBUG:
                    reciepents['primary'] = '<EMAIL>'
                    reciepents['cc'] = ['<EMAIL>']
                if not action_mails(reciepents, mail_type, hotelname):
                    unintimidated_hotels.append(hotel.hotelcode)
                    celery_logger.critical('%s\t%s\t%s\t' % ('change_hotel_status_task',
                                                             'Error While sending mail for enabling promocode',
                                                             hotel.hotelcode))
                    continue
            try:
                if active_status:
                    owner_group = Group.objects.filter(name__in=['Fraud Owner', 'FRAUD_OWNER']).first()
                    staff_group = Group.objects.filter(name__in=['Fraud Staff', 'FRAUD_STAFF']).first()

                    fraud_team_users = list(owner_group.user_set.all().values_list('id', flat=True)) \
                                       + list(staff_group.user_set.all().values_list('id', flat=True))

                    if hotel.deactivated_by_id in fraud_team_users and actor.id not in fraud_team_users:
                        update_failed_hotels.append(hotel.hotelcode)
                        continue

                hotel.deactivated_by = actor
                hotel.isactive = active_status
                hotel.save(update_fields=['isactive', 'deactivated_by_id'])
                ItemNote(
                    notes=notes,
                    user=actor,
                    content_object=hotel
                ).save()
            except Exception as e:
                celery_logger.critical(
                    '%s\t%s\t%s\t' % ('change_hotel_status_task', str(e), repr(traceback.format_exc())))
                update_failed_hotels.append(hotel.hotelcode)

    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t' % ('change_hotel_status_task', str(e), repr(traceback.format_exc())))
        update_failed_hotels = list(set(hotelcodes_mail_type.keys()) - set(unintimidated_hotels))
    if to_be_notified:
        send_delist_task_notification(hotelcodes_mail_type, unintimidated_hotels, update_failed_hotels,
                                      exec_cancel_hotels, to_be_notified)


@app.task(name='ingoibibo.send_mail_action_task')
def send_mail_action_task(hotelcodes_mail_type, to_be_notified):
    unintimidated_hotels = []

    try:
        # USAGE TO CHECKED BY DYNAMIC
        hotels = HotelDetail.objects.filter(hotelcode__in=hotelcodes_mail_type.keys()).only('hotelmanagers',
                                                                                            'contractbdo',
                                                                                            'contractmanager',
                                                                                            'hotelemail',
                                                                                            'hotelcode',
                                                                                            'hotelname', 'city')
        for hotel in hotels:
            reciepents = dict()
            reciepents['primary'] = hotel.hotelemail[0]
            reciepents['cc'] = hotel.createSendToEmailList(email_filter=['bdo', 'mm', 'non-staff'])
            hotelname = '%s, %s' % (hotel.hotelname, hotel.city_id.cityname)

            if settings.DEBUG:
                reciepents['primary'] = '<EMAIL>'
                reciepents['cc'] = ['<EMAIL>']
            if not action_mails(reciepents, hotelcodes_mail_type[hotel.hotelcode].get('mail_type'), hotelname):
                unintimidated_hotels.append(hotel.hotelcode)
                celery_logger.critical('%s\t%s\t%s\t' % ('send_mail_task',
                                                         'Error While sending mail',
                                                         hotel.hotelcode))
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t' % ('send_mail_task', str(e), repr(traceback.format_exc())))
    if to_be_notified:
        send_mail_task_notification(hotelcodes_mail_type, unintimidated_hotels, to_be_notified)


@app.task(name='ingoibibo.update_lat_long_task')
def update_lat_long_task(hotelcode_lat_long, file_name, notifier_mail):
    if hotelcode_lat_long:
        try:
            hotelcodes = hotelcode_lat_long.keys()
            hotels = HotelDetail.objects.filter(hotelcode__in=hotelcodes)
            failed_hotelcodes = []
            for hotel in hotels:
                try:
                    hotel.latitude = float(hotelcode_lat_long[hotel.hotelcode]['latitude'])
                    hotel.longitude = float(hotelcode_lat_long[hotel.hotelcode]['longitude'])
                    hotel.save(bulk=True)
                except Exception as e:
                    failed_hotelcodes.append(hotel.hotelcode)
                    pass

            subject = 'Latitude & Longitude Update Through Ingoibibo Reports and Exports Status'
            mail_body = 'Lat Long updated for all the hotels which were specified through %s file' % file_name

            if failed_hotelcodes:
                mail_body += ' with some hotelcodes failing "%s"' % ', '.join(failed_hotelcodes)

            sendMail('', '', mail_body, subject, '', notifier_mail, {}, [])

        except Exception as e:
            celery_logger.error('%s\t%s\t%s\t%s\t' % ('hotels.tasks', 'update_lat_long_task',
                                                      str(e), repr(traceback.format_exc())))


@app.task(name='ingoibibo.check_blocked_mail_task')
def check_blocked_emails():
    blocked_emails = BlockedEmails()
    if blocked_emails.bookings_missed:
        for booking in blocked_emails.booking_data:
            blocked_emails.get_blocked_booking_data(booking)
    if blocked_emails.payments_missed:
        for hotel_payment_data in blocked_emails.payment_dict:
            blocked_emails.get_blocked_payment_data(hotel_payment_data)
    if blocked_emails.cancellation_missed:
        for cancellation_hotel_data in blocked_emails.cancellation_dict:
            blocked_emails.get_blocked_cancellation_data(cancellation_hotel_data)
    blocked_emails.mail()


@app.task(name='ingoibibo.upload_gostay_hotels')
def upload_gostay_task(gostay_file, emailid, requested_user):
    from hotels.helper import validate_row , push_uploader_status_to_s3
    row_status = {}
    mdb = MDBStorage()
    mapping_csv = mdb.open(gostay_file)
    reader = csv.DictReader(mapping_csv)
    for row_num, row in enumerate(reader, start=1):
        hotelcode = row.get('hotelcode', None)
        gostay_flag = row.get('gostayflag', None)
        commission = row.get('commission', None)
        approver = row.get('approver', None)

        try:
            hotel_code, message, success = validate_row(hotelcode, gostay_flag, commission, approver)
            if not success:
                row_status[row_num] = [hotel_code, message, success]
                continue

            hotel_obj = HotelDetail.objects.filter(hotelcode=hotel_code).first()
            change_request_validator(hotel_obj, ContentTypeIdForHotel, GOSTAY_PROGRAM_CHANGE)

            existing_commission = PriceDerivative.objects.filter(content_type_id=ContentTypeIdForHotel,
                                        program_type = ProgramType.GoStays.value, object_id=hotel_obj.id).first()
            if commission:
                is_valid_data, msg = change_request_commission_validator(gostay_flag, existing_commission, commission)
                if not is_valid_data:
                    row_status[row_num] = [hotel_code, msg, False]
                    continue

            approver_obj = validate_and_fetch_approver_obj_for_change_request(hotel_obj, approver, GOSTAY_PROGRAM_CHANGE)
            requester_ldap_obj, approver_ldap_obj = fetch_requester_approver_ldap_details(hotel_obj, requested_user, approver_obj)

            gostay_payload = {
                "newData": {
                    "status": validate_bool(gostay_flag),
                }
            }
            if validate_bool(gostay_flag) and commission:
                gostay_payload["newData"]["commission"] = commission

            change_request_data, formatted_req = create_change_request_data(hotel_obj, requester_ldap_obj,
                                                                            approver_ldap_obj, gostay_payload,
                                                                            GOSTAY_PROGRAM_CHANGE)
            push_change_request_packet_to_dynamic_hub(hotelcode, change_request_data, True)

            api_logger.info( "Pushing changeRequest  for hotel id: {} for requestType: {} with data: {}".format(
                hotelcode, GOSTAY_PROGRAM_CHANGE, formatted_req),log_type="ingoibibo", bucket='hotels.tasks', stage='hotels.tasks.upload_gostay_flag_task')

        except ValidationError as e:
            row_status[row_num] = [hotel_code, str(e), False]
            continue

        except Exception as e:
            row_status[row_num] = [hotel_code, str(e), False]
            continue

        row_status[row_num] = [hotel_code, 'Change request pushed successfully', True]

    s3_url = push_uploader_status_to_s3(row_status)
    sendMail('', '', 'GoStay Uploader Result: {}'.format(s3_url),
             'GoStay Uploader Result', '', [emailid], {}, [])


@app.task(name='ingoibibo.upload_advantage_flag_task')
def upload_advantage_flag_task(requested_user, advantage_file):
    from hotels.helper import validate_row, push_uploader_status_to_s3
    row_status = {}
    mdb = MDBStorage()
    mapping_csv = mdb.open(gostay_file)
    reader = csv.DictReader(mapping_csv)
    for row_num, row in enumerate(reader, start=1):
        hotelcode = row.get('hotelcode', None)
        advantage_flag = row.get('advantageflag', None)
        commission = row.get('commission', None)
        approver = row.get('approver', None)

        try:
            hotel_code, message, success = validate_row(hotelcode, advantage_flag, commission, approver)
            if not success:
                row_status[row_num] = [hotel_code, message, success]
                continue

            hotel_obj = HotelDetail.objects.filter(hotelcode=hotel_code).first()
            change_request_validator(hotel_obj, ContentTypeIdForHotel, ADVANTAGE_PROGRAM_CHANGE)

            existing_commission = PriceDerivative.objects.filter(content_type_id=ContentTypeIdForHotel,
                                                                 program_type=ProgramType.Advantage.value,
                                                                 object_id=hotel_obj.id).first()
            if commission:
                is_valid_data, msg = change_request_commission_validator(advantage_flag, existing_commission, commission)
                if not is_valid_data:
                    row_status[row_num] = [hotel_code, msg, False]
                    continue

            approver_obj = validate_and_fetch_approver_obj_for_change_request(hotel_obj, approver,
                                                                              ADVANTAGE_PROGRAM_CHANGE)
            requester_ldap_obj, approver_ldap_obj = fetch_requester_approver_ldap_details(hotel_obj, requested_user,
                                                                                          approver_obj)

            advantage_payload = {
                "newData": {
                    "status": validate_bool(advantage_flag),
                }
            }
            if validate_bool(advantage_payload) and commission:
                advantage_payload["newData"]["commission"] = commission

            change_request_data, formatted_req = create_change_request_data(hotel_obj, requester_ldap_obj,
                                                                            approver_ldap_obj, advantage_payload,
                                                                            ADVANTAGE_PROGRAM_CHANGE)
            push_change_request_packet_to_dynamic_hub(hotelcode, change_request_data, True)

            api_logger.info("Pushing changeRequest  for hotel id: {} for requestType: {} with data: {}".format(
                hotelcode, ADVANTAGE_PROGRAM_CHANGE, formatted_req), log_type="ingoibibo", bucket='hotels.tasks',
                stage='hotels.tasks.upload_advantage_flag_task')

        except ValidationError as e:
            row_status[row_num] = [hotel_code, str(e), False]
            continue

        except Exception as e:
            row_status[row_num] = [hotel_code, str(e), False]
            continue

        row_status[row_num] = [hotel_code, 'Change request pushed successfully', True]

    s3_url = push_uploader_status_to_s3(row_status)
    sendMail('', '', 'Advantage Uploader Result: {}'.format(s3_url), 'Advantage Uploader Result', '', [requested_user.email], {}, [])\


@app.task(name='ingoibibo.upload_fake_hotels_task')
def upload_fake_hotels_task(requested_user, fake_hotels_file):
    from api.v1.fake_details.service import FakeDetailService
    try:
        mdb = MDBStorage()
        mapping_csv = mdb.open(fake_hotels_file)
        reader = csv.DictReader(mapping_csv)
        request_data = {'user': requested_user}
        for row in reader:
            hotelcode = str(row['hotelcode'])
            try:
                hotel = HotelDetail.objects.get(hotelcode=hotelcode)
                fake_detail_service = FakeDetailService(requesting_user=requested_user, hotel=hotel)
                if row['fakeflag'].lower() == 'true' and hotel.hotel_suspected and not hotel.hotel_fake:
                    hotel.hotel_fake = True
                    with transaction.atomic():
                        hotel.save(request_data=request_data)
                        fake_detail_service.add_fake_details_for_hotel()
                elif row['fakeflag'].lower() == 'false' and hotel.hotel_fake:
                    hotel.hotel_fake = False
                    hotel.hotel_suspected = False
                    with transaction.atomic():
                        hotel.save(request_data=request_data)
                        fake_detail_service.remove_fake_details_for_hotel()
                api_logger.info(
                    message='Hotel updated: {0}, fake: {1}'.format(hotelcode, row['fakeflag']), log_type='ingoibibo',
                    bucket='upload_fake_hotels_task', stage='hotels.tasks.upload_fake_hotels_task'
                )
            except HotelDetail.DoesNotExist:
                api_logger.critical(
                    message='Hotel not found: {0}'.format(hotelcode), log_type='ingoibibo',
                    bucket='upload_fake_hotels_task', stage='hotels.tasks.upload_fake_hotels_task'
                )
            except Exception as e:
                api_logger.critical(
                    message='Exception occurred for hotel: {0} {1} {2}'.
                        format(hotelcode, str(e), repr(traceback.format_exc())),
                    log_type='ingoibibo', bucket='hotels.tasks', stage='hotels.tasks.upload_fake_hotels_task'
                )
        mdb.delete(fake_hotels_file)
    except Exception as e:
        api_logger.critical(
            message='Exception occurred: {0} {1}'.format(str(e), repr(traceback.format_exc())), log_type='ingoibibo',
            bucket='hotels.tasks', stage='hotels.tasks.upload_fake_hotels_task'
        )


@app.task(name='ingoibibo.upload_advantage_flag_task')
def upload_hotel_remove_homestay_funnel_task(requested_user, remove_homestay_funnel_file):
    try:
        mdb = MDBStorage()
        mapping_csv = mdb.open(remove_homestay_funnel_file)
        reader = csv.DictReader(mapping_csv)
        request_data = {'user': requested_user}
        for row in reader:
            hotelcode = str(row['hotelcode'])
            try:
                hotel = HotelDetail.objects.get(hotelcode=hotelcode)
                remove_homestay_funnel = json.loads(hotel.special_tag)
                if row['specialtag'].lower() == 'true':
                    remove_homestay_funnel.append(SHOW_IN_HOMESTAY_FUNNEL_TAG)
                    hotel.special_tag = json.dumps(remove_homestay_funnel)
                    hotel.save(request_data=request_data)
                elif SHOW_IN_HOMESTAY_FUNNEL_TAG in remove_homestay_funnel:
                    remove_homestay_funnel.remove(SHOW_IN_HOMESTAY_FUNNEL_TAG)
                    hotel.special_tag = json.dumps(remove_homestay_funnel)
                    hotel.save(request_data=request_data)

                api_logger.info(
                    message='Hotel updated: {0}'.format(hotelcode), log_type='ingoibibo',
                    bucket='upload_hotel_remove_homestay_funnel_task', stage='hotels.tasks'
                )
            except HotelDetail.DoesNotExist:
                api_logger.critical(
                    message='Hotel not found: {0}'.format(hotelcode), log_type='ingoibibo',
                    bucket='upload_hotel_remove_homestay_funnel_task', stage='hotels.tasks'
                )
            except Exception as e:
                api_logger.critical(
                    message='Exception occurred for hotel: {0} {1} {2}'.
                        format(hotelcode, str(e), repr(traceback.format_exc())),
                    log_type='ingoibibo', bucket='upload_hotel_remove_homestay_funnel_task', stage='hotels.tasks'
                )
        mdb.delete(remove_homestay_funnel_file)
    except Exception as e:
        api_logger.critical(
            message='Exception occurred: {0} {1}'.format(str(e), repr(traceback.format_exc())), log_type='ingoibibo',
            bucket='upload_hotel_remove_homestay_funnel_task', stage='hotels.tasks'
        )


@app.task(name="ingoibibo.create_booking_vcc_task")
def create_booking_vcc_task(booking_id):
    from hotels.vcc_payment import initiate_booking_vcc
    from hotels.bookingpayments import close_db_connection
    close_db_connection()
    initiate_booking_vcc(booking_id)

@app.task(name='ingoibibo.pah_uploader')
def modify_pah_state_func(modify_pah_state_file, query_dict, user, request_data=None):
    from reports.pah_handling import pah_handling
    mdb = MDBStorage()
    uploaded_file_name = str(modify_pah_state_file)
    mapping_csv = mdb.open(modify_pah_state_file)
    city_list = []
    state_list = []
    hotel_code_list = []
    reader = csv.DictReader(mapping_csv)
    email_id = user.email

    based_on = query_dict['based_on']
    start_date = query_dict['start_date']
    end_date = query_dict['end_date']
    action = query_dict['action']

    for row in reader:
        if row.get('city', ''):
            city_list.append(row['city'])
        if row.get('state', ''):
            state_list.append(row['state'])
        if row.get('hotelcode', ''):
            hotel_code_list.append(row['hotelcode'])

    if (based_on == 'city' and city_list and state_list) or (based_on == 'state' and state_list) \
            or (based_on == 'hotel_code' and hotel_code_list):
        success = pah_handling(based_on, action, user, start_date, end_date, state_list, city_list, hotel_code_list,
                               uploaded_file_name, request_data)
        if success == 'success':
            sendMail('', '', 'PAH State has been changed', 'PAH state modified successfully', '', [email_id], {}, [])
            logger.info('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'pah_handling', '', ' ',
                                                    'task completed'))
        else:
            sendMail('', '', 'Please provide correct csv', 'PAH state could not be modified', '', [email_id], {}, [])
            logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'pah_handling', '', ' ',
                                                        'task failed'))
    else:
        sendMail('', '', 'Please provide correct csv', 'PAH state could not be modified', '', [email_id], {}, [])


@app.task(time_limit=600)
def update_hotel_payment_task(row_data, username):
    try:
        from hotels.admin.views import update_payment_row_data_v2
        msg_list = update_payment_row_data_v2(row_data, username)
    except Exception as e:
        msg_list = ["Unexpected Error in this row data %s with exception %s"
                    % (row_data, e)]
    return msg_list


@app.task(name='ingoibibo.process_hotel_payment_task')
def process_hotel_payment_task(user, pay_dict):
    response_payment = {'success': False}
    try:
        from hotels.bookingpayments import initiate_hotel_payment
        response_payment = initiate_hotel_payment(user, pay_dict)
    except Exception as e:
        response_payment['error'] = str(e)
    return response_payment


@app.task(name='finance_payout_report_task')
def finance_payout_report_task(pay_objs):
    try:
        from scripts.bookingdata import create_finance_payout_sheet
        create_finance_payout_sheet(pay_objs)
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t' % ('finance_payout_report', str(e),
                                                 repr(traceback.format_exc())))


@app.task(name='ingoibibo.pah_flag_activation')
def enable_pah_flag(modify_pah_flag_file, pah_inventory_threshold, email_id):
    from reports.enable_pah_flag import enable_pah_flag_on_hotels
    mdb = MDBStorage()
    mapping_csv = mdb.open(modify_pah_flag_file)
    reader = csv.DictReader(mapping_csv)

    hotel_code_list = []

    for row in reader:
        if row.get('Hotel Code', ''):
            hotel_code_list.append(row['Hotel Code'])

    success = enable_pah_flag_on_hotels(hotel_code_list, pah_inventory_threshold, email_id)
    if success:
        sendMail('', '', 'PAH flag has been enabled', 'PAH state modified successfully', '', [email_id], {}, [])
        logger.info('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'enable_pah_flag', '', ' ',
                                                'task completed'))
    else:
        sendMail('', '', 'Please provide correct csv', 'PAH state could not be modified', '', [email_id], {}, [])
        logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'enable_pah_flag', '', ' ',
                                                    'task failed'))


@app.task(name="ingoibibo.send_hotel_payment_info_voucher")
def send_hotel_payment_info_voucher(booking):
    try:
        response = {'success': False, 'message': ''}
        payment = booking.payments.using('default').all()[0].payment
        from communication.hotelMailsSMS import sendHotelPaymentInfoMail
        mail_data = sendHotelPaymentInfoMail(payment, )
        if mail_data:
            # CHECKUSERCHANGE - DONE
            user = User.objects.get(username='vccpayment')
            from api.v1.bookings.resources.booking_status_handler import StatusManager
            status_update_data = {'user': user, 'status': 'confirmed',
                                  'reason': 'normal_confirm', 'source_key': 9,
                                  'initiated_by': user.username}
            try:
                response = StatusManager.status_push_wrapper(status_update_data, booking)

            except Exception as error:
                logger.critical(message='Exception occurred while sending '
                                        'vcc vouchers for probooking Id : %s '
                                        'and confirm booking id : %s, Error : %s'
                                        % (booking.probookingid, booking.confirmbookingid, str(error)),
                                log_type='ingoibibo',
                                bucket='Confirm Booking',
                                stage='hotels.tasks.send_hotel_payment_info_voucher')
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'send_hotel_payment_info_voucher',
            str(e), repr(traceback.format_exc())))


@app.task(name="ingoibibo.send_combined_voucher")
def send_combined_voucher(booking):
    try:
        payments = booking.payments.using('default').all()
        if not payments:
            from communication.hotelMailsSMS import sendHotelBookingVoucher
            sendHotelBookingVoucher(booking)
        else:
            payment = payments[0].payment
            sent = send_vcc_plus_booking_voucher(booking, payment)
            if sent:
                # CHECKUSERCHANGE - DONE
                user = User.objects.get(username='vccpayment')
                from api.v1.bookings.resources.booking_status_handler import StatusManager
                status_update_data = {'user': user, 'status': 'confirmed',
                                      'reason': 'autoconfirmflag', 'source_key': 9,
                                      'initiated_by': user.username}
                try:
                    response = StatusManager.status_push_wrapper(status_update_data, booking)
                except Exception as error:
                    logger.critical(message='Exception occurred while sending '
                                            'vcc vouchers for probooking Id : %s '
                                            'and confirm booking id : %s, Error : %s'
                                            % (booking.probookingid, booking.confirmbookingid, str(error)),
                                    log_type='ingoibibo',
                                    bucket='Confirm Booking',
                                    stage='hotels.tasks.send_combined_voucher')
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'send_combined_voucher',
            str(e), repr(traceback.format_exc())))


@app.task(name="ingoibibo.auto_confirm_bookings_and_mail")
def auto_confirm_bookings_and_mail():
    """
    Picks up bookings that were made in the last 1 hour under autoconfirm but
    due to some reason could not be updated as confirmed. This will try to
    create VCC again and send a combined voucher.(Individual booking voucher is
    already sent.) At the end it updates confirmstatus.
    :return: None
    """
    log_identifier = {}
    from hotels.vcc_payment import initiate_booking_vcc
    from hotels.hotelreports import formatBookingVoucherData
    bookings_failed = []
    now = datetime.datetime.now()
    an_hour_earlier = now - datetime.timedelta(hours=3)
    payments = HotelOutwardPayment.objects.filter(paymode='VCC', paymentreference__isnull=True,
                                                  createdon__gte=an_hour_earlier,
                                                  createdon__lt=now)
    for payment in payments:
        try:
            paymentdetail = eval(payment.paymentdetail)
            bookings = paymentdetail.get('bookings', [])
            bookings_failed += bookings
        except Exception as e:
            err_msg = "Failed decoding paymentdetail %s %s" % (payment, e)
            update_error_identifier(error_message=err_msg, traceback=repr(traceback.format_exc()),
                                    log_identifier=log_identifier)
            inventory_logger.critical(log_type="ingoibibo", bucket="hotels.tasks",
                                      stage="auto_confirm_bookings_and_mail",
                                      identifier="{}".format(log_identifier))
    bookings = HotelBooking.objects.filter(
        confirmbookingid__in=bookings_failed, confirmstatus__in=CONFIRM_BOOKING_STATUS,
        paymentstatus='pending').exclude(payathotelflag=True)
    for booking in bookings:
        booking = formatBookingVoucherData(booking)
        status = initiate_booking_vcc(booking.confirmbookingid,
                                      mail_vcc_voucher=True)
        if status:
            # send_combined_voucher.apply_async(args=(booking,))
            send_combined_voucher(booking)
        else:
            err_msg = "VCC generation and mailing failed for %s" % booking
            update_error_identifier(error_message=err_msg, traceback=repr(traceback.format_exc()),
                                    log_identifier=log_identifier)
            inventory_logger.critical(log_type="ingoibibo", bucket="hotels.tasks",
                                      stage="auto_confirm_bookings_and_mail",
                                      identifier="{}".format(log_identifier))


@app.task(name="ingoibibo.create_booking_vcc_and_mail")
def create_booking_vcc_and_mail(booking, close_db=True):
    from hotels.hotelreports import formatBookingVoucherData
    from hotels.vcc_payment import initiate_booking_vcc
    from hotels.bookingpayments import close_db_connection
    if close_db:
        close_db_connection()
    booking = formatBookingVoucherData(booking)
    success = initiate_booking_vcc(booking.confirmbookingid, mail_vcc_voucher=True)
    if success:
        # for MakeMyTrip bookings, send only hotel payment voucher
        if booking.bookingvendorname and booking.bookingvendorname.lower() == 'makemytrip':
            if booking.payments.using('default').exists():
                pass
        else:
            send_combined_voucher(booking)


@app.task(name="ingoibibo.retry_hotel_vcc_payment")
def retry_hotel_vcc_payment():
    """
    retry of those payment id where vcc has failed due to server error.
    """
    log_identifier = {}
    from hotels.bookingpayments import close_db_connection
    close_db_connection()
    payments = HotelOutwardPayment.objects.filter(paymode='vcc', bankreference__isnull=True,
                                                  createdon__gte=datetime.datetime.now().date())
    for pay in payments:
        try:
            payment_details = eval(pay.paymentdetail)
            bid = payment_details.get('bookings', [])
            if bid:
                bobj = HotelBooking.objects.get(confirmbookingid=bid[0])
                payment_obj = bobj.payments.exclude(payment__bankreference__isnull=True)
                if bobj.confirmstatus in ['confirmed', 'reconfirmed'] and bobj.paymentstatus in ['processing',
                                                                                                 'pending'] \
                        and not bobj.payathotelflag and not payment_obj:
                    create_booking_vcc_and_mail(bobj, False)
        except Exception as e:
            update_error_identifier(error_message=str(e), traceback=repr(traceback.format_exc()),
                                    log_identifier=log_identifier)
            inventory_logger.critical(log_type="ingoibibo", bucket="hotels.hoteloutwardpayment",
                                      stage="retry_hotel_vcc_payment", identifier="{}".format(log_identifier))


@app.task(name="ingoibibo.send_booking_status_mail", max_retries=2)
def send_booking_status_mail(booking_response_dict, booking_obj, booking_dict):
    log_identifier = {}
    try:
        booking_id = booking_obj.confirmbookingid if booking_obj else ''
        update_specific_identifier('booking_id', booking_id, log_identifier)
        mail_data = ''
        if booking_response_dict['success']:
            booking_obj_dict = booking_obj.__dict__
            for k, v in booking_obj_dict.items():
                mail_data += str(k) + ' - ' + str(v) + '\n'
            if not settings.DEBUG:
                sendMail('', '', mail_data, 'Confirm Booking Successful On Goibibo Inventory', '',
                         settings.CONFIRMATION_ALERTS_EMAILIDS, {}, [])
        else:
            for k, v in booking_response_dict.items():
                mail_data += str(k) + ' - ' + str(v) + '\n'
            mail_data += 'Provisional id - ' + str(booking_dict.get('probookingid', '')) + '\n'
            if not settings.DEBUG:
                sendMail('', '', mail_data, 'Confirm Booking Failure On Goibibo Inventory', '',
                         settings.CONFIRMATION_ALERTS_EMAILIDS, {}, [])
        update_specific_identifier("remark", "Completed sending confirm booking alert email", log_identifier)
        inventory_logger.info(log_type="ingoibibo", bucket="hotels.hotelbooking", stage="send_booking_status_mail",
                              identifier="{}".format(log_identifier))
    except Exception as e:
        update_error_identifier(error_message=str(e), traceback=repr(traceback.format_exc()),
                                log_identifier=log_identifier)
        inventory_logger.critical(log_type="ingoibibo", bucket="hotels.hotelbooking",
                                  stage="send_booking_status_mail", identifier="{}".format(log_identifier))

        send_booking_status_mail.retry(args=(booking_response_dict, booking_obj, booking_dict))


@app.task(name="ingoibibo.send_cancellation_status_mail", max_retries=2)
def send_cancellation_status_mail(cancellation_response_dict, cancellation_obj, cancellation_dict):
    try:
        mail_data = ''
        if not cancellation_response_dict['success']:
            for k, v in cancellation_response_dict.items():
                mail_data += str(k) + ' - ' + str(v) + '\n'
            for k, v in cancellation_dict.items():
                mail_data += str(k) + ' - ' + str(v) + '\n'
            sendMail('', '', mail_data, 'Cancellation of Booking Failure On Goibibo Inventory', '',
                     settings.CANCELLATION_ALERT_EMAILIDS, {}, [])
    except Exception as e:
        logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'hotelcancellation',
                                                    'cancelBooking mail', '',
                                                    str(e) + '\t\t' + str(cancellation_response_dict),
                                                    repr(traceback.format_exc())))
        send_cancellation_status_mail.retry(args=(cancellation_response_dict, cancellation_obj,
                                                  cancellation_dict))


@app.task(name="ingoibibo.send_secret_deal_notification")
def send_secret_deal_notification_task(secret_deal_data):
    try:
        success = send_secret_deal_notification(secret_deal_data)
        if not success:
            api_logger.critical(
                message='Secret deal notification task %s error' % (str(secret_deal_data)),
                log_type='ingoibibo',
                bucket='SecretDealAPI', stage='hotels.tasks.send_secret_deal_notification_task')
            # success = send_secret_deal_notification_task.retry(args=(secret_deal_data, ))
        else:
            api_logger.info(message='Secret deal notification task completed: %s' % (str(secret_deal_data)),
                            log_type='ingoibibo',
                            bucket='SecretDealAPI', stage='hotels.tasks.send_secret_deal_notification_task')
    except Exception as e:
        import traceback
        api_logger.critical(
            message='Secret deal notification task %s Exception occured %s %s' % (
                str(secret_deal_data), str(e), repr(traceback.format_exc())),
            log_type='ingoibibo',
            bucket='SecretDealAPI', stage='hotels.tasks.send_secret_deal_notification_task')


# TODO: uncomment this task after production celery task failure issue resolved.
# @app.task(name='modify_vcc_task')
# def modify_vcc_task(booking_id, cancellation_id, cancellation_charges=False):
#     from hotels.vcc_payment import update_booking_vcc_details
#     update_booking_vcc_details(booking_id, cancellation_id, cancellation_charges)
#     logger.info('%s\t%s\t%s\t%s\t%s' % ('hotels', 'modify_vcc_task',
#                                         'modifying vcc', booking_id, cancellation_id))


@app.task(name='ingoibibo.create_bulk_slots')
def create_bulk_slots_task(slot_bulk_data):
    """

    :param slot_bulk_data: A list of dictionaries
        [
            {'hotelcode': '1000011490', 'slot_start_time': '06:00:00',
            'slot_end_time': '11:00:00', 'to_inventory_date': '31-03-2017',
            'inventory': '2', 'from_inventory_date': '23-02-2017',
            'roomtypecode': '45000249910'},
            ...
        ]
    :return:
    """
    from hotels.models import LinkedRateRule
    from api.v1.rooms.resources.slot_room import create_room_slots
    from api.v1.rateplans.serializers import RatePlanDetailSerializer
    from extranet.propertydetails import has_child_rateplan, add_linked_raterule

    get_rateplan = False
    # CHECKUSERCHANGE - DONE
    api_consumer = User.objects.get(username="api_consumer")

    for data in slot_bulk_data:
        try:
            response = {
                "message": "",
                "success": False
            }
            slot_data = {}
            slot_data["slot_start_time"] = datetime.datetime.strptime(
                ":".join(data["slot_start_time"].split(":")[:2]), "%H:%M").strftime("%H:%M")
            slot_data["slot_end_time"] = datetime.datetime.strptime(
                ":".join(data["slot_end_time"].split(":")[:2]), "%H:%M").strftime(
                "%H:%M")
            slot_data["roomtypecode"] = data["roomtypecode"]

            response.update(create_room_slots(slot_data, return_overlapping_slot=True))

            logger.info('%s\t%s\t%s\t%s\t%s\tSlot Data: %s' % (
                'hotels', 'tasks', 'create_bulk_slots_task', 'Slot Creation response',
                response, slot_data))

            # Add Rateplan for Slot
            # Fetching roomtype for creating rateplan
            roomcode = None

            if response.get("error_code", "") == "slot_e_101":
                roomcode = response["slot"]["roomtypecode"]

                # Making response success True as we found existing slot and inventory
                # will get updated according to the new data
                response["success"] = True
                get_rateplan = True

            elif response["success"]:
                roomcode = response["message"]["roomtypecode"]
                get_rateplan = True

            rateplan_data = {}

            if get_rateplan:
                slot_rateplan = None
                try:
                    roomtype_obj = RoomDetail.objects.get(roomtypecode=roomcode)
                except:
                    roomtype_obj = RoomDetail.objects.using('default').get(roomtypecode=roomcode)
                rateplan_data = {
                    "hotelcode": data["hotelcode"],
                    "roomtype": roomtype_obj,
                    "rateplanname": "SLOT EP",
                    "mealplan": "EP",
                    "inclusions": [],
                    "isactive": True,
                    "nonrefundable": True,
                    "user": api_consumer.id,
                    "pay_at_hotel": 0,
                    "sellcommission": data.get("commission_value", None),
                    "taxincluded": data.get("tax_included_rates", False)
                }

                existing_rateplans = RatePlan.objects.filter(roomtype__roomtypecode=roomcode, isactive=True)
                if not existing_rateplans:

                    rateplan_serializer = RatePlanDetailSerializer(data=rateplan_data)
                    if rateplan_serializer.is_valid():
                        inclusions_list = rateplan_serializer.validated_data.pop('inclusions', [])
                        rateplan_object = RatePlan(**rateplan_serializer.validated_data)
                        rateplan_object.save(admin_save=True)
                        slot_rateplan = rateplan_object
                        logger.info('%s\t%s\t%s\t%s\t%s\tSlot Data: %s' % (
                            'hotels', 'tasks', 'create_bulk_slots_task', 'Rateplan added',
                            rateplan_object.rateplancode, slot_data))

                    else:
                        logger.critical('%s\t%s\t%s\t%s\t%s\tSlot Data: %s' % (
                            'hotels', 'tasks', 'create_bulk_slots_task',
                            'Rateplan addition serializer response', rateplan_serializer.errors,
                            slot_data))
                else:
                    slot_rateplan = existing_rateplans[0]

                if slot_rateplan:
                    # Creating linkage with the provided parent rateplan - if doesn't exist
                    linked_rateplan = slot_rateplan

                    if linked_rateplan.parent_id:
                        linked_rule = LinkedRateRule.objects.get(
                            linked_rateplan_id=linked_rateplan.id)

                        linked_rule.linkage_basis = "percent" if \
                            data["percentage"].lower() == "true" else "flat"
                        linked_rule.linkage_amount = int(data["value"])
                        linked_rule.save()

                    else:
                        data['linked_rateplan_id'] = slot_rateplan.id

                        parent_rateplan_code = data.get('parent_rateplancode')
                        parent_rateplan = RatePlan.objects.only('id', 'parent_id').get(
                            id=parent_rateplan_code)

                        data["extra_guest_linkage_amount"] = "0"
                        data["linked_rateplan_code"] = slot_rateplan.rateplancode
                        data["linked_rateplan_type"] = "linked"
                        data["linkage_type"] = "low"
                        data["linkage_basis"] = "percent" if data["percentage"].lower() == "true" else "flat"
                        data["linkage_amount"] = data["value"]
                        data["parent_rateplan_code"] = parent_rateplan.rateplancode
                        data["linked_rateplan_contract_type"] = "all"
                        data["parent_rateplan_contract_type"] = "all"
                        data['link_block'] = 'true'

                        if has_child_rateplan(slot_rateplan.id):
                            logger.critical(
                                '%s\t%s\t%s\t%s\t%s\tSlot Data: %s' % (
                                    'hotels', 'tasks', 'create_bulk_slots_task',
                                    'Linked Rateplan error: Error: another linked '
                                    'plan is already linked with current rateplan',
                                    slot_rateplan.rateplancode, slot_data))
                        else:
                            if parent_rateplan.parent_id == 0:
                                parent_updated = False
                                # update parent id, if there is change
                                if linked_rateplan.parent_id != parent_rateplan.id:
                                    old_parent_id = linked_rateplan.parent_id
                                    linked_rateplan.parent_id = parent_rateplan.id
                                    linked_rateplan.pay_at_hotel = parent_rateplan.pay_at_hotel
                                    linked_rateplan.save(admin_save=True)
                                    parent_updated = True

                                # add/update linked rate rule
                                linkage_creation_response = add_linked_raterule(data)
                                if linkage_creation_response['success']:
                                    logger.info(
                                        '%s\t%s\t%s\t%s\t%s\t%s\tSlot Data: %s' % (
                                            'hotels', 'tasks',
                                            'create_bulk_slots_task',
                                            'Linked Rateplan added',
                                            linked_rateplan.rateplancode,
                                            slot_rateplan.rateplancode, slot_data))
                                else:
                                    logger.critical(
                                        '%s\t%s\t%s\t%s\t%s\t%s\tSlot Data: %s | error %s' % (
                                            'hotels', 'tasks',
                                            'create_bulk_slots_task',
                                            'Linked Rateplan linkage rule error',
                                            linked_rateplan.rateplancode,
                                            slot_rateplan.rateplancode, slot_data,
                                            str(linkage_creation_response)))

                            else:
                                logger.critical('%s\t%s\t%s\t%s\tSlot Data: %s' % (
                                    'hotels', 'tasks', 'create_bulk_slots_task',
                                    'Linked Rateplan error: Parent rateplan is a linked rateplan in itself!',
                                    slot_data))

            if response["success"]:
                inventory_data = {
                    "inventory_action": "default_inventory",
                    "cutoff_filter": "hours",
                    "cutoff": -3,
                    "roomtypecode": roomcode,
                    "user": api_consumer
                }
                parent_room_type_code = data["roomtypecode"]

                no_of_results = (datetime.datetime.strptime(data["to_inventory_date"], "%Y-%m-%d") -
                                 datetime.datetime.strptime(data["from_inventory_date"], "%Y-%m-%d")).days + 1

                parent_inventory_availability_response = get_formatted_inventory_list(parent_room_type_code,
                                                                                      data["from_inventory_date"],
                                                                                      no_of_results)

                if parent_inventory_availability_response["success"]:
                    for i in range(len(parent_inventory_availability_response['inventory_list'])):
                        inventory_response = {"success": False}
                        try:
                            inventory_data['available'] = parent_inventory_availability_response['inventory_list'][i][
                                'available']
                            inventory_data['fromDate'] = parent_inventory_availability_response['inventory_list'][i][
                                'idate']
                            inventory_data['toDate'] = parent_inventory_availability_response['inventory_list'][i][
                                'idate']
                            inventory_response = update_room_inventory(inventory_data, inventory_response,
                                                                       is_uploader_flow=True)
                        except Exception as e:
                            inventory_logger.error(
                                message='Exception in updating inventory %s: %s' % (str(inventory_data), str(e)),
                                log_type='ingoibibo', bucket='create_bulk_slots_task', stage='create_bulk_slots_task')
                    response.update(
                        {"inventory_update": inventory_response["success"],
                         "inventory_message": inventory_response["message"]})

        except Exception as e:
            import traceback
            logger.critical('%s\t%s\t%s\t%s\tSlot Data: %s' % (
                'hotels', 'tasks', 'create_bulk_slots_task',
                'Unhandled Exception: %s | %s' % (str(e), repr(traceback.format_exc())), data))


@app.task(name='ingoibibo.save_image_task')
def save_image_task(image_data_json, user_id):
    image_data = {}
    try:
        tmp_image_path = "/tmp/dummy_%s.jpg" % datetime.datetime.now()
        image_response = requests.get(image_data_json['url'])
        if image_response.status_code == 200:
            image = PILImage.open(StringIO(image_response.content))
            image.save(tmp_image_path)

            image_file = open(tmp_image_path)

            returned_file_name = mdb_storage.save(image_file.name, image_file)

            image_data = {
                "isactive": 1,
                "content_type_id": image_data_json['content_type_id'],
                "object_id": image_data_json['object_id'],
                "image": returned_file_name,
                "img_src": "HOTEL_CREATE_API",
                "status": "approved",
                "img_order": image_data_json['imgorder'],
                "user_id": user_id,
                "caption": image_data_json.get('caption', '')
            }

            image_obj = Image(**image_data)
            image_obj.save()
            celery_logger.info('%s\t%s\t%s\t%s\t' % ('hotels', 'tasks',
                                                     'save_image_task  Image Saved To Table', str(image_data)))
            os.remove(tmp_image_path)
        else:
            raise Exception("Unable to fetch Image")
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s\t' % ('hotels', 'tasks',
                                                             'save_image_task', '', str(e),
                                                             repr(traceback.format_exc())))

        sendMail('', '', "{0}\n\n{1}\n\n{2}".format(str(e), repr(traceback.format_exc()), image_data),
                 "Celery Job save_image_task failed", '', settings.TECH_EMAIL, {}, [])


@celery.task(name="update_hotel_nav_task_v2")
def update_hotel_nav_task_v2(hotel_id, vendor_sync_flag=True, hotel_sync_flag=True):
    from hotels.navision_vendor_management import update_hotel_on_navision_v2, update_vendor_on_navision_v2
    from hotels.models import VendorMapping
    hotel_obj = HotelDetail.objects.using('default').get(id=hotel_id)
    mapped_hotel_objects = VendorMapping.objects.using('default').filter(
        isactive=True, hotel=hotel_obj, vendor__vendor_code__isnull=False)
    unique_req_resp_id = str(uuid.uuid4())
    if mapped_hotel_objects:
        for obj in mapped_hotel_objects:
            try:
                if obj.vendor:
                    bank_account_obj = obj.vendor.accounts.filter(isactive=True).last()
                    if bank_account_obj and vendor_sync_flag:
                        if settings.DEBUG:
                            update_vendor_on_navision_v2(obj.vendor, hotel_obj, bank_account_obj, update_vendor_flag=True,
                                                         unique_req_resp_id=unique_req_resp_id)
                        else:
                            update_vendor_on_nav_async(obj.vendor.id)
                    elif hotel_sync_flag:
                        if settings.DEBUG:
                            update_hotel_on_navision_v2(obj.vendor, hotel_obj, unique_req_resp_id)
                        else:
                            update_hotel_on_nav_async(obj.vendor, hotel_obj, unique_req_resp_id)
            except Exception as e:
                celery_logger.critical('Exception occurred while saving hotel on navision : %s unique_req_resp_id: %s'
                                       % (e, unique_req_resp_id))
    return


@celery.task(name="send_to_etl_task")
def send_to_etl_task(event_type, object, code, old_code=None):
    if not settings.ETL_OLD_STATIC_CONTENT_PIPELINE_FLAG:
        return
    from hotels.pushtoetl import EtlConnector
    celery_logger = send_to_etl_task.get_logger()
    etl_connector = EtlConnector()
    if event_type == 'Create' and object == 'Hotel':
        try:
            has_succeeded, msg = etl_connector.send_create_event_hotel(code)
            celery_logger.info('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'send_to_etl_task', 'Create Hotel', code, ' ',
                                                           'task completed'))
            return (has_succeeded, msg)
        except Exception as e:
            celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'send_to_etl_task', '',
                                                               str(e), repr(traceback.format_exc())))
    elif event_type == 'Update' and object == 'Hotel':
        if code is None:
            return False
        try:
            has_succeeded, msg = etl_connector.send_update_event_hotel(code)
            celery_logger.info('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'send_to_etl_task', 'Update Hotel', code, ' ',
                                                           'task completed'))
            return (has_succeeded, msg)
        except Exception as e:
            celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'send_to_etl_task', '',
                                                               str(e), repr(traceback.format_exc())))
    elif object == 'Room':
        try:
            etl_connector.send_all_event_room(event_type, code, old_code)
            celery_logger.info(
                '%s\t%s\t%s\t%s\t%s\t%s\t%s' % ('room', 'send_to_etl_task', event_type, code, old_code, ' ',
                                                'task completed'))
            return True
        except Exception as e:
            celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'send_to_etl_task', '',
                                                               str(e), repr(traceback.format_exc())))


@celery.task(name="send_usp_to_etl_task")
def send_usp_to_etl_task(payload):
    if not settings.ETL_OLD_STATIC_CONTENT_PIPELINE_FLAG:
        return
    try:
        from hotels.pushtoetl import EtlConnector
        celery_logger = send_usp_to_etl_task.get_logger()
        etl_connector = EtlConnector()

        has_succeeded, msg = etl_connector.send_update_usp_event(payload)
        celery_logger.info('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'send_usp_to_etl_task', 'Update USP', payload, ' ',
                                                       'task completed'))
        return (has_succeeded, msg)
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'send_usp_to_etl_task', '',
                                                           str(e), repr(traceback.format_exc())))


@celery.task(name="send_image_etl_task")
def send_image_etl_task(event_type, image_id):
    if not settings.ETL_OLD_STATIC_CONTENT_PIPELINE_FLAG:
        return
    from hotels.pushtoetl import EtlConnector
    celery_logger = send_image_etl_task.get_logger()
    etl_connector = EtlConnector()

    try:
        etl_connector.send_event_image(event_type, image_id)
        celery_logger.info('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'send_image_etl_task', 'image_id', image_id, ' ',
                                                       'task completed'))
        return True
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'send_image_etl_task', '',
                                                           str(e), repr(traceback.format_exc())))


@celery.task(name="send_dedup_image_etl_task")
def send_dedup_image_etl_task(event_type, image_id, mmt_img_id):
    if not settings.ETL_OLD_STATIC_CONTENT_PIPELINE_FLAG:
        return
    from hotels.pushtoetl import EtlConnector
    celery_logger = send_dedup_image_etl_task.get_logger()
    etl_connector = EtlConnector()

    try:
        etl_connector.send_event_image(event_type, image_id, mmt_img_id=mmt_img_id)
        celery_logger.info('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'send_dedup_image_etl_task', 'image_id', image_id, ' ',
                                                       'task completed'))
        return True
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'send_dedup_image_etl_task', '',
                                                           str(e), repr(traceback.format_exc())))


@celery.task(name="send_and_create_amenity_to_etl_task")
def send_and_create_amenity_to_etl_task(event_type, etl_object, amenity_obj):
    from hotels.pushtoetl import EtlConnector
    celery_logger = send_and_create_amenity_to_etl_task.get_logger()
    etl_connector = EtlConnector()
    try:
        etl_connector.send_event_amenity(action_type=event_type, etl_object=etl_object, amenity_obj=amenity_obj)
        celery_logger.info('%s\t%s\t%s\t%s\t%s' % ('hotels', 'send_and_create_amenity_to_etl_task', event_type,
                                                   etl_object, amenity_obj, 'task completed'))
        return True
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'send_and_create_amenity_to_etl_task',
                                                           amenity_obj, str(e), repr(traceback.format_exc())))


@celery.task(name="send_amenity_etl_task")
def send_amenity_etl_task(event_type, object, code, facility_to_add, facility_to_delete):
    if not settings.ETL_OLD_STATIC_CONTENT_PIPELINE_FLAG:
        return
    from hotels.pushtoetl import EtlConnector
    celery_logger = send_image_etl_task.get_logger()
    etl_connector = EtlConnector()
    if object == 'Room':
        try:
            etl_connector.send_event_facility(event_type, room_code=code, facility_changed_id_list=facility_to_add,
                                              facility_to_remove=facility_to_delete)
            celery_logger.info(
                '%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'send_amenity_etl_task', 'room_id', code, facility_to_add,
                                            'task completed'))
            return True
        except Exception as e:
            celery_logger.critical(
                '%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'send_amenity_etl_task', facility_to_add,
                                            str(e), repr(traceback.format_exc())))
    elif object == 'Hotel':
        try:
            etl_connector.send_event_facility(event_type, hotel_code=code, facility_changed_id_list=facility_to_add,
                                              facility_to_remove=facility_to_delete)
            celery_logger.info('%s\t%s\t%s\t%s\t%s\t%s' % (
                'hotels', 'send_amenity_etl_task', code, facility_to_delete, facility_to_add,
                'task completed'))
            return True
        except Exception as e:
            celery_logger.critical(
                '%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'send_amenity_etl_task', facility_to_add,
                                            str(e), repr(traceback.format_exc())))


# TODO: uncomment this task after production celery task failure issue resolved.
# @app.task(name='ingoibibo.revive_inventory_pro_booking')
# def revive_inventory_pro_booking_task(pro_booking_id, revive_type='revert_booking', add_on_inventory_update=False):
#     revive_status = False
#     try:
#         from hotels.models import HotelProBooking
#         from hotels.hotelinventory import HotelInventoryRules
#         hir = HotelInventoryRules()
#
#         pro_booking = HotelProBooking.objects.using('default').get(id=pro_booking_id)
#         inventory_info_daywise = eval(pro_booking.pricebreakup).get('inventory_info_daywise', [])
#         static_copy_ari_data = eval(pro_booking.pricebreakup).get('static_copy_ari_data', {})
#         is_prebuy = json.loads(pro_booking.misc).get('is_prebuy', False)
#
#         try:
#             pro_booking_meta_info = HotelProBookingMetaInfo.objects.using('default').get(pro_booking=pro_booking)
#             is_inventory_revived = pro_booking_meta_info.is_inventory_revived
#         except HotelProBookingMetaInfo.DoesNotExist:
#             pro_booking_meta_info = None
#             is_inventory_revived = True
#
#         celery_logger.info('%s\t%s\t%s\t%s\t%s\t%s\t' % ('hotels', 'tasks',
#                                                          'revive_inventory_pro_booking pro booking fetched',
#                                                          pro_booking_id,
#                                                          pro_booking.bookingconfirmationflag,
#                                                          is_inventory_revived))
#
#         if pro_booking_meta_info and not pro_booking.bookingconfirmationflag and not is_inventory_revived:
#             inventory_action = revive_type
#             inventory_count = pro_booking.noofrooms
#             room_code = pro_booking.roomtype.roomtypecode
#             room_name = pro_booking.roomtype.roomtypename
#             from_date = pro_booking.checkin
#             to_date_plus_one = pro_booking.checkout
#             hotel = pro_booking.hotel
#             user_id = pro_booking.user.id if pro_booking.user else None
#             to_date = to_date_plus_one - datetime.timedelta(days=1)
#             uname = 'UNHOLD' if revive_type == 'unhold' else 'REVERT BOOKING'
#
#             time_now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
#             # Inventory action has been overridden back irrespective of un-hold
#             # since code is very coupled with 'revert_booking' string.
#             # un-hold is just being used for logging inventory logs.
#             inventory_action = 'revert_booking'
#             response = hir._updateInventory(inventory_action, room_code, from_date,
#                                             to_date_plus_one, inventory_count, uname, user_id,
#                                             pro_booking.payathotelflag, inventory_info_daywise, is_prebuy, booking_type='PRO_BOOKING', booking_id=pro_booking_id, static_copy_ari_data=static_copy_ari_data)
#
#             celery_logger.info('%s\t%s\t%s\t%s\t%s\t%s\t%s\t' % ('hotels', 'tasks',
#                                                                  'revive_inventory_pro_booking inventory update status',
#                                                                  pro_booking_id,
#                                                                  pro_booking.bookingconfirmationflag,
#                                                                  is_inventory_revived, response))
#
#             push_change_history_for_bookings(inventory_action, inventory_count,
#                                              user_id, hotel.id, uname, from_date, to_date,
#                                              room_name, room_code, time_now, inventory_info_daywise,
#                                              booking_id=pro_booking_id, booking_type='PRO_BOOKING',
#                                              inventory_response=response['success'])
#
#             if response['success']:
#                 is_inventory_revived = True
#                 if pro_booking_meta_info:
#                     pro_booking_meta_info.is_inventory_revived = is_inventory_revived
#                     pro_booking_meta_info.save()
#
#                 celery_logger.info('%s\t%s\t%s\t%s\t%s\t%s\t%s\t' %
#                                    ('hotels', 'tasks', 'revive_inventory_pro_booking inventory update success',
#                                     pro_booking_id,
#                                     pro_booking.bookingconfirmationflag,
#                                     is_inventory_revived, response))
#                 revive_status = True
#             else:
#                 sendMail(
#                     "{0}\n\n{1}".format('Inventory Update Failed when pro booking is not confirmed %s' % pro_booking_id,
#                                         'Pro Booking ID %s\n\n%s' % (pro_booking_id, response)),
#                     "Booking Celery Job revive_inventory_pro_booking Inventory Update failed",
#                     '', '', settings.TECH_EMAIL, '', from_email_id=settings.EMAIL_ALERT_SENDER)
#
#             if add_on_inventory_update and pro_booking.add_ons_info and pro_booking.add_ons_info['success']:
#                 add_on_booking_data_list = pro_booking.add_ons_info['add_on_booking_data_list']
#                 for add_on_booking_data in add_on_booking_data_list:
#                     add_on_type = add_on_booking_data['add_on_info']['type']
#                     if add_on_type == 'Early Checkin' or add_on_type == 'Late Checkout':
#                         if add_on_type == 'Early Checkin':
#                             to_date = from_date
#                             from_date = from_date - datetime.timedelta(days=1)
#                         else:
#                             from_date = to_date_plus_one
#                             to_date = to_date_plus_one + datetime.timedelta(days=1)
#                         inventory_count = int(add_on_booking_data['units'])
#                         response = hir._updateInventory(inventory_action, room_code, from_date,
#                                                         to_date, inventory_count, uname, user_id,
#                                                         pro_booking.payathotelflag, static_copy_ari_data=static_copy_ari_data)
#                         if not response['success']:
#                             sendMail("{0}\n\n{1}".format('Inventory Update Failed when pro booking is not confirmed %s'
#                                                          % pro_booking_id,
#                                                          'Pro Booking ID %s\n\n%s' % (pro_booking_id, response)),
#                                      "For Add On Booking Celery Job revive_inventory_pro_booking Inventory Update failed",
#                                      '', '', settings.TECH_EMAIL, '', from_email_id=settings.EMAIL_ALERT_SENDER)
#
#
#     except Exception as e:
#         celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s\t' % ('hotels', 'tasks',
#                                                              'revive_inventory_pro_booking', pro_booking_id, str(e),
#                                                              repr(traceback.format_exc())))
#         subject = "Booking Celery Job revive_inventory_pro_booking failed : %s" % (pro_booking_id)
#         sendMail("{0}\n\n{1}".format(str(e), repr(traceback.format_exc())), subject, '', '',
#                  settings.TECH_EMAIL, '', from_email_id=settings.EMAIL_ALERT_SENDER)
#
#     return revive_status
#

@app.task(name="ingoibibo.update_to_new_offer_model")
def update_to_new_offer_model():
    try:
        celery_logger.info("%s\t%s\t%s\t%s" % (
            "hotels", "tasks", "update_to_new_offer_model",
            "Updating to New Offer"))


    except Exception as e:
        celery_logger.critical("%s\t%s\t%s\t%s\t%s" % (
            "hotels", "tasks", "update_to_new_offer_model", str(e), repr(traceback.format_exc()),))


'''
@app.task(name="ingoibibo.push_message_to_ap_task")
def push_message_to_ap_task(topic, message):
    try:
        from hotels.ap_integration import push_message_to_ap
        start = time.clock()
        push_message_to_ap(topic, message)
        time_taken = time.clock() - start
        celery_logger.info("%s\t%s\t%s\t%s\t%s\t%s\t%s"
                           % ("hotels", "tasks", "push_message_to_ap_task", time_taken,
                              "AP MESSAGE PUSHED", message, topic))
    except Exception as e:
        celery_logger.critical("%s\t%s\t%s\t%s\t%s" % (
            "hotels", "tasks", "push_message_to_ap_task", str(e), repr(traceback.format_exc()),))


@app.task(name="ingoibibo.push_message_to_ap_kerb_task")
def push_message_to_ap_kerb_task(topic, message):
    try:
        global redis_conn
        if not redis_conn:
            host = settings.AP_TASK_QUEUE_CONFIG['HOST']
            port = settings.AP_TASK_QUEUE_CONFIG['PORT']
            db = settings.AP_TASK_QUEUE_CONFIG['DB']
            redis_conn = redis.Redis(host=host, port=port, db=db)
        cache_key = settings.AP_TASK_QUEUE_CONFIG['KEY']
        redis_conn.rpush(cache_key, msgpack.packb((topic, message)))
        celery_logger.info("%s\t%s\t%s\t%s\t%s\t%s"
                           % ("hotels", "tasks", "push_message_to_ap_kerb_task",
                              "AP MESSAGE PUSHED", message, topic))
    except Exception as e:
        celery_logger.critical("%s\t%s\t%s\t%s\t%s" % (
            "hotels", "tasks", "push_message_to_ap_kerb_task", str(e), repr(traceback.format_exc()),))


def redis_fetch(client, key, count):
    pipe = client.pipeline()
    pipe.lrange(key, 0, count-1)
    pipe.ltrim(key, count, -1)
    responses = pipe.execute()
    items = responses[0]
    return items
'''


def redis_fetch_all_keys(client, key):
    pipe = client.pipeline()
    pipe.lrange(key, 0, -1)
    pipe.ltrim(key, -1, 0)
    responses = pipe.execute()
    items = responses[0]
    return items

def segregate_items_for_gds_cache_updates(items):
    dayuse_items = []
    fullday_items = []
    hoteltravel_items = []
    soldout_dayuse_items = []
    soldout_fullday_items = []
    soldout_hoteltravel_items = []

    for elem in items:
        elem_dict = ast.literal_eval(elem)
        if elem_dict.get("payload", "").get("eventType", "") in ["InventoryBulkUpdate", "RestrictionsBulkUpdate"]:
            platform_list = elem_dict.get("payload", "").get("platform", "")
            if platform_list:
                for platform in platform_list:
                    if platform == PLATFORM_DAYUSE:
                        updated_elem_dict = copy.deepcopy(elem_dict)
                        updated_elem_dict["payload"]["platform"] = [PLATFORM_DAYUSE]
                        soldout_dayuse_items.append(updated_elem_dict)
                    elif platform == PLATFORM_HOTEL_TRAVEL:
                        updated_elem_dict = copy.deepcopy(elem_dict)
                        updated_elem_dict["payload"]["platform"] = [PLATFORM_HOTEL_TRAVEL]
                        soldout_hoteltravel_items.append(updated_elem_dict)
                    else:
                        updated_elem_dict = copy.deepcopy(elem_dict)
                        updated_elem_dict["payload"]["platform"] = [PLATFORM_FULLDAY]
                        soldout_fullday_items.append(updated_elem_dict)
            else:
                updated_elem_dict = copy.deepcopy(elem_dict)
                updated_elem_dict["payload"]["platform"] = [PLATFORM_FULLDAY]
                soldout_fullday_items.append(updated_elem_dict)
        else:
            platform_list = elem_dict.get("payload", "").get("platform", "")
            if platform_list:
                for platform in platform_list:
                    if platform == PLATFORM_DAYUSE:
                        updated_elem_dict = copy.deepcopy(elem_dict)
                        updated_elem_dict["payload"]["platform"] = [PLATFORM_DAYUSE]
                        dayuse_items.append(updated_elem_dict)
                    elif platform == PLATFORM_HOTEL_TRAVEL:
                        updated_elem_dict = copy.deepcopy(elem_dict)
                        updated_elem_dict["payload"]["platform"] = [PLATFORM_HOTEL_TRAVEL]
                        hoteltravel_items.append(updated_elem_dict)
                    else:
                        updated_elem_dict = copy.deepcopy(elem_dict)
                        updated_elem_dict["payload"]["platform"] = [PLATFORM_FULLDAY]
                        fullday_items.append(updated_elem_dict)
            else:
                updated_elem_dict = copy.deepcopy(elem_dict)
                updated_elem_dict["payload"]["platform"] = [PLATFORM_FULLDAY]
                fullday_items.append(updated_elem_dict)

    return dayuse_items, fullday_items, hoteltravel_items, soldout_dayuse_items, soldout_fullday_items, soldout_hoteltravel_items

def update_platform(item_dict, item_type):
    item_dict["payload"]["platform"] = [PLATFORM_FULLDAY]
    if item_type in ["hoteltravel", "soldout_hoteltravel"]:
        item_dict["payload"]["platform"] = [PLATFORM_HOTEL_TRAVEL]
    return item_dict

def push_item_to_topic(item_type, merged_updates, topic_name):
    try:
        from confluent_kafka import Producer
        batch_size = settings.AP_TASK_QUEUE_CONFIG['BATCH_SIZE']
        timeout = settings.AP_TASK_QUEUE_CONFIG['KAFKA_TIMEOUT_SECONDS']
        mmt_cache_event_server = settings.KAFKA_SERVER_CONF["servers"]["mmt"]["HOST"][0]
        mmt_cache_event_producer = Producer({
            'bootstrap.servers': mmt_cache_event_server,
            'acks': '0'
        })

        for count in xrange(0, len(merged_updates), batch_size):
            items_in_a_batch = merged_updates[count:count + batch_size]
            for item in items_in_a_batch:
                item = update_platform(item, item_type)
                item["payload"]["eventTriggerTime"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                try:
                    price_change = PriceCacheEventDataNew()
                    dict_to_protobuf(item['payload'], price_change)
                    mmt_cache_event_producer.produce(topic_name, price_change.SerializeToString())
                except Exception as e:
                    celery_logger.critical("%s\t%s\t%s\t%s\t%s\t%s" % (
                        "hotels", "tasks", "push_message_to_ap_kafka_task_from_%s_cache_raw_events"%(item_type), str(e),
                        repr(traceback.format_exc()), item['payload'],))
            mmt_cache_event_producer.flush(timeout)
    except Exception as e:
        celery_logger.critical("%s\t%s\t%s\t%s\t%s\t%s" % (
                "hotels", "tasks", str(item_type), "push_item_to_topic", str(e), repr(traceback.format_exc()),))

def merge_items_and_push_to_topic(item_type, item_list, topic_name):
    from hotel_store.tasks import update_hotelstore_object

    try:
        merged_updates, hotelcode_list = update_hotelstore_object.merge_ap_update_keys(item_list)
        celery_logger.info("%s\t%s\t%s\t%s" % ("hotels", "tasks", "push_message_to_ap_kafka_task",
                                                "AP MESSAGE keys reduction for %s from %s to %s hotelcode_list %s "
                                                "merged updates %s" % (item_type,
                                                    len(item_list), len(merged_updates),
                                                    hotelcode_list, merged_updates)))

        push_item_to_topic(item_type, merged_updates, topic_name)

        celery_logger.info("%s\t%s\t%s\t%s\t%s" % ("hotels", "tasks", "push_message_to_ap_kafka_task",
                                                    "AP MESSAGE %s PUSHED MESSAGES"%(item_type), len(merged_updates)))
    except Exception as e:
            celery_logger.critical("%s\t%s\t%s\t%s\t%s\t%s" % (
                "hotels", "tasks", str(item_type), "merge_items_and_push_to_topic", str(e), repr(traceback.format_exc()),))


@app.task(name="ingoibibo.push_message_to_ap_kafka_task")
def push_message_to_ap_kafka_task():
    try:
        celery_logger.info("%s\t%s\t%s\t%s" % ("hotels", "tasks", "push_message_to_ap_kafka_task",
                                               "AP MESSAGE PUSH STARTING"))
        from confluent_kafka import Producer

        cache_key = settings.AP_TASK_QUEUE_CONFIG['KEY']

        from hotel_store.tasks import update_hotelstore_object
        redis_conn = update_hotelstore_object.ap_r_cli
        items = redis_fetch_all_keys(redis_conn, cache_key)

        dayuse_cache_topic = settings.AP_TASK_QUEUE_CONFIG['KAFKA_TOPIC_DAYUSE_CACHE_EVENT']
        fullday_cache_topic = settings.AP_TASK_QUEUE_CONFIG['KAFKA_TOPIC_FULLDAY_CACHE_EVENT']
        soldout_cache_topic = settings.AP_TASK_QUEUE_CONFIG['KAFKA_TOPIC_SOLDOUT_CACHE_EVENT']

        dayuse_items, fullday_items, hoteltravel_items, soldout_dayuse_items, soldout_fullday_items, soldout_hoteltravel_items = segregate_items_for_gds_cache_updates(items)
        # 1. for non soldout dayuse
        merge_items_and_push_to_topic(PLATFORM_DAYUSE, dayuse_items, dayuse_cache_topic)

        # 2. For non soldout fullday
        merge_items_and_push_to_topic("fullday", fullday_items, fullday_cache_topic)

        # 3. For non soldout hoteltravel
        merge_items_and_push_to_topic("hoteltravel", hoteltravel_items, fullday_cache_topic)

        # 4. for soldout dayuse
        merge_items_and_push_to_topic("soldout_dayuse", soldout_dayuse_items, dayuse_cache_topic)

        # 5. For soldout fullday
        merge_items_and_push_to_topic("soldout_fullday", soldout_fullday_items, soldout_cache_topic)

        # 6. For soldout hoteltravel
        merge_items_and_push_to_topic("soldout_hoteltravel", soldout_hoteltravel_items, soldout_cache_topic)

    except Exception as e:
        celery_logger.critical("%s\t%s\t%s\t%s\t%s" % (
            "hotels", "tasks", "push_message_to_ap_kafka_task", str(e), repr(traceback.format_exc()),))

@app.task(name="ingoibibo.push_message_to_ap_kafka_task_soldout_normal")
def push_message_to_ap_kafka_task_soldout_normal():
    try:
        cache_key = settings.ARI_TO_GDS_UPDATE_TASK_REDIS_CONFIG['SOLDOUT_NORMAL_KEY']
        soldout_normal_cache_topic = settings.AP_TASK_QUEUE_CONFIG['KAFKA_TOPIC_SOLDOUT_CACHE_EVENT']
        common_push_message_to_ap_kafka(cache_key, soldout_normal_cache_topic,'push_message_to_ap_kafka_task_soldout_normal', 'Soldout Normal')
    except Exception as e:
        celery_logger.critical("%s\t%s\t%s\t%s\t%s" % (
            "hotels", "tasks", "push_message_to_ap_kafka_task_soldout_normal", str(e), repr(traceback.format_exc()),))

@app.task(name="ingoibibo.push_message_to_ap_kafka_task_price_change_normal")
def push_message_to_ap_kafka_task_price_change_normal():
    try:
        cache_key = settings.ARI_TO_GDS_UPDATE_TASK_REDIS_CONFIG['PRICE_CHANGE_NORMAL_KEY']
        price_change_normal_cache_topic = settings.AP_TASK_QUEUE_CONFIG['KAFKA_TOPIC_FULLDAY_CACHE_EVENT']
        common_push_message_to_ap_kafka(cache_key, price_change_normal_cache_topic,'push_message_to_ap_kafka_task_price_change_normal', 'Price Change Normal')
    except Exception as e:
        celery_logger.critical("%s\t%s\t%s\t%s\t%s" % (
            "hotels", "tasks", "push_message_to_ap_kafka_task_price_change_normal", str(e), repr(traceback.format_exc()),))

@app.task(name="ingoibibo.push_message_to_ap_kafka_task_soldout_priority")
def push_message_to_ap_kafka_task_soldout_priority():
    try:
        cache_key = settings.ARI_TO_GDS_UPDATE_TASK_REDIS_CONFIG['SOLDOUT_PRIORITY_KEY']
        soldout_priority_cache_topic = settings.AP_TASK_QUEUE_CONFIG['KAFKA_TOPIC_SOLDOUT_CACHE_EVENT']
        common_push_message_to_ap_kafka(cache_key, soldout_priority_cache_topic,'push_message_to_ap_kafka_task_soldout_priority', 'Soldout Priority')
    except Exception as e:
        celery_logger.critical("%s\t%s\t%s\t%s\t%s" % (
            "hotels", "tasks", "push_message_to_ap_kafka_task_soldout_priority", str(e), repr(traceback.format_exc()),))

@app.task(name="ingoibibo.push_message_to_ap_kafka_task_price_change_priority")
def push_message_to_ap_kafka_task_price_change_priority():
    try:
        cache_key = settings.ARI_TO_GDS_UPDATE_TASK_REDIS_CONFIG['PRICE_CHANGE_PRIORITY_KEY']
        price_change_priority_cache_topic = settings.AP_TASK_QUEUE_CONFIG['KAFKA_TOPIC_FULLDAY_CACHE_EVENT']
        common_push_message_to_ap_kafka(cache_key, price_change_priority_cache_topic,'push_message_to_ap_kafka_task_price_change_priority', 'Price Change Priority')
    except Exception as e:
        celery_logger.critical("%s\t%s\t%s\t%s\t%s" % (
            "hotels", "tasks", "push_message_to_ap_kafka_task_price_change_priority", str(e), repr(traceback.format_exc()),))

@app.task(name="ingoibibo.push_lmr_filter_data_to_hotstore")
def push_lmr_filter_data_to_hotstore():
    try:
        celery_logger.info("%s\t%s\t%s\t%s" % (
            "hotels", "tasks", "push_lmr_filter_data_to_hotstore", "STARTING TASK"))
        from promotion_v2.grpc_promo_genie_client import PromoGenieClient
        promo_genie_client = PromoGenieClient()
        correlation_key = str(uuid.uuid4())
        request_data = {"push_all_hotel_events":True,"correlation_key":correlation_key}
        metadata = [('userid', str(108860)), ('platform', 'BULK_UPDATE'), ('source', 'ingo_admin'),
                    ('language', 'eng'), ('country', 'in')]
        response = promo_genie_client.update_lmr_events(request_data,metadata)
        celery_logger.info("%s\t%s\t%s\t%s\t%s" % (
            "hotels", "tasks", "push_lmr_filter_data_to_hotstore", "PUSHED TO HOTSTORE", response))
    except Exception as e:
        celery_logger.critical("%s\t%s\t%s\t%s\t%s" % (
            "hotels", "tasks", "push_lmr_filter_data_to_hotstore", str(e), repr(traceback.format_exc()),))

@app.task(name="ingoibibo.confirm_pre_buy_bookings")
def confirm_pre_buy_bookings():
    try:
        filter_date = datetime.date.today() - datetime.timedelta(days=1)
        # CHECKUSERCHANGE - DONE
        user_obj = User.objects.get(username='advancepay')
        bobjs = HotelBooking.objects.using('default').filter(
            pre_buy_flag=True, confirmstatus='pending', checkout=filter_date,
            paymentstatus__isnull=True)
        data_dict = {'confirm': {'reason': 'pre_buy_confirm', 'by': user_obj}, 'misc': {'src': 11}}

        for bobj in bobjs.iterator():
            bobj.confirmstatus = 'confirmed'
            bobj.paymentstatus = 'pending'
            hm.updateBookedProcessInfo(bobj, data_dict)
            bobj.save()

    except Exception as e:
        celery_logger.critical("%s\t%s\t%s\t%s\t%s" % (
            "hotels", "tasks", "confirm_pre_buy_bookings", str(e), repr(traceback.format_exc()),))


@app.task(name="ingoibibo.upload_gst_csv")
def upload_gst_csv(filename, user):
    from lib.MDBStorage import MDBStorage
    from reports.upload_reports import add_gst_details
    mdb = MDBStorage()
    mapping_csv = mdb.open(filename)
    add_gst_details(mapping_csv, user)


@app.task(name="ingoibibo.create_pah_sku_task")
def create_pah_sku_task(hotel_codes_list, status_list, user, total_batch, uploaded_file_name='', request_data=None):
    from reports.pah_handling import create_linked_rateplan, update_linked_rateplan
    email_id = user.email
    rateplan_list = []
    rateplan_id_list = []
    visited_parent_rateplan = dict()

    base_paynow_rateplan_list = RatePlan.objects.filter(pay_at_hotel=0, parent_id=0, isactive=True,
                                                        roomtype__hotel__hotelcode__in=hotel_codes_list)
    linked_pah_rateplan_list = RatePlan.objects.filter(pay_at_hotel=1, parent_id__gt=0,
                                                       roomtype__hotel__hotelcode__in=hotel_codes_list)
    for rateplan in base_paynow_rateplan_list:
        linked_rateplans = filter(lambda x: x.parent_id == rateplan.id and x.mealplan == rateplan.mealplan,
                                  linked_pah_rateplan_list)
        if not linked_rateplans:
            rateplan_list.append(rateplan)
            rateplan_id_list.append(rateplan.id)
        else:
            '''Check if any linked rateplan is active then do nothing'''
            active_linked_rateplans = filter(lambda x: x.isactive == True, linked_rateplans)
            '''if linkedrateplan(s) exists for baserateplan but currently inactive'''
            if not active_linked_rateplans:
                is_updated = update_linked_rateplan(linked_rateplans[0], rateplan, user, uploaded_file_name,
                                                    request_data=request_data)
                if not is_updated:
                    rateplan_list.append(rateplan)
                    rateplan_id_list.append(rateplan.id)

    # Computing the logic for such rateplan which are on paynow and has parent rateplan
    linked_paynow_rateplan_list = RatePlan.objects.filter(pay_at_hotel=0, isactive=True, parent_id__gt=0,
                                                          roomtype__hotel__hotelcode__in=hotel_codes_list)
    for rateplan in linked_paynow_rateplan_list:
        if visited_parent_rateplan.get(rateplan.parent_id, False):
            continue;
        visited_parent_rateplan[rateplan.parent_id] = True
        linked_rateplans = filter(lambda x: x.parent_id == rateplan.parent_id and x.mealplan == rateplan.mealplan,
                                  linked_pah_rateplan_list)
        if not linked_rateplans:
            rateplan_list.append(rateplan)
            rateplan_id_list.append(rateplan.id)
        else:
            active_linked_rateplans = filter(lambda x: x.isactive == True, linked_rateplans)
            if not active_linked_rateplans:
                is_updated = update_linked_rateplan(linked_rateplans[0], rateplan, user, uploaded_file_name,
                                                    request_data)
                if not is_updated:
                    rateplan_list.append(rateplan)
                    rateplan_id_list.append(rateplan.id)

    if rateplan_list:
        create_linked_rateplan(rateplan_list, rateplan_id_list, user, uploaded_file_name, request_data)
    try:
        list_length = len(status_list)
        file_path = "/tmp/pah_status_%d.csv" % datetime.datetime.now().microsecond

        with open(file_path, 'wb') as email_file:
            csv_writer = csv.writer(email_file)
            csv_writer.writerows(status_list)

        email_file.close()

        subject = "PAH modification file"
        email_content = "PFA the file for further details, pah enabled for %d hotels. " \
                        "File uploaded is saved as %s " % (list_length, uploaded_file_name)

        sendMail('', '', email_content, subject, '', [email_id, '<EMAIL>'],
                 attached_file=email_file, bcc_emails=[])
    except Exception as e:
        logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'pah_handling', 'disable_pah_hotels', 'could not write to csv', str(e),
            repr(traceback.format_exc())))


@app.task(name="ingoibibo.back_payment")
def update_transacton_status():
    celery_logger.info('%s\t%s\t%s' % (
        'hotels', 'tasks', 'update_transacton_status Started'))
    from hotels.navision_payment import create_master_dict2
    try:
        create_master_dict2()
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'update_transacton_status End:', str(e),
            repr(traceback.format_exc())))


@app.task(name="ingoibibo.mmt_back_payment")
def update_mmt_transacton_status():
    celery_logger.info('%s\t%s\t%s' % (
        'hotels', 'tasks', 'update_mmt_transacton_status Started'))
    from hotels.navision_payment import create_master_dict2
    try:
        create_master_dict2(vendor='makemytrip')
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'update_transacton_status End:', str(e),
            repr(traceback.format_exc())))


@app.task(name="ingoibibo.send_hotel_payment_info_mail")
def send_hotel_payment_info_mail(hop):
    from communication.hotelMailsSMS import sendHotelPaymentInfoMail
    try:
        sendHotelPaymentInfoMail(hop)
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'update_transacton_status End:', str(e),
            repr(traceback.format_exc())))


@app.task(name="ingoibibo.update_mandatory_fees")
def update_mandatory_fees():
    celery_logger.info('%s\t%s\t%s' % (
        'hotels', 'tasks', 'update_mandatory_fees Started'))
    try:
        pull_and_update_mandatory_fees()
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'update_mandatory_fees End:', str(e),
            repr(traceback.format_exc())))


@app.task(name="ingoibibo.send_secret_deal_expiry_notification")
def send_secret_deal_expiry_notification():
    today = datetime.date.today()
    yesterday = today - datetime.timedelta(days=1)
    active_day = today + datetime.timedelta(days=3)
    secret_deal_hotels = SecretDealBounced.objects.filter(Q(checkin_end_date__lte=yesterday) |
                                                          Q(checkin_end_date__lte=(today + datetime.timedelta(days=3)),
                                                            checkin_end_date__gte=(today))).values_list('hotel_id',
                                                                                                        flat=True).distinct()
    active_secret_deal_hotels = SecretDealBounced.objects.filter(checkin_end_date__gt=active_day,
                                                                 is_active=True).values_list('hotel_id', flat=True)
    hotels_without_active_deals = list(set(secret_deal_hotels) - set(active_secret_deal_hotels))
    send_secret_deals_expiry_alerts(hotels_without_active_deals)


def send_secret_deals_expiry_alerts(hotels):
    today = datetime.date.today()
    yesterday = today - datetime.timedelta(days=1)
    for hotel in hotels:
        secret_deal = SecretDealBounced.objects.filter(hotel_id=hotel).latest('checkin_end_date')
        if secret_deal.checkin_end_date <= yesterday:
            expiry = 'has expired'
        else:
            expiry = 'is expiring'
        send_secret_deal_mail_alert.apply_async(args=(expiry, hotel, secret_deal))


@app.task(name="send_secret_deal_mail_alert")
def send_secret_deal_mail_alert(expiry, hotel, secret_deal_obj):
    booking_count = HotelBooking.objects.filter(hotel=hotel, secret_deal_applied=True).count()
    send_secret_deal_expiry_mail(booking_count, secret_deal_obj, expiry)


@app.task(name="ingoibibo.ingo_voyager_communication")
def ingo_voyager_communication_using_kinesis():
    update_ingo_from_voyager_kinesis_response()


@app.task(name="ingoibibo.create_add_ons_task")
def create_add_ons_task(filename, email_id=None, user=None):
    try:
        saved_file = mdb_storage.open(filename)
        file_content = saved_file.read()
        db_data = validate_addon_csv_data(file_content, email_id, user)
        data_size = len(db_data)
        if data_size > 0:
            start_index = 0
            batch_size = 100
            end_index = batch_size
            while start_index < data_size:
                temp_addon_data = db_data[start_index:end_index]
                if settings.HOST in settings.PROD_HOSTS:
                    create_bulk_add_ons.apply_async(args=(temp_addon_data,))
                else:
                    create_bulk_add_ons(temp_addon_data)
                start_index = end_index
                end_index = start_index + batch_size
        mdb_storage.delete(filename)
    except Exception as e:
        mdb_storage.delete(filename)
        log_msg = ("Error in create_add_ons_task %s, %s") % (e, repr(traceback.format_exc()))
        inventory_logger.critical(log_msg, log_type="ingoibibo",
                                  bucket="add_on_bulk_upload", stage="create_add_ons_task")
        sendMail('', '', log_msg, 'ERROR - in create_add_ons_task', '', settings.INGOIBIBO_ERROR_ALERT, '', [])


@app.task(name="ingoibibo.save_add_ons_task")
def create_bulk_add_ons(addons_data):
    try:
        AddOn.objects.bulk_create(addons_data)
    except Exception as e:
        log_msg = ("Error in bulk creating addons with exception %s, %s"
                   ) % (e, repr(traceback.format_exc()))
        inventory_logger.critical(log_msg, log_type="ingoibibo",
                                  bucket="add_on_bulk_upload",
                                  stage="send_hotel_ledger_report")
        sendMail('', '', log_msg, 'ERROR - in bulk updating addons', '', settings.INGOIBIBO_ERROR_ALERT, {}, [])


def validate_addon_csv_data(file_content, email_id, user):
    db_data = []
    error_data = []
    start_time = None
    end_time = None
    add_on_levels = {1: 'hotel', 2: 'roomdetail', 3: 'rateplan'}
    add_on_types = {1: 'early_cin', 2: 'late_cout'}
    charge_types = {1: 'flat_rate', 2: 'percent_of_sell_rate'}
    cancellation_rules = {1: 'rp_refundable', 2: 'non_refundable', 3: 'fully_refundable'}

    from api.v1.add_ons.views.viewsets import get_content_object
    try:
        for row in csv.DictReader(file_content.splitlines(), quotechar='"', quoting=csv.QUOTE_ALL):
            try:
                if row.get('addonType') == '':
                    continue
                add_on_data = {}
                add_on_type = add_on_types.get(int(row.get('addonType')), None)
                add_on_level = row.get('addonLevel')
                related_code = row.get('relatedCode')
                charge_type = charge_types.get(int(row.get('chargeType')), None)
                charge_value = row.get('chargeValue')
                margin = row.get('margin')
                cancellation_rule = cancellation_rules.get(int(row.get('cancellationRule')), None)
                inventory_cut_off = row.get('inventoryCutoff')
                st = row.get('startTime')
                if not (st == '' or st.strip() == 'NA'):
                    start_time = st
                et = row.get('endTime')
                if not (et == '' or et.strip() == 'NA'):
                    end_time = et

                relatedto = add_on_levels.get(int(add_on_level), None)
                relatedcode = related_code
                content_object = get_content_object(relatedto, relatedcode)
                add_on_data['charge_type'] = charge_type
                add_on_data['type'] = add_on_type
                add_on_data['object_id'] = content_object['object_id']
                if start_time:
                    add_on_data['start_time'] = start_time
                if end_time:
                    add_on_data['end_time'] = end_time
                add_on_data['relatedto'] = relatedto
                add_on_data['inventory_cut_off'] = inventory_cut_off
                add_on_data['cancellation_rule'] = cancellation_rule
                add_on_data['user'] = user.id
                add_on_data['content_type'] = content_object['content_type']
                add_on_data['margin'] = margin
                add_on_data['relatedcode'] = related_code
                add_on_data['charge_value'] = int(charge_value)
                add_on_data['based_on'] = 'room'
                add_on_data['text'] = row.get('Text')

                add_on_serializer = AddOnSerializer(data=add_on_data)
                if add_on_serializer.is_valid():
                    add_on_object = AddOn(**add_on_serializer.validated_data)
                    add_on_object.isactive = True
                    db_data.append(add_on_object)
                else:
                    error_data.append(row)
            except Exception as e:
                log_msg = ("Error in validate_addon_csv_data with exception %s, %s"
                           ) % (e, repr(traceback.format_exc()))
                inventory_logger.critical(log_msg, log_type="ingoibibo",
                                          bucket="add_on_bulk_upload",
                                          stage="validate_addon_csv_data")
                sendMail('', '', log_msg, 'ERROR - in validate_addon_csv_data', '', settings.INGOIBIBO_ERROR_ALERT, {},
                         [])
        if error_data:
            send_create_addon_error_mail(error_data, email_id)
    except Exception as e:
        log_msg = ("Error in validate_addon_csv_data with exception %s, %s"
                   ) % (e, repr(traceback.format_exc()))
        inventory_logger.critical(log_msg, log_type="ingoibibo",
                                  bucket="add_on_bulk_upload",
                                  stage="validate_addon_csv_data")
    return db_data


def send_create_addon_error_mail(error_data, email_id):
    title_fields = ['addonType', 'addonLevel', 'relatedCode', 'chargeType', 'chargeValue', 'margin',
                    'cancellationRule', 'inventoryCutoff', 'startTime', 'endTime']
    fname = "create_addons_errors.csv"
    source_file = "/tmp/" + fname
    subject = "Addon Creation Error Sheet"
    email_content = "Attached Addon Creation Error Report File"
    try:
        report_file = open(source_file, 'w')
        writer = csv.writer(report_file, dialect='excel', quoting=csv.QUOTE_ALL)
        writer.writerow(title_fields)
        for row in error_data:
            field_value = [row['addonType'], row['addonLevel'], row['relatedCode'], row['chargeType'],
                           row['chargeValue'],
                           row['margin'], row['cancellationRule'], row['inventoryCutoff'], row['startTime'],
                           row['endTime']]
            writer.writerow(field_value)
        report_file.close()
        email_list = [email_id] if email_id else ['<EMAIL>']
        sendMail('', '', email_content, subject, '', email_list, attached_file=report_file, bcc_emails=[])
        os.remove(source_file)
    except Exception as e:
        os.remove(source_file)
        log_msg = ("Error in send_create_addon_error_mail with exception %s, %s"
                   ) % (e, repr(traceback.format_exc()))
        inventory_logger.critical(log_msg, log_type="ingoibibo",
                                  bucket="add_on_bulk_upload",
                                  stage="send_create_addon_error_mail")


@app.task(name="ingoibibo.process_kinesis_shard_for_voyager_ack")
def process_kinesis_shard_for_voyager_ack(shard_info):
    count = 100
    shard_id = str(shard_info['ShardId'])
    last_read_seq_number_cache_key = 'last_read_seq_number_' + shard_id
    last_read_seq_number = cache.get(last_read_seq_number_cache_key)
    inventory_logger.info(message='Last read sequence number %s' % (str(last_read_seq_number)),
                          log_type='ingoibibo', bucket='pushingtovoyager',
                          stage='process_kinesis_shard_for_voyager_ack')

    celery_logger.info('%s\t%s\t%s\t%s\t%s\t%s' % (
        'hotels', 'tasks', 'process_kinesis_shard_for_voyager_ack Started:', shard_id, '', ''))
    inventory_logger.info(message='Successfully received shard %s for processing'
                                  % shard_id,
                          log_type='ingoibibo', bucket='pushingtovoyager',
                          stage='process_kinesis_shard_for_voyager_ack')
    try:
        kinesis_client = boto3.client('kinesis', region_name=settings.INGO_VOY_COMMUNICATION_SETTINGS['region_name'])

        if count == 0:
            return
        if not last_read_seq_number:
            shard_iterator = kinesis_client.get_shard_iterator(
                StreamName=settings.INGO_VOY_COMMUNICATION_SETTINGS['kinesis_stream']['ack'],
                ShardId=shard_info['ShardId'],
                ShardIteratorType='TRIM_HORIZON')
        else:
            shard_iterator = kinesis_client.get_shard_iterator(
                StreamName=settings.INGO_VOY_COMMUNICATION_SETTINGS['kinesis_stream']['ack'],
                ShardId=shard_info['ShardId'],
                ShardIteratorType='AFTER_SEQUENCE_NUMBER',
                StartingSequenceNumber=last_read_seq_number)
        get_response = kinesis_client.get_records(ShardIterator=shard_iterator['ShardIterator'], Limit=1)
        count -= 1
        if len(get_response['Records']) > 0:
            message = ujson.loads(get_response['Records'][0]['Data'])
            process_resp = process_message(message)
            if not process_resp['success']:
                inventory_logger.info(message='Something went wrong while updating voyagerId for ingoId %s '
                                              % str(process_resp),
                                      log_type='ingoibibo', bucket='pushingtovoyager',
                                      stage='update_ingo_from_voyager_kinesis_response')

            last_read_seq_number = get_response['Records'][0]['SequenceNumber']
            # save on each update, to not lose info.. just in case
            cache.set(last_read_seq_number_cache_key, last_read_seq_number, timeout=0)
            inventory_logger.info(message='Successfully update ingoId %s '
                                          % str(process_resp),
                                  log_type='ingoibibo', bucket='pushingtovoyager',
                                  stage='update_ingo_from_voyager_kinesis_response')
        else:
            inventory_logger.info(message='No records in kinesis stream',
                                  log_type='ingoibibo', bucket='pushingtovoyager',
                                  stage='update_ingo_from_voyager_kinesis_response')
        sleep(0.20)
        while 'NextShardIterator' in get_response:
            if count == 0:
                break
            get_response = kinesis_client.get_records(ShardIterator=get_response['NextShardIterator'], Limit=1)
            sleep(0.20)
            count -= 1
            if len(get_response['Records']) == 0:
                continue
            message = ujson.loads(get_response['Records'][0]['Data'])
            process_resp = process_message(message)
            if not process_resp['success']:
                inventory_logger.info(message='Something went wrong while updating voyagerId %s '
                                              % str(process_resp),
                                      log_type='ingoibibo', bucket='pushingtovoyager',
                                      stage='update_ingo_from_voyager_kinesis_response')

            last_read_seq_number = get_response['Records'][0]['SequenceNumber']
            # save on each update, to not lose info.. just in case
            cache.set(last_read_seq_number_cache_key, last_read_seq_number, timeout=0)
            inventory_logger.info(message='Last read sequence number %s' % (str(last_read_seq_number)),
                                  log_type='ingoibibo', bucket='pushingtovoyager',
                                  stage='update_ingo_from_voyager_kinesis_response')
        inventory_logger.info(message='Successfully update ingoId %s ' % str(process_resp),
                              log_type='ingoibibo', bucket='pushingtovoyager',
                              stage='update_ingo_from_voyager_kinesis_response')
        celery_logger.info('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'process_kinesis_shard_for_voyager_ack',
                                                       str(shard_info['ShardId']), '', str(process_resp)))
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'process_kinesis_shard_for_voyager_ack', str(shard_info['ShardId']), str(e),
            repr(traceback.format_exc())))


def process_message(message):
    inventory_logger.info(message='Kinesis ack stream data %s' % message,
                          log_type='ingoibibo', bucket='pushingtovoyager',
                          stage='process_message')
    try:
        response = {}
        if message.get('success'):
            hotel_id = message.get('data').get('id').split('_')
            hotel_code = hotel_id[len(hotel_id) - 1]
            voyager_id = message.get('data').get('voyager_id')
            if voyager_id:
                hotel = HotelDetail.objects.get(hotelcode=hotel_code)
                action = 'updated'
                if not hotel.voyagerid == voyager_id:
                    if hotel.country.lower() == 'india':
                        # for intl hotels updates are
                        # done through api
                        HotelDetail.objects.filter(hotelcode=hotel_code).update(voyagerid=voyager_id)
                    else:
                        action = 'not updated'
                    inventory_logger.info(message='Hotel %s successfully %s voyagerid %s - %s %s %s' %
                                                  (action, hotel.hotelcode, hotel.voyagerid,
                                                   voyager_id, hotel.country, message),
                                          log_type='ingoibibo', bucket='pushingtovoyager',
                                          stage='process_message')
                response['success'] = True
                response['hotel_code'] = hotel_code
            else:
                inventory_logger.info(message='VoyagerId is missing while updating hotel object %s '
                                              % str(message),
                                      log_type='ingoibibo', bucket='pushingtovoyager',
                                      stage='process_message')
                response['success'] = False
                response['hotel_code'] = hotel_code
        else:
            response['success'] = False
            response['error'] = message.get('data').get('error')
        return response
    except Exception as e:
        inventory_logger.critical(message='Exception while updating hotel object %s. Exception -> %s '
                                          % (str(message), str(e)),
                                  log_type='ingoibibo', bucket='pushingtovoyager',
                                  stage='process_message')


@app.task(name="ingoibibo.create_vendor_on_nav")
def auto_create_vendor_on_nav(hotel_id, user):
    try:
        from hotels.navision_vendor_management import auto_create_nav_vendor
        msg = auto_create_nav_vendor(hotel_id, user)
        return msg
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'auto_create_vendor_on_nav', str(e), repr(traceback.format_exc())))


@app.task(name="ingoibibo.auto_approve_vendor_on_bank_change")
def auto_approve_vendor_on_bank_change(hotel_obj_id, log_data={'api_specific_identifiers': {}, 'error': {}, 'request_id':''}):
    unique_req_resp_id = log_data.get('request_id', '')
    if not unique_req_resp_id:
        unique_req_resp_id = uuid.uuid4()
    try:
        data = {'status': 'APPROVED', 'id': None, 'reject_reasons': ''}
        # CHECKUSERCHANGE - DONE
        user = User.objects.get(email=AUTO_BANK_APPROVAL_USER)
        request = MockRequest(user_obj=user)
        request.path_info = "auto_approve_vendor_on_bank_change"
        request.META = {
            'REMOTE_ADDR': 'auto_approve_vendor_on_bank_change'
        }
        request.data = data
        async_flag = True
        from api.v1.finance_approval.views import BankAccountViewSet
        bank_viewset = BankAccountViewSet()
        api_logger.info(
            "{}| auto approve bank change initiated for hotel id: {} and request data: {}".format(
                unique_req_resp_id, hotel_obj_id, request.data),
            log_type='ingoibibo', bucket='ClientAPI', stage='auto_approve_vendor_on_bank_change',
        identifier='{}'.format(log_data))
        to_be_approved, to_be_rejected, success, message, approved, rejected = \
            bank_viewset.update_bank_status_from_hotel(request, hotel_obj_id, unique_req_resp_id, async_flag, True, log_data=log_data)
        api_logger.info(
            "{}| auto approve bank change completed with response is status: {}, approved: {}, rejected: {}, message: "
            "{}, to_be_approved: {}".format(unique_req_resp_id, success, approved, rejected, message, to_be_approved),
            log_type='ingoibibo', bucket='ClientAPI', stage='auto_approve_vendor_on_bank_change',
        identifier='{}'.format(log_data))
    except Exception as e:
        log_data['error']['message'] = str(e)
        log_data['error']['traceback'] = repr(traceback.format_exc())
        api_logger.critical("auto approval on bank change is failed for unique_req_resp_id: {}"
                            .format(e), log_type='ingoibibo',
                            bucket='ClientAPI', stage='auto_approve_vendor_on_bank_change',
                            identifier='{}'.format(log_data))


def update_push_async_task(redis_key, task_func, args, countdown):
    """
     Executes a push task asynchronously and ensures that only one push is made at a time by using a Redis lock.

     Args:
         redis_key (str): The Redis key used for locking.
         task_func (function): The asynchronous task function to be executed.
         args (tuple): The arguments to be passed to the task function.
         countdown (int): The countdown value in seconds for task execution.

     """
    redis_server = get_payment_cache_server()
    key_set_success = redis_server.setnx(redis_key, True)
    if key_set_success:
        task_func.apply_async(args=args, countdown=countdown)
        redis_server.expire(redis_key, countdown)

def update_hotel_on_nav_async(vendor_obj, hotel_obj, unique_req_resp_id=None, log_data={'api_specific_identifiers': {}, 'error': {}, 'request_id':''}):
    """
    This function works as a wrapper to call update_hotel_on_navision_v2 asynchronously.
    It ensures that only one push is made at a time to Navision.
    """
    from hotels.navision_vendor_management import update_hotel_on_navision_v2
    redis_key = "upd_htl_on_nav_{}".format(vendor_obj.id)
    update_push_async_task(redis_key, update_hotel_on_navision_v2, (vendor_obj, hotel_obj, unique_req_resp_id, True, log_data), NAVISION_PUSH_ASYNC_COUNTDOWN)


def update_vendor_on_nav_async(vendor_id, vendor_blocked_flag=False, log_data={'api_specific_identifiers': {}, 'error': {}, 'request_id':''}):
    """
    This function works as a wrapper to call update_vendor_on_nav_v2 asynchronously.
    It ensures that only one push is made at a time to Navision.
    """
    redis_key = "upd_vndr_on_nav_{}".format(vendor_id)
    update_push_async_task(redis_key, update_vendor_on_nav_v2, (vendor_id, vendor_blocked_flag, log_data), NAVISION_PUSH_ASYNC_COUNTDOWN)


@app.task(name="ingoibibo.update_vendor_on_nav_v2")
def update_vendor_on_nav_v2(vendor_id, vendor_blocked_flag=False, log_data={'api_specific_identifiers': {}, 'error': {}, 'request_id':''}):
    from hotels.navision_vendor_management import update_vendor_on_navision_v2, update_vendor_data_only
    from hotels.models import VendorMapping, VendorDetail, BankAccountDetail
    is_successfull = True
    try:
        vendor_obj = VendorDetail.objects.using('default').filter(id=vendor_id).last()
        account_object = BankAccountDetail.objects.using('default').filter(object_id=vendor_obj.id,
                                                                           isactive=True).last()
        mapped_hotels = VendorMapping.objects.filter(vendor=vendor_obj, isactive=True)
        unique_req_resp_id = str(uuid.uuid4())
        for mapped_hotel_obj in mapped_hotels:
            celery_logger.info(
                '%s\t%s\t%s\t%s' % (mapped_hotel_obj.hotel.hotelcode, 'tasks', 'update_vendor_on_nav_v2 Started with unique_req_resp_id:', unique_req_resp_id))
            is_successfull = update_vendor_on_navision_v2(vendor_obj, mapped_hotel_obj.hotel,
                                                          account_object, True, unique_req_resp_id, log_data=log_data)
        if vendor_obj and account_object and not mapped_hotels:
            celery_logger.info('%s\t%s\t%s' % (
                vendor_id, 'tasks', 'update_vendor_on_nav_v2 not mapped hotels Started.'))
            is_successfull = update_vendor_data_only(vendor_obj, account_object, log_data=log_data)
    except Exception as e:
        celery_logger.critical(
            '%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'update_vendor_on_nav_v2', str(e), repr(traceback.format_exc())))
    return is_successfull


# TODO: uncomment this task after production celery task failure issue resolved.
# @app.task(name="ingoibibo.aggregate_child_bookings")
# def aggregate_child_bookings():
#     from hotels.hotelbooking import collect_multi_room_data
#     try:
#         celery_logger.info('%s\t%s\t%s' % ('hotels', 'tasks', 'aggregate_child_bookings Started.'))
#         collect_multi_room_data()
#     except Exception as e:
#         celery_logger.critical('%s\t%s\t%s\t%s\t%s' % (
#             'hotels', 'tasks', 'aggregate_child_bookings', str(e),
#             repr(traceback.format_exc())))


@app.task(name="ingoibibo.map_mmt_multi_booking_data")
def map_mmt_multi_booking_data(data_list):
    from scripts.map_parent_child_mmt_bookings import update_booking_data
    try:
        celery_logger.info('%s\t%s\t%s' % ('hotels', 'tasks', 'map_mmt_multi_booking_data Started.'))
        update_booking_data(data_list)
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'map_mmt_multi_booking_data', str(e),
            repr(traceback.format_exc())))


@app.task(name="ingoibibo.task_pending_hotel_tnc_mailer")
def task_pending_hotel_tnc_mailer(filename, user):
    from lib.MDBStorage import MDBStorage
    from extranet.services.services import send_pending_tnc_accept_mailer

    mdb = MDBStorage()
    mapping_csv = mdb.open(filename)
    reader = csv.DictReader(mapping_csv)
    for record in reader:
        send_pending_tnc_accept_mailer(record)


@app.task(name='ingoibibo.map_existing_vendor')
def map_existing_vendor_task(vendor_file, emailid, user):
    from hotels.models.vendor_detail import VendorDetail, VendorMapping, HotelDetail
    from hotels.navision_vendor_management import update_vendor_on_navision_v2
    unique_req_resp_id = str(uuid.uuid4())
    inventory_logger.info(message='map_existing_vendor file: %s, email_id: %s, user: %s, unique_req_resp_id: %s' % (vendor_file, emailid, user, unique_req_resp_id),
                          log_type='ingoibibo', bucket='ClientAPI',
                          stage='map_existing_vendor')
    mdb = MDBStorage()
    mapping_csv = mdb.open(vendor_file)
    reader = csv.DictReader(mapping_csv)
    result = []
    for row in reader:
        try:
            status = False
            hotel_obj = None
            inventory_logger.info(message='hotels.task.map_existing_vendor: %s unique_req_resp_id: %s' % (row, unique_req_resp_id), log_type='ingoibibo',
                                  bucket='ClientAPI',
                                  stage='map_existing_vendor')
            hotel_code = int(row.get('Hotel Code'))
            vendor_code = row.get('Vendor Code')
            company = row.get('Company')
            # TODO: need to ask hoteldetail is active, vendor company check
            hotel_obj = HotelDetail.objects.filter(hotelcode=hotel_code).first()
            vendor_obj = VendorDetail.objects.filter(vendor_code=vendor_code, isactive=True,
                                                     vendor__code=company).last()
            if not (hotel_obj and vendor_obj):
                continue
            temp_result = {
                'hotelcode': row.get('Hotel Code'),
                'vendorcode': row.get('Vendor Code'),
                'company': row.get('Company'),
                'status': status
            }
            acc_obj = vendor_obj.accounts.filter(isactive=True).last()
            status = update_vendor_on_navision_v2(vendor_obj=vendor_obj, bank_account_obj=acc_obj, hotel_obj=hotel_obj, unique_req_resp_id=unique_req_resp_id)
            temp_result['status'] = status
            if status:
                VendorMapping.objects.filter(hotel=hotel_obj, vendor__vendor__code=company).update(isactive=False)
                vendor_mapping_obj = VendorMapping(
                    hotel=hotel_obj,
                    vendor=vendor_obj,
                    isactive=True,
                    user=user
                )
                vendor_mapping_obj.save()
            inventory_logger.info(
                message='hotels.task.map_existing_vendor: %s unique_req_resp_id: %s' % (temp_result, unique_req_resp_id),
                log_type='ingoibibo', bucket='ClientAPI',
                stage='map_existing_vendor')
        except Exception as e:
            temp_result['status'] = str(e)
            inventory_logger.critical(
                message='Response received from NAV %s, for hotel_code %s and company %s unique_req_resp_id %s \n %s' % (
                    status, row.get('Hotel Code'), row.get('Company'), unique_req_resp_id, e), log_type='ingoibibo', bucket='ClientAPI',
                stage='map_existing_vendor')
        result.append(temp_result)
    mdb.delete(vendor_file)
    csvfile = open('/tmp/vendor_mapping_existing_%s.csv' % (user), 'w')
    writer = csv.DictWriter(csvfile, fieldnames=['hotelcode', 'vendorcode', 'company', 'status'])
    writer.writeheader()
    writer.writerows(result)
    csvfile.flush()
    sendMail('', '', '', subject='Please find attachment', template_id='', cc_emails=[], attached_file=csvfile,
             bcc_emails=[])


@app.task(name='ingoibibo.update_termcode_methodcode')
def update_vendor_field_task(vendor_file, emailid, user):
    import re
    import ast
    from hotels.models.vendor_detail import VendorDetail, VendorMapping
    from hotels.navision_vendor_management import update_vendor_on_navision_v2
    from common.models import City
    from hotels.hotelchoice import VENDOR_GEN_POSTING_GROUP, ACCOUNT_NUMBER_REGEX, TAN_NUMBER_REGEX, IFSC_REGEX, \
        ACCOUNT_NAME_REGEX, CORPORATE_GST_NUMBER, PAN_CARD_REGEX, VALID_VENDOR_POSTING_GROUP, \
        STANDARD_VENDOR_PURCHASE_CODE
    from hotels.hotelchoice import PAYMENT_TERMS_CODE, PAYMENT_METHOD_CODE, VAT_REG_NUM_MAX_LENGTH
    inventory_logger.info(
        message='update_termcode_methodcode file: %s, email_id: %s, user: %s' % (vendor_file, emailid, user),
        log_type='ingoibibo', bucket='ClientAPI',
        stage='update_termcode_methodcode')
    mdb = MDBStorage()
    mapping_csv = mdb.open(vendor_file)
    reader = csv.DictReader(mapping_csv)
    result = []
    for row in reader:
        try:
            status = False
            temp_result = {
                'data': row,
                'status': False
            }
            inventory_logger.info(message='hotels.task.update_termcode_methodcode: %s' % row, log_type='ingoibibo',
                                  bucket='ClientAPI',
                                  stage='update_termcode_methodcode')
            vendor_code = row.get('Vendor Code')
            if not vendor_code:
                raise ValidationError('Vendor code not found')
            # TODO: need to ask hoteldetail is active, vendor company check

            vendor_mapping_obj = VendorMapping.objects.filter(vendor__vendor_code=vendor_code).last()

            if not vendor_mapping_obj:
                raise ValidationError('Vendor mapping not found')

            vendor_obj = vendor_mapping_obj.vendor
            acc_obj = vendor_obj.accounts.filter(isactive=True).last()

            if not vendor_obj:
                raise ValidationError('Vendor not found')

            city_obj = City.objects.filter(cityname__iexact=row.get('City'), locus_code__isnull=False,
                                           isactive=True).last()
            method_code = row.get('Method Code')

            if row.get('Vendor Posting Group') and row.get('Vendor Posting Group') not in VALID_VENDOR_POSTING_GROUP:
                raise ValidationError('Vendor posting group not valid')

            if row.get('Account Number') and not re.match(ACCOUNT_NUMBER_REGEX, row.get('Account Number')):
                raise ValidationError('Invalid Account number only alpha-numbers allowed')

            if row.get('IFSC') and not re.match(IFSC_REGEX, row.get('IFSC')):
                raise ValidationError('Invalid IFSC number, only alpha-numbers allow')

            if row.get('TAN Number') and not re.match(TAN_NUMBER_REGEX, row.get('TAN Number')):
                raise ValidationError('Invalid TAN number')

            if row.get('Account Name') and not re.match(ACCOUNT_NAME_REGEX, row.get('Account Name')):
                raise ValidationError('Invalid account name')

            if row.get('Corporate GST Number') and not re.match(CORPORATE_GST_NUMBER, row.get('Corporate GST Number')):
                raise ValidationError('Invalid corporate GST number')

            if row.get('PAN Number') and not re.match(PAN_CARD_REGEX, row.get('PAN Number')):
                raise ValidationError('Invalid PAN card number')

            if method_code and method_code not in [mc[0] for mc in PAYMENT_METHOD_CODE]:
                raise ValidationError('Invalid method code')

            term_code = row.get('Terms Code')

            if term_code and term_code not in [tc[0] for tc in PAYMENT_TERMS_CODE]:
                raise ValidationError('Invalid term code')

            if row.get('Adjustment Day Of Month') and not re.match('[0-9]{2}D', row.get('Adjustment Day Of Month')):
                raise ValidationError('Invalid adjustment day of month')

            if row.get('City') and not city_obj:
                raise ValidationError('Invalid city name')

            if not row.get('PAN Number') and row.get('Method Code') == 'CHEQUE':
                raise ValidationError('for cheque vendor PAN number is mandatory')

            if row.get('Blocked') and int(row.get('Blocked')) not in [0, 1, 2]:
                raise ValidationError('invalid block value')

            if row.get('Vendor GEN Posting Group') and \
                    row.get('Vendor GEN Posting Group') not in [vpg[0] for vpg in VENDOR_GEN_POSTING_GROUP]:
                raise ValidationError('Invalid STD_VENDOR_PURCHANGE_CODE')

            if row.get('VAT Registration Number'):
                if len(row.get('VAT Registration Number')) > VAT_REG_NUM_MAX_LENGTH:
                    raise ValidationError('VAT Registration Number greater than 20 char')
                if not re.match(r'^\w+$', row.get('VAT Registration Number')):
                    raise ValidationError('Invalid VAT Registration Number')

            if row.get('VAT Bus Posting Group') and row.get('VAT Bus Posting Group') != VAT_BUS_POSTING_GROUP[1][0]:
                raise ValidationError('Invalid VAT Bus Posting group')

            if row.get('Pay To Vendor'):
                pay_to_vendor = VendorDetail.objects.filter(vendor_code=row.get('Pay To Vendor')).last()
                if not pay_to_vendor:
                    raise ValidationError('Invalid pay to vendor code')
                else:
                    vendor_obj.payment_to_vendor = pay_to_vendor

            if row.get('Terms Code'):
                vendor_obj.payment_term_code = row.get('Terms Code')

            if row.get('Method Code'):
                vendor_obj.payment_method_code = row.get('Method Code')

            if row.get('Corporate GST Number'):
                vendor_obj.corporate_gstn = row.get('Corporate GST Number')

            if row.get('Vendor Posting Group'):
                vendor_obj.vendor_posting_group = row.get('Vendor Posting Group')
            if row.get('Adjustment Day Of Month'):
                vendor_obj.adjustment_day_of_month = row.get('Adjustment Day Of Month')
            # if row.get('Vendor Posting Group') or row.get('Adjustment Day Of Month'):
            #     misc = vendor_obj.misc
            #     try:
            #         misc_dict = ast.literal_eval(misc)
            #         inventory_logger.info(message='hotels.task.update_termcode_methodcode misc dict: %s' % misc_dict,
            #                               log_type='ingoibibo', bucket='ClientAPI',
            #                               stage='update_termcode_methodcode')
            #         if row.get('Vendor Posting Group'):
            #             misc_dict['vendorpostinggroup'] = row.get('Vendor Posting Group')
            #         if row.get('Adjustment Day Of Month'):
            #             misc_dict['adjustment_day_of_month'] = row.get('Adjustment Day Of Month')
            #         vendor_obj.misc = misc_dict
            #         inventory_logger.info(message='hotels.task.update_termcode_methodcode vendor object misc '
            #                                       'dict: %s' % vendor_obj.misc,
            #                               log_type='ingoibibo', bucket='ClientAPI',
            #                               stage='update_termcode_methodcode')
            #     except Exception as e:
            #         inventory_logger.critical(
            #             message='Vendor Posting group error %s and exception %s' % (
            #                 row.get('Vendor Posting Group'), e), log_type='ingoibibo',
            #             bucket='ClientAPI',
            #             stage='update_termcode_methodcode')

            if row.get('Corporate GST Name'):
                vendor_obj.corporate_gstn_name = row.get('Corporate GST Name')

            if row.get('Corporate GST Address'):
                vendor_obj.corporate_gstn_address = row.get('Corporate GST Address')

            if row.get('Vendor Name'):
                vendor_obj.vendor_name = row.get('Vendor Name')

            if row.get('TAN Number'):
                vendor_obj.tan_number = row.get('TAN Number')
                vendor_obj.tan_status = True

            if row.get('Search Name'):
                vendor_obj.search_name = row.get('Search Name')

            if row.get('IFSC'):
                acc_obj.ifsc = row.get('IFSC')

            if row.get('Account Number'):
                acc_obj.accno = row.get('Account Number')

            if row.get('Account Name'):
                acc_obj.accname = row.get('Account Name')

            if row.get('Bank City'):
                acc_obj.city = row.get('Bank City')

            if row.get('Branch Name'):
                acc_obj.branchname = row.get('Branch Name')

            if row.get('Branch Code'):
                acc_obj.branchcode = row.get('Branch Code')

            if row.get('Bank Code'):
                acc_obj.bankcode = row.get('Bank Code')

            if row.get('Account Type'):
                acc_obj.account_type = row.get('Account Type')

            if row.get('Bank Name'):
                acc_obj.bankname = row.get('Bank Name')

            if row.get('Name 2'):
                vendor_obj.name_2 = row.get('Name 2')

            if row.get('Vendor Address'):
                vendor_obj.address = row.get('Vendor Address')

            if row.get('Vendor Address 2'):
                vendor_obj.address_2 = row.get('Vendor Address 2')

            if row.get('Contact'):
                vendor_obj.contact = row.get('Contact')

            if row.get('Phone No'):
                vendor_obj.phone_no = row.get('Phone No')

            if row.get('Vendor GEN Posting Group'):
                vendor_obj.gen_bus_posting_group = row.get('Vendor GEN Posting Group')

            if row.get('Email'):
                vendor_obj.email = row.get('Email')

            if row.get('Primary Contact Number'):
                vendor_obj.primary_contact_number = row.get('Primary Contact Number')

            if row.get('Full Name'):
                vendor_obj.full_name = row.get('full_name')

            if row.get('Reservation Mobile No'):
                vendor_obj.reservation_mobile_no = row.get('Reservation Mobile No')

            if row.get('Sales Email'):
                vendor_obj.sales_email = row.get('Sales Email')

            if row.get('Sales Phone Number'):
                vendor_obj.sales_phone_number = row.get('Sales Phone Number')

            if row.get('Sales Mobile Number'):
                vendor_obj.sales_mobile_number = row.get('Sales Mobile Number')

            if row.get('Finance Email'):
                vendor_obj.finance_email = row.get('Finance Email')

            if row.get('Finance Phone Number'):
                vendor_obj.finance_phone_number = row.get('Finance Phone Number')

            if row.get('Finance Mobile Number'):
                vendor_obj.finance_mobile_number = row.get('Finance Mobile Number')

            if row.get('Non Adjustment Vendor'):
                vendor_obj.non_adjustment_vendor = bool(row.get('Non Adjustment Vendor'))

            if row.get('Hotel Credit MEMO Application'):
                vendor_obj.hotel_credit_memo_application = bool(row.get('Hotel Credit MEMO Application'))

            if row.get('TDS Application'):
                vendor_obj.tds_application = bool(row.get('TDS Application'))

            if row.get('Blocked'):
                vendor_obj.blocked = int(row.get('Blocked'))

            if row.get('City') and city_obj:
                vendor_obj.city = city_obj.cityname

            if row.get('VAT Registration Number'):
                vendor_obj.vat_registration_number = row.get('VAT Registration Number')
            if row.get('VAT Bus Posting Group'):
                vendor_obj.vat_bus_posting_group = row.get('VAT Bus Posting Group')

            if row.get('LDC Limit In INR'):
                vendor_obj.ldc_limit_in_inr = row.get('LDC Limit In INR')
            if row.get('LDC Number'):
                vendor_obj.ldc_number = row.get('LDC Number')
            if row.get('LDC Lower Percentage'):
                vendor_obj.ldc_lower_perc = row.get('LDC Lower Percentage')
            if row.get('TDS Excluded'):
                vendor_obj.__setattr__('excludetds', ast.literal_eval(row.get('TDS Excluded').title()))

            std_vend_purs_code = None
            if row.get('Standard Vendor Purchase Code'):
                st_code = [st_code[0] for st_code in STANDARD_VENDOR_PURCHASE_CODE]
                std_vend_purs_code = [st.upper() for st in row.get('Standard Vendor Purchase Code').split(',')]
                st_code_error = [st for st in std_vend_purs_code if st not in st_code]
                if st_code_error:
                    raise ValidationError(
                        'Invalid STD_VENDOR_PURCHANGE_CODE, Must be a subset of {code_supported}'.format(
                            code_supported=",".join([st[0] for st in STANDARD_VENDOR_PURCHASE_CODE])))

            if std_vend_purs_code:
                vendor_obj.std_vendor_purchase_code = ",".join(std_vend_purs_code)

            hotel_obj = vendor_mapping_obj.hotel
            unique_req_resp_id = str(uuid.uuid4())
            vendor_obj.save()
            acc_obj.save()
            if acc_obj and vendor_obj and hotel_obj:
                status = update_vendor_on_navision_v2(vendor_obj=vendor_obj, hotel_obj=hotel_obj,
                                                      bank_account_obj=acc_obj, update_vendor_flag=False,
                                                      unique_req_resp_id=unique_req_resp_id)
                if status:
                    vendor_obj.save()
                temp_result['status'] = status
            inventory_logger.info(
                message='hotels.task.update_termcode_methodcode: %s and unique_req_resp_id %s' % (temp_result, unique_req_resp_id),
                log_type='ingoibibo', bucket='ClientAPI',
                stage='update_termcode_methodcode')
            result.append(temp_result)
        except Exception as e:
            inventory_logger.critical(
                message='Response received from NAV %s, for hotel_code %s and company %s unique_req_resp_id %s \n %s' % (
                    status, row.get('Hotel Code'), row.get('Company'), unique_req_resp_id, repr(traceback.format_exc())),
                log_type='ingoibibo', bucket='ClientAPI',
                stage='update_termcode_methodcode')
    mdb.delete(vendor_file)
    csvfile = open('/tmp/vendor_termcode_methodcode_%s.csv' % (user), 'w')
    writer = csv.DictWriter(csvfile, fieldnames=['data', 'status'])
    writer.writeheader()
    writer.writerows(result)
    csvfile.flush()
    sendMail('', '', '', subject='Please find attachment vendor update', template_id='', cc_emails=[],
             attached_file=csvfile, bcc_emails=[])


@app.task(name='ingoibibo.vendor_sync')
def vendor_sync_task(vendor_file, emailid, user):
    from hotels.models.vendor_detail import VendorDetail, VendorMapping, HotelDetail
    from hotels.navision_vendor_management import update_vendor_on_navision_v2
    inventory_logger.info(message='vendor_sync file: %s, email_id: %s, user: %s' % (vendor_file, emailid, user),
                          log_type='ingoibibo', bucket='ClientAPI',
                          stage='vendor_sync_task')
    mdb = MDBStorage()
    mapping_csv = mdb.open(vendor_file)
    reader = csv.DictReader(mapping_csv)
    result = []
    for row in reader:
        try:
            status = []
            inventory_logger.info(message='hotels.task.vendor_sync: %s' % row, log_type='ingoibibo', bucket='ClientAPI',
                                  stage='vendor_sync')
            vendor_code = row.get('Vendor Code')
            # TODO: need to ask hoteldetail is active, vendor company check
            vendor_obj = VendorDetail.objects.filter(vendor_code=vendor_code, isactive=True).last()
            if not (vendor_obj):
                continue
            vendor_mapping_objs = VendorMapping.objects.filter(vendor=vendor_obj, isactive=True)
            if not vendor_mapping_objs:
                continue
            temp_result = {
                'vendorcode': row.get('Vendor Code'),
                'status': status
            }
            unique_req_resp_id = str(uuid.uuid4())
            acc_obj = vendor_obj.accounts.filter(isactive=True).last()
            for vendor_mapping_obj in vendor_mapping_objs:
                hotel_obj = vendor_mapping_obj.hotel
                temp_status = update_vendor_on_navision_v2(vendor_obj=vendor_obj, bank_account_obj=acc_obj,
                                                           hotel_obj=hotel_obj, unique_req_resp_id=unique_req_resp_id)
                status.append({'hotelcode': hotel_obj.hotelcode, 'status': temp_status})
            temp_result['status'] = str(status)
            inventory_logger.info(
                message='hotels.task.vendor_sync: %s unique_req_resp_id: %s' % (temp_result, unique_req_resp_id),
                log_type='ingoibibo', bucket='ClientAPI',
                stage='vendor_sync')
            result.append(temp_result)
        except Exception as e:
            inventory_logger.critical(
                message='Response received from NAV %s, for hotel_code %s and company %s and unique_req_resp_id: %s \n %s' % (
                    status, row.get('Hotel Code'), row.get('Company'), unique_req_resp_id, e), log_type='ingoibibo', bucket='ClientAPI',
                stage='vendor_sync_task')
    mdb.delete(vendor_file)
    csvfile = open('/tmp/vendor_termcode_methodcode_%s.csv' % (user), 'w')
    writer = csv.DictWriter(csvfile, fieldnames=['vendorcode', 'status'])
    writer.writeheader()
    writer.writerows(result)
    csvfile.flush()
    sendMail('', '', subject='Please find attachment', body='', template_id='', cc_emails=[], attached_file=csvfile,
             bcc_emails=[])


@app.task(name='ingoibibo.vcc_update_bulk_task')
def vcc_update_bulk_task(vcc_file, emailid, user):
    from hotels.vcc_payment import vcc_v2_fetch_data, decide_vcc_version
    inventory_logger.info(message='vcc cancel file: %s, email_id: %s, user: %s' % (vcc_file, emailid, user),
                          log_type='ingoibibo', bucket='ClientAPI',
                          stage='vcc_update_bulk_task')
    mdb = MDBStorage()
    mapping_csv = mdb.open(vcc_file)
    reader = csv.DictReader(mapping_csv)
    result = []
    for row in reader:
        booking_id = str(row.get('IngoBooking'))
        temp_result = {'Ingo Booking ID': booking_id,
                       'status': True,
                       'error': '',
                       'message': ''}
        try:
            bookings = HotelBooking.objects.select_related('hotels').filter(
                id=booking_id)
            bobj = bookings[0]
            pay_obj = bobj.payments.using('default').all()[0].payment
            if not decide_vcc_version(pay_obj):
                temp_result['status'] = False
                temp_result['error'] = 'V1 is not supported. Please cancel Manually.'
                continue

            url = 'https://' + settings.INGOIBIBO_BASE_URL + '/api/v2/vcc/delete'
            vcc_details = json.loads(pay_obj.paymentreference)
            payload = {
                'vcc_txn_id': vcc_details.get('vcc_txn_id'),
                'txnid': bobj.confirmbookingid
            }
            vcc_data = vcc_v2_fetch_data('DELETE', url, payload).get('responseData',
                                                                     {})
            if not vcc_data.get('status'):
                temp_result['status'] = False
                temp_result['error'] = vcc_data.get('error')
                temp_result['message'] = vcc_data.get('msg')
            else:
                pay_obj.paymentstatus = 'vcc_cancelled'
                hm.updateLogMsg(user, pay_obj, 'Cancelled via Bulk Uploader'
                                               ' {}'.format(vcc_data))
                pay_obj.save()

        except Exception as e:
            temp_result['status'] = False
            temp_result['error'] = str(e)

        result.append(temp_result)
    mdb.delete(vcc_file)
    csvfile = open('/tmp/create_vendor_%s.csv' % (user), 'w')
    writer = csv.DictWriter(csvfile, fieldnames=['Ingo Booking ID', 'status', 'error', 'message'])
    writer.writeheader()
    writer.writerows(result)
    csvfile.flush()
    sendMail('', '', "Report data has been updated successfully", "VCC Cancellation - Bulk Uploader", '', [user.email],
             attached_file=csvfile, bcc_emails=[])


@app.task(name='ingoibibo.vendor_create')
def vendor_create_task(vendor_file, emailid, user):
    import re
    from common.models import City
    from hotels.models.vendor_detail import VendorDetail, VendorMapping, HotelDetail, BankAccountDetail
    from hotels.navision_vendor_management import create_vendor_on_navision_v2, get_country_iso, create_inital_vendor
    from hotels.hotelchoice import (VENDOR_GEN_POSTING_GROUP, VAT_REG_NUM_MAX_LENGTH)
    from hotels.hotelchoice import PAYMENT_METHOD_CODE, PAN_CARD_REGEX, ACCOUNT_NAME_REGEX, \
        ACCOUNT_NUMBER_REGEX, IFSC_REGEX, TAN_NUMBER_REGEX, CORPORATE_GST_NUMBER
    import datetime

    inventory_logger.info(message='vendor_create file: %s, email_id: %s, user: %s' % (vendor_file, emailid, user),
                          log_type='ingoibibo', bucket='ClientAPI',
                          stage='vendor_create_task')
    INDIA = "INDIA"
    mdb = MDBStorage()
    mapping_csv = mdb.open(vendor_file)
    reader = csv.DictReader(mapping_csv)
    result = []
    for row in reader:
        try:
            # TODO: need to ask regarding vcc vendor
            status = False
            temp_result = {
                'data': row,
                'status': status
            }

            inventory_logger.info(message='hotels.task.vendor_create: %s' % row, log_type='ingoibibo',
                                  bucket='ClientAPI',
                                  stage='vendor_create')
            approval_status = int(row.get('Approval Status'))
            city_obj = City.objects.filter(cityname__iexact=row.get('City'), locus_code__isnull=False,
                                           isactive=True).last()
            hotel_obj = HotelDetail.objects.filter(hotelcode=row.get('Hotel Code')).last()
            if not hotel_obj:
                raise ValidationError('Either hotel is not active or hotelcode is not valid.')
            else:
                is_mmt_special_property = MMT_SPECIAL in json.loads(hotel_obj.special_tag)
                if row['Vendor'] == VENDOR_COMPANY_NAMES[0][1] and not is_mmt_special_property:
                    raise ValidationError('Non MMT-Special/ Holiday Selection hotel must not have mmt_special vendor')
                if row['Vendor'] != VENDOR_COMPANY_NAMES[0][1] and is_mmt_special_property:
                    raise ValidationError('MMT-Special/ Holiday Selection hotel must have mmt_special vendor')

            if not city_obj:
                raise ValidationError('Invalid city name')
            if row.get('Account Number') and not re.match(ACCOUNT_NUMBER_REGEX, row.get('Account Number')):
                raise ValidationError('Invalid Account number only alpha-numbers allowed')

            if row.get('IFSC') and not re.match(IFSC_REGEX, row.get('IFSC')):
                raise ValidationError('Invalid IFSC number, only alpha-numbers allow')

            if row.get('TAN Number') and not re.match(TAN_NUMBER_REGEX, row.get('TAN Number')):
                raise ValidationError('Invalid TAN number')

            if row.get('Account Name') and not re.match(ACCOUNT_NAME_REGEX, row.get('Account Name')):
                raise ValidationError('Invalid account name')

            if row.get('Corporate GST Number') and not re.match(CORPORATE_GST_NUMBER, row.get('Corporate GST Number')):
                raise ValidationError('Invalid corporate GST number')

            if row.get('PAN Number') and not re.match(PAN_CARD_REGEX, row.get('PAN Number')):
                raise ValidationError('Invalid PAN card number')

            if not row.get('PAN Number') and row.get('Method Code') == 'CHEQUE':
                raise ValidationError('for cheque vendor PAN number is mandatory')

            if row.get('Vendor GEN Posting Group') not in [vpg[0] for vpg in VENDOR_GEN_POSTING_GROUP]:
                raise ValidationError('Invalid VENDOR_GEN_POSTING_GROUP')

            if len(row.get('Vendor Address', 0)) > 50 or len(row.get('Vendor Address 2', 0)) > 50:
                raise ValidationError('Vendor address greater then 50 char')

            if len(row.get('Account Address', 0)) > 50:
                raise ValidationError('Account address greater then 50 char')

            vendor_obj_dict = {
                'vendor_name': row.get('Vendor Name'),
                'vendor_address': row.get('Vendor Address'),
                'vendor_address2': row.get('Vendor Address 2'),
                'vendor_post_code': row.get('Post Code'),
                'vendor_city_code': city_obj.citycode,
                'vendor_city_id': city_obj.id,
                'tan_number': row.get('TAN Number'),
                'vendor_email': row.get('Email'),
                'payment_term_code': row.get('Terms Code'),
                'search_name': row.get('Search Name'),
                'name_2': row.get('Name 2'),
                'contact': row.get('Contact'),
                'phone_no': row.get('Phone No'),
                'gen_bus_posting_group': row.get('Vendor GEN Posting Group'),
                'primary_contact_number': row.get('Primary Contact Number'),
                'state_code': row.get('State Code'),
                'full_name': row.get('Full Name'),
                'reservation_mobile_no': row.get('Reservation Mobile No'),
                'sales_email': row.get('Sales Email'),
                'sales_phone_number': row.get('Sales Phone Number'),
                'sales_mobile_number': row.get('Sales Mobile Number'),
                'finance_email': row.get('Finance Email'),
                'finance_phone_number': row.get('Finance Phone Number'),
                'finance_mobile_number': row.get('Finance Mobile Number'),
                'non_adjustment_vendor': True if (row.get("Non Adjustment Vendor") and
                                                  int(row.get("Non Adjustment Vendor"))) == 1 else False,
                'hotel_credit_memo_application': False if (row.get("Hotel Credit MEMO Application") and int(
                    row.get("Hotel Credit MEMO Application"))) == 0 else True,
                'tds_application': False if (row.get("TDS Application") and
                                             int(row.get("TDS Application"))) == 0 else True,
                'adjustment_day_of_month': row.get('Adjustment Day Of Month'),
                'vendor_posting_group': row.get('Vendor Posting Group'),
                'vat_registration_number': row.get('VAT Registration Number'),
                'std_vendor_purchase_code': row.get('Standard Vendor Purchase Code'),
                'corporate_gstn': row.get('Corporate GST Number'),
                'corporate_gstn_name': row.get('Corporate GST Name'),
                'corporate_gstn_address': row.get('Corporate GST Address')
            }

            payment_method_code = row.get('Method Code')
            if payment_method_code not in [mc[0] for mc in PAYMENT_METHOD_CODE]:
                raise ValidationError('Invalid method code')

            vendor_obj_dict['vat_registration_number'] = row.get("VAT Registration Number") or None
            vendor_obj_dict['vat_bus_posting_group'] = row.get("VAT Bus Posting Group") or None

            if row.get('Vendor') == VENDOR_COMPANY_NAMES[2][1]:
                if city_obj.country.iso2 in VAT_REQUIRED_COUNTRIES_ISO2:
                    if not vendor_obj_dict['vat_registration_number']:
                        raise ValidationError('VAT Registration Number is Required for a UAE hotel')
                    if len(vendor_obj_dict['vat_registration_number']) > VAT_REG_NUM_MAX_LENGTH:
                        raise ValidationError('VAT Registration Number greater than 20 char')
                    if not re.match(r'^\w+$', vendor_obj_dict['vat_registration_number']):
                        raise ValidationError('Invalid VAT Registration Number')

                if not vendor_obj_dict['vat_bus_posting_group']:
                    raise ValidationError('VAT Bus Posting Group is Required')
                if vendor_obj_dict['vat_bus_posting_group'] != VAT_BUS_POSTING_GROUP[1][0]:
                    raise ValidationError('Invalid VAT Bus Posting group')

            account_type = 0
            # if method code is VCC then,
            # we don't need to do validation on account type we will set it as saving(default).
            if payment_method_code != PAYMENT_METHOD_CODE[2][0]:
                account_type = row.get('Account Type')
                if account_type.lower() not in ['saving', 'current']:
                    raise ValidationError('Invalid account type')
                account_type = 1
                if row.get('Account Type').lower() == 'saving':
                    account_type = 0

            vendor_already_exists = VendorDetail.objects.filter(vendor_code=row.get('Vendor Code'),
                                                                isactive=True).last()
            if vendor_already_exists and row.get('Vendor Code'):
                raise ValidationError('Vendor already exits')

            correlation_id = uuid.uuid4()
            vendor_detail_objs = []
            if row.get('Vendor') == VENDOR_COMPANY_NAMES[0][1]:
                vendor_detail_objs.append(create_inital_vendor(hotel_obj, user, 'MakeMyTrip', payment_method_code, vendor_obj_dict, correlation_id))
            elif row.get('Vendor') == VENDOR_COMPANY_NAMES[1][1]:
                vendor_detail_objs.append(create_inital_vendor(hotel_obj, user, 'MakeMyTrip', payment_method_code, vendor_obj_dict,
                                     correlation_id))
                vendor_detail_objs.append(create_inital_vendor(hotel_obj, user, 'Goibibo', payment_method_code, vendor_obj_dict, correlation_id))
            elif row.get('Vendor') == VENDOR_COMPANY_NAMES[2][1]:
                vendor_detail_objs.append(create_inital_vendor(hotel_obj, user, 'MMTGCC', payment_method_code, vendor_obj_dict, correlation_id))
            else:
                raise ValidationError('Invalid Vendor. Vendors can only be mmt_special or gommt or gcc')

            for vendor_detail_obj in vendor_detail_objs:
                bank_account_detail_obj = BankAccountDetail(
                    content_object=vendor_detail_obj, object_id=vendor_detail_obj.id, isactive=False,
                    accno=row.get('Account Number'), accname=row.get('Account Name'), branchname=row.get('Branch Name'),
                    branchcode=row.get('Branch Code'), ifsc=row.get('IFSC'), bankname=row.get('Bank Name'),
                    pannumber=row.get('PAN Number'), nameonpancard=row.get('Name on PAN', ''), user=user, approved_by=user,
                    address=row.get('Account Address', ''), city=city_obj.cityname,
                    post_code=row.get('Account Post Code', ''), contact=row.get('Contact', ''),
                    country=hotel_obj.country, country_region_code=get_country_iso(hotel_obj),
                    email=row.get('Account Email', ''), iban=row.get('IBAN', ''), account_type=account_type
                )

                if row.get('PAN Number'):
                    encryption_details = hotel_obj.encryptionDetails
                    encryption_key = encryption_details.get('encryptedKey', None) if encryption_details else None
                    if encryption_key:
                        global db_encryptor
                        if not db_encryptor:
                            db_encryptor = AESEncryptor()
                        encrypted_pannumber = db_encryptor.encrypt_aes_cbc(row.get('PAN Number'), encryption_key)
                        encrypted_nameonpancard = db_encryptor.encrypt_aes_cbc(row.get('Name on PAN'), encryption_key)
                        HotelDetail.objects.filter(id=hotel_obj.id).update(pannumber=encrypted_pannumber,
                                                                       nameonpancard=encrypted_nameonpancard)
                    else:
                        HotelDetail.objects.filter(id=hotel_obj.id).update(pannumber=row.get('PAN Number'),
                                                                       # this hack to omit django signal
                                                                       nameonpancard=row.get('Name on PAN'))
                bank_account_detail_obj.save()
            success = True
            inventory_logger.info(message='Vendor and bank account created for hotel: {h} | Type of vendor: {v}'.format(h=hotel_obj.hotelcode, v=row.get('Vendor','')),
                                  log_type='ingoibibo', bucket='ClientAPI',
                                  stage='vendor_create_task')

            if approval_status:
                unique_req_resp_id = str(uuid.uuid4())
                request = MockRequest(user_obj=user, hotel_obj=hotel_obj)
                from api.v1.finance_approval.views import BankAccountViewSet
                bank_viewset = BankAccountViewSet()
                request.data = {
                    'reject_reasons': '',
                    'status': 'APPROVED'
                }
                request.path_info = "vendor_create_task"
                request.META = {
                    'REMOTE_ADDR': 'bulk_upload_bank_status'
                }
                to_be_approved, to_be_rejected, success, message, approved, rejected = \
                    bank_viewset.update_bank_status_from_hotel(request, hotel_obj.id, unique_req_resp_id)
                api_logger.info(
                    " update_bank_status_from_hotel resp is status: {}, approved: {}, rejected: {}, message: "
                    "{} unique_req_resp_id: {}".format(success, approved, rejected, message, unique_req_resp_id),
                    log_type='ingoibibo', bucket='ClientAPI',
                    stage='vendor_create_task')

            if success:
                temp_result['status'] = True
            else:
                temp_result['status'] = False
            inventory_logger.info(
                message='hotels.task.vendor_create: %s' % temp_result,
                log_type='ingoibibo', bucket='ClientAPI',
                stage='vendor_create')
            result.append(temp_result)
        except Exception as e:
            temp_result['status'] = str(e)
            inventory_logger.critical(
                message='Response received from NAV %s, for hotel_code %s and company %s \n %s' % (
                    status, row.get('Hotel Code'), row.get('Vendor'), repr(traceback.format_exc())),
                log_type='ingoibibo', bucket='ClientAPI',
                stage='vendor_create_task')
            result.append(temp_result)
    mdb.delete(vendor_file)
    output_csv_filename = '/tmp/create_vendor_%s.csv' % (user)
    with open(output_csv_filename, 'w') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=['data', 'status'])
        writer.writeheader()
        writer.writerows(result)
    run_date = datetime.datetime.now().strftime("%I:%M%p on %B %d, %Y")
    sendMail(to_emails=[emailid],
             from_email=settings.EMAIL_ALERT_SENDER,
             body='Please Find The Status Report Attached',
             subject='[INGO-Inventory Bulk Uploader] Report for Date-{}'.format(run_date),
             template_id='',
             cc_emails=[],
             attached_file=open(output_csv_filename, 'r') if output_csv_filename else None,
             bcc_emails=[])
    inventory_logger.info(message='create_vendor_task has been executed successfully',
                log_type='ingoibibo', bucket='ClientAPI',
                stage='vendor_create_task')


@app.task(name='ingoibibo.tan_status_update')
def update_vendor_tan_status(vendor_file, emailid, user):
    from hotels.models.vendor_detail import VendorDetail
    inventory_logger.info(message='tan_status_update file: %s, email_id: %s, user: %s' % (vendor_file, emailid, user),
                          log_type='ingoibibo', bucket='ClientAPI',
                          stage='map_existing_vendor')
    mdb = MDBStorage()
    mapping_csv = mdb.open(vendor_file)
    reader = csv.DictReader(mapping_csv)
    result = []
    for row in reader:
        try:
            temp_result = {
                'data': row,
                'status': False
            }
            vendor_id = row.get('id')
            vendor_obj = VendorDetail.objects.get(pk=vendor_id)
            if (vendor_obj.tan_number == '' or vendor_obj.tan_number is None) and (vendor_obj.tan_status is not None):
                vendor_obj.tan_status = None
                vendor_obj.save()
                inventory_logger.info(message='Updated TAN Status for vendor %s' % (vendor_obj.id),
                                      log_type='ingoibibo', bucket='tan_status_update',
                                      stage='updated_message')
            elif (vendor_obj.tan_number != '' and vendor_obj.tan_number is not None) and (
                    vendor_obj.tan_status != True):
                vendor_obj.tan_status = True
                vendor_obj.save()
                inventory_logger.info(message='Updated TAN Status for vendor %s' % (vendor_obj.id),
                                      log_type='ingoibibo', bucket='tan_status_update',
                                      stage='updated_message')
            temp_result['status'] = "Updated"
        except Exception as e:
            inventory_logger.critical(message='Exception while updating tan_status, traceback: %s' % (repr(e)),
                                      log_type='ingoibibo', bucket='tan_status_update',
                                      stage='updated_message')
            temp_result['status'] = repr(e)
        result.append(temp_result)
    mdb.delete(vendor_file)
    csvfile = open('/tmp/vendor_mapping_existing_%s.csv' % (user), 'w')
    writer = csv.DictWriter(csvfile, fieldnames=['data', 'status'])
    writer.writeheader()
    writer.writerows(result)
    csvfile.flush()
    sendMail('', '', '', subject='Please find attachment vendor update', template_id='', cc_emails=[],
             attached_file=csvfile, bcc_emails=[])


@app.task(name="ingoibibo.create_cug_task_promotionv2")
def create_cug_task_promotionv2(filename, user, segment):
    email_id_list = settings.INGOIBIBO_ERROR_ALERT
    error_log_msg = ""
    success_log_msg = ""
    correlation_key = str(uuid.uuid4())

    if user and user.email:
        user_email = user.email
        if user_email in email_id_list:
            email_id_list.remove(user_email)
    try:
        saved_file = mdb_storage.open(filename)
        file_content = saved_file.read()
        hotelcode_to_mmtid_map = {}
        hotel_offer_map, hotelcode_list = populate_cug_csv_data_promotionv2(file_content, email_id_list, segment)
        if not isinstance(user, User):
            user = User.objects.get(id=user)

        hotelcode_mmtid_list = HotelDetail.objects.filter(hotelcode__in=hotelcode_list).values_list("hotelcode",
                                                                                                    "mmt_id")
        for hotelcode_mmtid in hotelcode_mmtid_list:
            hotelcode_to_mmtid_map[hotelcode_mmtid[0]] = str(hotelcode_mmtid[1])

        for hotelcode, data in hotel_offer_map.items():
            if segment == MMT_BLACK and  len(data)>0 and data[0].get("offerValueList") and len(data[0]["offerValueList"])>0:
                for segment_data in data[0]["offerValueList"]:
                    if segment_data.get("segment",'') in [CUG_SEGMENT_CUG5,CUG_SEGMENT_CUG6]:
                        error_log_msg += "Error in create_cug_task ,black cug  update is not allowed,hotelcode: {},  Segment - {} \n".format(hotelcode,segment_data['segment'])
            else:
                offerCode = ''
                if len(data) > 0:
                    offerCode = data[0].pop("offerCode", '')
                isValid = True
                if offerCode:
                    get_offer_response =  get_offersv2(hotelcode_to_mmtid_map.get(hotelcode, ''),CUG_PROMOTIONV2_KEY_MAPPING[segment],correlation_key,user.id)
                    if get_offer_response is not None and get_offer_response.get('success') == True:
                        if 'offerDataList' in get_offer_response['data'] and len(get_offer_response['data']['offerDataList']) > 0:
                            data[0]["offerCode"] = {"value": get_offer_response['data']['offerDataList'][0]['offerCode']}
                            modify_timebase_data(data,get_offer_response,segment)
                    else:
                        isValid = False
                        error_log_msg += "Error in fetching cug_task {}, Segment - {} {} \n".format(hotelcode, 'All Segment', str(get_offer_response))
                if isValid:
                    request_data = {"mmtId": hotelcode_to_mmtid_map.get(hotelcode, ''), "correlationKey": correlation_key, "offerData": data}               
                    response = create_cug_enigma_call(request_data, user.id)
                    if response is not None and response.status_code == 200:
                        error_code = response.json().get('data', {}).get('RpcUpdateCugV2', {}).get('errorCode','failed')
                        if error_code is None:
                            success_log_msg += "CUG created successfully in create_cug_task {}, Segment - {} {} \n".format(
                                                hotelcode, segment, str(response.json()))
                        elif error_code in [CREATE_MOBILE_ERROR_CODE, UPDATE_MOBILE_ERROR_CODE, CREATE_MEMBER_ERROR_CODE, UPDATE_MEMBER_ERROR_CODE]:
                            success_log_msg += "CUG created successfully in create_cug_task {}, Segment - {}, Warning - {} \n".format(
                                                hotelcode, segment, BLACK_SUBTEXT )                   
                        else:
                            error_log_msg += "Error in create_cug_task {}, Segment - {},ErrorCode {} \n".format(hotelcode, 'All Segment', error_code)
                    else:
                        error_log_msg += "Error in create_cug_task {}, Segment - {},ErrorCode {} \n".format(hotelcode, 'All Segment', str(response))        
        try:
            if error_log_msg != "":
                sendMail(user_email, '', error_log_msg, 'ERROR - Invalid Hotels In CSV File', '', email_id_list, {}, [])
            if success_log_msg != "":
                sendMail(user_email, '', success_log_msg, 'Success Hotels In CSV File', '', email_id_list, {}, [])
        except Exception as e:
            log_msg = "Error in create_cug_task correlationKey- %s, %s, %s" % (correlation_key, str(e), repr(traceback.format_exc()))
            inventory_logger.critical(log_msg, log_type="ingoibibo",
                                      bucket="bulk_cug", stage="create_cug_task_promotionv2")
        mdb_storage.delete(filename)
    except Exception as e:
        mdb_storage.delete(filename)
        log_msg = "Error in create_cug_task correlationKey - %s, %s, %s" % (correlation_key, str(e), repr(traceback.format_exc()))
        inventory_logger.critical(log_msg, log_type="ingoibibo",
                                  bucket="bulk_cug", stage="create_cug_task_promotionv2")
        sendMail(user_email, '', log_msg, 'ERROR - in create_cug_task', '', email_id_list, {}, [])


@app.task(name="ingoibibo.create_offer_task")
def create_offer_task(filename, offer_condition_data=None, offer_value_data=None):
    email_id_list = settings.INGOIBIBO_ERROR_ALERT
    log_identifier = {}
    if offer_value_data and offer_value_data['user'] and offer_value_data['user'].email:
        email_id_list.append(offer_value_data['user'].email)
        email_id_set = set(email_id_list)
        email_id_list = list(email_id_set)
    try:
        saved_file = mdb_storage.open(filename)
        file_content = saved_file.read()
        file_data, invalid_hotel_codes = validate_offer_csv_data(file_content, email_id_list)
        data_size = len(file_data)

        start_index = 0
        batch_size = 100
        end_index = batch_size
        while start_index < data_size:
            temp_file_data = file_data[start_index:end_index]
            if settings.HOST in settings.PROD_HOSTS:
                create_offer_in_chunks.apply_async(args=(temp_file_data, offer_condition_data, offer_value_data,))
            else:
                create_offer_in_chunks(temp_file_data, offer_condition_data, offer_value_data)
            start_index = end_index
            end_index = start_index + batch_size
        if invalid_hotel_codes:
            try:
                sendMail('', '', json.dumps(invalid_hotel_codes), 'ERROR - Invalid Hotels In CSV File', '',
                         email_id_list,
                         {}, [])
            except Exception as e:
                update_error_identifier(error_message=str(e), traceback=repr(traceback.format_exc()),
                                        log_identifier=log_identifier)
                inventory_logger.critical(log_type="ingoibibo",
                                          bucket="add_on_bulk_upload", stage="create_add_ons_task",
                                          identifier="{}".format(log_identifier))
        mdb_storage.delete(filename)
    except Exception as e:
        mdb_storage.delete(filename)
        update_error_identifier(error_message=str(e), traceback=repr(traceback.format_exc()),
                                log_identifier=log_identifier)
        inventory_logger.critical(log_type="ingoibibo",
                                  bucket="add_on_bulk_upload", stage="create_add_ons_task",
                                  identifier="{}".format(log_identifier))
        log_msg = "Error in create_offer_task %s" % e
        sendMail('', '', log_msg, 'ERROR - in create_offer_task', '', email_id_list, {}, [])


@app.task(name="ingoibibo.create_cug_task")
def create_cug_task(filename, user, segment):
    email_id_list = settings.INGOIBIBO_ERROR_ALERT
    error_log_msg = ""
    success_log_msg = ""
    user_email = ""

    if user and user.email:
        user_email = user.email
        if user_email in email_id_list:
            email_id_list.remove(user_email)

    try:
        saved_file = mdb_storage.open(filename)
        file_content = saved_file.read()
        file_data = populate_cug_csv_data(file_content, user_email, email_id_list, segment)

        for data in file_data:
            resp = validate_request(data, True)
            if not resp.get('success'):
                error_log_msg += "Error in create_cug_task {}, {} \n".format(data.get('hotelcode'), resp['message'])
            else:
                post_data = populate_request_data(data)

                response, resp_status = create_cug_util(post_data, user=user, source=OfferSources.bulk_uploader)

                if not resp_status:
                    error_log_msg += "Error in create_cug_task {}, {}\n".format(data.get('hotelcode'),
                                                                                response['message'])
                else:
                    success_log_msg += "CUG created successfully in create_cug_task {}, {}\n".format(
                        data.get('hotelcode'), response['message'])

        try:
            if error_log_msg != "":
                sendMail('', '', error_log_msg, 'ERROR - Invalid Hotels In CSV File', '', email_id_list, {}, [])

            if success_log_msg != "":
                sendMail('', '', success_log_msg, 'Success Hotels In CSV File', '', email_id_list, {}, [])
        except Exception as e:
            log_msg = ("Error in create_cug_task %s, %s") % (e, repr(traceback.format_exc()))
            inventory_logger.critical(log_msg, log_type="ingoibibo",
                                      bucket="add_on_bulk_upload", stage="create_add_ons_task")
        mdb_storage.delete(filename)

    except Exception as e:
        mdb_storage.delete(filename)
        log_msg = ("Error in create_cug_task %s, %s") % (e, repr(traceback.format_exc()))
        inventory_logger.critical(log_msg, log_type="ingoibibo",
                                  bucket="add_on_bulk_upload", stage="create_add_ons_task")
        sendMail('', '', log_msg, 'ERROR - in create_cug_task', '', email_id_list, {}, [])


def populate_cug_csv_data(file_content, user_email, email_id_list, segment):
    import datetime
    file_data = []
    for row in csv.DictReader(file_content.splitlines(), quotechar='"', quoting=csv.QUOTE_ALL):
        try:
            hotel_code = row.get('hotelcode', '')
            offer_value = row.get('offervalue', '')
            if segment == MMT_SELECT :
                offer_value1 = row.get('MMTSelect1_value', '')
                offer_value2 = row.get('MMTSelect2_value', '')
            else :
                offer_value1 = row.get('MMTBlack1_value', '')
                offer_value2 = row.get('MMTBlack2_value', '')
            offer_values = [offer_value, offer_value1, offer_value2]
            segment = segment
            now = datetime.datetime.now()
            default_checkindatestart = now.strftime("%Y-%m-%d")
            default_checkoutdateend = (now + datetime.timedelta(days=1500)).strftime("%Y-%m-%d")

            if segment == 'MMT BLACK':

                checkindatestart = row.get('checkindatestart', default_checkindatestart)
                checkoutdateend = row.get('checkoutdateend', default_checkoutdateend)
                cug_segments = []
                black_offer_values = []
                for val, seg in zip(offer_values, CUG_BLACK_LIST):
                    if val:
                        black_offer_values.append(val)
                        cug_segments.append(seg)
                if black_offer_values:
                    offer_values = black_offer_values
                else:
                    offer_values = [offer_values[0]]
                    cug_segments = [CUG_BLACK_LIST[0]]

            elif segment == MMT_SELECT:

                checkindatestart = row.get('checkindatestart', default_checkindatestart)
                checkoutdateend = row.get('checkoutdateend', default_checkoutdateend)
                cug_segments = []
                select_offer_values = []
                for val, seg in zip(offer_values, CUG_SELECT_LIST):
                    if val:
                        select_offer_values.append(val)
                        cug_segments.append(seg)
                if select_offer_values:
                    offer_values = select_offer_values
                else:
                    offer_values = [offer_values[0]]
                    cug_segments = [CUG_SELECT_LIST[0]]

            else:
                try:
                    checkindatestart = row['checkindatestart']
                    checkoutdateend = row['checkoutdateend']
                    cug_segments = [segment]
                    offer_values = [offer_values[0]]
                except Exception as e:
                    raise Exception('please provide checkindatestart and checkoutdateend')

            checkinblackoutdates = row.get('checkinblackoutdates', "")
            if checkinblackoutdates:
                checkinblackoutdates = [blackout.split(':') for blackout in checkinblackoutdates.split(',')]
            bookingdatestart = row.get('bookingdatestart', "")
            bookingdateend = row.get('bookingdateend', "")
            nond = row.get('nond', False) in ['True', 'true', 1]

            for cug_segment, value in zip(cug_segments, offer_values):
                data = dict()
                data['hotelcode'] = hotel_code
                data['offer_value'] = value
                data['checkindatestart'] = checkindatestart
                data['checkoutdateend'] = checkoutdateend
                data['bookingdatestart'] = bookingdatestart
                data['bookingdateend'] = bookingdateend
                data['checkinblackoutdates'] = checkinblackoutdates
                data['nond'] = nond
                data['segment'] = cug_segment
                file_data.append(data)
        except Exception as e:
            log_msg = "Error in validate_offer_csv_data %s, %s" % (e, repr(traceback.format_exc()))
            inventory_logger.critical(log_msg, log_type="ingoibibo",
                                      bucket="validate_offer_csv_data", stage="create_offer")
            sendMail('', '', log_msg, 'ERROR - in validate_offer_csv_data', '', email_id_list, {}, [])

    return file_data


@app.task(name="ingoibibo.create_coupon_task_promotionv2")
def create_coupon_task_promotionv2(filename, user):
    email_id_list = settings.INGOIBIBO_ERROR_ALERT
    error_log_msg = ""
    success_log_msg = ""
    correlation_key = str(uuid.uuid4())

    if user and user.email:
        user_email = user.email
        if user_email in email_id_list:
            email_id_list.remove(user_email)
    try:
        saved_file = mdb_storage.open(filename)
        file_content = saved_file.read()
        hotelcode_to_mmtid_map = {}
        hotel_offer_map, hotelcode_list = populate_coupon_csv_data_promotionv2(file_content, email_id_list)

        if not isinstance(user, User):
            user = User.objects.get(id=user)
        hotelcode_mmtid_list = HotelDetail.objects.filter(hotelcode__in=hotelcode_list).values_list("hotelcode",
                                                                                                    "mmt_id")
        for hotelcode_mmtid in hotelcode_mmtid_list:
            hotelcode_to_mmtid_map[hotelcode_mmtid[0]] = str(hotelcode_mmtid[1])

        for hotelcode, data in hotel_offer_map.items():
            request_data = {"mmtId": hotelcode_to_mmtid_map.get(hotelcode, ''), "correlationKey": correlation_key, "offerData": data}
            response = create_coupon_promotionv2(request_data, user.id)
            data = response.get('data')
            if data:
                for offer_data in data:
                    if offer_data.get("errorCode") == "":
                        success_log_msg += "Coupon created successfully in create_coupon_task {}, {}\n".format(
                            hotelcode, offer_data['message'])
                    else:
                        error_log_msg += "Error in create_coupon_task {},  {}\n".format(hotelcode, offer_data['message'])
            else:
                error_log_msg += "Error in create_coupon_task {}, {}\n".format(hotelcode, response['message'])
        try:
            if error_log_msg != "":
                sendMail(user_email, '', error_log_msg, 'ERROR - Invalid Hotels In CSV File', '', email_id_list, {}, [])
            if success_log_msg != "":
                sendMail(user_email, '', success_log_msg, 'Success Hotels In CSV File', '', email_id_list, {}, [])
        except Exception as e:
            log_msg = "Error in create_coupon_task correlationKey- %s, %s, %s" % (correlation_key, str(e), repr(traceback.format_exc()))
            inventory_logger.critical(log_msg, log_type="ingoibibo",
                                      bucket="bulk_coupon", stage="create_coupon_task_promotionv2")
        mdb_storage.delete(filename)
    except Exception as e:
        mdb_storage.delete(filename)
        log_msg = "Error in create_coupon_task correlationKey - %s, %s, %s" % (correlation_key, str(e), repr(traceback.format_exc()))
        inventory_logger.critical(log_msg, log_type="ingoibibo",
                                  bucket="bulk_coupon", stage="create_coupon_task_promotionv2")
        sendMail(user_email, '', log_msg, 'ERROR - in create_coupon_task', '', email_id_list, {}, [])


def populate_coupon_csv_data_promotionv2(file_content, email_id_list):
    hotel_offer_map = {}
    hotelcode_list = []
    for row in csv.DictReader(file_content.splitlines(), quotechar='"', quoting=csv.QUOTE_ALL):
        try:
            coupon_map = {}
            hotelcode = row.get('hotelcode', '').strip()
            hotelcode_list.append(hotelcode)
            offer_value = row.get('offervalue', '')
            checkindatestart = row.get('checkindatestart', '')
            checkoutdateend = row.get('checkoutdateend', '')
            if not checkindatestart or not checkoutdateend:
                raise Exception('please provide checkindatestart and checkoutdateend')
            if checkindatestart:
                coupon_map['checkinDateStart'] = row.get('checkindatestart')
            if checkoutdateend:
                coupon_map['checkoutDateEnd'] = row.get('checkoutdateend')
            bookingdatestart = row.get('bookingdatestart', "")
            bookingdateend = row.get('bookingdateend', "")
            if bookingdatestart:
                coupon_map['bookingDateStart'] = bookingdatestart
            if bookingdateend:
                coupon_map['bookingDateEnd'] = bookingdateend
            checkinblackoutdates = row.get('checkinblackoutdates', "")
            if checkinblackoutdates:
                checkinblackoutdates = [",".join(blackout.split(':')) for blackout in checkinblackoutdates.split(',')]
            if checkinblackoutdates:
                coupon_map['checkinBlackoutDates'] = checkinblackoutdates
            coupon_map['offerValue'] = float(offer_value)
            if hotelcode in hotel_offer_map:
                hotel_offer_map[hotelcode].append(coupon_map)
            else:
                hotel_offer_map[hotelcode] = [coupon_map]
        except Exception as e:
            log_msg = "Error in validate_offer_csv_data %s, %s" % (e, repr(traceback.format_exc()))
            inventory_logger.critical(log_msg, log_type="ingoibibo",
                                      bucket="bulk_coupon", stage="populate_coupon_csv_data_promotionv2")
            sendMail('', '', log_msg, 'ERROR - in validate_offer_csv_data', '', email_id_list, {}, [])
    return hotel_offer_map, hotelcode_list


def populate_cug_csv_data_promotionv2(file_content, email_id_list, segment):
    hotel_offer_map = {}
    hotelcode_list = []

    for row in csv.DictReader(file_content.splitlines(), quotechar='"', quoting=csv.QUOTE_ALL):
        try:
            segment_map = {}
            hotelcode = row.get('hotelcode', '').strip()
            hotelcode_list.append(hotelcode)
            offer_value = row.get('offervalue', '')
            offer_value1=''
            offer_value2=''
            if segment == MMT_SELECT:
                offer_value1 = row.get('MMTSelect1_value', '')
                offer_value2 = row.get('MMTSelect2_value', '')
            offer_values = [offer_value, offer_value1, offer_value2]
            checkindatestart = row.get('checkindatestart', '')
            checkoutdateend = row.get('checkoutdateend', '')
            offercode = row.get('offercode', '')
            if segment == MMT_SELECT:
                cug_segments = []
                segment_offer_values = []
                cug_segment_list = CUG_BLACK_LIST if segment == MMT_BLACK else CUG_SELECT_LIST
                for val, seg in zip(offer_values, cug_segment_list):
                    if val:
                        segment_offer_values.append(val)
                        cug_segments.append(seg)
                if segment_offer_values:
                    offer_values = segment_offer_values
                else:
                    offer_values = [offer_values[0]]
                    cug_segments = [CUG_BLACK_LIST[0]]
            else:
                if not checkindatestart or not checkoutdateend:
                    raise Exception('please provide checkindatestart and checkoutdateend')
                cug_segments = [segment]
                offer_values = [offer_values[0]]

            create_affiliate = row.get('create_affiliate', False) in ['True', 'true', 1, 'TRUE', '1']
            if offercode:
                segment_map['offerCode'] = {'value': offercode}
            if checkindatestart:
                segment_map['checkinDateStart'] = {'value': row.get('checkindatestart')}
            if checkoutdateend:
                segment_map['checkoutDateEnd'] = {'value': row.get('checkoutdateend')}
            bookingdatestart = row.get('bookingdatestart', "")
            bookingdateend = row.get('bookingdateend', "")
            if bookingdatestart:
                segment_map['bookingDateStart'] = {'value': bookingdatestart}
            if bookingdateend:
                segment_map['bookingDateEnd'] = {'value': bookingdateend}
            checkinblackoutdates = row.get('checkinblackoutdates', "")
            if checkinblackoutdates:
                checkinblackoutdates = [",".join(blackout.split(':')) for blackout in checkinblackoutdates.split(',')]
            if checkinblackoutdates:
                segment_map['checkinBlackoutDates'] = checkinblackoutdates
            segment_map['offerValueList'] = []
            for cug_segment, value in zip(cug_segments, offer_values):
                data = dict()
                data['offerValue'] = {'value': int(value)}
                data['isActive'] = {'value': True}
                data['segment'] = CUG_PROMOTIONV2_KEY_MAPPING[cug_segment]
                segment_map['offerValueList'].append(data)
                if create_affiliate:
                    affiliate_cug_contracts = affiliate_cug_helper.get_affiliates_contract([CUG_SEGMENT_AF1],
                                                                                           [cug_segment])
                    for key, contract in affiliate_cug_contracts.items():
                        affiliate_data = {'offerValue': {'value': int(contract["offer_proportion"] * float(value))}, 'isActive': {'value': True}, 'segment': CUG_PROMOTIONV2_KEY_MAPPING[CUG_SEGMENT_AF1]}
                        segment_map['offerValueList'].append(affiliate_data)
            if segment == CUG_SEGMENT_CUG2 or segment == CUG_SEGMENT_CUG4 :
                timebased_offervalue = row.get('timebased_offervalue', '')
                if timebased_offervalue !='':
                    segment_map['timeBasedOfferData'] = []
                    timeBaseddata = dict()
                    timeBaseddata['startTime']= row.get('start_time', "")
                    timeBaseddata['endTime']= row.get('end_time', "")
                    timeBaseddata['offerValueList'] = [
                            {
                                "isActive": {"value": True},
                                "offerValue": {"value": int(timebased_offervalue)},
                                "segment": CUG_PROMOTIONV2_KEY_MAPPING[cug_segment]
                            }
                        ]
                    segment_map['timeBasedOfferData'].append(timeBaseddata)

            if hotelcode in hotel_offer_map:
                hotel_offer_map[hotelcode].append(segment_map)
            else:
                hotel_offer_map[hotelcode] = [segment_map]
        except Exception as e:
            log_msg = "Error in validate_offer_csv_data %s, %s" % (e, repr(traceback.format_exc()))
            inventory_logger.critical(log_msg, log_type="ingoibibo",
                                      bucket="bulk_cug", stage="populate_cug_csv_data_promotionv2")
            sendMail('', '', log_msg, 'ERROR - in validate_offer_csv_data', '', email_id_list, {}, [])
    return hotel_offer_map, hotelcode_list


def modify_timebase_data(data, get_offer_response, segment):
    if segment == CUG_SEGMENT_CUG2 or segment == CUG_SEGMENT_CUG4 and len(data[0].get('timeBasedOfferData', [])) > 0:
        if len(get_offer_response['data']['offerDataList'][0]['timeBasedOfferData']) > 0:
            offer_code = get_offer_response['data']['offerDataList'][0]['timeBasedOfferData'][0]['offerCode']
            data[0]['timeBasedOfferData'][0]['offerCode'] = {"value": offer_code}

            for offer_data in get_offer_response['data']['offerDataList'][0]['timeBasedOfferData'][1:]:
                timeBaseddata = dict()
                timeBaseddata['startTime']=  offer_data['startTime']
                timeBaseddata['endTime']= offer_data['endTime']
                timeBaseddata['offerValueList'] = [
                        {
                            "isActive": {"value": False},
                            "offerValue": {"value": int(offer_data['offerValueDataList'][0]['offerValue'])},
                            "segment": CUG_PROMOTIONV2_KEY_MAPPING[segment]
                        }
                    ]
                timeBaseddata['offerCode'] = {"value": offer_data['offerCode']}
                data[0]['timeBasedOfferData'].append(timeBaseddata)
    return data

@app.task(name="ingoibibo.create_coupon_task")
def create_coupon_task(filename, user):
    email_id_list = settings.INGOIBIBO_ERROR_ALERT
    error_log_msg = ""
    success_log_msg = ""

    if user and user.email:
        email_id_list.append(user.email)
        email_id_set = set(email_id_list)
        email_id_list = list(email_id_set)

    try:
        saved_file = mdb_storage.open(filename)
        file_content = saved_file.read()
        file_data = populate_coupon_csv_data(file_content, email_id_list)

        for data in file_data:
            resp = validate_coupon_request(data)
            if not resp.get('success'):
                error_log_msg += "Error in create_coupon_task {}, {} \n".format(data.get('hotelcode'), resp['message'])
            else:
                post_data = populate_coupon_request_data(data)

                response, resp_status = create_hotel_offer_util(post_data, user=user, source=OfferSources.bulk_uploader)

                if not resp_status:
                    error_log_msg += "Error in create_coupon_task {}, {}\n".format(data.get('hotelcode'),
                                                                                   response['message'])
                else:
                    success_log_msg += "Coupon created successfully in create_coupon_task {}, {}\n".format(
                        data.get('hotelcode'), response['message'])

        try:
            if error_log_msg != "":
                sendMail('', '', error_log_msg, 'ERROR - Invalid Hotels In CSV File', '', email_id_list, {}, [])

            if success_log_msg != "":
                sendMail('', '', success_log_msg, 'Success Hotels In CSV File', '', email_id_list, {}, [])
        except Exception as e:
            log_msg = ("Error in create_coupon_task %s, %s") % (e, repr(traceback.format_exc()))
            inventory_logger.critical(log_msg, log_type="ingoibibo",
                                      bucket="add_on_bulk_upload", stage="create_add_ons_task")
        mdb_storage.delete(filename)

    except Exception as e:
        mdb_storage.delete(filename)
        log_msg = ("Error in create_coupon_task %s, %s") % (e, repr(traceback.format_exc()))
        inventory_logger.critical(log_msg, log_type="ingoibibo",
                                  bucket="add_on_bulk_upload", stage="create_add_ons_task")
        sendMail('', '', log_msg, 'ERROR - in create_coupon_task', '', email_id_list, {}, [])


def populate_coupon_csv_data(file_content, email_id_list):
    file_data = []
    for row in csv.DictReader(file_content.splitlines(), quotechar='"', quoting=csv.QUOTE_ALL):
        try:
            hotel_code = row.get('hotelcode', '')
            offer_value = row.get('offervalue', '')

            try:
                checkindatestart = row['checkindatestart']
                checkoutdateend = row['checkoutdateend']
            except Exception as e:
                raise Exception('please provide checkindatestart and checkoutdateend')

            bookingdatestart = row.get('bookingdatestart', "")
            bookingdateend = row.get('bookingdateend', "")

            data = dict()
            data['hotelcode'] = hotel_code
            data['offer_value'] = offer_value
            data['checkindatestart'] = checkindatestart
            data['checkoutdateend'] = checkoutdateend
            data['bookingdatestart'] = bookingdatestart
            data['bookingdateend'] = bookingdateend
            file_data.append(data)
        except Exception as e:
            log_msg = "Error in validate_offer_csv_data %s, %s" % (e, repr(traceback.format_exc()))
            inventory_logger.critical(log_msg, log_type="ingoibibo",
                                      bucket="validate_offer_csv_data", stage="create_offer")
            sendMail('', '', log_msg, 'ERROR - in validate_offer_csv_data', '', email_id_list, {}, [])

    return file_data


@app.task(name="ingoibibo.create_offer_in_chunks")
def create_offer_in_chunks(file_data, offer_condition_data, offer_value_data):
    email_id_list = settings.INGOIBIBO_ERROR_ALERT
    if offer_value_data and offer_value_data['user'] and offer_value_data['user'].email:
        email_id_list.append(offer_value_data['user'].email)
        email_id_set = set(email_id_list)
        email_id_list = list(email_id_set)
    invalid_data = []
    segment_list = offer_value_data['segment']
    offer_value_data.pop('segment')
    log_identifier = {}
    hotelwise_status = {}
    try:
        for data in file_data:
            try:
                log_identifier['request_id'] = data.get('object_id', '')
                hotelcode = int(data['object_id'])
                approver = data.get('approver', '')
                offer_value = data.get('value', None)

                hotel_obj = HotelDetail.objects.filter(id=hotelcode).first()
                change_request_validator(hotel_obj, ContentTypeIdForHotel, COMMISSION_BASED_OFFER_CHANGE)

                approver_obj = validate_and_fetch_approver_obj_for_change_request(hotel_obj, approver,
                                                                                  COMMISSION_BASED_OFFER_CHANGE)
                requester_ldap_obj, approver_ldap_obj = fetch_requester_approver_ldap_details(hotel_obj, offer_value_data['user'],
                                                                            approver_obj)

                create_offer_payload = {
                    "checkinDateStart": offer_condition_data.get('checkindatestart').strftime('%Y-%m-%d') if offer_condition_data.get('checkindatestart') is not None else None,
                    "checkinDateEnd": offer_condition_data.get('checkoutdateend').strftime('%Y-%m-%d') if offer_condition_data.get('checkoutdateend') is not None else None,
                    "bookingDateStart": offer_condition_data.get('bookingdatestart').strftime('%Y-%m-%d') if offer_condition_data.get('bookingdatestart') is not None else None,
                    "bookingDateEnd": offer_condition_data.get('bookingdateend').strftime('%Y-%m-%d') if offer_condition_data.get('bookingdateend') is not None else None,
                    "description": "ORC created from bulk upload",
                    "channel": offer_value_data.get('channel'),
                    "onlyPahApplicable": validate_bool(offer_condition_data.get('only_pah_applicable')) if offer_condition_data.get('only_pah_applicable') else None,
                    "pahApplicable": validate_bool(offer_condition_data.get('pah_applicable')) if offer_condition_data.get('pah_applicable') else None,
                    "segmentList": {"value": segment_list},
                    "checkinWeekday": {"value": offer_condition_data.get('checkinweekday')},
                    "bookingWeekday": {"value": offer_condition_data.get('bookingweekday')},
                    "offerValue": offer_value
                }

                change_request_data, formatted_req = create_change_request_data(hotel_obj, requester_ldap_obj,
                                                                                approver_ldap_obj, create_offer_payload,
                                                                                COMMISSION_BASED_OFFER_CHANGE)
                push_change_request_packet_to_dynamic_hub(str(hotelcode), change_request_data, True)

                api_logger.info("Pushing changeRequest  for hotel id: {} for requestType: {} with data: {}".format(
                    hotelcode, COMMISSION_BASED_OFFER_CHANGE, formatted_req), log_type="ingoibibo", bucket='hotels.tasks',
                    stage='hotels.tasks.create_offer_task')

            except ValidationError as e:
                update_error_identifier(error_message=str(e), traceback=repr(traceback.format_exc()),
                                        log_identifier=log_identifier)
                inventory_logger.critical(log_type="ingoibibo",
                                            bucket="create_offer_in_chunks", stage="create_offer",
                                            identifier="{}".format(log_identifier))
                hotelwise_status[int(data['object_id'])] = str(e)
                invalid_data.append(data)
            except Exception as e:
                update_error_identifier(error_message=str(e), traceback=repr(traceback.format_exc()),
                                        log_identifier=log_identifier)
                inventory_logger.critical(log_type="ingoibibo",
                                          bucket="create_offer_in_chunks", stage="create_offer",
                                          identifier="{}".format(log_identifier))
                hotelwise_status[int(data['object_id'])] = str(e)
                invalid_data.append(data)
        if invalid_data:
            sendMail('', '', json.dumps(hotelwise_status), 'Error - Invalid Data In Offer CSV File', '', email_id_list, {},
                     [])
    except Exception as e:
        update_error_identifier(error_message=str(e), traceback=repr(traceback.format_exc()),
                                log_identifier=log_identifier)
        inventory_logger.critical(log_type="ingoibibo",
                                  bucket="create_offer_in_chunks", stage="create_offer",
                                  identifier="{}".format(log_identifier))
        log_msg = "Error in create_offer_in_chunks %s" % e
        sendMail('', '', log_msg, 'ERROR - in create_offer_in_chunks', '', email_id_list, {}, [])


@app.task(name="ingoibibo.task_hotel_usp_create_from_input_file")
def task_hotel_usp_create_from_input_file(filename, user):
    subtask_jobs = []
    try:
        file_data, invalid_hotel_codes = filter_csv_valid_data(filename)
        data_size = len(file_data)

        start_index = 0
        batch_size = 100
        end_index = batch_size
        while start_index < data_size:
            temp_file_data = file_data[start_index:end_index]
            result = create_usp_in_chunks.apply_async(args=(temp_file_data,))
            subtask_jobs.append(result.id)
            start_index = end_index
            end_index = start_index + batch_size
    except Exception as e:
        log_msg = ("Error in task_hotel_usp_create_from_input_file %s, %s") % (e, repr(traceback.format_exc()))
        inventory_logger.critical(log_msg, log_type="ingoibibo",
                                  bucket="hotels.tasks", stage="task_hotel_usp_create_from_input_file")

    return {'subtask_jobs': subtask_jobs, 'invalid_hotel_codes': invalid_hotel_codes}


@app.task(name="ingoibibo.create_usp_in_chunks")
def create_usp_in_chunks(file_data):
    invalid_data = []
    new_usps_id = []
    try:
        for data in file_data:
            try:
                from hotels.bookingpayments import close_db_connection
                close_db_connection()

                usptag = data.get('value', {}).get('tag').strip() if data.get('value', {}).get('tag') else None
                usp_text = data.get('value', {}).get('usp_text').strip() if data.get('value', {}).get(
                    'usp_text') else None
                if usptag and usp_text:
                    usptag_obj, created = USPTag.objects.get_or_create(title=usptag)
                    object_id = int(data['object_id'])
                    content_type = hotel_content_type

                    # Explicitly trigger post save as it's not triggering
                    # https://goibibo.atlassian.net/browse/IC-708
                    from django.db.models.signals import post_save
                    usp_detail_obj = USPDetail(content_type=content_type, object_id=object_id, usp_tag=usptag_obj,
                                               usp_text=usp_text)
                    usp_detail_obj.save()
                    post_save.send(USPDetail, instance=usp_detail_obj, created=True)
                    new_usps_id.append(usp_detail_obj.id)
                else:
                    invalid_data.append(data)
            except Exception as e:
                log_msg = ("Error in create_usp_in_chunks %s, %s") % (e, repr(traceback.format_exc()))
                inventory_logger.critical(log_msg, log_type="ingoibibo",
                                          bucket="hotel.tasks", stage="create_usp_in_chunks")
                invalid_data.append(data)
    except Exception as e:
        log_msg = ("Error in create_usp_in_chunks %s, %s") % (e, repr(traceback.format_exc()))
        inventory_logger.critical(log_msg, log_type="ingoibibo",
                                  bucket="hotels.tasks", stage="create_usp_in_chunks")

    return {'new_usps_id': new_usps_id, 'invalid_data': invalid_data}


def validate_offer_csv_data(file_content, email_id_list):
    file_data = []
    invalid_hotel_codes = []
    received_hotel_codes = []
    hotel_code_offer_value_map = dict()
    hotel_code_hotel_id_map = dict()
    log_identifier = {}
    for row in csv.DictReader(file_content.splitlines(), quotechar='"', quoting=csv.QUOTE_ALL):
        try:
            hotel_code = row.get('hotelcode')
            hotel_code_offer_value_map[hotel_code] = {}
            hotel_code_offer_value_map[hotel_code]["offer_value"] = row.get('offervalue')
            hotel_code_offer_value_map[hotel_code]["approver"] = row.get('approver')
            received_hotel_codes.append(hotel_code)
        except Exception as e:
            update_error_identifier(error_message=str(e), traceback=repr(traceback.format_exc()),
                                    log_identifier=log_identifier)
            inventory_logger.critical(log_type="ingoibibo",
                                      bucket="validate_offer_csv_data", stage="create_offer",
                                      identifier="{}".format(log_identifier))
            log_msg = "Error in validate_offer_csv_data %s" % e
            sendMail('', '', log_msg, 'ERROR - in validate_offer_csv_data', '', email_id_list, {}, [])
    try:
        hotel_details = HotelDetail.objects.filter(hotelcode__in=received_hotel_codes).values('id', 'hotelcode')
        for data in hotel_details:
            hotel_code_hotel_id_map[data['hotelcode']] = data['id']

        for hotel_code in hotel_code_offer_value_map.keys():
            if hotel_code_hotel_id_map.has_key(hotel_code):
                data = dict()
                data['object_id'] = hotel_code_hotel_id_map[hotel_code]
                data['value'] = hotel_code_offer_value_map[hotel_code]["offer_value"]
                data['approver'] = hotel_code_offer_value_map[hotel_code]["approver"]
                file_data.append(data)
            else:
                invalid_hotel_codes.append(hotel_code)
    except Exception as e:
        update_error_identifier(error_message=str(e), traceback=repr(traceback.format_exc()),
                                log_identifier=log_identifier)
        inventory_logger.critical(log_type="ingoibibo",
                                  bucket="validate_offer_csv_data", stage="create_offer",
                                  identifier="{}".format(log_identifier))
        log_msg = "Error in validate_offer_csv_data %s" % e
        sendMail('', '', log_msg, 'ERROR - in validate_offer_csv_data', '', email_id_list, {}, [])
    return file_data, invalid_hotel_codes


def filter_csv_valid_data(filename):
    file_data = []
    invalid_hotel_codes = []
    received_hotel_codes = []
    hotel_code_map = dict()
    hotel_code_hotel_id_map = dict()
    try:
        with mdb_storage.open(filename) as mapping_csv:
            reader = csv.DictReader(mapping_csv)
            for record in reader:
                hotel_code = record.get('hotelcode').strip() if record.get('hotelcode') else None
                if hotel_code:
                    received_hotel_codes.append(hotel_code)
                    if not hotel_code_map.get(hotel_code):
                        hotel_code_map[hotel_code] = {}
                    list_of_usps_for_hotelcode = hotel_code_map.get(hotel_code, {}).get('records', [])
                    list_of_usps_for_hotelcode.append(record)
                    hotel_code_map[hotel_code]['records'] = list_of_usps_for_hotelcode
    except Exception as e:
        log_msg = ("Error in filter_csv_valid_data %s, %s") % (e, repr(traceback.format_exc()))
        inventory_logger.critical(log_msg, log_type="ingoibibo",
                                  bucket="hotels.tasks", stage="filter_csv_valid_data")

    try:
        hotel_details = HotelDetail.objects.filter(hotelcode__in=received_hotel_codes).values('id', 'hotelcode')
        for hotel_detail in hotel_details:
            hotel_code_hotel_id_map[hotel_detail['hotelcode']] = hotel_detail['id']

        for hotel_code in hotel_code_map.keys():
            if hotel_code_hotel_id_map.has_key(hotel_code):
                for record in hotel_code_map[hotel_code]['records']:
                    data = dict()
                    data['object_id'] = hotel_code_hotel_id_map[hotel_code]
                    data['value'] = record
                    file_data.append(data)
            else:
                invalid_hotel_codes.append(hotel_code)
    except Exception as e:
        log_msg = ("Error in filter_csv_valid_data %s, %s") % (e, repr(traceback.format_exc()))
        inventory_logger.critical(log_msg, log_type="ingoibibo",
                                  bucket="hotels.tasks", stage="filter_csv_valid_data")
    return file_data, invalid_hotel_codes


@app.task(name="ingoibibo.booking_status_push")
def booking_status_push(user, booking, status_update_data):
    from api.v1.bookings.resources.booking_status_handler import StatusManager
    try:
        celery_logger.info('%s\t%s\t%s\t Booking Id : %s' % ('hotels',
                                                             'tasks',
                                                             'booking_status_push Started.',
                                                             booking.confirmbookingid))
        response = StatusManager.status_push_wrapper(status_update_data, booking)
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t Booking Id : %s' % (
            'hotels', 'tasks', 'booking_status_push', booking.confirmbookingid, str(e),
            repr(traceback.format_exc())))


# TODO: uncomment this task after production celery task failure issue resolved.
# @app.task(name='ingoibibo.send_booking_voucher')
# def send_voucher_task(booking_obj, view, send, profile_update, mail_record_obj, extra_subject, extra_email):
#     from hotels.voucher_manager import VoucherManager
#     VoucherManager.process_voucher(booking_obj, view, send, profile_update, mail_record_obj, extra_subject, extra_email)


# Post confirm booking task are-
# 1. Initiate VCC payment if applicable
# 2. Autoconfirm status if applicable
# 3. Send voucher and sms

# TODO: uncomment this task after production celery task failure issue resolved.
# @app.task(name='ingoibibo.post_confirmation_booking_task')
# def post_confirmation_booking_task(booking_obj, booking_dict, mail_record_obj=''):
#     try:
#         from hotels.vcc_payment import initiate_booking_vcc
#         from hotels.hotelchoice import RTP
#         if booking_obj.hotel.booking_auto_confirm and not booking_obj.payathotelflag:
#             is_rtp_model = booking_obj.hotel.booking_model == RTP
#             # if the hotel is on RTP model, vcc creation is happening as part of confirmbookingapi itself and
#             # skip vcc creation in such case
#             if booking_obj.hotel.vcc_payment and not is_rtp_model:
#                 payment_success = initiate_booking_vcc(booking_obj.confirmbookingid, True)
#             else:
#                 payment_success = True
#             if payment_success:
#                 try:
#                     status_update_data = {'user': booking_dict['user'], 'status': 'confirmed',
#                                           'reason': 'autoconfirmflag',
#                                           'source_key': 9, 'initiated_by': booking_dict['user'].username}
#                     booking_status_push(booking_dict['user'], booking_obj, status_update_data)
#                 except Exception as ex:
#                     inventory_logger.critical(message='Exception occurred while updating status for booking id %s, %s'
#                                                       % (booking_obj.confirmbookingid, str(ex)),
#                                               log_type='ingoibibo', bucket='post confirm booking task',
#                                               stage='post_confirmation_booking_task')
#             else:
#                 inventory_logger.error(message='Initiate booking vcc payment failed for booking id %s' % (
#                     booking_obj.confirmbookingid), log_type='ingoibibo',
#                                        bucket='post confirm booking task', stage='post_confirmation_booking_task')
#         else:
#             inventory_logger.info(
#                 message='Booking id %s is Non ARC or PAH' % (booking_obj.confirmbookingid),
#                 log_type='ingoibibo', bucket='post confirm booking task',
#                 stage='post_confirmation_booking_task')
#
#         misc = json.loads(booking_obj.misc) if booking_obj.misc else {}
#         if misc.get('send_voucher', True):
#             from communication.notifications import create_mail_record_entry
#             mail_record_obj = create_mail_record_entry(booking_obj, '', True)
#             if settings.VOUCHER_DEBUG:
#                 send_voucher_task(booking_obj, False, True, False, mail_record_obj, '', '')
#             else:
#                 send_voucher_task.apply_async(args=(booking_obj, False, True, False, mail_record_obj, '', '',),
#                                               countdown=5)
#             send_hotel_booking_sms_task(booking_obj)
#     except Exception as exp:
#         inventory_logger.critical(
#             message='Booking id : %s, Exception: %s' % (booking_obj.confirmbookingid, str(exp)),
#             log_type='ingoibibo', bucket='post confirm booking task',
#             stage='post_confirmation_booking_task')


@app.task(name="ingoibibo.update_rateplan_commission_task")
def update_rateplan_comission_task(rateplancode, commission_value, user, bulk_uploader_file_name, request_data=None):
    try:
        rateplan = RatePlan.objects.only('sellcommission', 'modifiedon', 'user').get(rateplancode=rateplancode)
        rateplan.sellcommission = commission_value
        rateplan.modifiedon = datetime.datetime.now()
        rateplan.user = user
        rateplan.save(update_fields=['sellcommission', 'modifiedon', 'user'], request_data=request_data,
                      admin_save=True, rate_change_required=False, isCommissionUpdateAllowed=True)
        content_type_id = get_content_id_for_model('rateplan')
        LogEntry.objects.log_action(user_id=user.pk, content_type_id=content_type_id,
                                    object_id=rateplan.id, object_repr=force_unicode(rateplan), action_flag=CHANGE,
                                    change_message='Commission value changed to %s through rateplan_commission_bulk_uploader: %s'
                                                   % (commission_value, bulk_uploader_file_name))
    except Exception as ex:
        content = 'Error in updating commission for rateplan: %s' % (rateplan.rateplancode)
        sendMail('', '', content, content, '', [user.email], {}, [])
        inventory_logger.error(message='Exception occured while updating commission for rateplancode {rp} value {v} '
                                       'exception {e} traceback {t}'
                               .format(rp=rateplancode, v=commission_value, e=ex, t=repr(traceback.format_exc())),
                               log_type='ingoibibo', bucket='update_rateplan_comission_task',
                               stage='update_rateplan_comission_task')


@app.task(name="ingoibibo.create_free_cancellation_rule_task")
def create_free_cancellation_rule_task(hotelcode_blackouts_map, free_cancellation_rule, user, bulk_uploader_file_name):
    failed_hotel_codes = []
    free_cancellation_rule_content_id = get_content_id_for_model("cancellationrules")
    priority = priority_map['FC']
    is_special_policy = free_cancellation_rule.pop('is_special_policy', False)
    if is_special_policy:
        priority = SPECIAL_POLICY_PRIORITY
    curr_date = datetime.date.today()
    for hotel_code, blackouts_list in hotelcode_blackouts_map.items():
        curr_time = datetime.datetime.now()
        try:
            hotel_id = helper.get_id_from_code(hotel_code, HotelConf.HotelCodeLength, HotelConf.HotelCodePrefix)
            free_cancellation_rule['object_id'] = hotel_id
            free_cancellation_rule['user_id'] = user.id
            free_cancellation_rule['content_type'] = hotel_content_type
            free_cancellation_rule['priority'] = priority
            free_cancellation_rule['created_on'] = curr_time
            free_cancellation_rule['modified_on'] = curr_time

            blackout_objs = []
            with transaction.atomic():
                free_cancellation_rule_obj = CancellationRules.objects.create(**free_cancellation_rule)
                if is_special_policy:
                    free_cancellation_rule_obj.is_special_policy = True
                    free_cancellation_rule_obj.save()
                if blackouts_list and not is_special_policy:
                    new_blackout_object = []
                    completed_blackouts = set([])
                    for blackout in blackouts_list:
                        if blackout:
                            if (blackout < free_cancellation_rule_obj.stay_start.date() or
                                    blackout > free_cancellation_rule_obj.stay_end.date() or blackout < curr_date):
                                raise Exception("Invalid blackout date")
                            if blackout in completed_blackouts:
                                continue
                            completed_blackouts.add(blackout)
                            new_blackout_object.append(CancellationBlackOutDates(cancel_rule=free_cancellation_rule_obj,
                                                                                 black_out_date=blackout,
                                                                                 is_active=True))
                    CancellationBlackOutDates.objects.bulk_create(new_blackout_object)

            LogEntry.objects.log_action(
                user_id=user.pk, content_type_id=free_cancellation_rule_content_id,
                object_id=free_cancellation_rule_obj.id,
                object_repr=force_unicode(free_cancellation_rule_obj), action_flag=ADDITION,
                change_message='Created through free_cancellation bulk uploader:%s with blackout_date ids %s' % (
                    bulk_uploader_file_name, [item.id for item in blackout_objs]))

        except Exception as ex:
            failed_hotel_codes.append(hotel_code)
            inventory_logger.error(
                message='Exception while creating free cancellation rule for hotel %s :%s' % (hotel_code, str(ex)),
                log_type='ingoibibo', bucket='create_free_cancellation_rule_task',
                stage='create_free_cancellation_rule_task')

    if failed_hotel_codes:
        content = 'Error in creating free cancellation rule for these hotelcodes: %s' % (",".join(failed_hotel_codes))
        try:
            pass
            sendMail('', '', content, content, '', [user.email], {}, [])
        except Exception as ex:
            inventory_logger.error(message='Exception occured while sending mail for failed hotelcodes %s' % (str(ex)),
                                   log_type='ingoibibo', bucket='create_free_cancellation_rule_task',
                                   stage='create_free_cancellation_rule_task')


@app.task(name="ingoibibo.update_free_cancellation_rule_task_new")
def update_free_cancellation_rule_task_new(ruleid_action_blackout_map, user, bulk_uploader_file_name):
    from collections import defaultdict
    failed_rule_ids = []
    errors = []
    free_cancellation_rule_content_id = get_content_id_for_model("cancellationrules")
    curr_date = datetime.date.today()
    curr_time = datetime.datetime.now()
    cancel_rule_ids = list(ruleid_action_blackout_map.keys())
    blackout_objects = CancellationBlackOutDates.objects.filter(cancel_rule_id__in=cancel_rule_ids)

    blackout_objects_map = defaultdict(lambda: defaultdict(lambda: [[], []]))
    for item in blackout_objects:
        cancel_rule_id = item.cancel_rule.id
        if cancel_rule_id not in blackout_objects_map:
            blackout_objects_map[cancel_rule_id]['range'] = [item.cancel_rule.stay_start, item.cancel_rule.stay_end]
        blackout_objects_map[cancel_rule_id][int(item.is_active)][0].append(item.black_out_date)
        blackout_objects_map[cancel_rule_id][int(item.is_active)][1].append(item.id)

    for rule_id, action_blackout_map in ruleid_action_blackout_map.items():
        try:
            create_blackout_objects = []
            completed_blackouts = set([])
            update_blackouts = [[], []]
            rule_specific_blackout_map = blackout_objects_map[rule_id]
            inactive_blackouts = rule_specific_blackout_map[0]
            active_blackouts = rule_specific_blackout_map[1]

            for blackout in action_blackout_map['add']:
                if blackout < curr_date:
                    errors.append((rule_id, blackout, 'add'))
                    raise Exception("Blackout should be equal or greater than today")
                if blackout in inactive_blackouts[0]:
                    blackout_index = inactive_blackouts[0].index(blackout)
                    update_blackouts[1].append(inactive_blackouts[1][blackout_index])
                elif blackout in active_blackouts[0]:
                    errors.append((rule_id, blackout, 'add'))
                    raise Exception
                else:
                    if (blackout < rule_specific_blackout_map['range'][0] or
                            blackout > rule_specific_blackout_map['range'][1]):
                        raise Exception("Invalid blackout dates")
                    if blackout in completed_blackouts:
                        continue
                    completed_blackouts.add(blackout)
                    create_blackout_objects.append(
                        CancellationBlackOutDates(cancel_rule_id=rule_id, black_out_date=blackout,
                                                  is_active=True))

            for blackout in action_blackout_map['delete']:
                if blackout in active_blackouts[0]:
                    blackout_index = active_blackouts[0].index(blackout)
                    update_blackouts[0].append(active_blackouts[1][blackout_index])
                else:
                    errors.append((rule_id, blackout, 'delete'))
                    raise Exception

            if create_blackout_objects:
                CancellationBlackOutDates.objects.bulk_create(create_blackout_objects)

            for idx, blackout_vals in enumerate(update_blackouts):
                if blackout_vals:
                    CancellationBlackOutDates.objects.filter(id__in=blackout_vals).update(is_active=bool(idx))

            CancellationRules.objects.filter(id__in=set(cancel_rule_ids)).update(modified_on=curr_time)

            LogEntry.objects.log_action(
                user_id=user.pk, content_type_id=free_cancellation_rule_content_id,
                object_id=rule_id, object_repr='', action_flag=ADDITION,
                change_message='Updated through new free_cancellation bulk uploader:%s for rule id %s' % (
                    bulk_uploader_file_name, rule_id))

        except Exception as ex:
            failed_rule_ids.append(rule_id)
            message = 'Exception occured while updating new free cancellation rule : '
            if errors:
                message += ",".join(map(str, errors))
            else:
                message += str(ex)
            inventory_logger.error(message=message, log_type='ingoibibo',
                                   bucket='update_free_cancellation_rule_task_new',
                                   stage='update_free_cancellation_rule_task_new')

    if failed_rule_ids:
        content = 'Error in updating new free cancellation rule for these rule ids: %s' % (
            ",".join(map(str, failed_rule_ids)))
        try:
            sendMail('', '', content, content, '', [user.email], {}, [])
        except Exception as ex:
            inventory_logger.error(message='Exception occured while sending mail for failed rule ids(update) %s' % (
                ",".join(map(str, failed_rule_ids))), log_type='ingoibibo',
                                   bucket='update_free_cancellation_rule_task_new',
                                   stage='update_free_cancellation_rule_task_new')


@app.task(name="ingoibibo.migrate_hotel_inventory")
def migrate_hotel_inventory(hotel_id, goku_host):
    try:
        inventory_logger.info(message="Inventory migration started for hotel %d" % (hotel_id),
                              bucket="tasks.migrate_hotel_inventory",
                              stage="migrate_hotel_inventory")
        h = HotelDetail.objects.get(id=hotel_id)
        migrate_hotel(hotel_id, goku_host)
        h.enable_phoenix_call_inventory = True
        h.save()
        migrate_hotel(hotel_id, goku_host)
        inventory_logger.info(message="Inventory migration finished for hotel %d" % (hotel_id),
                              bucket="tasks.migrate_hotel_inventory",
                              stage="migrate_hotel_inventory")
    except Exception as ex:
        inventory_logger.error(
            message='Exception occured while migrating inventory for hotel %s %s' % (str(hotel_id), str(ex)),
            log_type='ingoibibo', bucket='migrate_hotel_inventory',
            stage='migrate_hotel_inventory')


def migrate_hotel(hotel_id, goku_host):
    url = goku_host + '/migrate-hotels-inventory'

    payload = 'hotelcodelist=' + str(hotel_id)
    headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    }

    response = requests.request("POST", url, headers=headers, data=payload)


@app.task(name="ingoibibo.update_hotel_flag_value_task")
def update_hotel_flag_value_task(hotel_code_list, flag_name, flag_value, user, bulk_uploader_file_name, flag_extra_data):
    start = time()
    is_backward_compatible = False
    failed_hotel_codes = []
    hotel_objects = HotelDetail.objects.only('id', 'modifiedon', 'flag_bits_1', 'flag_bits_3','flag_bits_4', 'hotelcode',
                                             'contractbdo', HotelService.FIELD_ABSO_REASON).filter(
        hotelcode__in=hotel_code_list)
    content_type_id = get_content_id_for_model('hoteldetail')
    error_msg = ""
    for hotel_obj in hotel_objects:
        try:
            hotel_obj.modifiedon = datetime.datetime.now()
            setattr(hotel_obj, flag_name, flag_value)

            if flag_name == HotelService.FIELD_ABSO and flag_value:
                abso_reason = flag_extra_data.get(HotelService.FIELD_ABSO_REASON, None)
                if not HotelService.is_hotel_abso_valid(flag_value, abso_reason):
                    raise ValidationError({HotelService.FIELD_ABSO_REASON: HotelService.ERROR_ABSO_REASON_UNAVAILABLE})

                hotel_obj.abso_reason = abso_reason
            elif flag_name == HotelService.FIELD_ABSO and not flag_value:
                hotel_obj.abso_reason = None

            # for backward compatibility with myBiz
            from hotels.models.hotel_flag_configuration import GST_WITH_PENALTY, GST_WITHOUT_PENALTY
            if flag_name == GST_WITH_PENALTY or flag_name == GST_WITHOUT_PENALTY:
                is_backward_compatible = True
                # Check if GSTNAssurance entry exists for the combination of hotelcode and lob
                gstn_assurance, created = GSTNAssurance.objects.get_or_create(hotelcode=hotel_obj.hotelcode, lob=MY_BIZ_CAMEL_CASE, defaults={'hotelcode': hotel_obj.hotelcode, 'lob': MY_BIZ_CAMEL_CASE})

                if flag_name == GST_WITH_PENALTY and flag_value is not None:
                    gstn_assurance.gst_with_penalty = flag_value
                if flag_name == GST_WITHOUT_PENALTY and flag_value is not None:
                    gstn_assurance.gst_without_penalty = flag_value
                if gstn_assurance.gst_with_penalty or gstn_assurance.gst_without_penalty:
                    gstn_assurance.gstn_assured = True
                else:
                    gstn_assurance.gstn_assured = False # setting it to false if both flags are false because hotel can be removed from gstn assured list
                gstn_assurance.modifiedon = datetime.datetime.now()
                gstn_assurance.save()

            hotel_obj.save(update_fields=['flag_bits_1', 'flag_bits_3','flag_bits_4', 'modifiedon', 'contractbdo', HotelService.FIELD_ABSO_REASON,
                                          HotelService.FIELD_ABSO_MODIFIED], silent_save_flag=True)
            LogEntry.objects.log_action(user_id=user.pk, content_type_id=content_type_id,
                                        object_id=hotel_obj.id, object_repr=force_unicode(hotel_obj),
                                        action_flag=CHANGE,
                                        change_message='Value of Key %s changed to %s through flag bit bulk uploader: %s'
                                                       % (flag_name, flag_value, bulk_uploader_file_name))

            # if changing a flagbit requires sync at mojo end, then push to mojo
            if flag_name in MOJO_PUSH_FLAGBITS:
                    update_hotel_nav_task_v2.apply_async(args=(hotel_obj.id, True, True), countdown=180)

            from hotels.post_save_connector import trigger_content_pipeline_push
            trigger_content_pipeline_push(hotel_obj.id, hotel_obj.hotelcode, hotel_obj.__class__.__name__, hotel_obj.id, False, None, 'ingo', 'Flag_Value_Update_Uploader')

            log_identifier = {}
            update_specific_identifier("remark", "Total time taken by update_hotel_flag_value_task method: %s, is_backward_compatible: %s" % (time() - start, is_backward_compatible), log_identifier)
            inventory_logger.info(
                message="Total time taken by update_hotel_flag_value_task", log_type="ingoibibo", bucket="update_hotel_flag_value_task", stage="update_hotel_flag_value_task", identifier="{}".format(log_identifier))
        except Exception as ex:
            error_msg = "{hotelcode}({error})".format(error=ex, hotelcode=hotel_obj.hotelcode)
            failed_hotel_codes.append(error_msg)
            inventory_logger.error(message='Exception occurred while updating flag %s' % (str(ex)),
                                   log_type='ingoibibo', bucket='update_hotel_flag_value_task',
                                   stage='update_hotel_flag_value_task')

    if failed_hotel_codes:
        content = 'Error in updating %s flag for these hotelcodes: %s' % (
            flag_name, ",".join(map(str, failed_hotel_codes)))
        try:
            sendMail('', '', content, content, '', [user.email], {}, [])
        except Exception as ex:
            inventory_logger.error(message='Exception occurred while sending mail for failed hotelcodes %s' % (str(ex)),
                                   log_type='ingoibibo', bucket='update_hotel_flag_value_task',
                                   stage='update_hotel_flag_value_task')

@app.task(name="ingoibibo.update_gstn_assurance_task")
def update_gstn_assurance_task(hotelcode, lob, update_data):
    """
    Example:
        hotelcode="1000000060"
        lob="myBiz"
        update_data = {'gst_with_penalty': 0, 'gst_without_penalty': 1}
    """
    try:
        if hotelcode == "" or lob == "" or not update_data:
            return
        # for backward compatibility with myBiz, update flag_bits_1 and flag_bits_3
        # can be removed once GST_WITH_PENALTY, GST_WITHOUT_PENALTY flags are removed from flag_bits_3
        hotel_obj = HotelDetail.objects.only('id', 'modifiedon', 'flag_bits_1', 'flag_bits_3', 'hotelcode').filter(hotelcode=hotelcode).first()
        if lob == 'myBiz':
            hotel_obj.modifiedon = datetime.datetime.now()
            for flag_name, flag_value in update_data.items():
                setattr(hotel_obj, flag_name, flag_value)
            hotel_obj.save(update_fields=['flag_bits_1', 'flag_bits_3', 'modifiedon'], silent_save_flag=True)

        # Check if GSTNAssurance entry exists for the combination of hotelcode and lob
        gstn_assurance, created = GSTNAssurance.objects.get_or_create(hotelcode=hotelcode, lob=lob, defaults={'hotelcode': hotelcode, 'lob': lob})

        if update_data.get(GST_WITH_PENALTY, None) is not None:
            gstn_assurance.gst_with_penalty = update_data[GST_WITH_PENALTY]
        if update_data.get(GST_WITHOUT_PENALTY, None) is not None:
            gstn_assurance.gst_without_penalty = update_data[GST_WITHOUT_PENALTY]
        if gstn_assurance.gst_with_penalty or gstn_assurance.gst_without_penalty:
            gstn_assurance.gstn_assured = True
        else:
            gstn_assurance.gstn_assured = False
        gstn_assurance.modifiedon = datetime.datetime.now()
        gstn_assurance.save()

        from hotels.post_save_connector import trigger_content_pipeline_push
        trigger_content_pipeline_push(hotel_obj.id, hotel_obj.hotelcode, hotel_obj.__class__.__name__, hotel_obj.id, False, None, 'ingo','Flag_Value_Update_Uploader')

    except Exception as ex:
        inventory_logger.error(message='Exception occurred while updating gstn assurance %s' % (str(ex)),
                               log_type='ingoibibo', bucket='update_gstn_assurance_task',
                               stage='update_gstn_assurance_task')


@app.task(name="ingoibibo.update_host_flag_value_task")
def update_host_flag_value_task(user_id_list, flag_name, flag_value, user, bulk_uploader_file_name):
    failed_user_ids = []
    host_objects = HostProfile.objects.only('id', 'modified_on', 'flagbit1', 'user_id').filter(
        user_id__in=user_id_list)
    content_type_id = get_content_id_for_model('hostprofile')
    error_msg = ""
    for host_obj in host_objects:
        try:
            host_obj.modified_on = datetime.datetime.now()
            setattr(host_obj, flag_name, flag_value)
            host_obj.save(update_fields=['flagbit1', 'modified_on'])
            LogEntry.objects.log_action(user_id=user.pk, content_type_id=content_type_id,
                                        object_id=host_obj.id, object_repr=force_unicode(host_obj),
                                        action_flag=CHANGE,
                                        change_message='Value of Key %s changed to %s through flag bit bulk uploader: %s'
                                                       % (flag_name, flag_value, bulk_uploader_file_name))

        except Exception as ex:
            error_msg = "{user_id}({error})".format(error=ex, user_id=host_obj.user_id)
            failed_user_ids.append(error_msg)
            inventory_logger.error(message='Exception occurred while updating flag %s' % (str(ex)),
                                   log_type='ingoibibo', bucket='update_host_flag_value_task',
                                   stage='update_host_flag_value_task')

    if failed_user_ids:
        content = 'Error in updating %s flag for these user ids: %s' % (
            flag_name, ",".join(map(str, failed_user_ids)))
        try:
            sendMail('', '', content, content, '', [user.email], {}, [])
        except Exception as ex:
            inventory_logger.error(message='Exception occurred while sending mail for failed user ids %s' % (str(ex)),
                                   log_type='ingoibibo', bucket='update_host_flag_value_task',
                                   stage='update_host_flag_value_task')


@app.task(name="ingoibibo.update_rateplan_flag_value_task")
def update_rateplan_flag_value_task(rateplan_code_list, flag_name, flag_value, user, bulk_uploader_file_name):
    failed_rateplan_codes = []
    rate_plan_fields = ['id', 'modifiedon', 'flag_bits_1', 'rateplancode']
    rateplan_objects = RatePlan.objects.filter(rateplancode__in=rateplan_code_list).only(*rate_plan_fields)
    content_type_id = get_content_id_for_model('rateplan')

    for rateplan_obj in rateplan_objects:
        try:
            rateplan_obj.modifiedon = datetime.datetime.now()
            setattr(rateplan_obj, flag_name, flag_value)
            rateplan_obj.save(update_fields=['flag_bits_1', 'modifiedon'])
            LogEntry.objects.log_action(user_id=user.pk, content_type_id=content_type_id,
                                        object_id=rateplan_obj.id, object_repr=force_unicode(rateplan_obj),
                                        action_flag=CHANGE,
                                        change_message='Value of Key %s changed to %s through Rate Plan flag bit '
                                                       'bulk uploader: %s'
                                                       % (flag_name, flag_value, bulk_uploader_file_name))

        except Exception as ex:
            error_msg = "{rateplancode}({error})".format(error=ex, rateplancode=rateplan_obj.rateplancode)
            failed_rateplan_codes.append(error_msg)
            inventory_logger.error(message='Exception occurred while updating Rate Plan flag %s' % (str(ex)),
                                   log_type='ingoibibo', bucket='update_rateplan_flag_value_task',
                                   stage='update_rateplan_flag_value_task')

    if failed_rateplan_codes:
        content = 'Error in updating %s flag for these rateplancodes: %s' % (
            flag_name, ",".join(map(str, failed_rateplan_codes)))
        try:
            sendMail('', '', content, content, '', [user.email], {}, [])
        except Exception as ex:
            inventory_logger.error(message='Exception occurred while sending mail for failed rateplancodes %s'
                                           % (str(ex)), log_type='ingoibibo', bucket='update_rateplan_flag_value_task',
                                   stage='update_rateplan_flag_value_task')


@app.task(name='ingoibibo.remove_rates_gt_base_occ_for_rateplan')
def remove_rates_for_change_in_base_occ_for_rateplan(rate_plan_code, old_base_occupancy, new_base_occupancy, hotel_code, user):
    remove_rates_request = dict()
    remove_rates_request['code_list'] = [rate_plan_code]
    remove_rates_request['level'] = 'rate_plan'
    remove_rates_request['old_base_occupancy'] = old_base_occupancy
    remove_rates_request['new_base_occupancy'] = new_base_occupancy
    remove_rates_request['hotel_code'] = hotel_code
    remove_rates_request['source'] = 'inventory'
    remove_rates_request['user_id'] = user.id
    remove_rates_request['user_name'] = str(user.username)
    remove_rates_response = rates_client.remove_rates(remove_rates_request)
    if remove_rates_response.get('success', False):
        inventory_logger.critical(message='Remove rates unsuccessful request %s response %s' %
                                          (remove_rates_request, remove_rates_response),
                                  log_type='ingoibibo', bucket='hotels.tasks', stage='remove_rates_gt_base_occ')
    return remove_rates_response


@app.task(name="ingoibibo.move_rates_across_contract_types")
def move_rates_across_contract_types(rateplan, old_contract_type, new_contract_type, hotel_code, user):
    try:
        move_rates_request = {
            'code_list': [rateplan.rateplancode],
            'level': 'rate_plan',
            'old_contract_type': old_contract_type,
            'new_contract_type': new_contract_type,
            'hotel_code': hotel_code,
            'source': 'inventory',
            'user_id': user.id,
            'user_name': str(user.username)
        }

        move_rates_response = grpc_rates_client.move_rates(move_rates_request)
        if not move_rates_response.get('success'):
            inventory_logger.critical(
                message='Move Rates Failed Request %s Response %s' % (move_rates_request, move_rates_response),
                log_type='ingoibibo', bucket='rateplan', stage='move_rates_across_contract_types')

        log_message = "Moving rates from contract type %s to %s %s" % (old_contract_type, new_contract_type,
                                                                       'successful' if move_rates_response.get(
                                                                           'success') else 'failed')
        LogEntry.objects.log_action(
            user_id=rateplan.user_id,
            content_type_id=ContentType.objects.get_for_model(RatePlan).id,
            object_id=rateplan.id,
            action_flag=CHANGE,
            change_message=log_message,
            object_repr=force_unicode(rateplan)
        )

    except Exception as e:
        inventory_logger.critical(
            message='Move Rates Exception %s RatePlanCode %s OldContractType %s NewContractType %s TraceBack %s' % (
                str(e), rateplan.rateplancode, old_contract_type, new_contract_type, repr(traceback.format_exc())),
            log_type='ingoibibo', bucket='rateplan', stage='move_rates_across_contract_types')


def update_rate_obj(rate_obj, remove_price_indx_list):
    is_rate_changed = False
    current_rate_data = rate_obj.ratedata
    try:
        updated_price_list = string.split(current_rate_data, "#")
        for indx in remove_price_indx_list:
            if str(updated_price_list[indx]) > "0.0":
                updated_price_list[indx] = "-1"
                is_rate_changed = True
        updated_rate_data = "#".join(updated_price_list)
        rate_obj.ratedata = updated_rate_data
        rate_obj.modifiedon = datetime.datetime.now()
    except Exception as e:
        inventory_logger.critical(message="Exception %s occured while updating rateobj %s and date %s. "
                                          "Traceback : %s " % (str(e), rate_obj.id, rate_obj.idate.strftime("%Y-%m-%d"),
                                                               repr(traceback.format_exc())), log_type="ingoibibo",
                                  bucket="hotels.tasks", stage="update_rate_obj")
    return current_rate_data, is_rate_changed


@app.task(name="ingoibibo.one_time_remove_rates_gt_base_occ")
def remove_rates_gt_room_occupancy_for_hotel(hotel_obj, occ_rate_indx_map, month_wise_date_range, date_range_active_rps,
                                             index):
    inventory_logger.info(message=("Fetching rooms for hotel id %s and index : %s" % (hotel_obj.id, index)),
                          log_type='ingoibibo',
                          bucket='scripts.remove_rates', stage='remove_rates_gt_room_occupancy')
    room_obj_list = RoomDetail.objects.only('id', 'base_adult_occupancy', 'isactive'). \
        filter(hotel_id=hotel_obj.id, base_adult_occupancy__lte=2, base_adult_occupancy__gt=0)
    for room_obj in room_obj_list:
        base_occ = room_obj.base_adult_occupancy
        rateplan_obj_list = RatePlan.objects.only('id', 'rateplancode', 'isactive').filter(roomtype_id=room_obj.id)
        remove_price_indx_list = occ_rate_indx_map.get(base_occ)
        for rateplan in rateplan_obj_list:
            isactive = hotel_obj.isactive and room_obj.isactive and rateplan.isactive
            from scripts.rate_scripts.remove_rates import remove_rates_gt_occ_for_rateplan
            remove_rates_gt_occ_for_rateplan(rateplan, room_obj.id, base_occ, isactive,
                                             remove_price_indx_list, month_wise_date_range, date_range_active_rps)
    inventory_logger.info(message=("Rates updated for hotel id %s and index : %s" % (hotel_obj.id, index)),
                          log_type='ingoibibo',
                          bucket='scripts.remove_rates', stage='remove_rates_gt_room_occupancy')


@app.task(name="ingoibibo.create_linked_rateplan_task")
def create_linked_rateplan_task(hotel_code_list, existing_mealplan, new_mealplan, sell_commission,
                                linkage_rule_map, user, bulk_uploader_file_name, request_data=None):
    failed_rateplan_codes = []
    rateplan_obj_list = RatePlan.objects.filter(isactive=True, mealplan=existing_mealplan, parent_id=0,
                                                pay_at_hotel=0, roomtype__hotel__hotelcode__in=hotel_code_list)
    linked_rateplan_list = RatePlan.objects.filter(isactive=True, pay_at_hotel=0, parent_id__gt=0,
                                                   mealplan=new_mealplan,
                                                   roomtype__hotel__hotelcode__in=hotel_code_list)

    rateplan_content_type_id = get_content_id_for_model('rateplan')
    linked_raterule_content_type_id = get_content_id_for_model('linkedraterule')
    cancellation_rules_list = HotelCancellationRule.objects.filter(isactive=1, object_id__in=rateplan_obj_list,
                                                                   content_type_id=31)
    for rateplan in rateplan_obj_list:
        try:
            linked_rateplans = filter(lambda x: x.parent_id == rateplan.id, linked_rateplan_list)
            if linked_rateplans:
                continue
            rateplan_linkage_rule = copy.copy(linkage_rule_map)
            kwargs = model_to_dict(rateplan, exclude=['id', 'rateplancode', 'user'])
            kwargs['roomtype_id'] = kwargs.pop('roomtype', None)
            kwargs['parent_id'] = rateplan.id
            kwargs['mealplan'] = new_mealplan
            kwargs['sellcommission'] = sell_commission if sell_commission != '' else rateplan.sellcommission
            kwargs['rateplanname'] = rateplan.rateplanname.replace(existing_mealplan, new_mealplan)
            if new_mealplan not in kwargs['rateplanname']:
                kwargs['rateplanname'] = new_mealplan
            kwargs['modifiedon'] = kwargs['createdon'] = datetime.datetime.now()
            kwargs['user_id'] = user.id
            inclusion_list = kwargs.pop('inclusions')
            new_rateplan_obj = RatePlan(**kwargs)
            new_rateplan_obj.save(request_data=request_data)
            new_rateplan_obj.inclusions = inclusion_list
            new_rateplan_obj.save(request_data=request_data)

            rateplan_linkage_rule['linked_rateplan'] = new_rateplan_obj
            rateplan_linkage_rule['user_id'] = user.id
            if 'link_block' in rateplan_linkage_rule:
                is_block_cta_ctd_linked = rateplan_linkage_rule['link_block']
            else:
                is_block_cta_ctd_linked = rateplan_linkage_rule.pop('is_block_cta_ctd_linked', True)
            is_cutoff_linked = rateplan_linkage_rule.pop('is_cutoff_linked', True)
            is_mlos_linked = rateplan_linkage_rule.pop('is_mlos_linked', True)
            linked_raterule = LinkedRateRule(**rateplan_linkage_rule)
            linked_raterule.is_block_cta_ctd_linked = is_block_cta_ctd_linked
            linked_raterule.is_cutoff_linked = is_cutoff_linked
            linked_raterule.is_mlos_linked = is_mlos_linked
            linked_raterule.save()

            LogEntry.objects.log_action(user_id=user.pk, content_type_id=rateplan_content_type_id,
                                        object_id=new_rateplan_obj.id, object_repr=force_unicode(new_rateplan_obj),
                                        action_flag=ADDITION,
                                        change_message='Created through linked_rateplan_uploader:%s'
                                                       % (bulk_uploader_file_name))
            LogEntry.objects.log_action(user_id=user.pk, content_type_id=linked_raterule_content_type_id,
                                        object_id=linked_raterule.id, object_repr=force_unicode(linked_raterule),
                                        action_flag=ADDITION,
                                        change_message='Created through linked_rateplan_uploader:%s'
                                                       % (bulk_uploader_file_name))

            rateplan_cancellation_rule_list = filter(lambda x: x.object_id == rateplan.id, cancellation_rules_list)
            for cp_rule in rateplan_cancellation_rule_list:
                try:
                    kwargs = model_to_dict(cp_rule, exclude=['id', 'object_id', 'user', 'content_type'])
                    kwargs['object_id'] = new_rateplan_obj.id
                    kwargs['content_type_id'] = rateplan_content_type_id
                    kwargs['user_id'] = user.id
                    new_cp_rule_instance = HotelCancellationRule(**kwargs)
                    new_cp_rule_instance.save()
                except Exception as ex:
                    inventory_logger.error(message='Exception occured while creating cancellation list %s' % (str(ex)),
                                           log_type='ingoibibo', bucket='create_linked_rateplan_task',
                                           stage='create_linked_rateplan_task')
        except Exception as ex:
            failed_rateplan_codes.append(rateplan.rateplancode)
            inventory_logger.error(message='Exception occured while creating linked plan %s' % (str(ex)),
                                   log_type='ingoibibo', bucket='create_linked_rateplan_task',
                                   stage='create_linked_rateplan_task')

    if failed_rateplan_codes:
        content = 'Error in creating linked rateplan for these rateplancodes: %s' % (",".join(failed_rateplan_codes))
        try:
            sendMail('', '', content, content, '', [user.email], {}, [])
        except Exception as ex:
            inventory_logger.error(
                message='Exception occured while sending mail for failed rateplancodes %s' % (str(ex)),
                log_type='ingoibibo', bucket='create_linked_rateplan_task',
                stage='create_linked_rateplan_task')


@app.task(name='ingoibibo.update_optin_status')
def update_whatsapp_optin_status_task(contact, status):
    update_whatsapp_optin_status(contact, status)


@app.task(name='ingoibibo.sync_all_ical')
def sync_all_ical_data():
    sync_all_calendars()


@app.task(name='ingoibibo.update_sync_status_for_otas')
def update_ingo_sync_status():
    update_ingo_sync_details()

@app.task(name='ingoibibo.create_external_calsync_communication')
def trigger_external_calsync_comm():
    create_communication_events()


@app.task(name='ingoibibo.create_hotel_room_images')
def create_hotel_room_images_task(request_data, hotel, room, user_id, context=None, enable_picasso_flow = False):
    from api.v2.images.resources import create_hotel_room_images, create_hotel_room_images_from_picasso
    api_logger.info(message='task start for image creation for request : %s' % str(request_data),
                    log_type='ingoibibo', bucket='ImageApiv2',
                    stage='api.v2.images.viewset.create')
    if enable_picasso_flow:
        response_list = create_hotel_room_images_from_picasso(request_data, hotel, room, user_id)
    else:
        response_list = create_hotel_room_images(request_data, hotel, room, user_id)
    if context and context.get("request_id"):
        ack_context = {"request_id": context['request_id'], "request": request_data, "response": response_list,
                       "content_type": "images"}
        ack_flow.push_to_ack_table(ack_context)


@app.task(name='ingoibibo.create_hotel_room_videos')
def create_hotel_video_task(file_url, user_id, **kwargs):
    api_logger.info(message='task start for video creation for video: {}'.format(file_url),
                    log_type='ingoibibo', bucket='ImageApiv2',
                    stage='api.v2.videos.viewset.create')
    from api.v2.videos.resources.resources import create_video_from_url
    create_video_from_url(file_url, user_id, kwargs)


@app.task(name="ingoibibo.create_text_based_promotion_task")
def create_text_based_promotion_task(hotel_code_list, api_data, post_data, user):
    from api.v1.offers.resources import create_textual_offer_data
    post_data['template_type'] = post_data['text_offers_template_map'].split(",")[0]
    post_data['template_id'] = int(post_data['text_offers_template_map'].split(",")[1])
    failed_hotel_codes = []

    api_data['offercategory'] = 'customised'
    api_data['offercondition'] = 'all'
    api_data['offer_value_list'] = []

    try:
        offer_value_data = create_textual_offer_data(post_data)
    except Exception as ex:
        inventory_logger.error(message='Error occured while while creating offer value data, template_id %s' % (
            str(ex), post_data['template_id']),
                               log_type='ingoibibo', bucket='create_text_based_promotion_task',
                               stage='create_text_based_promotion_task')

    api_data['offer_value_list'].append(offer_value_data)
    for hotel_code in hotel_code_list:
        try:
            api_data['object_id'] = helper.get_id_from_code(hotel_code, HotelConf.HotelCodeLength,
                                                            HotelConf.HotelCodePrefix)
            response, status = create_hotel_offer_util(api_data, user, source=OfferSources.bulk_uploader)
            if status == False:
                failed_hotel_codes.append(hotel_code)
                inventory_logger.error(
                    message='Error occured while creating text based promotion %s for hotel_code %s' % (
                        response['message'], hotel_code),
                    log_type='ingoibibo', bucket='create_text_based_promotion_task',
                    stage='create_text_based_promotion_task')

        except Exception as ex:
            inventory_logger.error(
                message='Exception occured while creating text based promotion %s for hotel_code %s' % (
                    str(ex), hotel_code),
                log_type='ingoibibo', bucket='create_text_based_promotion_task',
                stage='create_text_based_promotion_task')
            failed_hotel_codes.append(hotel_code)
            close_old_connections()

        if failed_hotel_codes:
            content = 'Error in creating text based promotion for these hotelcodes: %s' % (
                ",".join(map(str, failed_hotel_codes)))
            try:
                sendMail('', '', content, content, '', [user.email], {}, [])
            except Exception as ex:
                inventory_logger.error(
                    message='Exception occured while sending mail for failed hotelcodes %s' % (str(ex)),
                    log_type='ingoibibo', bucket='create_text_based_promotion_task',
                    stage='create_text_based_promotion_task')


@app.task(name='mail_hotel_booking_report')
def booking_report_task(hotelcode, request_query_params, download_action, response_list, mail_flag=False):
    from api.v1.reports.serializers import BookingReportSerializer
    from rest_framework import status
    from django.http.response import HttpResponse
    from rest_framework.response import Response
    try:
        try:
            booking_hotel = HotelDetail.objects.get(hotelcode=hotelcode)
        except ObjectDoesNotExist as e:
            return Response("Hotelcode does not exist.", status=status.HTTP_400_BAD_REQUEST)
        if settings.IS_MO_ENABLE:
            header = ['BookingID', 'Vendor Booking ID', 'Parent Booking ID (only for MultiRoom booking)',
                      'Booking Vendor', 'Booking Status', 'Customer Name', 'Check-in', 'Check-out', 'Booked On',
                      'PAH Booking', 'Payment Status', 'Room name (Rate Plan)', 'No. of Rooms', 'Length of Stay',
                      'Total Room Nights', 'Base Price', 'Room Charges (A)', 'Extra Adult/Child Charges (B)',
                      'Service Charge (T)', 'Hotel Taxes (C)', 'Hotel Gross Charges (A+B+C+T)', 'Commission',
                      'GST on Commission', 'Total Commission (D)', 'Gross Payable (A+B+C+T-D)', 'TCS Amount',
                      'TDS Amount', 'Amount Payable to Hotel',
                      'Amount to be collected from customer (only for PAH booking)', 'Amount Paid', 'Payment Ref.',
                      'Payment Date', 'Amount adjusted', 'Adjustment Ref.', 'GSTN Assured', 'Customer GSTN', 'Customer Company Name',
                      'Hotel GSTN', 'Sales Channel Name', 'Pre-buy ID']

        else:
            header = ['BookingID', 'Check-in', 'Check-out', 'Booked On', 'PAH Booking?',
                      'Amount to be collected from customer (only for PAH booking)', 'Payable Amount',
                      'Nett Payable to Hotel', 'Customer Name', 'Booking Status', 'Payment Status', 'Booking Vendor',
                      'Vendor Booking ID', 'Parent Booking ID (only for Cart booking)', 'No. of Rooms',
                      'Length of Stay', 'Total room nights', 'Room Charges', 'Extra Adult/Child Charges', 'Hotel Taxes',
                      'Hotel Gross Charges', 'Commission Charges', 'GST Charges', 'Commission (Including GST)',
                      'Amount Paid', 'Payment Date', 'Payment Id', 'Bank Ref/VCC No.', 'Amount adjusted',
                      'Adjustment ID', 'Pre-buy ID']

        report = {'up': 'Upcoming', 'past': 'Past', 'cancelled': 'Cancelled', 'payathotel': 'PayAtHotel',
                  'all': '', 'new': 'New', 'formFilter': 'Filtered'}

        booking_type = request_query_params.get('bookingtype', None)
        booking_type = str(booking_type[0]) if booking_type else ''
        if booking_type == 'corporate':
            header.extend(['Customer GSTN', 'Customer Company Name', 'Hotel GSTN', 'Sales Channel Name'])

        if not mail_flag:
            response = HttpResponse(content_type='text/csv')
            if request_query_params['fromDate'] and request_query_params['toDate'] and download_action == 'formFilter':
                response['Content-Disposition'] = 'attachment; filename=Filtered_Bookings_report_from_%s_to_%s_for' \
                                                  '_%s.csv' % \
                                                  (request_query_params['fromDate'], request_query_params['toDate'],
                                                   booking_hotel.hotelname.replace(' ', '_').replace(',', '_'))
            else:
                response['Content-Disposition'] = 'attachment; filename=%s_Bookings_Report_for_%s.csv' \
                                                  % (report[download_action],
                                                     booking_hotel.hotelname.replace(' ', '_').replace(',', '_'))

            writer = csv.writer(response, dialect='excel', quoting=csv.QUOTE_ALL)
        else:
            email_file = open("/tmp/booking_report_%s.csv" % hotelcode, "w+")
            writer = csv.writer(email_file)
        writer.writerow(header)
        if response_list:
            try:
                for entry in response_list:
                    entry = entry.getHotelBookingVoucherData(pricetype='sell')
                    serialized_data = BookingReportSerializer(entry)
                    entry = serialized_data.data
                    api_logger.info(message='entry: %s' % entry,
                                    log_type='ingoibibo', bucket='booking report',
                                    stage='api.v1.bookings.views.booking')
                    if settings.IS_MO_ENABLE:
                        data = [
                            entry['bookingid'], entry['vendorbookingid'], entry['parent_booking_id'],
                            entry['bookingvendor'], entry['status'], entry['customername'], entry['checkin'],
                            entry['checkout'], entry['bookingdate'], entry['pah_booking'],
                            entry['payment_summary']['payment_status'], '%s-%s' % (entry['room_type'],
                                                                                   entry['meal_plan']),
                            entry['no_roomstay'], entry['no_of_nights'], entry['total_room_nights'],
                            entry['base_price'],
                            entry['room_charge'], entry['extra_adult_charge'], entry['service_charge'],
                            entry['hotel_tax'],
                            entry['hotel_gross_charge'], entry['gi_commission'], entry['gi_gst_charge'],
                            entry['gi_total_commission'], entry['hotel_gross_payable'], entry['tcs_amount'],
                            entry['total_tds_amount'], round(entry['net_payable_amount'] - entry['amount_collected'] -
                                                             entry.get('tcs_amount', 0) - entry.get('total_tds_amount',
                                                                                                    0), 2),
                            entry['amount_collected'], entry['payment_summary']['total_amount_payed'],
                            entry['payment_summary']['payment_detail'], entry['payment_summary']['payment_dates'],
                            entry['payment_summary']['total_amount_adjusted'],
                            entry['payment_summary']['adjustment_detail'], entry['gstn_invoice_required'],
                            entry['customer_gstn'], entry['customer_cn'], entry['hotel_gstn'], entry['sales_channel_name'],
                            entry['prebuy_orderid']
                        ]
                    else:
                        data = [
                            entry['bookingid'], entry['checkin'], entry['checkout'],
                            entry['bookingdate'], entry['pah_booking'], entry['amount_collected'],
                            entry['net_payable_amount'], entry['net_rate'], entry['customername'], entry['status'],
                            entry['payment_status'], entry['bookingvendor'], entry['vendorbookingid'],
                            entry['parent_booking_id'], entry['no_roomstay'], entry['no_of_nights'],
                            entry['total_room_nights'], entry['room_charge'], entry['extra_adult_charge'],
                            entry['hotel_tax'], entry['hotel_gross_charge'], entry['gi_commission'],
                            entry['gi_gst_charge'], entry['gi_total_commission'], entry['total_amount_paid'],
                            entry['payment_date'], entry['payment_ids'], entry['bank_reference_number'],
                            entry['adjustment_data']['total_adjustment_amount'],
                            entry['adjustment_data']['adjustment_detail'],
                            entry['prebuy_orderid']
                        ]

                    if (booking_type == 'corporate'):
                        data.extend([entry['customer_gstn'], entry['customer_cn'], entry['hotel_gstn'], entry['sales_channel_name']])

                    writer.writerow(data)
            except Exception as e:
                api_logger.critical(
                    message='Exception Occurred : %s, Error Traceback %s, Hotelcode : %s'
                            % (str(e), repr(traceback.format_exc()), hotelcode),
                    log_type='ingoibibo', bucket='BookingAPI',
                    stage='bookings.views.download_bookings')
        if not mail_flag:
            return response
        else:
            receiver_mails = request_query_params['to_mail_ids'] if 'to_mail_ids' in request_query_params \
                else booking_hotel.hotelemail
            subject = "booking report"
            email_content = "PFA the file for further details\n"
            sendMail(receiver_mails, '', body='', subject=subject, template_id='60.072', cc_emails=receiver_mails,
                     attached_file=email_file, bcc_emails=[])
    except Exception as e:
        api_logger.critical(
            message='Exception Occurred : %s, Error Traceback %s, Hotelcode : %s'
                    % (str(e), repr(traceback.format_exc()), hotelcode),
            log_type='ingoibibo', bucket='BookingAPI',
            stage='bookings.views.download_bookings')
        return Response('Error Occured', status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@app.task(name="generate_rate_plan_mean")
def initiate_rate_plan_mean():
    from_date = datetime.datetime.today() - timedelta(days=90)
    rate_plan_dump = get_rate_mean_db(from_date)
    if rate_plan_dump is None:
        celery_logger.info('%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'initiate_rate_plan_mean', 'No active rate plan present to generate min and max bounds'))
        return
    count = 0
    for tup in convert_dict_to_array_tuple(rate_plan_dump):
        try:
            celery_logger.info('%s\t%s\t%s\t%s\t%s' % (
                'hotels', 'tasks', 'initiate_rate_plan_mean', 'Count info:', str(count)))
            close_old_connections()
            cur = connection.cursor()
            cur.executemany(
                "update hotels_rateplan set min_rate_threshold=%(min_rate_threshold)s, max_rate_threshold=%(max_rate_threshold)s where id=%(id)s",
                tup)
            count = count + 1
        except Exception as e:
            celery_logger.error('%s\t%s\t%s\t%s\t%s' % (
                'hotels', 'tasks', 'initiate_rate_plan_mean', 'After Error initial', str(e)))
            for plan_dict in tup:
                try:
                    cur.execute("update hotels_rateplan set min_rate_threshold=%s, max_rate_threshold=%s where id=%s",
                                (plan_dict['min_rate_threshold'], plan_dict['max_rate_threshold'], plan_dict['id']))
                    count = count + 1
                except Exception as e:
                    celery_logger.error('%s\t%s\t%s\t%s\t%s\t%s' % (
                        'hotels', 'tasks', 'initiate_rate_plan_mean', 'After error filtered', str(plan_dict), str(e)))


# @app.task(name="ingoibibo.push_vendor_event_to_newralic_async")
# def populate_vendor_event_to_newralic_async(vendor_id, hotel_code, response, type, post_data, ttl):
#     from hotels.navision_vendor_management import populate_vendor_newralic_data
#     populate_vendor_newralic_data(vendor_id, hotel_code, response, type, post_data, ttl)


@app.task(name="ingoibibo.push_vcc_event_to_newrelic_async")
def push_vcc_event_to_newrelic_async(hotel_code, booking_id, response):
    from hotels.vcc_payment import populate_vcc_newrelic_data
    populate_vcc_newrelic_data(hotel_code, booking_id, response)


@app.task(name="ingoibibo.push_vcc_v2_event_to_newrelic_async")
def push_vcc_v2_event_to_newrelic_async(hotel_code, booking_id, request_payload, vcc_data, type):
    from hotels.vcc_payment import populate_vcc_v2_newrelic_data
    populate_vcc_v2_newrelic_data(hotel_code, booking_id, request_payload, vcc_data, type)


# @app.task(name="ingoibibo.push_payment_event_to_newrelic_async")
# def populate_payment_event_to_newrelic_async(booking_id, status_code, event_type, response_time):
#     from mo_finance.MO_payment_management import populate_payment_newrelic_data
#     populate_payment_newrelic_data(booking_id, status_code, event_type, response_time)


# @app.task(name="ingoibibo.populate_adt_newrelic_data_async")
# def populate_adt_newrelic_data_async(event_type, hotelcode, status_code, request_payload, response_data):
#     from mo_finance.MO_payment_management import populate_adt_newrelic_data
#     populate_adt_newrelic_data(event_type, hotelcode, status_code, request_payload, response_data)


# TODO: uncomment this task after production celery task failure issue resolved.
# @app.task(name="ingoibibo.push_booking_status_to_partners_async")
# def push_booking_status_to_partners_async(booking_id, data, apply_cancellation_percentage):
#     try:
#         from api.v1.bookings.resources.booking_status_handler.status_manager import StatusManager
#         StatusManager.push_status_to_partners(booking_id, data, apply_cancellation_percentage)
#     except Exception as e:
#         api_logger.critical(
#             message='Booking status Push, Booking Id : %s Exception occured %s %s' % (
#                 str(booking_id), str(e), repr(traceback.format_exc())),
#             log_type='ingoibibo',
#             bucket='StatusUpdateAPI', stage='hotels.tasks.push_booking_status_to_partners')


def gostay_img_upload():
    # this function is written for spot image upload for mmt.
    # image should be approved, should not flow to voyager again.
    file_path = '/tmp/gostays_image.csv'
    try:
        from api.v2.images.resources import create_hotel_room_images
        user_id = 2 if settings.DEBUG else 118322
        image_list = []
        failed_image_list = []
        with open(file_path, 'rb') as csv_file:
            rows = csv.DictReader(csv_file)
            for row in rows:
                try:
                    request_data = {}
                    hotel = row.pop('hotelcode')
                    room = row.pop('roomcode')
                    image = {}
                    image['description'] = row.pop('description')
                    image['url'] = row.pop('image_url')
                    image['caption'] = row.pop('caption')
                    image['is_active'] = True
                    image['tags'] = row.pop('tags')
                    image['order'] = 1
                    img_list = []
                    img_list.append(image)
                    request_data['images'] = img_list

                    if room:
                        request_data['type'] = 'room'
                        request_data['type_id'] = room
                        room_obj = RoomDetail.objects.get(roomtypecode=room)
                        if settings.DEBUG:
                            response_list = create_hotel_room_images(request_data, None, room_obj, user_id)
                        else:
                            response_list = create_hotel_room_images_task.apply_async(
                                args=(request_data, None, room_obj, user_id, None, False), )
                    else:
                        request_data['type'] = 'hotel'
                        request_data['type_id'] = hotel
                        ho_obj = HotelDetail.objects.get(hotelcode=hotel)
                        if settings.DEBUG:
                            response_list = create_hotel_room_images(request_data, ho_obj, None, user_id)
                        else:
                            response_list = create_hotel_room_images_task.apply_async(
                                args=(request_data, ho_obj, None, user_id, None, False), )
                    image_list.append(image['url'])
                    api_logger.info(message='Image %s upload for hotel : %s' % (str(image['url']), str(response_list)),
                                    log_type='ingoibibo',
                                    bucket='gostay_image', stage='hotels.tasks.gostay_image_upload')
                except Exception as e:
                    failed_image_list.append(image['url'])
                    api_logger.critical(
                        message=' Exception occured for Image Url %s for hotel: %s' % (str(image['url']), str(hotel)),
                        log_type='ingoibibo',
                        bucket='gostay_image', stage='hotels.tasks.gostay_image_upload')
                    close_old_connections()
            api_logger.info(message='Total %s Image upload for hotel : %s' % (len(image_list), str(image_list)),
                            log_type='ingoibibo',
                            bucket='gostay_image', stage='hotels.tasks.gostay_image_upload')
    except Exception as e:
        api_logger.critical(message='Exception occured loop out while image upload : %s, Failed list %s ' % (
            str(e), str(failed_image_list)),
                            log_type='ingoibibo',
                            bucket='gostay_image', stage='hotels.tasks.gostay_image_upload')
        close_old_connections()


@app.task(name="ingoibibo.push_adjustment_entry_to_navision")
def push_adjustment_entry_to_navision():
    try:
        from hotels.performlinkbonus import adjustment_entry_to_navision
        api_logger.info(message="push_adjustment_entry_to_navision Task Started",
                        log_type='ingoibibo', bucket='hotel_performlinkbonus',
                        stage='hotels.tasks.push_adjustment_entry_to_navision')
        adjustment_entry_to_navision()
    except Exception as e:
        api_logger.critical(message='push_adjustment_entry_to_navision:Exception occured %s %s' % (
            e, repr(traceback.format_exc())), log_type='ingoibibo',
                            bucket='hotel_performlinkbonus', stage='hotels.tasks.push_adjustment_entry_to_navision')


@app.task(name="ingoibibo.get_adt_entry_status_from_navision")
def get_adt_entry_status_from_navision():
    try:
        from hotels.performlinkbonus import adjustment_entry_process_check
        api_logger.info(message="get_adt_entry_status_from_navision Task Started",
                        log_type='ingoibibo', bucket='hotel_performlinkbonus',
                        stage='hotels.tasks.get_adt_entry_status_from_navision')
        adjustment_entry_process_check()
    except Exception as e:
        api_logger.critical(message='get_adt_entry_status_from_navision:Exception occured %s %s' % (
            e, repr(traceback.format_exc())), log_type='ingoibibo',
                            bucket='hotel_performlinkbonus', stage='hotels.tasks.get_adt_entry_status_from_navision')

def get_hotels_by_db_modified_on(time_threshold):
    """
    Fetch hotels where db_modified_on > time_threshold using raw SQL.
    Returns list of tuples: (id, statemachine_run_on, db_modified_on)
    """
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT id, statemachine_run_on, db_modified_on
            FROM hotels_hoteldetail
            WHERE db_modified_on > %s AND db_modified_on < %s
            ORDER BY db_modified_on
        """, [time_threshold, datetime.datetime.now()])
        rows = cursor.fetchall()
    return rows

@app.task(name="ingoibibo.push_to_state_machine")
def push_to_state_machine():
    from django.conf import settings
    status, message = False, ''
    try:
        # Use IST for time threshold
        ist = timezone('Asia/Kolkata')
        now_ist = datetime.datetime.now(ist).replace(tzinfo=None)
        time_threshold = now_ist - timedelta(minutes=70)
        celery_logger.info('push_to_state_machine: Using time_threshold=%s, now_ist=%s', time_threshold, now_ist)

        hotels = get_hotels_by_db_modified_on(time_threshold)
        request_data = {
            "user": {
                "id": "",
                "username": "celery",
                "firstname": "celery"
            },
            "ip": ""
        }
        processed, skipped, errors = 0, 0, 0
        for hotel in hotels:
            try:
                hotel_id, statemachine_run_on, db_modified_on = hotel
                if db_modified_on is None:
                    skipped += 1
                    celery_logger.warning('Hotel id %s skipped: db_modified_on is None', hotel_id)
                    continue
                if statemachine_run_on and statemachine_run_on >= db_modified_on:
                    skipped += 1
                    continue
                push_hotel_to_state_machine.apply_async(args=(hotel_id, None, '', request_data),
                                                        countdown=settings.ETL_DELAY)
                processed += 1
            except Exception as e:
                celery_logger.error('Error processing hotel_id %s: %s', hotel_id if 'hotel_id' in locals() else 'unknown', str(e))
                errors += 1
                continue
        celery_logger.info('push_to_state_machine: processed=%d, skipped=%d, errors=%d, threshold=%s, now_ist=%s',
                           processed, skipped, errors, time_threshold, now_ist)
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'push_hotels_to_state_machine', '', str(e), repr(traceback.format_exc())))
    return status, message


@app.task(name="ingoibibo.push_hotel_to_state_machine")
def push_hotel_to_state_machine(hotel_id, state=None, deactivation_reason='', request_data=None, is_db_persistence_enabled = True,
                                response_data=None):
    if response_data is None:
        response_data = {}
    from hotels.state_machine import StateMachine
    from datetime import datetime
    status, message = False, ''
    try:

        request_uuid = str(uuid.uuid4())

        hotel_obj = HotelDetail.objects.using('default').get(id=hotel_id)

        if request_data is None:
            request_data = {}

        if request_data.get("request_id", None) is None:
            request_data['request_id'] = request_uuid
        request_data["is_db_persistence_enabled"] = is_db_persistence_enabled
        

        log_message = "Request Data to process in push_hotel_to_state_machinefor state : {} -  hotelid: {} - is_db_persistence_enabled: {}".format(state, hotel_id, is_db_persistence_enabled)
        api_logger.info(message=log_message, log_type='ingoibibo', bucket='property_state_machine', stage='hotels.state_machine', identifier='{}'.format(log_message))
                

        import struct
        import mmh3
        hotel_id_bytes = struct.pack('<Q', int(hotel_id))  # '<Q' for little-endian unsigned long long (64-bit)
        # Compute MurmurHash3 (32-bit)
        hash_value = mmh3.hash(hotel_id_bytes) & 0xFFFFFFFF  # Ensure unsigned 32-bit
        # Modulo to control percentage
        process_using_ncs_based_on_rollout = (hash_value % 100) < settings.STATE_MACHINE_ROLLOUT_NCS["rollout_percentage"]
        log_message = "For {} : {} - hotelid: {}  request_id: {} hash: {} process_using_ncs_based_on_rollout: {}".format("HASH_CHECK", "STATE_MACHINE",hotel_id, request_uuid ,(hash_value % 100),process_using_ncs_based_on_rollout)
        api_logger.info(message=log_message, log_type='ingoibibo', bucket='property_state_machine', stage='hotels.state_machine', identifier='{}'.format(log_message))


        inventory_property_state_code = None
        
        if settings.STATE_MACHINE_ROLLOUT_NCS["enable"]:
            if process_using_ncs_based_on_rollout and is_db_persistence_enabled:
                response = Trigger_state_machine_for_content_service(hotel_obj.hotelcode, request_uuid, is_db_persistence_enabled =True, property_state=state)
                log_message = "For {} : {} - hotelcode: {} property_states_code: {} request_id: {} message: {}".format("NCS", "STATE_MACHINE",hotel_obj.hotelcode, response.get('propertyStateCode',0), request_uuid, response.get('message', ''))
                ncs_property_state_code =  response.get('propertyStateCode', 1)
                status = True
                message = response.get('message', '')
                if len(message) == 0 and ncs_property_state_code > 0 and len(PROPERTY_STATES) > ncs_property_state_code - 1:
                    message = 'Moved To Property State {}'.format(PROPERTY_STATES[ncs_property_state_code - 1][1])
                celery_logger.info(log_message)
                api_logger.info(message=log_message, log_type='ingoibibo', bucket='property_state_machine', stage='hotels.state_machine', identifier='{}'.format(log_message))
            else:
                if is_db_persistence_enabled:
                    response = Trigger_state_machine_for_content_service(hotel_obj.hotelcode, request_uuid,
                                                                         is_db_persistence_enabled=False,
                                                                         property_state=state)
                    log_message = "For {} : {} - hotelcode: {} property_states_code: {} request_id: {} message: {} process_using_ncs_based_on_rollout {}".format(
                        "NCS", "STATE_MACHINE", hotel_obj.hotelcode, response.get('propertyStateCode', 0), request_uuid,
                        response.get('message', ''), process_using_ncs_based_on_rollout)
                    celery_logger.info(log_message)
                    api_logger.info(message=log_message, log_type='ingoibibo', bucket='property_state_machine',
                                    stage='hotels.state_machine', identifier='{}'.format(log_message))

                with StateMachine(hotel_obj, deactivation_reason, request_data) as machine:
                    status, message = machine.start(state_to_start_with=state)
                inventory_property_state_code = machine.final_property_state_code

                refreshed_hotel_object = HotelDetail.objects.using('default').get(id=hotel_id)
                
                log_message = "Checking diff in inventory_property_state_code : {} and refreshed_hotel_object : {} -  hotelid: {} - is_db_persistence_enabled: {}".format(inventory_property_state_code, refreshed_hotel_object.property_states_code, hotel_id, is_db_persistence_enabled)
                api_logger.info(message=log_message, log_type='ingoibibo', bucket='property_state_machine', stage='hotels.state_machine', identifier='{}'.format(log_message))
        

                log_message = "For {} : {} - hotelcode: {} property_states_code: {} request_id: {} message: {}".format("INVENTORY", "STATE_MACHINE",refreshed_hotel_object.hotelcode, refreshed_hotel_object.property_states_code, request_uuid, message)
                celery_logger.info(log_message)
                api_logger.info(message=log_message, log_type='ingoibibo', bucket='property_state_machine', stage='hotels.state_machine', identifier='{}'.format(log_message))

        else:
            if not settings.STATE_MACHINE_ROLLOUT_NCS["is_db_persistence_enabled_on_ncs"]:
                # when we mar enable false later on we need to change this behaviour and just keep inventory since db updates will be enabled from NCS as well
                response = Trigger_state_machine_for_content_service(hotel_obj.hotelcode,request_uuid,is_db_persistence_enabled =False,property_state=state)
                log_message = "For {} : {} - hotelcode: {} property_states_code: {} request_id: {} message: {} process_using_ncs_based_on_rollout {}".format("NCS", "STATE_MACHINE",hotel_obj.hotelcode, response.get('propertyStateCode',0), request_uuid, response.get('message', ''),process_using_ncs_based_on_rollout)
                celery_logger.info(log_message)
                api_logger.info(message=log_message, log_type='ingoibibo', bucket='property_state_machine', stage='hotels.state_machine', identifier='{}'.format(log_message))

            with StateMachine(hotel_obj, deactivation_reason, request_data) as machine:
                status, message = machine.start(state_to_start_with=state)
            inventory_property_state_code = machine.final_property_state_code

            refreshed_hotel_object = HotelDetail.objects.using('default').get(id=hotel_id)

            log_message = "For {} : {} - hotelcode: {} property_states_code: {} request_id: {} message: {}".format("INVENTORY", "STATE_MACHINE",refreshed_hotel_object.hotelcode, refreshed_hotel_object.property_states_code, request_uuid, message)
            celery_logger.info(log_message)
            api_logger.info(message=log_message, log_type='ingoibibo', bucket='property_state_machine', stage='hotels.state_machine', identifier='{}'.format(log_message))

        
        caller = 'push_to_state_machine'
        # add stateid in logs
        celery_logger.info('%s\t%s\t%s\t%s\tCaller: %s\t%s\t%s\t%s' % (
            'hotel - ', str(hotel_id), 'tasks', 'push_hotel_to_state_machine', caller, str(status), 'else', message))
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotel - ', str(hotel_id), 'tasks', 'push_hotel_to_state_machine', '',
            str(e), repr(traceback.format_exc())))

    log_message = "Finally setting in response state : {} -  hotelid: {} - is_db_persistence_enabled: {}".format(inventory_property_state_code, hotel_id, is_db_persistence_enabled)
    api_logger.info(message=log_message, log_type='ingoibibo', bucket='property_state_machine', stage='hotels.state_machine', identifier='{}'.format(log_message))
        
    response_data["property_state_code"] = inventory_property_state_code
    return status, message


@app.task(name="ingoibibo.task_property_deactivate_and_request_reactivation_request")
def task_property_deactivate_and_request_reactivation_request(hotel_code, deactivation_reason, request_data=None):
    try:
        from hotels.helper import property_deactivate_and_request_reactivation_request
        inventory_logger.info(message="Task Received for hotel {}".format(hotel_code), log_type="ingoibibo",
                              bucket="hotels.tasks",
                              stage="task_property_deactivate_and_request_reactivation_request")
        hotel_obj = HotelDetail.objects.get(hotelcode=hotel_code)
        property_deactivate_and_request_reactivation_request(hotel_obj, deactivation_reason, request_data=None)
    except Exception as e:
        inventory_logger.critical(message="Error Occurred for hotel {}".format(str(e), hotel_code),
                                  log_type="ingoibibo", bucket="hotels.tasks",
                                  stage="task_property_deactivate_and_request_reactivation_request")


@app.task(name="ingoibibo.booking_push_to_nexus", max_retries=5)
def booking_push_to_nexus(url, data):
    inventory_logger.info(
        message="Before Push to nexus data: %s" % (data),
        log_type="ingoibibo", bucket="hotels.tasks", stage="booking_push_to_nexus")
    try:
        response = requests.post(url, data=data)
        if response.status_code >= 500:
            raise Exception('Response: %s. Response Code: %s' % (response.content, response.status_code))
        elif response.status_code == 200:
            inventory_logger.info(
                message="After Push to nexus data: %s, Response: %s" % (data, response.content),
                log_type="ingoibibo", bucket="hotels.tasks", stage="booking_push_to_nexus")
        else:
            try:
                response_content = json.loads(response.content)
                if response_content.get('is_retry_required', False):
                    raise Exception('Response: %s. Response Code: %s' % (response.content, response.status_code))
            except Exception as e:
                inventory_logger.info(
                    message="Push data: %s. Response: %s. Exception: %s" % (data, response.content, str(e)),
                    log_type="ingoibibo", bucket="hotels.tasks", stage="booking_push_to_nexus")
                raise Exception('Response: %s. Response Code: %s' % (response.content, response.status_code))
    except Exception as e:
        inventory_logger.critical(message="Error during nexus api call. Push data: %s. Exception: %s" % (data, str(e)),
                                  log_type="ingoibibo", bucket="hotels.tasks", stage="booking_push_to_nexus")
        booking_push_to_nexus.retry(args=(url, data,), countdown=300)


@app.task(name="ingoibibo.publish_cm_bookings_to_kafka")
def publish_cm_bookings_to_kafka(booking_code, hotel_code, push_type, channel_manager_id):
    log_identifier = {}
    try:
        if channel_manager_id in test_cm_ids:
            return 
        update_specific_identifier('booking_code', booking_code, log_identifier)
        topic = 'ingoibibo_push_cm_bookings'
        
        booking_dict = {'booking_code': str(booking_code), 'hotel_code': str(hotel_code), 'push_type': push_type,
                        'channel_manager_id': channel_manager_id}
        message = json.dumps(booking_dict)
        pushed = push_to_kafka(topic, message, 'mmt', 0)
        if pushed:
            update_specific_identifier("remark", "Pushed cm booking data to kafka", log_identifier)
            inventory_logger.info(
                message="Pushed booking data to kafka %s\t time: %s" % (message, str(datetime.datetime.now())),
                log_type="ingoibibo", bucket="hotels.tasks", stage="publish_cm_bookings_to_kafka",
                identifier="{}".format(log_identifier))
        else:
            update_specific_identifier("remark", "Unable to push cm booking data to kafka", log_identifier)
            inventory_logger.critical(
                message="Unable to push booking data to kafka %s\t time: %s" % (message, str(datetime.datetime.now())),
                log_type="ingoibibo", bucket="hotels.tasks", stage="publish_cm_bookings_to_kafka",
                identifier="{}".format(log_identifier))
    except Exception as e:
        update_error_identifier(error_message=str(e), log_identifier=log_identifier)
        log_msg = "Error occured while creating booking push data."
        update_specific_identifier("remark", log_msg, log_identifier)
        inventory_logger.critical(log_type="ingoibibo", bucket="hotels.tasks",
                                  stage="publish_cm_bookings_to_kafka",
                                  identifier="{}".format(log_identifier))


# TODO: uncomment this task after production celery task failure issue resolved.
# @app.task(name="ingoibibo.publish_bookings_to_kafka")
# def publish_bookings_to_kafka(booking_code, hotel_code, event_type):
#     try:
#         topic = 'ingommt_booking'
#         proto_response = BookingKafkaMessage()
#         booking_dict = {'booking_code': str(booking_code), 'hotel_code': str(hotel_code), 'event_type': event_type}
#         message = json.dumps(booking_dict)
#         try:
#             proto_response = Parse(message, proto_response, True)
#             response_message = proto_response.SerializeToString()
#             inventory_logger.info(
#                 message="Before push: booking data %s\t time: %s" % (message, str(datetime.datetime.now())),
#                 log_type="ingoibibo", bucket="publish_bookings_to_kafka",
#                 stage="booking.resources.booking_resources.publish_bookings_to_kafka")
#             pushed = push_to_kafka(topic, response_message, 'mmt',  0)
#             if pushed:
#                 inventory_logger.info(
#                     message="Pushed booking data to kafka %s\t time: %s" % (message, str(datetime.datetime.now())),
#                     log_type="ingoibibo", bucket="publish_bookings_to_kafka",
#                     stage="booking.resources.booking_resources.publish_bookings_to_kafka")
#             else:
#                 inventory_logger.critical(
#                     message="Unable to push booking data to kafka %s\t time: %s" % (
#                         message, str(datetime.datetime.now())),
#                     log_type="ingoibibo", bucket="publish_bookings_to_kafka",
#                     stage="booking.resources.booking_resources.publish_bookings_to_kafka")
#         except Exception as ex:
#             inventory_logger.critical(message='Exception: %s occurred while proto conversion for %s' %
#                                               (repr(traceback.format_exc()), booking_code),
#                                       log_type='ingoibibo', bucket='publish_bookings_to_kafka',
#                                       stage='booking.resources.booking_resources.publish_bookings_to_kafka')
#     except Exception as ex:
#         log_msg = "Error occured while creating booking push data. Exception: %s" % str(ex)
#         inventory_logger.critical(message=log_msg, log_type="ingoibibo", bucket="publish_bookings_to_kafka",
#                         stage="booking.resources.booking_resources.publish_bookings_to_kafka")


def trigger_qualityscore_events(hotels, target='all', source='cron', data=None, event_time=''):
    server = settings.QUALITY_SCORE_KAFKA_SERVER_CONF['servers']['default']['HOST']
    topic = settings.QUALITY_SCORE_KAFKA_SERVER_CONF['servers']['default']['TOPIC'] if target != 'sync' else \
        settings.TOTAL_QUALITY_SCORE_KAFKA_SERVER_CONF['servers']['default']['TOPIC']
    producer = KafkaProducer(bootstrap_servers=server)
    utcms = get_unix_time(event_time) if event_time else get_unix_time()
    extra_data = {} if not data else data
    for hotel in hotels:
        effective_target = 'dch' if is_dch(hotel) and target == 'all' else target
        key = effective_target + "_" + hotel
        celery_logger.info('%s\t%s\t%s\t%s\t%s' % ('sending_quality_score_event_hotel_id',
                                                   hotel, effective_target, source, utcms))
        msg = {
            'hotelCode': hotel,
            'hotelId': hotel,  # temporary watson reads from this field.
            'target': effective_target,
            'utcms': utcms,
            'source': source,
            'uuid': str(uuid.uuid4())
        }
        msg.update(extra_data) if extra_data else None
        msg = json.dumps(msg)
        producer.send(topic, msg, str(key), None, utcms)
    producer.flush()
    producer.close()


@app.task(name='ingoibibo.quality_score_event')
def send_quality_score_event_for_hotel():
    """ celery task to put quality-score event to kafka topic.
         This will be read by watson to calculate individual score.

        data = all {
          'hotelCode': 1000199028,
            'target': 'all',
            'utcms': 1573456041,
            'source': 'cron',
            'uuid': 1af31641-420e-4a28-9e6d-d661b35dc584
         }

    """
    # sources_to_exclude = [constants.VOYAGER, constants.MMT]
    hotels = HotelDetail.objects.only('hotelcode', 'flag_bits_1').filter(isactive=True).exclude(
        special_tag__contains='1').values_list('hotelcode', flat=True)
    celery_logger.info('%s\t%s' % ('sending_quality_score_events_for', hotels.count()))
    trigger_qualityscore_events(hotels)


@app.task(name='ingoibibo.quality_score_total_event')
def send_total_quality_score_event_for_hotel(hotelcode, event_time):
    """ celery task to put quality-score event to kafka topic.
         This will be read by watson to calculate final score.

        data = all {
          'hotelCode': 1000199028,
            'target': 'sync',
            'utcms': 1573456041,
            'source': 'completion_trigger',
            'uuid': 1af31641-420e-4a28-9e6d-d661b35dc584
         }
    """
    trigger_qualityscore_events([hotelcode], target='sync', source='completion_trigger', event_time=event_time)


@app.task(name='ingoibibo.quality_score_communication_event')
def send_quality_score_communication_event_for_hotel():
    """ celery task to put quality-score communication event to kafka topic.
         This will be read by watson to push the score to sandesh kafka topic.

        data =
        communication :{
        "target":"communication",
        "hotelId":"1000001645",
        "communicationType":"qualityscore" ,
        "utcms" : "1583150400000" ,
        "frequency" : "daily" ,
        "uuid":"1af31641-420e-4a28-9e6d-d661b35dc584"
        }

    """
    hotels = HotelDetail.objects.only('hotelcode', 'flag_bits_1').filter(isactive=True).exclude(
        special_tag__contains='1').values_list('hotelcode', flat=True)
    celery_logger.info('%s\t%s' % ('sending_quality_score_communication_events_for', hotels.count()))
    extra_data = {'frequency': 'daily', 'communicationType': 'qualityscore'}
    trigger_qualityscore_events(hotels, target='communication',
                                data=extra_data)


@app.task(name='ingoibibo.guest_chat_event')
def send_guest_chat_event_for_hotel():
    """ celery task to put guest-chat event to kafka topic.
             This will be read by watson to push the reply-rate of hotelier to ES.

            data =
            guestChat :{
            "target":"guestChat",
            "hotelId":"1000001645",
            "utcms" : "1583150400000" ,
            "source" : "cron" ,
            "uuid":"1af31641-420e-4a28-9e6d-d661b35dc584"
            }

        """
    sources_to_exclude = [constants.VOYAGER, constants.CONTENT_ONLY, constants.MMT]
    hotels = HotelDetail.objects.only('hotelcode', 'flag_bits_1').filter(isactive=True). \
        exclude(datasource_id__in=sources_to_exclude).exclude(
        special_tag__contains='1').values_list('hotelcode', flat=True)
    trigger_qualityscore_events(hotels, target='guestChat')


# TODO: uncomment this task after production celery task failure issue resolved.
# @app.task(name="ingoibibo.push_change_history_for_bookings")
# def push_change_history_for_bookings(inventory_action, inventory_count, user_id, hotel_id, uname, from_date,
#                                      to_date, room_name, room_code, time_now=None,
#                                      inventory_info_daywise=[], booking_id=None, booking_type='BOOKING',
#                                      inventory_response=True):
#     try:
#         from hotels.hotelinventory import create_inventory_log_for_bookings
#         hotel_object = HotelDetail.objects.only('logs_bucket', 'flag_bits_1',
#                                                 'flag_bits_2', 'flag_bits_3').get(id=hotel_id)
#         create_inventory_log_for_bookings(inventory_action, inventory_count, user_id, hotel_id, uname,
#                                           from_date, to_date, room_name, room_code, time_now,
#                                           inventory_info_daywise, booking_id, booking_type,
#                                           inventory_response, hotel_object.logs_bucket)
#     except Exception as e:
#         log_msg = "Error occured push_change_history_for_bookings " \
#                   "Pro Booking Id : %s. Exception: %s" % (str(booking_id), str(e))
#         inventory_logger.critical(message=log_msg, log_type="ingoibibo", bucket="hotels.tasks",
#                                   stage="push_change_history_for_bookings")


# @app.task(name="ingoibibo.push_chat_newrelic_event", max_retries=5)
# def push_chat_event(event_data, trigger_point):
#     """
#     Push chat API NewRelic event
#     :param event_data: dict
#     :param trigger_point: NewRelic event trigger point
#     :return: None
#     """
#     try:
#         from lib.newrelic_services.insights import trigger_event_api
#         # trigger_event_api(event_data=event_data, trigger_point=trigger_point)
#     except Exception as e:
#         # push_chat_event.retry(args=(event_data, trigger_point))
#         inventory_logger.critical(message="push_chat_event: event_data = {event_data}: "
#                                           "error = {error}".format(event_data=event_data,
#                                                                    error=repr(traceback.format_exc())),
#                                   log_type='ingoibibo', bucket='api.v1.HotelChatTemplateViewSet',
#                                   stage='push_chat_event')


@app.task(name="ingoibibo.send_plb_acrn_multi_reports")
def send_plb_vdi_multi_reports(ids, email_id, hotel_code, report_type, brand=None):
    celery_logger.info('[MO Phase 3] Started creating zip file for hotel {}'
                       'for type {} and email to {}'.format(hotel_code, report_type, email_id))
    from zipfile import ZipFile
    from mo_finance.MO_payment_management import MOAdjustmentHandler
    mo_adt_handler = MOAdjustmentHandler()
    zipfilename = '/tmp/{}_{}.zip'.format(report_type, hotel_code)
    email_content = 'Please find the attached zip as requested.'
    filepaths = []
    for id in ids:
        filename = '/tmp/{}.csv'.format(id)
        with open(filename, 'w') as csvfile:
            csvwriter = csv.writer(csvfile)
            try:
                if report_type == 'acrn':
                    mo_adt_handler.get_acrn_report(csvwriter=csvwriter, hotel_code=hotel_code, adt_id=id)
                elif report_type == 'frn':
                    mo_adt_handler.get_frn_report(csvwriter=csvwriter, hotel_code=hotel_code, booking_id=id)
                elif report_type == 'vdi':
                    mo_adt_handler.get_vdi_report(csvwriter=csvwriter, adt_id=id, hotel_code=hotel_code)
                elif report_type == 'mib':
                    mo_adt_handler.get_mib_report(csvwriter=csvwriter, reference_id=id, brand=brand,
                                                  hotel_code=hotel_code)
            except Exception as e:
                celery_logger.info('[MO Phase 3] Error {} while creating zip file for hotel {}'
                                   'for type {} and email to {}'.format(str(e), hotel_code, report_type, email_id),
                                   log_type='ingoibibo', bucket='MOPhaseIII',
                                   stage='send_plb_vdi_multi_reports')
        filepaths.append(filename)
    with ZipFile(zipfilename, 'w') as zipfile:
        for filepath in filepaths:
            zipfile.write(filepath)
    sendMail('', '', email_content, '[Go-MMT] {} REPORTS'.format(report_type.upper()), '', [email_id],
             attached_file=zipfile, bcc_emails=[], is_zip=True)
    os.remove(zipfilename)
    for filepath in filepaths:
        os.remove(filepath)


@app.task(name="ingoibibo.send_adt_combine_zip_report")
def send_adt_combine_zip_report(id, email_id, hotel_code, report_type, brand=None):
    celery_logger.info('[MO Phase 3] Started creating csv file for hotel {}'
                       'for type {} and email to {}'.format(hotel_code, report_type, email_id))
    from zipfile import ZipFile
    from mo_finance.MO_payment_management import MOAdjustmentHandler
    mo_adt_handler = MOAdjustmentHandler()
    from reports.plbreports import send_vdi_statement_report
    try:
        email_content = 'Please find the attached file as requested.'
        zipfilename = '/tmp/{}_{}.zip'.format(report_type, hotel_code)
        # get the ADT ids for this plb id
        performance_link_records = PerformanceLinkRecords.objects.filter(plb__id__in=[id])
        # fetch object ids
        obj_ids = []
        for each in performance_link_records:
            obj_ids.append(each.id)
        adt_ids = AdjustmentEntry.objects.filter(object_id__in=performance_link_records).values_list('adjustmentid',
                                                                                                     flat=True)
        filepaths = []
        for adt_id in adt_ids:
            filename = '/tmp/{}.csv'.format(adt_id)
            with open(filename, 'w') as csvfile:
                csvwriter = csv.writer(csvfile)
                try:
                    mo_adt_handler.get_vdi_report(csvwriter=csvwriter, hotel_code=hotel_code, adt_id=adt_id)
                except Exception as e:
                    celery_logger.info('[MO Phase 3] Error {} while creating zip file for hotel {}'
                                       'for type {} and email to {}'.format(str(e), hotel_code, report_type, email_id),
                                       log_type='ingoibibo', bucket='MOPhaseIII',
                                       stage='send_plb_vdi_multi_reports')
            filepaths.append(filename)

        # Get the performance booster report
        report_file = send_vdi_statement_report(plr_obj_list=performance_link_records,
                                                download_statement=True)
        filepaths.append(report_file.name)
        with ZipFile(zipfilename, 'w') as zipfile:
            for filepath in filepaths:
                zipfile.write(filepath)
        sendMail('', '', email_content, '[Go-MMT] {} REPORTS'.format(report_type.upper()), '', [email_id],
                 attached_file=zipfile, bcc_emails=[], is_zip=True)
        os.remove(zipfilename)
        for filepath in filepaths:
            os.remove(filepath)

    except Exception as e:
        celery_logger.critical('[MO Phase 3] Error {} while creating zip file for hotel {}'
                               'for type {} and email to {}'.format(str(e), hotel_code, report_type, email_id),
                               log_type='ingoibibo', bucket='MOPhaseIII',
                               stage='send_plb_vdi_multi_reports')


@app.task(name="ingoibibo.send_plb_combined_report")
def send_plb_combined_report(id, email_id, hotel_code, report_type, brand=None):
    celery_logger.info('[MO Phase 3] Started creating csv file for hotel {}'
                       'for type {} and email to {}'.format(hotel_code, report_type, email_id))
    from mo_finance.MO_payment_management import MOAdjustmentHandler
    mo_adt_handler = MOAdjustmentHandler()
    email_content = 'Please find the attached file as requested.'
    filename = '/tmp/{}_{}_{}.csv'.format(report_type, hotel_code, id)
    try:
        with open(filename, 'w') as csvfile:
            csvwriter = csv.writer(csvfile)
            if report_type == 'frn':
                mo_adt_handler.get_frn_combined_report(csvwriter=csvwriter, hotel_code=hotel_code,
                                                       plb_id=id, brand=brand)
            elif report_type in {'acrn', 'vdi'}:
                mo_adt_handler.get_vdi_acrn_combined_report(csvwriter=csvwriter, hotel_code=hotel_code,
                                                            plb_id=id, brand=brand)
        sendMail('', '', email_content, '[Go-MMT] {} REPORTS'.format(report_type.upper()), '', [email_id],
                 attached_file=csvfile, bcc_emails=[])
    except Exception as e:
        celery_logger.info('[MO Phase 3] Error {} while creating zip file for hotel {}'
                           'for type {} and email to {}'.format(str(e), hotel_code, report_type, email_id),
                           log_type='ingoibibo', bucket='MOPhaseIII',
                           stage='send_plb_combined_report')
    os.remove(filename)


# @app.task(name="ingoibibo.populate_otp_newrelic_data")
# def populate_otp_newrelic_data_async(username, success, type, regenerate):
#     populate_otp_newrelic_data(username, success, type, regenerate)


@app.task(name="ingoibibo.close_hotel_conversation", max_retries=10)
def close_hotel_conversation_task():
    """
    Close a conversation after two day of checkout.
    :return: None
    """
    import datetime
    from hotels.models import HotelBooking
    from api.v1.chat_box.resources.hotel_booking import gia_close_bookings_conversation
    try:
        api_logger.info("Hotel close conversation task started.")
        checkout = (datetime.datetime.now() - datetime.timedelta(
            days=settings.CLOSE_POSTCHAT_CONVERSATION_N_DAYS_AFTER_CHECKOUT)).date()
        booking_ids = HotelBooking.objects.filter(checkout=checkout).values_list('id', flat=True)
        status = gia_close_bookings_conversation(bookings_ids=booking_ids)
        celery_logger.info('close_hotel_conversation_task: task_status: {status}'.format(status=status))
    except Exception as e:
        close_hotel_conversation_task.retry(args=(), countdown=10)
        celery_logger.critical('close_hotel_conversation_task: task_status: fail error: {error}'.format(
            error=repr(traceback.format_exc())))


@app.task(name='ingoibibo.upload_event_images')
def upload_event_images_and_save_to_es(image_list, user_id, content_type_id, event_id):
    images_status = ""
    for i in range(0, len(image_list)):
        image = image_list[i]
        if image:
            try:
                s3_file = s3.upload(image)
                # TODOS3 why is this even done?
                file_key = s3_file["file_key"]
                img_url = CDN_DOMAIN + file_key
                response = requests.get(img_url)
                img = PILImage.open(BytesIO(response.content))
                width, height = img.size
                if width >= EVENT_IMAGE_MIN_WIDTH and height >= EVENT_IMAGE_MIN_HEIGHT:
                    image_id = save_event_image_to_common_image(file_key, user_id, content_type_id, event_id)
                    crop_image_to_different_sizes(image_id)
                    images_status += save_image_id_to_event_history(image_id, event_id)
                else:
                    images_status += "Image with URL {url} not uploaded becasue it does not meet the reqd resolution." \
                        .format(url=file_key)
            except:
                pass
    api_logger.info("Images status for event ID {eid} is : {status}".format(eid=event_id, status=images_status))


@app.task(name="ingoibibo.push_user_delta_in_kafka")
def push_user_delta_in_kafka(message_dict):
    """
    This task is responsible for pushing the delta information of an admin user
    on redshift
    :param message_dict:
    :return:
    """
    try:
        # Convert to proto to be pushed to `ADMIN_ACTIVITY` over kafka.
        admin_logs_proto = AdminLogs()
        dict_to_protobuf(message_dict, admin_logs_proto)
        notify_msg = admin_logs_proto.SerializeToString()
        api_logger.info(
            message='User Admin logs Pushing {} on Kafka. time '
                    '{}'.format(str(notify_msg), str(datetime.datetime.now())),
            log_type='ingoibibo',
            bucket='AdminUserKafkaLogs', stage='hotels.tasks.push_user_delta_in_kafka')

        pushed = push_to_kafka('ADMIN_ACTIVITY', notify_msg)

        if pushed:
            api_logger.info(
                message='User Admin logs Pushed {} on Kafka. time '
                        '{}'.format(str(notify_msg), str(datetime.datetime.now())),
                log_type='ingoibibo',
                bucket='AdminUserKafkaLogs', stage='hotels.tasks.push_user_delta_in_kafka')
        else:
            raise IOError('Could not Push {} on Kafka'.format(notify_msg))

    except Exception as err:
        api_logger.critical(
            message='Error while pushing the admin packet on Kafka {}'.format(str(err)),
            log_type='ingoibibo',
            bucket='AdminUserKafkaLogs', stage='hotels.tasks.push_user_delta_in_kafka')


@app.task(name="ingoibibo.default_inclusion_creation")
def meal_plan_change_action(rateplan_id):
    '''
    :param rateplan_object:
    :param new_meal_plan:
    :return: disable the old services(default services and leaf mandatory services) and return leaf template list
    '''
    try:

        rateplan_object = RatePlan.objects.using('default').select_related('roomtype__hotel__hotelcode',
                                                                           'roomtype__hotel__id').only(
            'roomtype__hotel__hotelcode', 'roomtype__hotel__id').get(id=rateplan_id)
        mandatory_inclusion_list = get_mandatory_template_ids(rateplan_object)
        api_logger.info(message='Task start: default meal plan creation for rateplan : %s' % str(rateplan_object.id),
                        log_type='ingoibibo', bucket='meal_plan_change_action',
                        stage='hotels.tasks.meal_plan_change_action')
        make_default_inclusion(rateplan_object, mandatory_inclusion_list)

    except Exception as e:
        api_logger.debug(
            message='Task Failed: default meal plan creation failed for rateplan : %s, exception: %s' % (str(
                rateplan_id), str(e)),
            log_type='ingoibibo', bucket='meal_plan_change_action',
            stage='hotels.tasks.meal_plan_change_action')


@app.task(name="ingoibibo.refresh_services_metadata")
def refresh_services_async(service_id):
    try:
        refresh_service_metadata(service_id)
        log_identifier = {}
        log_msg = 'inclusion data refresh task for service id %s' % (service_id)
        update_specific_identifier("remark", log_msg, log_identifier)
        api_logger.info(message='inclusion data refresh task',
                        log_type='ingoibibo', bucket='services', stage='hotels.tasks.refresh_services_async', identifier='{}'.format(log_identifier))

    except Exception as e:
        api_logger.critical(
            message='exception occured %s while refresh metadata from service id %s, traceback %s' % (
                str(e), str(service_id), repr(traceback.format_exc())), bucket='services',
            stage='hotels.tasks.refresh_services_async', log_type='ingoibibo')
        raise e


@app.task(name="ingoibibo.send_rm_vendor_mapping_alert_event")
def send_rm_vendor_mapping_alert_task(rm_id, user_id):
    '''
    Send RM Vendor Mapping Alert
    '''
    from communication.hotelMailsSMS import send_rm_vendor_mapping_alert_event
    rm_vendor = VendorRMMapping.objects.get(id=rm_id)
    send_rm_vendor_mapping_alert_event(rm_vendor_mapping_obj=rm_vendor, user_id=user_id)


def create_agreementmapping_uploader(hotels_list, agreement_list, user_id):
    try:
        success_list = []
        failed_list = []
        hotel_objs = HotelDetail.objects.filter(hotelcode__in=hotels_list)
        user_object = User.objects.get(id=user_id)

        for hotel_object in hotel_objs:
            for agreement_id in agreement_list:
                try:
                    new_agreement_mapping, created = HotelAgreementMapping.objects.get_or_create(
                        hotel_id=hotel_object.id,
                        agreement_id=agreement_id,
                        defaults={'user': user_object})
                    if not created:
                        new_agreement_mapping.status = 'pending'
                        new_agreement_mapping.save()

                except Exception as e:
                    failed_list.append(hotel_object.hotelcode)
                    api_logger.critical(
                        message='exception occured while mapping : %s, agreement_id: %s' % (str(e), str(agreement_id)),
                        log_type='ingoibibo', bucket='create_agreementmapping_uploader',
                        stage='hotels.tasks.create_agreementmapping_uploader')

            success_list.append(hotel_object.hotelcode)

        api_logger.info(
            message='mapping created successfully hotelcode : %s, agreement_id: %s' % (
                success_list, failed_list),
            log_type='ingoibibo', bucket='create_agreementmapping_uploader',
            stage='hotels.tasks.create_agreementmapping_uploader')


    except Exception as e:
        api_logger.critical(
            message='exception occured: %s, success_list : %s, failed_list: %s' % (
                str(e), success_list, failed_list),
            log_type='ingoibibo', bucket='create_agreementmapping_uploader',
            stage='hotels.tasks.create_agreementmapping_uploader')
        raise e


def create_hotel_agreement_mapping_from_excel():
    try:
        success_list = []
        failed_list = []
        file_path = '/tmp/hotel_agreement_mapping.csv'
        user_id = 2 if settings.DEBUG else 118322
        user_object = User.objects.get(id=user_id)
        with open(file_path, 'rb') as csv_file:
            rows = csv.DictReader(csv_file)
            for row in rows:
                try:
                    hotel_code = int(row.pop('hotel_code')) if row.get('hotel_code') else ''
                    agreement_id = int(row.pop('agreement_id')) if row.get('agreement_id') else ''
                    if hotel_code and agreement_id:
                        hotel_object = HotelDetail.objects.get(hotelcode=hotel_code)
                        new_agreement_mapping, created = HotelAgreementMapping.objects.get_or_create(
                            hotel_id=hotel_object.id,
                            agreement_id=agreement_id,
                            defaults={'user': user_object})
                        if not created:
                            new_agreement_mapping.status = 'pending'
                            new_agreement_mapping.save()
                        success_list.append(hotel_object.hotelcode)
                    else:
                        failed_list.append(hotel_code)
                except Exception as e:
                    failed_list.append(hotel_code)
                    api_logger.critical(
                        message='exception occured while mapping : %s, hotel_code: %s' % (str(e), str(hotel_code)),
                        log_type='ingoibibo', bucket='agreement_mapping',
                        stage='hotels.tasks.create_hotel_agreement_mapping_from_excel')

    except Exception as e:
        api_logger.critical(
            message='exception occured: %s, success_list : %s, failed_list: %s' % (
                str(e), success_list, failed_list),
            log_type='ingoibibo', bucket='agreement_mapping',
            stage='hotels.tasks.create_hotel_agreement_mapping_from_excel')


@app.task(name="ingoibibo.create_helpcenter_user")
def create_helpcenter_user():
    from hotels.helpcenter import create_freshworks_contacts
    api_logger.info(message='helpcenter created contacts task start',
                    log_type='ingoibibo', bucket='create_helpcenter_user', stage='hotels.tasks.create_helpcenter_user')
    start_time = datetime.datetime.now() - datetime.timedelta(minutes=30)
    # CHECKUSERCHANGE - DONE
    users = User.objects.filter(date_joined__gte=start_time).exclude(email__isnull=True)
    create_freshworks_contacts(users)
    api_logger.info(message='helpcenter created contacts task end',
                    log_type='ingoibibo', bucket='create_helpcenter_user', stage='hotels.tasks.create_helpcenter_user')


@app.task(name="ingoibibo.create_helpcenter_company")
def create_helpcenter_company():
    from hotels.helpcenter import create_helpcenter_company
    api_logger.info(message='helpcenter created company task start',
                    log_type='ingoibibo', bucket='create_helpcenter_company',
                    stage='hotels.tasks.create_helpcenter_company')
    start_time = datetime.datetime.now() - datetime.timedelta(minutes=30)
    hotels = HotelDetail.objects.filter(createdon__gte=start_time)
    create_helpcenter_company(hotels)
    api_logger.info(message='helpcenter created company task end',
                    log_type='ingoibibo', bucket='create_helpcenter_company',
                    stage='hotels.tasks.create_helpcenter_company')


@app.task(name="ingoibibo.vendor_correction")
def vendor_sync_correction(vendor_id):
    try:
        vm = VendorMapping.objects.using('default').get(
            vendor__id=vendor_id, isactive=True)
        hotel_obj = vm.hotel
        vendor = vm.vendor
        if hotel_obj.country.upper() == 'INDIA':
            vendor_posting_group = 'HOTDOMON'
        else:
            vendor_posting_group = 'HOTINTON'
        adjustment_day_of_month = settings.DEFAULT_ADJUSTMENT_DOM
        if not vendor.vendor_posting_group or \
                (str(vendor.vendor_posting_group).lower() == 'none'):
            vendor.vendor_posting_group = vendor_posting_group
        if not vendor.adjustment_day_of_month or \
                (str(vendor.vendor_posting_group).lower() == 'none'):
            vendor.adjustment_day_of_month = adjustment_day_of_month
        vendor.save()
    except Exception as e:
        api_logger.critical(
            message='exception occured: %s, vendor id : %s' % (
                str(e), str(vendor_id)),
            log_type='ingoibibo', bucket='vendor_correction',
            stage='hotels.tasks.vendor_correction')


@app.task(name="update_amended_booking_status")
def update_amended_booking_status_task(old_booking_id, amended_booking_id):
    try:
        log_identifier= {}
        update_specific_identifier('booking_id', amended_booking_id, log_identifier)
        bobj = HotelBooking.objects.using('default').only('confirmstatus', 'misc').get(confirmbookingid=old_booking_id)
        misc_data = json.loads(bobj.misc)
        misc_data['amended_booking_id'] = amended_booking_id
        bobj.misc = json.dumps(misc_data)
        old_status = bobj.confirmstatus
        bobj.confirmstatus = 'amended'
        bobj.save()
        log_msg = 'Booking status updated, Booking id- %s, old_status - %s, current_status - %s' % (
            old_booking_id, old_status, bobj.confirmstatus)
        update_specific_identifier('remark', log_msg, log_identifier)
        api_logger.info(log_type='ingoibibo', bucket='hotels.hotelbooking',
                        stage='update_amended_booking_status_task', identifier='{}'.format(log_identifier))
    except Exception as e:
        update_error_identifier(error_message=str(e), traceback=repr(traceback.format_exc()),
                                log_identifier=log_identifier)
        api_logger.critical(log_type='ingoibibo', bucket='hotels.hotelbooking',
                            stage='update_amended_booking_status_task',
                            identifier="{}".format(log_identifier))


@app.task(name="update_whatsapp_number_sandesh", max_retries=2)
def sync_whatsapp_sandesh_async(object_id):
    """
    Celery task to to sync information
    :param object_id: InstantMessenger object id
    :return: None
    """
    from hotels.models import InstantMessenger
    try:
        mobile_number = ""
        obj = InstantMessenger.objects.using("default").get(id=object_id)
        if obj.status == 'active' \
                and obj.subscription_status == 'subscribed' \
                and obj.contact_type == 'whatsapp':
            mobile_number = obj.country.dialing_prefix + obj.contact_no
        url = settings.SANDESH_ENDPOINT + settings.WHATSAPP_UPDATE_ENDPOINT.format(client_id=obj.user.id)
        headers = {
            'Content-Type': 'application/json',
            'Authorization': 'Basic aW5nb2liaWJvX2FwaV91c2VyOiFzYW5kZXNoQDEyMw=='
        }
        payload = {
            "user_type": "auth_user",
            "mobile_number": mobile_number
        }
        response = requests.request("PUT", url, headers=headers, data=json.dumps(payload))
        if response.status_code != 200:
            api_logger.critical(message='sync_whatsapp_sandesh did not return 200: object_id: {object_id}, payload: {payload}, response_code: {response_code}'.format(object_id=object_id, payload=payload, response_code=response.status_code),
                                log_type='ingoibibo', bucket='sync_whatsapp_sandesh',
                                stage='hotels.tasks.sync_whatsapp_sandesh')
            raise Exception("Fail to update user WhatsApp number, retry count: {retry_count}".format(
                retry_count=sync_whatsapp_sandesh_async.request.retries))
        else:
            api_logger.info(message='sync_whatsapp_sandesh concluded successfully: object_id: {object_id}, payload: {payload}'.format(object_id=object_id, payload=payload),
                                log_type='ingoibibo', bucket='sync_whatsapp_sandesh',
                                stage='hotels.tasks.sync_whatsapp_sandesh')
    except Exception as e:
        api_logger.critical(message='sync_whatsapp_sandesh returned exception: object_id: {object_id}, traceback: {traceback}'.format(object_id=object_id, traceback=repr(traceback.format_exc())),
                        log_type='ingoibibo', bucket='sync_whatsapp_sandesh',
                        stage='hotels.tasks.sync_whatsapp_sandesh')
        sync_whatsapp_sandesh_async.retry(args=(object_id,), countdown=300)


@app.task(name="ingoibibo.create_booking_scheduler")
def create_guest_chat_booking_scheduler(template_obj, hotel_id=None, booking_id=None, booking_domain=None,
                                        checkin_date=None, checkout_date=None):
    """
    Function to create a scheduler (if exists) for a new booking or new template creation.
    :param template_obj: Template object
    :param hotel_id: Hotel ID against which the booking is made.
    :param booking_id: Ingo booking ID
    :param booking_domain: Booking Vendor (Goibibo / MakeMyTrip)
    :param checkin_date: Checkin date
    :param checkout_date: Checkout date
    :return:
    """
    try:
        if template_obj is None:
            # Case of new confirmed booking
            hotel_templates = HotelChatTemplate.objects.filter(isactive=True, is_scheduled=True, hotel__id=hotel_id)

            for template in hotel_templates:
                # Create a task only if a task does not already exist for this template ID and booking ID.
                if not check_guest_chat_task_duplicacy(booking_id, template.id):
                    create_ingo_scheduler(template, booking_id, booking_domain, checkin_date, checkout_date)

        else:
            # Todo case of new template creation.
            pass

    except Exception as e:
        api_logger.critical("Exception while creating guest chat booking scheduler with hotel_id {hid} booking_id "
                            "{bid} exception {e} traceback {t}"
                            .format(hid=hotel_id, bid=booking_id, e=e, t=repr(traceback.format_exc())),
                            log_type="ingoibibo", bucket="api.v2.guest_chat",
                            stage=create_guest_chat_booking_scheduler.__name__)


@app.task(name="ingoibibo.close_guest_chat_conversation")
def close_guest_chat_conversation(hotelcode=None, booking_id=None, booking_domain=None):
    """
    Function to call the close conversation API. It is called in two cases:
    1. Daily cron at 11 AM, where we close all the prechat and postchat which had their checkout n days ago.
    2. When a booking is cancelled, we close it's session if it existed.
    :param hotelcode: Ingo hotelcode
    :param booking_id: Vendor booking ID
    :param booking_domain: Goibibo / MakeMyTrip
    :return:
    """
    try:
        close_conversation(hotelcode, booking_id, booking_domain)
    except Exception as e:
        api_logger.critical("Exception closing guest chat conversation for hotelcode {h} booking_id {bid} exception "
                            "{e} traceback {t}".format(h=hotelcode, bid=booking_id, e=e,
                                                       t=repr(traceback.format_exc())),
                            log_type="ingoibibo", bucket="api.v2.guest_chat",
                            stage=close_guest_chat_conversation.__name__)

@app.task(name="ingoibibo.vendor_update_alert")
def vendor_update_alert(vendor_id, alert_vendor_status, bank_account_id=None):
    api_logger.info(message='vendor_update_alert task start for vendor {}'.format(vendor_id),
                    log_type='ingoibibo', bucket='vendor_update_alert',
                    stage='hotels.tasks.vendor_update_alert')
    from communication.hotelMailsSMS import send_vendor_update_request_event
    send_vendor_update_request_event(vendor_id, alert_vendor_status, bank_account_id)
    api_logger.info(message='vendor_update_alert task end for vendor {}, status {} bankaccount {}'.format(
        vendor_id, alert_vendor_status, bank_account_id),
        log_type='ingoibibo', bucket='vendor_update_alert',
        stage='hotels.tasks.vendor_update_alert')


@app.task(name="ingoibibo.reject_pending_waiver_request")
def reject_pending_waiver_request():
    from common.constants import PENDING_WAIVER_FLAG_BIT1, ACCEPTED_WAIVER_FLAG_BIT1
    api_logger.info(message='reject_pending_waiver_request task start',
                    log_type='ingoibibo', bucket='reject_pending_waiver_request',
                    stage='hotels.tasks.reject_pending_waiver_request')

    pending_waiver_requests_booking_id = BookingFlagsInfo.objects.filter(flagbit1=PENDING_WAIVER_FLAG_BIT1).values_list(
        'confirmbookingid', flat=True)
    bookings = HotelBooking.objects.filter(confirmbookingid__in=pending_waiver_requests_booking_id,
                                           confirmstatus='confirmed',
                                           checkout=datetime.date.today() - datetime.timedelta(days=2))

    for booking in bookings:
        waiver = BookingFlagsInfo.objects.get(confirmbookingid=booking.confirmbookingid)
        waiver.waiver_rejected = True
        waiver.waiver_pending = False
        waiver.save()
        booking.set_misc_data('hotelier_waiver_message', "No Response")
        booking.set_misc_data('waiver_status', HOTEL_WAIVER_STATUS["Rejected"])

    hotelier_initiated_waiver_requests_booking_id = BookingFlagsInfo.objects.filter(
        flagbit1=ACCEPTED_WAIVER_FLAG_BIT1).values_list(
        'confirmbookingid', flat=True)
    hotelier_initiated_waiver_bookings = HotelBooking.objects.filter(
        confirmbookingid__in=hotelier_initiated_waiver_requests_booking_id,
        confirmstatus='confirmed', checkout=datetime.date.today() - datetime.timedelta(days=1))

    for booking in hotelier_initiated_waiver_bookings:
        waiver = BookingFlagsInfo.objects.get(confirmbookingid=booking.confirmbookingid)
        waiver.waiver_rejected = True
        waiver.waiver_accepted = False
        waiver.save()

    api_logger.info(message='reject_pending_waiver_request task end',
                    log_type='ingoibibo', bucket='reject_pending_waiver_request',
                    stage='hotels.tasks.reject_pending_waiver_request')


@app.task(name='"ingoibibo.create_hotel_route_grammar_text')
def create_hotel_route_grammar_text(hotel_route_object, caller='post_save'):
    from api.v2.routes.resources.resources import create_route_grammar_data

    try:
        grammar_text = create_route_grammar_data(hotel_route_object)
        hotel_route_object.grammar_text = grammar_text
        hotel_route_object.save()
        celery_logger.info(
            'Hotel route id: {} grammar creation success in caller {}'.format(hotel_route_object.id, caller))
    except Exception as e:
        celery_logger.critical(
            'Hotel route id: {} grammar creation success in caller {}, error {}'.format(hotel_route_object.id, caller,
                                                                                        e))


@app.task(name="ingoibibo.execute_scheduled_jobs")
def execute_scheduled_jobs(scheduled_jobs):
    try:
        from actions.action_helper import action_runner
        for job in scheduled_jobs:
            if job.action_job.status == "pending":

                # Getting cases where celery task is getting executed multiple times for same action job ID. Hence,
                # adding this as a temporary fix. Before executing, we check whether there exists an entry for this task
                # in cache. Task is executed only if no entry is found.
                action_job_id = job.action_job.id
                if cache.get("ACTION_JOB_{aid}".format(aid=action_job_id)):
                    celery_logger.info("Action job ID {aid} already picked up for execution hence skipping."
                                       .format(aid=action_job_id))
                    return

                # set in cache the action job ID with a TTL of 1 minute.
                cache.set("ACTION_JOB_{aid}".format(aid=action_job_id), True, 60)
                action_runner(job.action_job)

    except Exception as e:
        celery_logger.critical("Exception executing scheduled jobs exception {e} traceback {t}"
                               .format(e=e, t=repr(traceback.format_exc())))


@app.task(name="ingoibibo.update_rtb_post_booking")
def update_rtb_post_booking(rtb_obj, pbobj, booking_response, confirmbookingid):
    log_identifier = {}
    try:
        update_specific_identifier('rtb_id', str(rtb_obj.id), log_identifier)
        if rtb_obj:
            from communication.tasks import rtb_event_communication
            if booking_response['success'] and confirmbookingid:
                rtb_obj.confirmbookingid = confirmbookingid
                rtb_obj.booking_created = True
                rtb_event_type = RTB_COMMUNICATION_EVENT[1]
            else:
                booking_error = json.loads(rtb_obj.misc).get('error_message', '')
                if booking_error:
                    booking_error = ", ".join([booking_error, booking_response.get('error', '')])
                else:
                    booking_error = booking_response.get('error', '')
                rtb_obj.set_misc_data('error_message', booking_error)
                rtb_obj.booking_failed = True
                rtb_event_type = RTB_COMMUNICATION_EVENT[2]
            rtb_obj.save()
            rtb_event_communication(pbobj, rtb_obj, rtb_event_type)
    except Exception as e:
        update_error_identifier(error_message=str(e), traceback=repr(traceback.format_exc()),
                                log_identifier=log_identifier)
        inventory_logger.critical(log_type="ingoibibo", bucket="hotels.tasks",
                                  stage="update_rtb_post_booking",
                                  identifier="{}".format(log_identifier))

@app.task(name="ingoibibo.verify_offers_value")
def verify_offers_value(response, booking_dict):
    try:
        probooking_response = json.loads(json.dumps(response["message"]))
        offer_source_for_booking = probooking_response.get('pricebreakup', {}).get('offer_source', None)
        request_obj = booking_dict.pop('request', None)
        probooking_id = probooking_response.get('probookingid', 0)

        check_offer_source = "mysql" if offer_source_for_booking == "aerospike" else "aerospike"

        booking_dict["source"] = check_offer_source
        booking_dict["bypass_inventory"] = True
        check_offer_source_response = api_pricing_client.get_pricing_info(booking_dict, request_obj)
        check_offer_source_response = json.loads(json.dumps(check_offer_source_response))
        price_value = check_offer_source_response.get('pricebreakup', {}).get('sell_amt_all_inclusive', 0)
        probooking_price_value = probooking_response.get('pricebreakup', {}).get('sell_amt_all_inclusive', 0)

        diff = int(price_value) - int(probooking_price_value)
        status = False if abs(diff) > 1 else True
        if status:
            api_logger.info(
                message='Verify Offers Value task probooking_id %s check_offer_source_value %s probooking_price_value %s offer_source_for_booking %s status %s' %
                        (probooking_id, price_value, probooking_price_value, offer_source_for_booking, status),
                log_type='ingoibibo',
                bucket='ProBookingAPI', stage='hotels.tasks.verify_offers_value')
        else:
            api_logger.critical(
                message='Verify Offers Value task CRITICAL probooking_id %s check_offer_source_value %s probooking_price_value %s offer_source_for_booking %s status %s' %
                        (probooking_id, price_value, probooking_price_value, offer_source_for_booking, status),
                log_type='ingoibibo',
                bucket='ProBookingAPI', stage='hotels.tasks.verify_offers_value')
    except Exception as e:
        api_logger.critical(message='Verify Offers Value task EXCEPTION error %s' % (str(e)), log_type='ingoibibo',
                            bucket='ProBookingAPI', stage='hotels.tasks.verify_offers_value')


@app.task(name="ingoibibo.offers_verification_script")
def offers_verification_script_task():
    try:
        hotel_code_list = HotelDetail.objects.filter(hotelcode__isnull=False).values_list("hotelcode", flat=True)
        hotel_code_list = map(str, hotel_code_list)
        verify_list = []

        from scripts.verify_offers_in_aerospike import verify_data_from_aerospike

        for hotelcode in hotel_code_list:
            if len(verify_list) == 10000:
                verify_data_from_aerospike(verify_list)
                verify_list = []
            else:
                verify_list.append(hotelcode)

        if len(verify_list) != 0:
            verify_data_from_aerospike(verify_list)
            verify_list = []

    except Exception as e:
        api_logger.critical(message='offers_verification_script_task EXCEPTION error %s' % (str(e)),
                            log_type='ingoibibo',
                            bucket='OfferVerification', stage='hotels.tasks.offers_verification_script_task')


@app.task(name="ingoibibo.offers_delete_script")
def offers_delete_script_task():
    try:
        from scripts.delete_inactive_offers_from_aerospike import validate_offers_to_delete
        validate_offers_to_delete()

    except Exception as e:
        api_logger.critical(message='offers_delete_script_task EXCEPTION error %s' % (str(e)), log_type='ingoibibo',
                            bucket='OfferEviction', stage='hotels.tasks.offers_delete_script_task')


@app.task(name="ingoibibo.update_linked_rateplan_model")
def update_linked_rateplan_model(rate_plan_code):
    try:
        celery_logger.info('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'update_linked_rateplan_model',
            rate_plan_code, 'Rate plan net rate model changed'))

        # Reading from master to avoid master-slave lag for net_rate flag update
        rate_plan_obj = RatePlan.objects.using('default').get(rateplancode=rate_plan_code)
        if rate_plan_obj:
            linked_rateplan_list = RatePlan.objects.filter(parent_id=rate_plan_obj.id)
            if linked_rateplan_list:
                for rateplan in linked_rateplan_list:
                    rateplan.is_net_rate_model = rate_plan_obj.is_net_rate_model
                    rateplan.save()
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'update_linked_rateplan_model',
            rate_plan_code, repr(e)))


@app.task(name="ingoibibo.update_rates_on_commission_change")
def update_rates_on_commission_change(rate_plan_code, sellcommission, user):
    try:
        celery_logger.info('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'update_rates_on_commission_change',
            rate_plan_code, 'Rate plan commission change initiated'))
        rate_plan_obj = RatePlan.objects.get(rateplancode=rate_plan_code)
        rates_dict = dict()
        rates_dict['hotel_code'] = rate_plan_obj.roomtype.hotel.hotelcode
        rates_dict['user_id'] = str(user.id)
        rates_dict['source'] = 'CommissionChange'
        rates_dict['user_name'] = str(user.username)
        rates_dict['data'] = [
            {"code_list": [rate_plan_code], "action": "commission_update", "old_value": sellcommission,
             "level": "rate_plan"}]
        response = grpc_rates_client.rate_plan_update_task(rates_dict)
        if not response.get('success', False):
            celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
                'hotels', 'tasks', 'update_rates_on_commission_change',
                rate_plan_code, str(response), 'Unable to perform the commission update task'))
        else:
            celery_logger.info('%s\t%s\t%s\t%s\t%s\t%s' % (
                'hotels', 'tasks', 'update_rates_on_commission_change',
                rate_plan_code, str(response), 'Successfully updated the rates on commission change'))

    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'update_rates_on_commission_change',
            rate_plan_code, repr(e), repr(traceback.format_exc())))


@app.task(name="ingoibibo.update_rateplan_nr_model")
def update_rateplan_nr_model(hotel_code):
    try:
        celery_logger.info('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'update_rateplan_nr_model',
            hotel_code, 'Update net rate model on rateplan initiated'))

        rate_plan_list = RatePlan.objects.select_related('roomtype__hotel__is_net_rate_model').filter(
            roomtype__hotel__hotelcode=hotel_code)
        if len(rate_plan_list) > 0:
            is_net_rate_model = rate_plan_list[0].roomtype.hotel.is_net_rate_model
            for rate_plan in rate_plan_list:
                rate_plan.is_net_rate_model = is_net_rate_model
                rate_plan.save()
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'update_rateplan_nr_model',
            hotel_code, repr(e), repr(traceback.format_exc())))


@app.task(name="ingoibibo.update_rateplan_apply_commission_on_post_tax")
def update_rateplan_apply_commission_on_post_tax(hotel_code):
    try:
        celery_logger.info('%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'update_rateplan_apply_commission_on_post_tax',
                                                   hotel_code,
                                                   'Update Apply Commission On Post Tax on rateplan initiated'))

        rate_plan_list = RatePlan.objects.select_related('roomtype__hotel__apply_commission_on_post_tax').filter(
            roomtype__hotel__hotelcode=hotel_code)
        if len(rate_plan_list) > 0:
            apply_commission_on_post_tax = rate_plan_list[0].roomtype.hotel.apply_commission_on_post_tax
            for rate_plan in rate_plan_list:
                rate_plan.apply_commission_on_post_tax = apply_commission_on_post_tax
                rate_plan.save()
    except Exception as e:
        celery_logger.critical(
            '%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'update_rateplan_apply_commission_on_post_tax',
                                        hotel_code, repr(e), repr(traceback.format_exc())))


@app.task(name="ingoibibo.update_rates_on_commission_and_apply_commission_on_post_tax_change")
def update_rates_on_commission_and_apply_commission_on_post_tax_change(rate_plan_code, sellcommission, user):
    try:
        celery_logger.info('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'update_rates_on_commission_and_apply_commission_on_post_tax_change',
            rate_plan_code, 'RatePlan Commission & Apply Commission On Post Tax change initiated'))
        rate_plan_obj = RatePlan.objects.get(rateplancode=rate_plan_code)
        rates_dict = dict()
        rates_dict['hotel_code'] = rate_plan_obj.roomtype.hotel.hotelcode
        rates_dict['user_id'] = str(user.id)
        rates_dict['source'] = 'Commission&ApplyCommissionOnPostTaxChange'
        rates_dict['user_name'] = str(user.username)
        rates_dict['data'] = [
            {"code_list": [rate_plan_code], "action": "commission_and_apply_commission_on_post_tax_update",
             "old_value": sellcommission, "level": "rate_plan"}]
        response = grpc_rates_client.rate_plan_update_task(rates_dict)
        if not response.get('success', False):
            celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
                'hotels', 'tasks', 'update_rates_on_commission_and_apply_commission_on_post_tax_change',
                rate_plan_code, str(response),
                'Unable to perform the commission & Apply Commission On Post Tax update task'))
        else:
            celery_logger.info('%s\t%s\t%s\t%s\t%s\t%s' % (
                'hotels', 'tasks', 'update_rates_on_commission_and_apply_commission_on_post_tax_change',
                rate_plan_code, str(response),
                'Successfully updated the rates on commission & Apply Commission On Post Tax change'))

    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'update_rates_on_commission_and_apply_commission_on_post_tax_change',
            rate_plan_code, repr(e), repr(traceback.format_exc())))


@app.task(name="ingoibibo.update_rates_on_apply_commission_on_post_tax_update")
def update_rates_on_apply_commission_on_post_tax_update(rate_plan_code, user):
    try:
        celery_logger.info('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'update_rates_on_apply_commission_on_post_tax_update',
            rate_plan_code, 'Rate plan Apply Commission On Post Tax change initiated'))
        rate_plan_obj = RatePlan.objects.get(rateplancode=rate_plan_code)
        rates_dict = dict()
        rates_dict['hotel_code'] = rate_plan_obj.roomtype.hotel.hotelcode
        rates_dict['user_id'] = str(user.id)
        rates_dict['source'] = 'ApplyCommissionOnPostTaxChange'
        rates_dict['user_name'] = str(user.username)
        rates_dict['data'] = [{"code_list": [rate_plan_code], "action": "apply_commission_on_post_tax_update",
                               "old_value": float(rate_plan_obj.apply_commission_on_post_tax), "level": "rate_plan"}]
        response = grpc_rates_client.rate_plan_update_task(rates_dict)
        if not response.get('success', False):
            celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
                'hotels', 'tasks', 'update_rates_on_apply_commission_on_post_tax_update', rate_plan_code,
                str(response), 'Unable to perform the Apply Commission On Post Tax update task'))
        else:
            celery_logger.info('%s\t%s\t%s\t%s\t%s\t%s' % (
                'hotels', 'tasks', 'update_rates_on_apply_commission_on_post_tax_update',
                rate_plan_code, str(response), 'Successfully updated the rates on Apply Commission On Post Tax change'))

    except Exception as e:
        celery_logger.critical(
            '%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'tasks', 'update_rates_on_apply_commission_on_post_tax_update',
                                        rate_plan_code, repr(e), repr(traceback.format_exc())))


@app.task(name="ingoibibo.update_host_app_hotel_rate_plan_commission")
def update_host_app_hotel_rate_plan_commission(hotel_code, new_partner_commission):
    try:
        celery_logger.info('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'update_host_app_hotel_rate_plan_commission',
            hotel_code, 'Update rate_plan commission for host app hotels'))
        RatePlan.objects.filter(roomtype__hotel__hotelcode=hotel_code).update(sellcommission=new_partner_commission)
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'update_host_app_hotel_rate_plan_commission',
            hotel_code, repr(e), repr(traceback.format_exc())))


@app.task(name="ingoibibo.update_hotel_commission_for_program")
def update_hotel_commission_for_program(requested_user, hotel_id, program_type, is_active, program_commission):
    try:
        celery_logger.info('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'update_hotel_commission_for_program',
            hotel_id, program_type, 'Update rateplan commission for program enrolled hotel'))
        resp = update_program_commission(requested_user, hotel_id, program_type, is_active, program_commission, True)
        if not resp["success"]:
            celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
                'hotels', 'tasks', 'update_hotel_commission_for_program',
                hotel_id, program_type, resp["msg"]))
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'update_hotel_commission_for_program',
            hotel_id, program_type, repr(e), repr(traceback.format_exc())))


@app.task(name="ingoibibo.push_offers_in_aerospike")
def push_offers_in_aerospike():
    try:
        hotel_list = list(HotelDetail.objects.filter(~Q(hotelcode=None)).values_list('hotelcode', flat=True))
        hotelcode_list_length = 1000
        hotel_list = [hotel_list[i:i + hotelcode_list_length] for i in range(0, len(hotel_list), hotelcode_list_length)]
        for hotelcode_list in hotel_list:
            from scripts.push_offers_to_aerospike import push_active_offers_to_aerospike_cache
            if settings.DEBUG:
                push_active_offers_to_aerospike_cache(hotelcode_list)
            else:
                push_active_offers_to_aerospike_cache.apply_async(args=(hotelcode_list), )

    except Exception as e:
        api_logger.critical(message='push_offers_in_aerospike EXCEPTION error %s' % (str(e)), log_type='ingoibibo',
                            bucket='OfferVerification', stage='hotels.tasks.push_offers_in_aerospike')


@app.task(name="ingoibibo.templates_isactive_toggle")
def templates_isactive_toggle():
    try:
        today_date = datetime.datetime.today().date()
        inactive_templates = Templates.objects.filter(is_active=False, start_date=today_date,
                                                      end_date__gte=today_date)
        active_templates = Templates.objects.filter(is_active=True, end_date__lt=today_date)

        inactive_templates.update(is_active=True)
        active_templates.update(is_active=False)

        api_logger.info("Marked inactive templates {it} active and active templates {at} inactive using daily cron."
                        .format(it=inactive_templates, at=active_templates), log_type="ingoibibo",
                        bucket="CommonTemplates", stage="templates_isactive_toggle")

    except Exception as e:
        api_logger.critical("Error toggling templates isactive exception {e} traceback {t} using daily cron."
                            .format(e=e, t=repr(traceback.format_exc())), log_type="ingoibibo",
                            bucket="CommonTemplates", stage="templates_isactive_toggle")


@app.task(name="ingoibibo.confirmstatus_cron")
def confirmstatus_cron():
    '''
    This cron changes confirmstatus of bookings from pending to confirmed after checkout+7 days.
    The job runs on a day basis.
    '''
    try:
        filter_params = {}

        today = datetime.date.today()
        check_period = datetime.timedelta(days=7)

        filter_params['checkout'] = today - check_period
        filter_params['confirmstatus'] = 'pending'
        filter_params['payathotelflag'] = 0
        bobjs = HotelBooking.objects.filter(**filter_params)

        for bobj in bobjs:
            bobj.confirmstatus = 'confirmed'
            bobj.save()

    except Exception as e:
        api_logger.critical("Exception while changing confirmstatus from pending to confirmed"
                            "{e} traceback {t}".format(e=e, t=repr(traceback.format_exc())),
                            log_type="ingoibibo", bucket="confirmstatus_cron",
                            stage=confirmstatus_cron.__name__)


@app.task(name="ingoibibo.caretaker_info_missing")
def caretaker_info_missing():
    from common.models import Caretaker
    from communication.tasks import send_caretaker_info_missing_notification

    try:
        constants = ['is_fulltime', 'tasks', 'languages', 'is_communicable', 'is_vaccinated']
        required_fields = {'{0}__isnull'.format(_): True for _ in constants}
        # Caretakers which have any of above constants or active image missing.
        caretakers = (Caretaker.objects.exclude(is_active=False).filter(
            reduce(operator.or_, (Q(**d) for d in (dict([i]) for i in required_fields.items())))
        ) | Caretaker.objects.exclude(image__isactive=True)).distinct()
        for caretaker in caretakers:
            send_caretaker_info_missing_notification.apply_async(args=(caretaker.name, caretaker.object_id))

    except Exception as e:
        celery_logger.critical(
            'common task, caretaker_info_missing, error: {0} - {1}'.format(e, traceback.format_exc()))


@app.task(name="ingoibibo.check_invite_expired")
def check_invite_expired():
    from hotels.models.invitation import Invitation
    from communication.tasks import send_invite_expired_notification

    try:
        yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
        # Invitations that expired yesterday.
        invitations = Invitation.objects.exclude(is_active=False).filter(expiry_date__range=(
            datetime.datetime.combine(yesterday, datetime.time.min),
            datetime.datetime.combine(yesterday, datetime.time.max))
        )
        for invitation in invitations:
            send_invite_expired_notification.apply_async(args=(invitation.id,))

    except Exception as e:
        celery_logger.critical('common task, check_invite_expired, error: {0} - {1}'.format(e, traceback.format_exc()))


@app.task(name='ingoibibo.update_import_listing_hotel_data')
def update_import_listing_hotel_data(hotel_id, hotel_data):
    try:
        hotel_obj_master = HotelDetail.objects.using('default').get(id=hotel_id)

        for attr, value in hotel_data.items():
            if getattr(hotel_obj_master, attr) is None and value is not None:
                setattr(hotel_obj_master, attr, value)

        hotel_obj_master.save()
    except Exception as e:
        celery_logger.critical('hotels task, update_import_listing_hotel_data, hotel id: {} error: {}'.format(hotel_id,
                                                                                                              traceback.format_exc()))


@app.task(name="ingoibibo.update_default_data_at_rateplan_level")
def update_default_data_at_rateplan_level(rule_code, request_data=None):
    try:
        celery_logger.info('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'update_default_data_at_rateplan_level',
            rule_code, 'Default data rule updated'))
        # Fetch matching rateplan, room, hotel for this rule code using content type id
        default_data_qry_set = DefaultData.objects.filter(rule_code=rule_code)
        if len(default_data_qry_set) > 0:
            default_data = default_data_qry_set[0]
            if default_data and default_data.id:
                # Rateplan level update
                if default_data.content_type_id == 31:
                    default_rule_filter = {}
                    if default_data.property_code and default_data.property_code != '':
                        default_rule_filter['roomtype__hotel__hotelcode'] = default_data.property_code
                    if default_data.chain_code and default_data.chain_code != '':
                        default_rule_filter['roomtype__hotel__chainname__id'] = int(default_data.chain_code)
                    if default_data.source_rateplancode and default_data.source_rateplancode != '':
                        default_rule_filter['source_rateplancode'] = default_data.source_rateplancode
                    if default_data.applicable_to and default_data.applicable_to != '':
                        source_config_id = config_data.get(default_data.applicable_to, None)
                        default_rule_filter['source_config'] = source_config_id
                    rp_list = RatePlan.objects.filter(**default_rule_filter)
                    celery_logger.info('%s\t%s\t%s\t%s\t%s' % (
                        'hotels', 'tasks', 'update_default_data_at_rateplan_level',
                        rule_code, len(rp_list)))
                    for rp in rp_list:
                        data_source_config_name = (rp.source_config and rp.source_config.name) or 'ingo'
                        if rateplan_associated_with_non_ingo_partners(data_source_config_name,
                                                                      rp.roomtype.hotel.hotelcode):
                            # Call update data in mongo as ingo data
                            push_rateplan_default_data_to_mongo_non_ingo_partner(rp, default_data.rule_code,
                                                                                 request_data=request_data)
                        else:
                            parse_rateplan_default_data_to_rateplan_obj(rp, default_data.rule_code, request_data)

    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'update_default_data_at_rateplan_level',
            rule_code, repr(e)))


@app.task(name="ingoibibo.optimize_slot_room_inventory")
def optimize_slot_room_inventory(hotelcode, checkin_date, roomtypecode, trigger_booking_id, trigger_reason):
    log_identifier = {}
    update_specific_identifier('booking_id', trigger_booking_id, log_identifier)
    try:
        log_msg = "Received slot room optimization celery task for booking ID {bid} reason {tr}".format(
            bid=trigger_booking_id, tr=trigger_reason)
        update_specific_identifier("remark", log_msg, log_identifier)
        inventory_logger.info(log_type="ingoibibo",
                              bucket="SlotRoomOptimization", stage="optimize_slot_room_inventory",
                              identifier="{}".format(log_identifier))
        optimize_dayuse_inventory(hotelcode, checkin_date, roomtypecode, trigger_booking_id, trigger_reason)

    except Exception as e:
        update_error_identifier(error_message=str(e), traceback=repr(traceback.format_exc()),
                                log_identifier=log_identifier)
        inventory_logger.critical(log_type="ingoibibo", bucket="SlotRoomOptimization",
            stage="optimize_slot_room_inventory", identifier="{}".format(log_identifier))


@app.task(name="ingoibibo.update_inventory_after_first_checkin_last_checkout_change")
def update_inventory_after_first_checkin_last_checkout_change(request_body):
    try:
        response = inv_client.update_inventory(request_body)
        inventory_logger.info("GRPC response {r} for request body {rb}".format(r=response, rb=request_body),
                              log_type="ingoibibo", bucket="RoomCheckinCheckoutChange",
                              stage="update_inventory_after_first_checkin_last_checkout_change")

    except Exception as e:
        inventory_logger.critical("Exception executing update inventory after first checkin last checkout change for "
                                  "request body {rb}".format(rb=request_body), log_type="ingoibibo",
                                  bucket="RoomCheckinCheckoutChange",
                                  stage="update_inventory_after_first_checkin_last_checkout_change")


@app.task(name="ingoibibo.push_hotel_image_to_gmp_task")
def push_hotel_image_to_gmp_task(image_object_id):
    try:
        from common.models import Image
        image_object = Image.objects.get(id=image_object_id)
        celery_logger.info(
            '%s\t%s\t%s\t%s\t' % ('hotels', 'tasks', 'push_hotel_image_to_gmp_task', str(image_object_id)))
        from hotels.push_moderation_data_to_kafka import push_hotel_image_to_gmp_moderation
        push_hotel_image_to_gmp_moderation('image_moderation', image_object)

    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'push_hotel_image_to_gmp_task',
            str(image_object_id), repr(e), repr(traceback.format_exc())))


@app.task(name="ingoibibo.set_upgradation_flag")
def set_upgradation_flag(hotel_id=None, Caller=""):
    status, message = False, ''
    try:
        if hotel_id:
            hotel = HotelDetail.objects.get(id=hotel_id)
            from scripts.hostapp_migration.post_migrate_flags import set_upgrade_flag
            set_upgrade_flag(hotel)
            celery_logger.info('Caller: %s\t%s\thotelcode: %s Function: %s' % (
            'set_upgradation_flag', 'success', str(hotel.hotelcode), Caller))
        else:
            celery_logger.info(
                'Caller: %s\t%s\tMissing hotel id!!!' % ('set_upgradation_flag', 'failure'))
    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s\t%s\thotel_id: %s' % (
            'hotels', 'tasks', 'set_upgradation_flag', '', str(e), repr(traceback.format_exc()), str(hotel_id)))

@app.task(name="ingoibibo.set_linked_rates")
def set_linked_rates(linked_rp):
    from hotels.hotelrates import set_partitioned_linked_rates_from_parent_obj
    try:
        celery_logger.info('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'set_linked_rates',
            linked_rp.rateplancode, 'Update linked rates'))
        set_partitioned_linked_rates_from_parent_obj(linked_rp)

    except Exception as e:
        celery_logger.critical('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'tasks', 'set_linked_rates',
            linked_rp.rateplancode, repr(e)))


@app.task(name="ingoibibo.deactivate_staff_users_task")
def deactivate_staff_users_task():
    from hotels.helper import deactivate_or_activate_staff_users
    from hotels.helper import send_email_for_user_deactivated
    from api.v2.users.resources.constants import UserManagementConstants
    try:
        fromdate = datetime.date.today() - datetime.timedelta(days=UserManagementConstants.INACTIVE_USER_DEACTIVATE_DAYS)
        email_id_list = list(User.objects.using('report-slave').filter(last_login__lte=fromdate, is_active=1, is_staff=1,
                                                                  email__isnull=False).exclude(email__exact='').values_list('email', flat=True))
        inventory_logger.info("Deactivating user ids and sending email to {} for inactivity in last 30 days".
                              format(email_id_list), log_type="ingoibibo", bucket="deactivate_staff_users_task",
                              stage="deactivate_staff_users_task")
        deactivate_or_activate_staff_users(email_list=email_id_list)
        send_email_for_user_deactivated(email_id_list)
    except Exception as e:
        inventory_logger.critical("Error while deactivating staff users: {}".format(repr(e)), log_type="ingoibibo",
                                  bucket="deactivate_staff_users_task", stage="deactivate_staff_users_task")


@app.task(name="ingoibibo.send_email_to_inactive_users_task")
def send_email_to_inactive_users_task():
    from hotels.helper import send_reminder_mail_for_user_deactivation
    from api.v2.users.resources.constants import UserManagementConstants
    import datetime
    try:
        # Calculate the date 23 days ago from the current date
        fromdate = datetime.date.today() - datetime.timedelta(
            days=UserManagementConstants.INACTIVE_USER_DEACTIVATE_DAYS-UserManagementConstants.INACTIVE_USER_DEACTIVATE_ALERT_DAYS)
        # Query the User model to get inactive staff users
        # CHECKUSERCHANGE - DONE
        inactive_users_encrypted_email_ids = User.objects.using('report-slave').filter(last_login__lte=fromdate, is_active=1, is_staff=1, email__isnull=False).exclude(email__exact='').values_list('email', 'encryptionDetails')
        inactive_users_email_ids = []
        for encrypted_email, encryption_details in inactive_users_encrypted_email_ids:
            if encryption_details:
                inactive_users_email_ids.append(decrypt_column_data(encrypted_email, 'email', encryption_details))
            else:
                inactive_users_email_ids.append(encrypted_email)
        # CHECKUSERCHANGE - DONE
        email_id_list_encrypted = User.objects.filter(email__in=inactive_users_email_ids).exclude(username__in=UserManagementConstants.DEACTIVATION_EXCLUDE_LIST).values_list('email', 'encryptionDetails')
        email_id_list = []
        for encrypted_email, encryption_details in email_id_list_encrypted:
            if encryption_details:
                email_id_list.append(decrypt_column_data(encrypted_email, 'email', encryption_details))
            else:
                email_id_list.append(encrypted_email)
        inventory_logger.info("Fetched Email Ids of inactive staff users for the last 23 days: {}".
                              format(email_id_list), log_type="ingoibibo", bucket="send_email_to_inactive_users_task",
                              stage="send_email_to_inactive_users_task")
        send_reminder_mail_for_user_deactivation(email_id_list)
    except Exception as e:
        inventory_logger.critical("Exception while sending mail to inactive staff users {}".format(repr(e)),
                                  log_type="ingoibibo", bucket="send_email_to_inactive_users_task",
                                  stage="send_email_to_inactive_users_task")



def common_push_message_to_ap_kafka(cache_key, topic, method_name, method_type):
    try:
        celery_logger.info("%s\t%s\t%s\t%s" % ("hotels", "tasks", method_name,
                                               "AP MESSAGE PUSH STARTING"))
        from hotel_store.tasks import update_hotelstore_object
        redis_conn = update_hotelstore_object.ap_r_cli
        items = redis_fetch_all_keys(redis_conn, cache_key)

        dayuse_cache_topic = settings.AP_TASK_QUEUE_CONFIG['KAFKA_TOPIC_DAYUSE_CACHE_EVENT']
        fullday_cache_topic = settings.AP_TASK_QUEUE_CONFIG['KAFKA_TOPIC_FULLDAY_CACHE_EVENT']
        soldout_cache_topic = settings.AP_TASK_QUEUE_CONFIG['KAFKA_TOPIC_SOLDOUT_CACHE_EVENT']

        dayuse_items, fullday_items, hoteltravel_items, soldout_dayuse_items, soldout_fullday_items, soldout_hoteltravel_items = segregate_items_for_gds_cache_updates(items)

        # TODO Change this topic after new consumers are created for this at GDS as per priority and normal logic
        #  1. for non soldout dayuse
        merge_items_and_push_to_topic(PLATFORM_DAYUSE, dayuse_items, dayuse_cache_topic)

        # 2. For non soldout fullday
        merge_items_and_push_to_topic("fullday", fullday_items, fullday_cache_topic)

        # 3. For non soldout hoteltravel
        merge_items_and_push_to_topic("hoteltravel", hoteltravel_items, fullday_cache_topic)

        # 4. for soldout dayuse
        merge_items_and_push_to_topic("soldout_dayuse", soldout_dayuse_items, dayuse_cache_topic)

        # 5. For soldout fullday
        merge_items_and_push_to_topic("soldout_fullday", soldout_fullday_items, soldout_cache_topic)

        # 6. For soldout hoteltravel
        merge_items_and_push_to_topic("soldout_hoteltravel", soldout_hoteltravel_items, soldout_cache_topic)

    except Exception as e:
        celery_logger.critical("%s\t%s\t%s\t%s\t%s" % (
            "hotels", "tasks", "common_push_message_to_ap_kafka", str(e), repr(traceback.format_exc()),))

@app.task(name="ingoibibo.create_manager_mapping_entry")
def create_manager_mapping_entry(csv_data, user, last_row=False):
    from common.services import ManagerMappingWrapper
    try:
        ManagerMappingWrapper.create_manager_mapping(csv_data, user)
        if last_row:
            ManagerMappingWrapper.update_cache()
            body = 'Manager Mapping completed. Please check on admin'
            sendMail(user.email, from_email=settings.EMAIL_ALERT_SENDER, body=body, subject='Manager Mapping Completed',
                     template_id='',
                     cc_emails=[user.email], attached_file=None, bcc_emails=[])
    except Exception as e:
        inventory_logger.critical('Manager mapping failed: {}'.format(repr(traceback.format_exc())),
                                  bucket='ManagerMapping', stage='create_manager_mapping_entry')
        body = 'Manager Mapping failed for following row: {} with exception: {}'.format(csv_data, str(e))
        sendMail(user.email, from_email=settings.EMAIL_ALERT_SENDER, body=body, subject='Manager Mapping Failed',
                 template_id='',
                 cc_emails=[user.email], attached_file=None, bcc_emails=[])

@app.task(name="ingoibibo.create_update_manager_mapping")
def create_update_manager_mapping(csv_data, user, prod=True):
    csv_reader = csv.DictReader(csv_data)
    csv_list_data = list(csv_reader)
    index = 0
    limit = len(csv_list_data) - 1
    for csv_row in csv_list_data:
        if prod:
            create_manager_mapping_entry.apply_async(args=(csv_row, user, index == limit))
        else:
            create_manager_mapping_entry(csv_row, user, index == limit)
        index+=1


@app.task(name='ingoibibo.daily_scalyr_reports')
def send_daily_scalyr_reports():
    try:
        from django.conf import settings
        from django.template import Template
        import datetime
        from common.constants import CORPORATE_GSTN_REPORTING_INTERNAL_MAIL_IDS, \
            CORPORATE_GSTN_REPORTING_INTERNAL_MAIL_IDS_CC
        from communication.common_comms.iris_notify import Notify

        for report_name, report_details in SCALYR_REPORTS.iteritems():

            template_file = report_details['template']
            function = report_details['function']
            to_emails = report_details['to_emails']
            cc_emails = report_details['cc_emails']
            subject = report_details['subject']

            if report_name == BOOKING_REPORT_KEY:
                yesterday_date = (datetime.datetime.today().date() - datetime.timedelta(days=1))
                formatted_date = str(yesterday_date.day) + get_day_suffix(
                    yesterday_date.day) + " " + yesterday_date.strftime("%b")
                subject = subject.format(formatted_date)

            with open(template_file, 'r') as f:
                template_str = f.read()

            template = Template(template_str)

            context, attachments = function(report_details)

            email_body = template.render(context)
            notify = Notify()
            notify.sendEmail(to_emails=to_emails,
                             from_email=settings.EMAIL_ALERT_SENDER,
                             body=email_body,
                             subject=subject,
                             cc_emails=cc_emails,
                             attachments=attachments,
                             bcc_emails=[])
    except Exception as e:
        api_logger.critical("Exception occurred - {}, traceback - {}".format(repr(e), repr(traceback.format_exc())),
                            log_type='ingoibibo',
                            bucket='gstn_invoice', stage='send_daily_alert_task')

@app.task(name="ingoibibo.update_social_media_status")
def update_social_media_status(csv_file, user):
    try:
        csv_reader = csv.DictReader(csv_file)
        csv_list_data = list(csv_reader)
        update_social_media_in_batches.apply_async(args=(csv_list_data, ))
        body = "Social media status has been updated for the provided account ids"
        sendMail(user.email, from_email=settings.EMAIL_ALERT_SENDER, body=body, subject="Social media status update", template_id='',
                 cc_emails=[user.email], attached_file=None, bcc_emails=[])
    except Exception as ex:
        api_logger.critical(message="update_social_media_status exception : {}".format(repr(traceback.format_exc())))
        body = "Error in updating social media status. Please check if the uploaded data is valid."
        sendMail(user.email, from_email=settings.EMAIL_ALERT_SENDER, body=body, subject="Social media status update",
                 template_id='',
                 cc_emails=[user.email], attached_file=None, bcc_emails=[])


@app.task(name="ingoibibo.update_social_media_in_batches")
def update_social_media_in_batches(csv_list_data, index = 0):
    import grpc
    from social_media_service.grpc_social_media_client import SocialMediaClient
    social_media_grpc_client = SocialMediaClient()
    batch_size = 20
    batch_number = 0

    try:
        platform = csv_list_data[0].get('platform', "HOTEL_INSTAGRAM")
        account_type = csv_list_data[0].get('account_type', "HOTEL")
        action = csv_list_data[0].get('action', None)
        if action is None:
            raise Exception('Action not provided.')
        request = {'platform': platform, 'account_type': account_type, "action": action}
        while index < len(csv_list_data) - 1:
            last_index = index + batch_size
            batch = csv_list_data[index:last_index]
            account_id_list = []
            for row in batch:
                mmt_id = HotelDetail.objects.only('mmt_id').filter(hotelcode=row['hotelcode']).first().mmt_id
                account_id_list.append(str(mmt_id))
            request['account_id'] = account_id_list
            resp = social_media_grpc_client.social_media_account_update(request)
            inventory_logger.info(message="batch number: %s, response: %s" % (str(batch_number), repr(resp)),
                log_type='ingoibibo',
                bucket='social_media_bulk_upload', stage='update_social_media_in_batches')
            index = last_index
            batch_number += 1
        if index == len(csv_list_data) - 1:
           mmt_id = HotelDetail.objects.only('mmt_id').filter(hotelcode=csv_list_data[index].get('hotelcode')).first().mmt_id
           request['account_id'] = [str(mmt_id)]
           resp = social_media_grpc_client.social_media_account_update(request)
           inventory_logger.info(message="batch number: %s, response: %s" % (str(batch_number), repr(resp)),log_type='ingoibibo',
                                                            bucket='social_media_bulk_upload', stage='update_social_media_in_batches')


    except Exception as ex:
        inventory_logger.critical(message="update_social_media_in_batches exception: {}, traceback: {}".format(str(ex), repr(traceback.format_exc())))


@app.task(name='ingoibibo.reorder_city_services')
def reorder_city_services(locus_code, leaf_category_id, old_priority, new_priority, created):
    if old_priority == new_priority:
        ServicePriorityService.mark_reorder_complete(locus_code, leaf_category_id)
        return

    rateplan_ids = RatePlan.objects.filter(roomtype__hotel__city_id__locus_code=locus_code).values_list('id', flat=True)
    rateplan_ids_chunks = get_chunk_list(rateplan_ids)  # default size is 100
    count = 0
    for rateplan_ids_chunk in rateplan_ids_chunks:
        count += 1
        reorder_city_rateplan_services_chunk.apply_async(
            args=(locus_code, leaf_category_id, rateplan_ids_chunk, old_priority, new_priority, created,
                  count == len(rateplan_ids_chunks)))


@app.task(name='ingoibibo.reorder_city_rateplan_services_chunk')
def reorder_city_rateplan_services_chunk(locus_code, leaf_category_id, rateplan_ids, old_priority, new_priority,
                                         created=False, last_set=False):
    rateplan_content_type_id = ContentType.objects.get_for_model(RatePlan).id
    count = 0
    for rateplan_id in rateplan_ids:
        count += 1
        reorder_rateplan_services.apply_async(
            args=(
            locus_code, leaf_category_id, rateplan_id, rateplan_content_type_id, old_priority, new_priority, created,
            last_set and count == len(rateplan_ids)))


@app.task(name='ingoibibo.reorder_rateplan_services')
def reorder_rateplan_services(locus_code, leaf_category_id, rateplan_id, rateplan_content_type_id, old_priority,
                              new_priority, created=False,
                              last_item=False):

    if new_priority > old_priority:
        start_priority = old_priority + 1
        end_priority = new_priority
        delta = -1
    else:
        start_priority = new_priority
        end_priority = old_priority - 1
        delta = 1
    start_priority = start_priority * LEAF_PRIORITY_MULTIPLIER
    end_priority = end_priority * LEAF_PRIORITY_MULTIPLIER
    delta = delta * LEAF_PRIORITY_MULTIPLIER
    Services.objects.filter(content_type_id=rateplan_content_type_id,
                            object_id=rateplan_id, priority__gte=start_priority, priority__lte=end_priority,
                            status=1).update(priority=F('priority') + delta)
    if not created:
        first_priority = (old_priority * LEAF_PRIORITY_MULTIPLIER) + 1
        last_priority = (old_priority + 1) * LEAF_PRIORITY_MULTIPLIER
        Services.objects.filter(content_type_id=rateplan_content_type_id, object_id=rateplan_id,
                                leaf_category_id=leaf_category_id, status=1, priority__gte=first_priority,
                                priority__lte=last_priority).update(priority=((F('priority') % LEAF_PRIORITY_MULTIPLIER)+(new_priority*LEAF_PRIORITY_MULTIPLIER)))
    else:
        Services.objects.filter(content_type_id=rateplan_content_type_id, object_id=rateplan_id,
                                leaf_category_id=leaf_category_id, status=1).update(priority=((F('priority') % LEAF_PRIORITY_MULTIPLIER)+(new_priority*LEAF_PRIORITY_MULTIPLIER)))
    if last_item:
        ServicePriorityService.mark_reorder_complete(locus_code, leaf_category_id)

@app.task(name="ingoibibo.inclusions_bulk_upload")
def inclusions_bulk_upload(csv_file, user):
    csv_reader = csv.reader(csv_file)
    csv_list_data = [row for row in csv_reader]

    try:
        if len(csv_list_data) > 10000:
            raise Exception("Enter csv with lesser than 10000 rows")
        status, msg = inclusions_bulk_upload_helper(csv_list_data, user)
        if not status:
            raise Exception(msg)
        body = "Inclusions bulk uploader has been run successfully. " + str(msg)
        sendMail(user.email, from_email=settings.EMAIL_ALERT_SENDER, body=body, subject="Inclusions bulk uploader status - Passed",
                 template_id='', cc_emails=[], attached_file=None, bcc_emails=[])
    except Exception as ex:
        api_logger.critical(message="inclusions_bulk_upload exception : {}, exception: {}".format(repr(traceback.format_exc()), str(ex)))
        body = "Error in creating/updating inclusions. Please check if the uploaded data is valid. Error: %s" % (str(ex))
        sendMail(user.email, from_email=settings.EMAIL_ALERT_SENDER, body=body, subject="Inclusions bulk uploader status - Failed",
                 template_id='', cc_emails=[], attached_file=None, bcc_emails=[])

def attribute_name_exists_in_leaf_category_attributes(attribute_name_to_check, leaf_category_attributes):
    import re
    for attribute in leaf_category_attributes:
        attribute_name = re.sub(r'[^A-Za-z0-9]', '', attribute.get('label')).lower()
        attribute_name_to_check = re.sub(r'[^A-Za-z0-9]', '', attribute_name_to_check).lower()
        if attribute_name == attribute_name_to_check:
            return int(attribute.get('attribute_id')), str(attribute.get('label')), str(attribute.get('type'))

    return None, None, None

def get_valid_attribute_details(csv_data, csv_row_len, leaf_category_attributes): # returns dict of format {attribute id: attribute name}
    valid_attributes = [] # list of tuple of attribute_id and attribute_name

    for index in range(5, csv_row_len): # attribute names start from the 5th index
        attribute_id, attribute_name, attribute_type = attribute_name_exists_in_leaf_category_attributes(csv_data[0][index], leaf_category_attributes)
        if attribute_id:
            valid_attributes.append((attribute_id, attribute_name, attribute_type, int(index))) # return attribute id and its corresponding column index in the csv data
    return valid_attributes

def handle_attribute_value_null(attribute_type):
    if attribute_type in ['multi_select', 'date_range']:
        return []
    else:
        return None

def parse_attribute_value(attribute_value, attribute_type, attribute_name):
    from hotels.hotelchoice import SERVICES_ATTRIBUTE_DATATYPE
    attribute_integer_types = ['single_select', 'boolean_select', 'number', 'number_select', 'number_range', 'sub_number']
    attribute_string_types = ['text', 'text_area', 'date', 'time_select', 'duration_select']
    attribute_list_int_types = ['multi_select']
    attribute_list_string_types = ['date_range']

    if attribute_value.lower() in ('na', 'null', 'none'):
        return handle_attribute_value_null(attribute_type)
    else:
        if attribute_type in attribute_integer_types:
            return int(attribute_value)
        elif attribute_type in attribute_string_types:
            if attribute_type == "date": # parse date (received as dd/mm/yy, change to yyyy-mm-dd)
                return str(attribute_value)
            return str(attribute_value)
        elif attribute_type in attribute_list_int_types:
            attribute_value_list = [int(num) for num in attribute_value.split(',')] # convert to list of int
            return attribute_value_list
        else:
            dates = [str(date).strip() for date in attribute_value.split(',')]
            if len(dates) % 2 != 0:
                raise Exception("Column %s must have an even number of dates" % str(attribute_name))
            attribute_value_list = [dates[i:i+2] for i in range(0, len(dates), 2)]
            return attribute_value_list

def get_service_attribute_value(attribute_id, attribute_name, attribute_type, service_selected_attributes, leaf_category_attributes):
    from datetime import date
    attribute_value = service_selected_attributes.get(str(attribute_id), None)
    if attribute_value is not None:
        if isinstance(attribute_value, date):
            return attribute_value.strftime('%Y-%m-%d')
        elif attribute_type == 'date_range':
            if isinstance(attribute_value, list):
                for i in range(len(attribute_value)):
                    if isinstance(attribute_value[i], list):
                        attribute_value[i] = [date.strftime('%Y-%m-%d') for date in attribute_value[i]]
                    else:
                        attribute_value[i] = attribute_value[i].strftime('%Y-%m-%d')

        return attribute_value
    else:
        _, _, attribute_type = attribute_name_exists_in_leaf_category_attributes(attribute_name, leaf_category_attributes)
        return handle_attribute_value_null(attribute_type)

def get_csv_row_len(csv_list_data):
    length = 0
    for i in range (0, len(csv_list_data[0])):
        if csv_list_data[0][i].strip() == "":
            return length
        length += 1
    return length

def create_selected_attributes(csv_data, leaf_category_id, row, action, service_obj):
    from hotels.models.basic_models import ServicesTemplate
    leaf_category = ServicesTemplate.objects.get(id=int(leaf_category_id))

    # call a function which finds all the possible columns for the given leaf category id
    csv_row_len = get_csv_row_len(csv_data)
    valid_attribute_details = get_valid_attribute_details(csv_data, csv_row_len, leaf_category.attributes)
    selected_attributes = {}

    if valid_attribute_details:
        try:
            for attribute_detail in valid_attribute_details:
                attribute_id, attribute_name, attribute_type, attribute_column_index = attribute_detail
                attribute_value = csv_data[row][attribute_column_index]
                if attribute_value.strip() != "-": # if user does not want to update a column (and hence passes '-' as attribute value), we will skip it. Else, the attribute value will be updated as None.(or the equivalent None type)
                    selected_attributes[str(attribute_id)] = parse_attribute_value(csv_data[row][attribute_column_index], attribute_type, attribute_name)
                else:
                    if action == "update":
                        selected_attributes[str(attribute_id)] = get_service_attribute_value(attribute_id, attribute_name, attribute_type, service_obj.selected_attributes, leaf_category.attributes)
        except Exception as ex:
            raise Exception("%s" % str(ex))
    else:
        raise Exception('No valid columns for leaf category id: %s' % leaf_category_id)

    return selected_attributes

def validate_inclusions_data(action, leaf_category_id, object_id, mmt_id, service_id=None):
    from hotels.models.basic_models import ServicesTemplate
    from hotels.models.rateplan import RatePlan
    from hotels.models.basic_models import Services
    if not action:
        raise Exception("Action not provided.")
    if action.lower() not in ("create", "update", "deactivate"):
        raise Exception("Invalid action")
    if not leaf_category_id:
        raise Exception("Leaf category id not provided.")
    if not ServicesTemplate.objects.filter(id=leaf_category_id).exists():
        raise Exception('leaf category id %s is invalid' % str(leaf_category_id))
    if not object_id:
        raise Exception('Rate plan code not provided.')
    if not RatePlan.objects.filter(rateplancode=object_id).exists():
        raise Exception('Rate plan code is invalid.')
    if action in ("create", "update") and not mmt_id:
        raise Exception('MMT ID not provided.')
    if mmt_id and not HotelDetail.objects.filter(mmt_id=mmt_id).exists():
        raise Exception('mmt_id %s is invalid' % str(mmt_id))
    if action.strip().lower() in ("update", "deactivate") and not service_id:
        raise Exception("Service id not provided for %s action" % str(action.strip()))
    if service_id:
        if not Services.objects.filter(id=service_id).exists():
            raise Exception('service id %s is invalid' % str(mmt_id))
    return True


def inclusions_bulk_upload_helper(csv_list_data, user):
    from api.v2.services.resources.resource import is_service_possible
    from hotels.models.basic_models import Services
    from api.v2.services.resources.resource import organize_request_data, create_service, update_service
    from hotels.services.helpers.priority_service import ServicePriorityService
    from api.v2.services.resources.resource import is_valid_update_for_service_multivendor
    from hotels.models.source_config import SourceConfig
    from hotels.models.basic_models import ServicesTemplate
    from hotels.hotelchoice import SERVICE_CODE
    from hotels.models.helper import get_id_from_code
    import ast

    rows = 0
    rows_failed = 0
    errors = ""
    for data_row in csv_list_data[1:]: # 0th rows will have headers, hence starting from 1st
        try:
            rows += 1
            service_id = data_row[1].strip()
            if service_id.strip() == "-":
                service_id = None
            action = data_row[4].strip()
            leaf_category_id = data_row[2].strip()
            object_id = data_row[3].strip()
            mmt_id = data_row[0].strip()

            if mmt_id.strip() == "": # Skipping row where mmt id is not given
                continue

            try:
                validate_inclusions_data(action, leaf_category_id, object_id, mmt_id, service_id)
                hotelcode, hotel_id = HotelDetail.objects.values_list('hotelcode', 'id').get(mmt_id=mmt_id)
                leaf_category_id = int(leaf_category_id)
                object_id = int(object_id)
                rateplan_obj = RatePlan.objects.get(rateplancode=object_id)
                service_obj = None

                if action.lower() in ("update", "deactivate"):
                    service_obj = Services.objects.get(id=service_id)
                if action == "create":
                    source_config = "ingo"  # default is ingo
                    selected_attributes_dict = create_selected_attributes(csv_list_data, leaf_category_id, rows, action,
                                                                          service_obj)
                    if rateplan_obj.source_config:
                        source_config = rateplan_obj.source_config.name
                    hobj = HotelDetail.objects.get(hotelcode=hotelcode)

                    # constructing request data to pass to organize
                    requestdata = {'status': 1, 'hotel': hobj, 'hotel_code': hotelcode,
                                   'leaf_category': leaf_category_id, 'selected_attributes': selected_attributes_dict,
                                   'source_config': source_config, 'object_ids': [int(rateplan_obj.id)],
                                   'service_type': SERVICE_CODE.get('inclusion'), 'modified_by': user.id}

                    # preprocessing (organizing) request data
                    request_data = organize_request_data(requestdata)
                    sources_dict = get_config_data(value_key=True)
                    if request_data.get("source_config") and sources_dict.get(request_data["source_config"]):
                        request_data["source_config_id"] = sources_dict.get(request_data["source_config"],
                                                                            sources_dict['ingo'])
                        request_data.pop("source_config")

                    # checking if inclusion is valid
                    try:
                        is_service_possible(request_data)
                    except Exception as ex:
                        msg = "Not possible to create service of rate plan code: %s, exception %s" % (
                        str(object_id), str(ex))
                        api_logger.error(message=msg, bucket="bulk_uploader",
                                            stage="tasks.inclusions_bulk_upload_helper")
                        raise Exception(msg)

                    # creating service
                    create_service(request_data, possibility_check_done=True)
                    LogEntry.objects.log_action(user_id=user.id,
                                                content_type_id=int(ContentType.objects.get_for_model(Services).id),
                                                object_id= hotel_id,
                                                object_repr='Inclusions creation - bulk uploader',
                                                action_flag=ADDITION,
                                                change_message="Creating inclusion of leaf category id {} for rateplancode {}".format(str(leaf_category_id),
                                                                                                                                      str(object_id)))


                elif action == "update":
                    selected_attributes_dict = create_selected_attributes(csv_list_data, leaf_category_id, rows, action,
                                                                          service_obj)
                    if not service_obj:
                        msg = 'Service does not exist for given leaf category id and rate plan code'
                        api_logger.error(message=msg, bucket="bulk_uploader",
                                            stage="tasks.inclusions_bulk_upload_helper")
                        raise Exception(msg)
                    hobj = HotelDetail.objects.get(hotelcode=hotelcode)
                    requestdata = {'status': 1, 'hotel': hobj, 'object_ids': [int(rateplan_obj.id)],
                                   'selected_attributes': selected_attributes_dict, 'id': service_obj.id}
                    source_config_name = "ingo"
                    if service_obj.source_config:
                        source_config_name = service_obj.source_config.name
                    if is_valid_update_for_service_multivendor(source_config_name, service_obj.id):
                        request_data = organize_request_data(requestdata, service_obj)
                        payload = copy.deepcopy(request_data)
                        update_service(service_obj, request_data, payload)
                    else:
                        msg = 'Update of service corresponding to leaf_category_id %s and rateplancode %s skipped because of multi vendor logic' % (str(leaf_category_id), str(object_id))
                        api_logger.error(message=msg, bucket="bulk_uploader",
                                            stage="tasks.inclusions_bulk_upload_helper")
                        raise Exception(msg)

                    msg = "Service object of id %s updated successfully" % (str(service_obj.id))
                    api_logger.info(message=msg, bucket="bulk_uploader",
                                    stage="tasks.inclusions_bulk_upload_helper")
                    LogEntry.objects.log_action(user_id=user.id,
                                                content_type_id=int(ContentType.objects.get_for_model(Services).id),
                                                object_id= hotel_id,
                                                object_repr='Inclusions updation - bulk uploader',
                                                action_flag=CHANGE,
                                                change_message="Updating inclusion of service_id {}".format(str(object_id)))

                elif action == "deactivate":
                    content_type_obj = ContentType.objects.get_for_model(RatePlan)
                    content_type_id = content_type_obj.id
                    rateplan_id = get_id_from_code(object_id, RatePlanCodeLength, RatePlanCodePrefix)
                    msg = ("Services param list for rateplan code %s and generated rateplan_id %s and "
                           "content_type_id %s updated successfully") % (str(object_id), rateplan_id, content_type_id)
                    api_logger.info(message=msg, bucket="bulk_uploader",
                                    stage="tasks.inclusions_bulk_upload_helper")
                    if not service_obj:
                        msg = 'Inclusion does not exist for given service id'
                        raise Exception(msg)
                    service_obj.status = 3
                    service_obj.save()

                else:
                    raise Exception("Invalid action.")

            except Exception as e:
                msg = "Row: %s, error: %s" % (str(rows), str(e.message))
                errors += "%s;\n" % msg
                api_logger.error(message=msg,
                                    bucket="bulk_uploader",
                                    stage="bulk_uploader.external_function.inclusions_bulk_upload_helper")
                api_logger.info("traceback: %s" % repr(traceback.format_exc()))
                rows_failed += 1
        except Exception as ex:
            msg = "Error occured in running inclusions bulk uploader: %s" % str(ex)
            api_logger.error(message=msg,
                                bucket="bulk_uploader",
                                stage="bulk_uploader.external_function.inclusions_bulk_upload_helper")
            return False, msg
    rows_passed = rows - rows_failed
    sendMail(user.email, from_email=settings.EMAIL_ALERT_SENDER, body=errors,
             subject="Inclusions bulk uploader - errors",
             template_id='', cc_emails=[], attached_file=None, bcc_emails=[])
    return True, "Services updated successfully. %s row(s) passed, %s row(s) failed" % (str(rows_passed), str(rows_failed))

@app.task(name="ingoibibo.update_rtb_and_waiver_data")
def update_rtb_and_waiver_data(confirmbookingid):
    from hotels.hotelcancellation import update_rtb_obj_on_cancellation, update_booking_flag_on_cancellation
    try:
        bobj = HotelBooking.objects.using('default').get(confirmbookingid=confirmbookingid)
        misc_data = json.loads(bobj.misc)
        data_update_eligible = True if settings.HOST not in settings.PROD_HOSTS else bobj.confirmstatus not in [
                               "pending", "confirmed"]
        if data_update_eligible:
            if misc_data.get('is_rtb', False):
                update_rtb_obj_on_cancellation(confirmbookingid)
            if "waiver_status" in misc_data:
                update_booking_flag_on_cancellation(confirmbookingid, misc_data, bobj.hotel.id)
    except Exception as e:
        api_logger.critical(message="update_rtb_and_waiver_data task failed exception : {}, exception: {}".format(repr(traceback.format_exc()),
                                                                                  str(e)))

@app.task(name="ingoibibo.deactivate_spa_inclusions_for_inactive_spa_amenity")
def deactivate_spa_inclusions_for_inactive_spa_amenity(mmt_id, correlation_key, user_id):
    if not mmt_id:
        inventory_logger.info(message='No MMT ID provided',bucket='hotels.tasks', stage='deactivate_spa_inclusions_for_inactive_spa_amenity')
        return
    try:
        deactivate_spa_inclusions_for_inactive_amenity(mmt_id, correlation_key, user_id)
    except Exception as e:
        inventory_logger.critical(message='Deactivate spa inclusions for inactive spa amenity failed, error: {0} - {1}'.format(
            e, traceback.format_exc()),bucket='hotels.tasks', stage='deactivate_spa_inclusions_for_inactive_spa_amenity')



@app.task(name="ingoibibo.push_policy_mapping_update")
def push_policy_mapping_update(hotel_code, category_id):
    import json
    import logging
    from django.conf import settings

    try:
        # Get Kafka server config for MMT
        from confluent_kafka import Producer
        mmt_kafka_server = settings.KAFKA_SERVER_CONF["servers"]["mmt"]["HOST"][0]

        producer = Producer({
            'bootstrap.servers': mmt_kafka_server,
            'acks': '1'  # Wait for leader acknowledgment
        })

        message = {
            "ingo_hotel_code": hotel_code,
            "policy_category_id": category_id,
        }

        topic = "ingo_policy_mapping_updates"

        producer.produce(
            topic,
            key=str(hotel_code),
            value=json.dumps(message)
        )

        # Wait for message to be delivered
        producer.flush()

        api_logger.info(
            message='Successfully pushed policy mapping update to Kafkadynamics for hotel_code: {}, category_id: {}'.format(hotel_code, category_id),
            log_type='ingoibibo',
            bucket='push_policy_mapping_update',
            stage='hotels.tasks',
        )
        return True

    except Exception as e:
        api_logger.critical(
            message='Failed to push policy mapping update to Kafkadynamics for hotel_code: {}, category_id: {}. Exception: ({}) and traceback: {}'.format(
                hotel_code, category_id, str(e), traceback.format_exc()
            ),
            log_type='ingoibibo',
            bucket='push_policy_mapping_update',
            stage='hotels.tasks',
        )
        return False


