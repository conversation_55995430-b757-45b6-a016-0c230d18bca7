import json
import traceback

from django.conf import settings
from django.contrib.contenttypes.models import ContentType

from goibibo_inventory.settings import app
from kafka_helper.push_msg_to_kafka import Producer
from utils.logger import Logger

inventory_logger = Logger(logger='inventoryLogger')


def push_contact_details_to_kafka(key, kafka_packet):
    try:
        config = settings.SANDESH_KAFKA_SERVER_CONF['servers']['generic_contact_detail_data_push']
        topic = config['TOPIC']
        p = Producer(config['HOST'])
        kafka_meta_data = p.send_msg(topic=topic, key=key, value=json.dumps(kafka_packet))
        return kafka_meta_data
    except Exception as e:
        inventory_logger.critical(
            message='{0} Exception while pushing object to moderation kafka, {1}, {2}'.
                format(key, repr(e), repr(traceback.format_exc())),
            log_type='ingoibibo', bucket='hotels.push_generic_contact_detail',
            stage='push_generic_contact_detail_to_kafka'
        )
        raise e


def get_generic_contact_details(contact_detail):
    gcds = contact_detail.generic_contacts.using('default').filter(is_active=True, verified_on__isnull=False).\
        prefetch_related('country').all()
    gcd_dict = {_.contact_type: _ for _ in gcds}

    from common.commonchoice import ContactTypes
    mobile = gcd_dict.get(ContactTypes.MOBILE)
    whatsapp = gcd_dict.get(ContactTypes.WHATSAPP)
    email = gcd_dict.get(ContactTypes.EMAIL)

    return mobile, whatsapp, email


def get_market_managers(hotel):
    from ingouser.models import User

    market_managers = ['contract_bdo', 'contract_manager', 'market_coordinator']
    data = {_: {'name': '', 'email': ''} for _ in market_managers}

    if hotel.contractbdo:
        # CHECKUSERCHANGE - DONE
        contract_bdo = User.objects.filter(username=hotel.contractbdo).first()
        if contract_bdo:
            data['contract_bdo']['name'] = contract_bdo.get_full_name()
            data['contract_bdo']['email'] = contract_bdo.email

    if hotel.contractmanager:
        # CHECKUSERCHANGE - DONE
        contract_manager = User.objects.filter(username=hotel.contractmanager).first()
        if contract_manager:
            data['contract_manager']['name'] = contract_manager.get_full_name()
            data['contract_manager']['email'] = contract_manager.email

    if hotel.market_coordinator:
        # CHECKUSERCHANGE - DONE
        market_coordinator = User.objects.filter(username=hotel.market_coordinator).first()
        if market_coordinator:
            data['market_coordinator']['name'] = market_coordinator.get_full_name()
            data['market_coordinator']['email'] = market_coordinator.email

    return data


def get_client_address(hotel):
    return {}


def get_hotel_related_details(contact_detail):
    hotel = contact_detail.hotel
    mobile, whatsapp, email = get_generic_contact_details(contact_detail)
    return {
        'first_name': contact_detail.name,
        'last_name': '',
        'mobile': mobile and '{0}{1}'.format(mobile.country.dialing_prefix, mobile.contact) or '',
        'whatsapp_no': whatsapp and '{0}{1}'.format(whatsapp.country.dialing_prefix, whatsapp.contact) or '',
        'primary_email': email and email.contact or '',
        'cns_user_type': 'hotel_contact',
        'market_managers': get_market_managers(hotel),
        # 'client_user_type': None,
        'is_primary': contact_detail.is_primary,
        'is_active': contact_detail.isactive,
        'is_communicable': True,
        'available_at_property': True,
        'client_id': contact_detail.id,
        # 'client_email': None,
        # 'host_source': None,
        'client_address': get_client_address(hotel)
    }


def get_caretaker_related_details(caretaker):
    hotel = caretaker.content_object
    mobile, whatsapp, email = get_generic_contact_details(caretaker)
    return {
        'first_name': caretaker.name,
        'last_name': '',
        'mobile': mobile and '{0}{1}'.format(mobile.country.dialing_prefix, mobile.contact) or '',
        'whatsapp_no': whatsapp and '{0}{1}'.format(whatsapp.country.dialing_prefix, whatsapp.contact) or '',
        'primary_email': email and email.contact or '',
        'cns_user_type': 'caretaker',
        'market_managers': get_market_managers(hotel),
        # 'client_user_type': None,
        'is_primary': False,
        'is_active': caretaker.is_active,
        'is_communicable': caretaker.is_communicable,
        'available_at_property': caretaker.is_fulltime,
        'client_id': caretaker.id,
        # 'client_email': None,
        # 'host_source': None,
        'client_address': get_client_address(hotel)
    }


@app.task(name="ingoibibo.push_generic_contact_detail_to_kafka")
def push_generic_contact_detail_to_kafka(gcd_id):
    try:
        inventory_logger.info(
            message='push generic contact detail static data, id: {0}'.format(gcd_id), log_type='ingoibibo',
            bucket='hotels.push_generic_contact_detail', stage='push_generic_contact_detail_to_kafka'
        )

        from hotels.models import GenericContactDetail
        gcd = GenericContactDetail.objects.using('default').get(id=gcd_id)

        from hotels.models import ContactDetail
        contact_detail_content_type = ContentType.objects.get_for_model(ContactDetail)

        from common.models import Caretaker
        caretaker_content_type = ContentType.objects.get_for_model(Caretaker)

        if gcd.content_type_id == contact_detail_content_type.id:
            packet_dict = get_hotel_related_details(gcd.content_object)
        elif gcd.content_type_id == caretaker_content_type.id:
            packet_dict = get_caretaker_related_details(gcd.content_object)
        else:
            # Either return, raise or log
            return

        kafka_meta_data = push_contact_details_to_kafka(str(gcd.object_id), packet_dict)

        if kafka_meta_data and kafka_meta_data.get("error_message", None):
            inventory_logger.critical(
                message='Error for push generic contact detail static data: id: {0}, meta_data: {1}, packet: {2}'.
                    format(gcd_id, kafka_meta_data, packet_dict),
                log_type='ingoibibo', bucket='hotels.push_generic_contact_detail',
                stage='push_generic_contact_detail_to_kafka'
            )
        else:
            inventory_logger.info(
                message='success for push generic contact detail static data, id: {0}, meta_data: {1}, packet: {2}'.
                    format(gcd_id, kafka_meta_data, packet_dict),
                log_type='ingoibibo', bucket='hotels.push_generic_contact_detail',
                stage='push_generic_contact_detail_to_kafka'
            )

    except Exception as e:
        inventory_logger.critical(
            message='Error for push generic contact detail static data: {0} {1} {2}'.
                format(gcd_id, repr(e), repr(traceback.format_exc())),
            log_type='ingoibibo', bucket='hotels.push_generic_contact_detail',
            stage='push_generic_contact_detail_to_kafka'
        )
