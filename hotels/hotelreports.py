import ast
import datetime #not to be removed!
import requests
import os

from django import db
from django.http import HttpResponse
from django.template.loader import render_to_string
from django.template import RequestContext
from django.shortcuts import render_to_response
from django.contrib.auth.decorators import login_required
from django.db.models import Count, Q
from django.db import models
from django.conf import settings
from django.contrib.admin.views.decorators import staff_member_required
from django.utils.safestring import mark_safe
from django.core.exceptions import PermissionDenied, ObjectDoesNotExist
from django.http import HttpResponseForbidden

from lib.aes_encryption.helpers import decrypt_column_data
from lib.custom_decorators import custom_required, token_required
from utils.libs import goibibo_smartjson as json
from utils.logger import Logger
import math
from collections import defaultdict
from operator import itemgetter
import logging, traceback
from hotels.hotelchoice import ADD_ON_BOOKING_PAY_MODE, HOTELS_ADD_ONS_TYPES
from communication.hotelMailsSMS import sendHotelBookingVoucher, sendPayAtHotelBookingVoucher, getConfirmBookinghashkey
from communication.common_comms.communications import sendMail
from hotels.models import *
from hotelrules import HotelPriceRules
from methods import HotelMethods, JSONDateTimeEncoder, is_international_hotel
from hotelmailmsgs import HotelAdminAlerts
from lib.num2word import num_to_words
from hotels.navision_payment import create_master_dict2
from lib.sanitize_request import sanitize_request
from hotels.pi_data import get_customer_gst

hm = HotelMethods()
hpr = HotelPriceRules()
hotelalerts = HotelAdminAlerts()
logger = logging.getLogger("inventoryLogger")
utils_logger = Logger("inventoryLogger")

fields_hotel = [('rooms', 'Rooms'), ('amenities', 'Amenities'), ('images', 'Images'),
                ('nearestattractions', 'Nearest Attractions'), ('img_sqr', 'Thumb Image'), ('img_rct', 'Main image'),
                ('latitude', 'Latitude'), ('longitude', 'Longitude'), ('hoteltype', 'Hotel Type'),
                ('starrating', 'Star Rating'), ('chainname', 'Chain Name'),
                 ('noofrooms', 'Total Rooms'), ('phonelist', 'Phone List'),
                ('emaillist', 'Email List'), ('websitelist', 'Website'), ('hotelpolicy', 'Hotel Policy'),
                ('stdcancelpolicy', 'Standard Cancellation Policy'), ('hotelnotice', 'Hotel Notice')]
fields_room = [('rateplans', 'RatePlans'), ('amenities', 'Room Amenities'), ('images', 'Images'), ('desc', 'Overview'),
               ('description', 'Description'), ('bedtype', 'Bed Type'), ('extra_bed', 'Extra Bed')]
fields_rateplan = [('mealplan', 'Meal Plan'), ('inclusions', 'Inclusions'), ('cancellationrules', 'Cancellation Rules')]


def mergelist(l1, l2, key):
    d = defaultdict(dict)
    for l in (l1, l2):
        for elem in l:
            d[elem[key]].update(elem)
    l3 = sorted(d.values(), key=itemgetter(key))
    return l3


def copyf(data, key, allowed):
    return filter(lambda x: key in x.keys() and x[key] in allowed, data)


def propertyRegistration(postDict):
    response = {'success': False}
    from api.v1.fake_details.service import FakeDetailException
    try:
        bList = ListYourHotel.objects.filter(propertyname=postDict.get('propertyname', ''),
                                             city=postDict.get('city', ''), email=postDict.get('email', ''),
                                             phone=postDict.get('phone', ''))
        if bList:
            return {'success': False, 'error': 'Thanks !!! Your property request is already with us.'}

        data = {}
        if 'email' in postDict:
            data['email'] = postDict['email']
        if 'phone' in postDict:
            data['mobile'] = postDict['phone']
        from api.v1.fake_details.service import FakeDetailService
        FakeDetailService.check_exists(data, raise_exception=True)

        bObj = ListYourHotel()
        bObj.propertyname = postDict.get('propertyname', '')
        bObj.hotelchain = postDict.get('chainname', '')
        bObj.propertytype = postDict.get('propertytype', '')
        bObj.starrating = int(postDict.get('starrating', 0))
        bObj.noofrooms = int(postDict.get('noofrooms', 0))
        bObj.propertywebsite = postDict.get('website', '')
        bObj.city = postDict.get('city', '')
        bObj.firstname = postDict.get('firstname', '')
        bObj.lastname = postDict.get('lastname', '')
        bObj.email = postDict.get('email', '')
        bObj.phone = postDict.get('phone', '')
        bObj.fax = postDict.get('fax', '')
        bObj.save()
        response['success'] = True
        try:
            from hotelmailmsgs import getHotelListMailContent

            email_content = getHotelListMailContent(postDict)
            email_to = postDict.get('email', '').split(',')
            sendMail('', from_email=settings.INV_ALERTS[0], body=email_content, subject='Goibibo Hotels Contract Form',
                     template_id='', cc_emails=email_to, attached_file=open(
                    settings.PROJECT_PATH + '/communication/templates/hotelmails/Hotel_contract_final.docx', 'r'),
                     bcc_emails=[])
        except Exception, e:
            logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'reports', 'propertyRegistration', '', str(e), repr(traceback.format_exc())))

    except FakeDetailException as e:
        logger.info('%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'reports', 'propertyRegistration', 'Fake Detail', str(e)))
        response['success'] = False
        response['error'] = str(e)

    except Exception, e:
        response['success'] = False
        response['error'] = "Submitted data is not in right format.Please check your information before submission."

    return response


'''hotel mails or ticket html'''


@staff_member_required
def broadcastToHotelForm(request):
    if request.user.is_superuser or request.user.hoteluser.addInventory:
        try:
            citylist = HotelDetail.objects.values_list('city', flat=True).distinct()
        except Exception, e:
            logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'reports', 'broadcastToHotelForm', '', str(e), repr(traceback.format_exc())))
        return render_to_response("hotelreport/hotelbroadcastemail.html", {'citylist': citylist, },
                                  context_instance=RequestContext(request))
    else:
        raise PermissionDenied


@staff_member_required
def submitBroadcastToHotelForm(request):
    if request.user.is_superuser or request.user.hoteluser.addInventory:
        querydict = {}
        querydict['city'] = request.POST.get('city', None)
        querydict['emailtest'] = request.POST['emailtest'].split(',')
        querydict['subject'] = request.POST['subject']
        querydict['ishtml'] = True if request.POST.get('ishtml', False) else False
        querydict['emailfrom'] = request.POST['emailfrom']
        querydict['emailcontent'] = request.POST['emailcontent']

        querydict['user'] = request.user
        # get hotel email ids
        if not querydict['city']:
            querydict['hotel'] = 'No hotel'
            hotelids = []
        elif querydict['city'] == 'all':
            querydict['hotel'] = 'all hotels'
            # TODO CHECK THIS - DONE
            hotelids = []
            hotelids_encrypted = HotelDetail.objects.values_list('hotelemail', 'encryptionDetails')
            for hotelemail, encryption_details  in hotelids_encrypted:
                if encryption_details:
                    hotelids.append(decrypt_column_data(hotelemail, 'hotelemail', encryption_details))
                else:
                    hotelids.append(hotelemail)

        else:
            querydict['hotel'] = request.POST.getlist('hotel')
            if 'all' in querydict['hotel']:
                querydict['hotel'] = ' all hotels of ' + querydict['city']
                hotelids = list(HotelDetail.objects.filter(city=querydict['city']).values_list('hotelemail', flat=True))
            else:
                hotelids = list(
                    HotelDetail.objects.filter(hotelcode__in=querydict['hotel']).values_list('hotelemail', flat=True))

        hotelemail = HotelBroadcastEmail(subject=str(querydict['subject']), emailto=hotelids,
                                         emailfrom=querydict['emailfrom'], user=querydict['user'],
                                         emailcontent=querydict['emailcontent'], emailtestids=querydict['emailtest'],
                                         ishtml=querydict['ishtml'], hotelids=querydict['hotel'], emailstatus='waiting')
        hotelemail.save()
        hm.updateLogMsg(request.user, hotelemail, 'email with subject "' + str(querydict['subject']) + '" added.')

        if querydict['ishtml']:
            sendMail('', from_email=querydict['emailfrom'], body=mark_safe(querydict['emailcontent']),
                     subject=str(querydict['subject']), template_id='',
                     cc_emails=querydict['emailtest'] + settings.INV_ALERTS[0], attached_file=None, bcc_emails=[])
        else:
            sendMail('', from_email=querydict['emailfrom'], body=querydict['emailcontent'],
                     subject=str(querydict['subject']), template_id='',
                     cc_emails=querydict['emailtest'] + settings.INV_ALERTS[0], attached_file=None, bcc_emails=[])

        return render_to_response("hotelreport/hotelbroadcastresponse.html", {'response': querydict, },
                                  context_instance=RequestContext(request))
    else:
        raise PermissionDenied

@custom_required(token_required, login_required)
def get_hotel_payment_voucher(request, paymentid):
    """
    Getting Hotel Payment Voucher Data calling from admin
    """
    try:
        from communication.hotelMailsSMS import sendHotelPaymentInfoMail
        pay_obj = HotelOutwardPayment.objects.get(paymentid=paymentid)
        #Here calling Twice reason dont want to update calling function
        if request.GET.get('sm', 'no') == 'yes':
            mail_data = sendHotelPaymentInfoMail(pay_obj)
        else:
            mail_data = sendHotelPaymentInfoMail(pay_obj, {}, True)
        return HttpResponse(mail_data)

    except ObjectDoesNotExist:
        return HttpResponse('Paymentid is not found in the database.')

    except Exception, e:
        logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'hotelreports', 'get_hotel_payment_voucher', ' ', str(e),
            repr(traceback.format_exc())))
        return HttpResponse(str(e) + str(traceback.format_exc()))

@custom_required(token_required, login_required)
def getHotelBookingInvoice(request, bid):
    from lib.AWSS3Upload import S3Upload, BucketMapper, DOCUMENT_OBJECT_TYPE
    from django.core.servers.basehttp import FileWrapper
    try:
        vendor_booking_id = request.GET.get('vendorId','')
        if not vendor_booking_id:
            return HttpResponse('Vendor Bookingid is not found in the database.')
        vendor_code = request.GET.get('vendorCode', '')
        vendor_code_shorthand = 'MMT' if vendor_code =='MakeMyTrip' else 'GI'
        sendMailParam = request.GET.get('sm',False)
        hotel_booking_obj = HotelBooking.objects.filter(confirmbookingid = bid).last()
        checkout = hotel_booking_obj.checkout
        udf1 = hotel_booking_obj.udf1
        filename = vendor_booking_id if vendor_code =='MakeMyTrip' else udf1
        # TODOS3
        s3 = S3Upload()
        dateStr = "%s%s" %(checkout.strftime('%B').lower(),checkout.year)
        file_url = "invoices/booking-invoices/%s/%s/%s.jpg" % (dateStr, vendor_code_shorthand, filename)
        logger.critical('Looking for File at S3 location %s' % (file_url))
        utils_logger.critical(message='Looking for File at S3 location %s ' %(filename),
                                  log_type='ingoibibo',
                                  bucket='reports',
                                  stage='hotelierreports.get_report_from_aws')
        data = s3.open_from_region(file_url,
                                params={'bucket_key': settings.PROD_BUCKET_KEY,'filename': '/tmp/%s.jpg' % filename})
        if not data['success']:
            file_url = "invoices/booking-invoices/%s/%s/%s-1.jpg" % (dateStr, vendor_code_shorthand, filename)
            logger.critical('Looking for File at S3 location %s' % (file_url))
            utils_logger.critical(message='Looking for File at S3 location %s ' % (filename),
                                  log_type='ingoibibo',
                                  bucket='reports',
                                  stage='hotelierreports.get_report_from_aws')
            data = s3.open_from_region(file_url,
                                       params={'bucket_key': settings.PROD_BUCKET_KEY, 'filename': '/tmp/%s.jpg' % filename})
        if data['success']:
            if sendMailParam:
                emailList = hotel_booking_obj.hotel.hotelemail
                if(len(emailList)>0):
                    sendMail(emailList[0], None, "Invoice ", "For Booking %s" % (bid), '', [], attached_file=data,
                             bcc_emails=[])
            #response = HttpResponse(content_type='application/vnd.ms-excel')
            #response['Content-Disposition'] = 'attachment; filename="' + '/tmp/%s' % file_url + '"'
            #result_file = file('/tmp/%s' % file_url)
            result_file = open('/tmp/%s.jpg' % filename, 'r')
            wrapper = FileWrapper(file(result_file.name))
            response = HttpResponse(wrapper, content_type='image/jpg')
            response['Content-Disposition'] = 'attachment; filename=%s ' % \
                                              (os.path.basename(result_file.name))
            response['Content-Length'] = os.path.getsize(result_file.name)
            return response
        else :
            #try with xls report .
            file_url = "invoices/booking-invoices/%s/%s/%s.xls" % (dateStr, vendor_code_shorthand, filename)
            data = s3.open_from_region(file_url,
                                       params={'bucket_key': settings.PROD_BUCKET_KEY, 'filename': '/tmp/%s.xls' % filename})
            if not data['success']:
                file_url = "invoices/booking-invoices/%s/%s/%s-1.xls" % (dateStr, vendor_code_shorthand, filename)
                data = s3.open_from_region(file_url,
                                           params={'bucket_key': settings.PROD_BUCKET_KEY,
                                                   'filename': '/tmp/%s.xls' % filename})
            if data['success']:
                if sendMailParam:
                    emailList = hotel_booking_obj.hotel.hotelemail
                    if(len(emailList)>0):
                        sendMail(emailList[0], None, "Invoice ", "For Booking %s" % (bid), '', [], attached_file=data,
                                 bcc_emails=[])
                result_file = open('/tmp/%s.xls' % filename, 'r')
                wrapper = FileWrapper(file(result_file.name))
                response = HttpResponse(wrapper, content_type='application/vnd.ms-excel')
                response['Content-Disposition'] = 'attachment; filename=%s ' % \
                                                  (os.path.basename(result_file.name))
                response['Content-Length'] = os.path.getsize(result_file.name)
                return response
        return HttpResponse('Invoice for the selected booking / period is not yet generated .')
    except Exception, e:
        logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'hotelreports', 'hotelVoucher', ' ', str(e), repr(traceback.format_exc())))
        return HttpResponse(str(e) + str(traceback.format_exc()))
    pass

# @staff_member_required
@custom_required(token_required, login_required)
def getHotelBookingVoucher(request, bid):
    from api.v1.bookings.resources.booking_resource import booking_voucher
    user = request.user
    old = False if request.GET.get('old') else True
    sm = request.GET.get('sm', 'no').lower()
    return booking_voucher(bid, user, old, sm, db_slave="report-slave", request_from_admin=True)


def get_sample_voucher(request):
    from django.template.loader import render_to_string
    sample_file = 'hotelmails/web_engage_test_voucher.html'
    response = render_to_string(sample_file)
    return HttpResponse(response)


@custom_required(token_required, login_required)
def getOldMMTHotelBookingVoucher(request, bid):
    try:
        from api.v1.bookings.resources.booking_resource import mmt_voucher
        hotelcode = request.GET.get('hotelcode', '')
        confirmBookingIds = request.GET.get('confirmbookingids', '')
        user = request.user
        return mmt_voucher(user, hotelcode, bid, request_from_admin=True, confirmBookingIds=confirmBookingIds)
    except Exception as e:
        logger.critical(message="Error Occurred in mmt booking voucher Error: %s, Booking Id: %s" %
                                (str(e), bid), log_type='ingoibibo',
                        bucket='BookingAPI', stage='bookings.views.mmt_voucher')
        return HttpResponse('Some internal issue occured while fetching MMT booking voucher.')


def formatBookingVoucherData(bObj):
    db.close_connection()
    bookingcancellationrules = None
    bookings = list()
    if bObj.parent_booking_id is not None:
        # At this moment bObj wont be completely saved in the db
        # as this is called from inside a transaction at some points!
        bookings = HotelBooking.objects.filter(
            Q(parent_booking_id=bObj.parent_booking_id) | Q(confirmbookingid=bObj.parent_booking_id)).exclude(
            confirmbookingid=bObj.confirmbookingid).order_by(
            '-confirmbookingid_tracker')
        # We can handle this load of converting it to list as it wont ever be more than 10
        # bookings - Modified.
        bookings = list(bookings)
        bookings.insert(0, bObj)
    else:
        bookings = [bObj]

    if bookings:
        for index, booking in enumerate(bookings):
            if not booking.payathotelflag:
                bookings[index] = booking.getHotelBookingVoucherData()
            else:
                bookings[index] = booking.getHotelBookingVoucherData('sell')
            bookings[index] = hm.getAccounDetail(booking)
            cpList = list()
            try:
                try:
                    bookingcancellationrules = eval(booking.cancellationrules)
                except Exception:
                    if booking.cancellationrules:
                        bookingcancellationrules = booking.cancellationrules

                for rule in bookingcancellationrules:
                    desc = rule.get('data_description', '')
                    if desc=='':
                        desc = rule.get('description', '')
                    cpList.append(desc)
            except Exception as e:
                utils_logger.critical(message="Cancellation Rules Exception : %s" % repr(e),
                                      log_type='ingoibibo', bucket='booking_voucher',
                                      stage='hotels.hotelreports.formatBookingVoucherData')
            bookings[index].cancelpolicy = cpList

            if booking.cancelbookingtime:
                cancellation_objects = HotelCancellation.objects.filter(bookingobj=booking)
                if cancellation_objects:
                    cancellation_object = cancellation_objects[0]
                    booking.cancellation_charges = cancellation_object.cancellationcharges
                    booking.refund_amount = cancellation_object.refundedamount

            booking.is_midnight_checkin = True if booking.checkin < booking.createdon.date() else False
            bookings[index].mealplan = bookings[index].rateplan.mealplan if bookings[index].rateplan.mealplan else ""

            '''TODO: remove this - Conditional Extras''' 
            try:
                conditional_extras = []
                bookings[index].conditional_extras_flag = True if conditional_extras else False
                bookings[index].conditional_extras = conditional_extras
            except Exception, e:
                utils_logger.critical(message="Conditional Extras Exception : %s" % repr(e),
                                      log_type='ingoibibo', bucket='booking_voucher',
                                      stage='hotels.hotelreports.formatBookingVoucherData')

            '''Add Ons'''
            try:
                bookings[index].add_ons_flag = False
                from api.v1.bookings.resources.add_on_booking_resources import get_booking_specific_booked_add_ons
                add_ons_resp = get_booking_specific_booked_add_ons(booking.confirmbookingid)
                bookings[index].add_ons_flag = add_ons_resp['success']
                if bookings[index].add_ons_flag:
                    bookings[index].add_ons_data = add_ons_resp.get('add_on_booking_list',[])
                    if not add_ons_resp['add_on_booking_list']:
                        bookings[index].add_ons_flag = False
                bookings[index].add_ons = {'instant_add_on_amount' : 0,
                                           'delayed_add_on_amount': 0,
                                           'payment_pend_add_on_amount' : 0,
                                           'pay_at_hotel_amount' : 0,
                                           'paid_amount' : 0
                                           }
                for add_on in add_ons_resp['add_on_booking_list']:
                    try:
                        if add_on.get('pay_mode', 2) == 2:
                            bookings[index].add_ons['pay_at_hotel_amount'] += add_on['sell_amount']
                        elif add_on['pay_mode'] == 1:
                            bookings[index].add_ons['paid_amount'] += add_on['nett_amount']
                        add_on['pay_mode'] = dict(ADD_ON_BOOKING_PAY_MODE)[int(add_on.get('pay_mode', 2))]
                    except Exception, e:
                        utils_logger.critical(message="Add Ons Exception in for loop : %s" % repr(e),
                                              log_type='ingoibibo', bucket='booking_voucher',
                                              stage='hotels.hotelreports.formatBookingVoucherData')


            except Exception, e:
                utils_logger.critical(message="Add Ons Exception : %s" % repr(e),
                                      log_type='ingoibibo', bucket='booking_voucher',
                                      stage='hotels.hotelreports.formatBookingVoucherData')

            if booking.roomtype.parent_id:
                bookings[0].slot_start_time, bookings[
                    0].slot_end_time = booking.roomtype.get_formatted_slot_time()

            promotions_applied = []
            if bookings[index].commission_based_offer:
                promotions_applied = bookings[index].commission_based_offer
            if bookings[index].offer:
                if promotions_applied:
                    promotions_applied = ('%s| %s') % (promotions_applied, bookings[index].offer)
                else:
                    promotions_applied = bookings[index].offer
            if bookings[index].cug_desc != '':
                cug_desc = bookings[index].cug_desc
                # for consistency
                cug_desc = cug_desc.replace('Discount', 'discount')
                cug_desc = cug_desc.replace('segment', 'customers')
                if bookings[index].booking_vendor_name.lower() == 'goibibo':
                    cug_desc = blackToTribeMapping(cug_desc)
                promotions_applied = ('%s| %s') % (promotions_applied, cug_desc)
            if promotions_applied:
                promotions_applied = promotions_applied.split("|")

            bookings[index].promotions_applied = promotions_applied
            '''Default ARI'''
            from api.v1.bookings.resources.booking_resource import get_room_breakup_by_pattern

            room_breakup = get_room_breakup_by_pattern(booking, 'room_day_wise')
            booking_breakup_dict = json.loads(booking.bookingbreakup)
            static_copy_ari_data = booking_breakup_dict.get('static_copy_ari_data', {})
            default_inv_info_msg = None
            default_rate_info_msg = None 
            default_inv_date_list = set()
            default_rates_date_list = set()

            room_index = 0

            for room in room_breakup:
                day_index = 0
                for day, booking_detail in room.iteritems():
                    date_time_object = datetime.datetime.strptime(day, '%Y-%m-%d')
                    booking.append_default_ari_info(date_time_object, day_index, room_index, static_copy_ari_data, default_rates_date_list, default_inv_date_list)
                    day_index += 1

                room_index += 1
            
            default_ari_info = {}
            default_ari_summary = 'As you have not updated your rates & inventory data, we have picked your default rates & inventory for the concerned dates.'
            default_ari_description = []

            if len(default_inv_date_list) > 0:
                default_inv_info_msg = "Default inventory dates %s." % (',  '.join(default_inv_date_list))
                default_ari_description.append(default_inv_info_msg)
            if len(default_rates_date_list) > 0:
                default_rate_info_msg = "Default rates dates %s." % (',  '.join(default_rates_date_list))
                default_ari_description.append(default_rate_info_msg)

            if len(default_ari_description) > 0:
                default_ari_info['summary'] = default_ari_summary
                default_ari_info['description'] = default_ari_description

            # add the attribute for default ari info which will be shown in Extranet
            setattr(booking,  "default_ari_info", default_ari_info)

            if booking.frn_code:
                booking.nettamount=0


    if bookings[index].booking_vendor_name.lower() == 'goibibo':
        bookings[index].contracttype = blackToTribeMapping(bookings[index].contracttype)

    if bookings[0].misc:
        misc_dict = json.loads(bookings[0].misc)
        if misc_dict.get('paymentdetail',{}):
            bookings[0].paymentdetail = misc_dict.get('paymentdetail')
        if misc_dict.get('is_flyer_exclusive', False):
            bookings[0].is_flyer_exclusive = misc_dict.get('is_flyer_exclusive')
        if misc_dict.get('frn_code', ""):
            bookings[0].frn_code=misc_dict.get('frn_code')
            
    if bookings[0].bookingvendor:
        bookings[0].bookingvendorname = bookings[0].bookingvendor.name.lower()
    payment_data = {}
    if bookings[0].paymentstatus == "processed" and bookings[0].booking_vendor_name == 'Goibibo':
        payment_data = create_master_dict2(arg_vendorbookingid=bookings[0].udf1, popup=True, checkhop=False)
    if bookings[0].paymentstatus == "processed" and bookings[0].booking_vendor_name == 'MakeMyTrip':
        payment_data = create_master_dict2(arg_vendorbookingid=bookings[0].vendorbookingid, vendor='Makemytrip', popup=True, checkhop=False)
    if payment_data:
        bookings[0].paymentdetailsdata = json.dumps(payment_data)
        bookings[0].bank_ref = payment_data[payment_data.keys()[0]].get('bankref', '')
        bookings[0].amount = payment_data[payment_data.keys()[0]].get('booking_payout', 0)
        bookings[0].plb_list = ','.join([str(obj['rbooking_id']) for obj in payment_data[payment_data.keys()[0]].get('recovery_dict_list', []) if 'plb' in obj])
        bookings[0].booking_list = ','.join([str(obj['booking_id']) for obj in payment_data[payment_data.keys()[0]].get('booking_list', [])])
        bookings[0].recover_list = ','.join([str(obj['rbooking_id']) for obj in payment_data[payment_data.keys()[0]].get('recovery_dict_list', []) if 'plb' not in obj])
        bookings[0].adjustment_list = ','.join([str(obj['adjustmentid']) for obj in payment_data[payment_data.keys()[0]].get('adjustment_dict_list', [])])

    if len(bookings) == 1:
        bookings = bookings[0]
    # In case there is only one booking in bookings
    # It becomes an object itself. Kind of wrong but had to make this function
    # backward compatible. Don't know where all its used so making sure
    # it works the old way for single booking with no parent_booking_id
    return bookings

def blackToTribeMapping(desc_str):
    desc_str = desc_str.replace('MMT BLACK2', 'Gotribe Superstar')
    desc_str = desc_str.replace('MMT BLACK1', 'Gotribe Superstar')
    desc_str = desc_str.replace('MMT BLACK', 'Gotribe Star')
    return desc_str

def HttpComaptibleBookingObject(bookObj, showdetails=True):
    try:
        bookObj.roomtypename = bookObj.roomtype.roomtypename
        bookObj.hotelname = bookObj.hotel.hotelname
        bookObj.rateplanname = bookObj.rateplan.rateplanname
        if not showdetails:
            bookObj.__dict__['_bookingemail'] = ''
            bookObj.__dict__['_customerdetails'] = ''
        try:
            del bookObj.__dict__['_roomtype_cache']
            del bookObj.__dict__['_rateplan_cache']
            del bookObj.__dict__['_state']
            del bookObj.__dict__['_hotel_cache']
            del bookObj.__dict__['_bookingvendor_cache']
        except KeyError:
            pass
        bookObj.__dict__['bookingemail'] = bookObj.__dict__['_bookingemail']
        bookObj.__dict__['customerdetails'] = bookObj.__dict__['_customerdetails']
        try:
            del bookObj.__dict__['_bookingemail']
            del bookObj.__dict__['_customerdetails']
            del bookObj.__dict__['vendor_name']
        except KeyError:
            pass
        bookJSON = json.dumps(bookObj.__dict__, cls=JSONDateTimeEncoder)
    except Exception as e:
        bookJSON = json.dumps({'success': False})
        logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % ('hotels', 'hotelreports',
        'httpbooking', '', str(e), repr(traceback.format_exc())))
    return bookJSON


''' reports functions'''

@custom_required(token_required, login_required)
def getReports(request):
    return render_to_response("page403.html")
    # if request.hotelusertype == 'contentoutsource':
    #     raise PermissionDenied
    # userhotel = []
    # #today=datetime.date.today()
    # title = "Hotels Reports And Stats"
    # allhotels = HotelDetail.objects.all()
    # allrooms = RoomDetail.objects.filter(parent_id__isnull=True)
    # allrateplan = RatePlan.objects.all()
    # if allhotels:
    #     allcities = allhotels.values_list('city', flat=True).distinct()
    #     allhotelcount = allhotels.count()
    #     activehotel = allhotels.values('isactive').annotate(acount=Count('isactive'))
    #     activerooms = allrooms.values('isactive').annotate(rcount=Count('isactive'))
    #     activerateplan = allrateplan.values('isactive').annotate(rpcount=Count('isactive'))
    #     '''
    #     thismonth=today.replace(day=1)
    #     lastmonth=(thismonth-datetime.timedelta(days=1)).replace(day=1)
    #     content_type = ContentType.objects.get_for_model(allhotels[0])
    #     totaladded = LogEntry.objects.filter(content_type=content_type)
    #     if totaladded:
    #         addedhotel=totaladded.filter(action_flag=ADDITION).values('user').annotate(added=Count('user'))
    #         updatedhotel=totaladded.filter(action_flag=CHANGE).values('user').annotate(updated=Count('user'))
    #         addedthismonth=totaladded.filter(action_flag=ADDITION,action_time__gte=thismonth).values('user').annotate(addedthismonth=Count('user'))
    #         addedlastmonth=totaladded.filter(action_flag=ADDITION,action_time__lte=thismonth,action_time__gte=lastmonth).values('user').annotate(addedlastmonth=Count('user'))
    #         updatedthismonth=totaladded.filter(action_flag=CHANGE,action_time__gte=thismonth).values('user').annotate(updatedthismonth=Count('user'))
    #         updatedlastmonth=totaladded.filter(action_flag=CHANGE,action_time__lte=thismonth,action_time__gte=lastmonth).values('user').annotate(updatedlastmonth=Count('user'))
    #
    #         userhotel=mergelist(mergelist(updatedthismonth,updatedlastmonth,'user'),mergelist(mergelist(addedhotel,updatedhotel,'user'),mergelist(addedthismonth,addedlastmonth,'user'),'user'),'user')
    #         for usr in userhotel:
    #             try:
    #                 usr['username']=User.objects.get(id=usr['user']).username
    #             except Exception,e:
    #                 logger.critical('%s\t%s\t%s\t%s\t%s\t%s' %('hotels','hotelreports','getReports','',str(e),repr(traceback.format_exc())))
    #     '''
    # context = {'title': title, 'allcities': allcities, 'hotelcount': allhotelcount,
    #            'roomcount': activerooms, 'rateplancount': activerateplan,
    #            'activehotel': activehotel, 'userhotel': userhotel, 'fields_hotel':
    #                fields_hotel, 'fields_room': fields_room, 'fields_rateplan': fields_rateplan}
    # return render_to_response('hotelreport/reportindex.html', context)


@custom_required(token_required, login_required)
def getCharts(request, objecttype, pageno=1):
    try:
        city = request.GET.get('city', 'all')
        user = request.user
        resultsperpage = 20.0
        model_class = models.get_model('hotels', objecttype)
        if city == 'all':
            hotelcount = HotelDetail.objects.filter(id__in=user.hotels.only('id')).count()
        else:
            hotelcount = HotelDetail.objects.filter(city=city, id__in=user.hotels.only('id')).count()
        maxpageno = int(math.ceil(hotelcount / resultsperpage))
        pageno = int(pageno) - 1
        if pageno > maxpageno:
            pageno = maxpageno - 1

        start = int(pageno * resultsperpage + 0)
        end = int(pageno * resultsperpage + resultsperpage)
        if end > hotelcount:
            end = hotelcount
        if objecttype == 'hoteldetail':
            if city == 'all':
                hotels = HotelDetail.objects.filter(id__in=user.hotels.only('id')).select_related('locality', 'chainname', 'user').prefetch_related('rooms',
                                                                                                              'images',
                                                                                                              'amenities',
                                                                                                              'nearestattractions').all().order_by(
                    'disppriority', '-modifiedon')[start:end]
            else:
                hotels = HotelDetail.objects.filter(city=city, id__in=user.hotels.only('id')).select_related('locality', 'chainname',
                                                                              'user').prefetch_related('rooms',
                                                                                                       'images',
                                                                                                       'amenities',
                                                                                                       'nearestattractions').all().order_by(
                    'disppriority', '-modifiedon')[start:end]
            hotelhtml = render_to_string('hotelreport/hotelchart.html',
                                         {'city': city, 'hotels': hotels, 'thispage': pageno, 'lastpage': maxpageno,
                                          'objecttype': objecttype, })
            return HttpResponse(hotelhtml)
        else:
            if city == 'all':
                hotellist = HotelDetail.objects.filter(id__in=user.hotels.only('id')).values_list('hotelcode', flat=True).order_by('disppriority',
                                                                                             '-modifiedon')
            else:
                hotellist = HotelDetail.objects.filter(city=city, id__in=user.hotels.only('id')).values_list('hotelcode', flat=True).order_by(
                    'disppriority', '-modifiedon')
            if hotellist:
                hotellist = hotellist[start:end]
            if objecttype == 'roomdetail':
                rooms = RoomDetail.objects.select_related('hotel', 'user').prefetch_related('amenities', 'rateplans'). \
                    filter(hotel__hotelcode__in=hotellist, parent_id__isnull=True)
                roomhtml = render_to_string('hotelreport/roomchart.html',
                                            {'city': city, 'rooms': rooms, 'thispage': pageno, 'lastpage': maxpageno,
                                             'objecttype': objecttype, })
                return HttpResponse(roomhtml)
            else:
                rateplans = RatePlan.objects.select_related('user', 'roomtype', 'roomtype__hotel').prefetch_related(
                    'inclusions', 'cancellationrules').filter(roomtype__hotel__hotelcode__in=hotellist)
                rateplanhtml = render_to_string('hotelreport/rateplanchart.html',
                                                {'city': city, 'rateplans': rateplans, 'thispage': pageno,
                                                 'lastpage': maxpageno, 'objecttype': objecttype, })
                return HttpResponse(rateplanhtml)
    except Exception, e:
        logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
        'hotels', 'hotelreports', 'getCharts', '', str(e), repr(traceback.format_exc())))
    return HttpResponse(json.dumps(traceback.format_exc()))



def getUnloadedData(request, objecttype):
    return render_to_response("page403.html")
    # try:
    #     # sanitizing request to prevent XSS
    #     request.GET = request.GET.copy()
    #     request.GET = sanitize_request(request.GET)
    #     city = request.GET.get('city', 'all')
    #     field = request.GET.get('field', '')
    #     model_class = models.get_model('hotels', objecttype)
    #     fieldcount = 0
    #     fieldlist = []
    #     if city == 'all':
    #         totalcount = model_class.objects.count()
    #         exec "fieldcount=model_class.objects.filter(Q(" + field + "__isnull=True)|Q(" + field + "=None)).count()"
    #         exec "fieldlist=model_class.objects.filter(Q(" + field + "__isnull=True)|Q(" + field + "=None))"
    #     else:
    #         if objecttype == 'hoteldetail':
    #             totalcount = model_class.objects.filter(city=city).count()
    #             exec "fieldcount=model_class.objects.filter(city=city).filter(Q(" + field + "__isnull=True)|Q(" + field + "=None)).count()"
    #             exec "fieldlist=model_class.objects.filter(city=city).filter(Q(" + field + "__isnull=True)|Q(" + field + "=None))"
    #         elif objecttype == 'roomdetail':
    #             totalcount = model_class.objects.filter(hotel__city=city).count()
    #             exec "fieldcount=model_class.objects.filter(hotel__city=city).filter(Q(" + field + "__isnull=True)|Q(" + field + "=None)).count()"
    #             exec "fieldlist=model_class.objects.filter(hotel__city=city).filter(Q(" + field + "__isnull=True)|Q(" + field + "=None))"
    #         elif objecttype == 'rateplan':
    #             totalcount = model_class.objects.filter(roomtype__hotel__city=city).count()
    #             exec "fieldcount=model_class.objects.filter(roomtype__hotel__city=city).filter(Q(" + field + "__isnull=True)|Q(" + field + "=None)).count()"
    #             exec "fieldlist=model_class.objects.filter(roomtype__hotel__city=city).filter(Q(" + field + "__isnull=True)|Q(" + field + "=None))"
    #
    #     contextlDict = {'totalcount': totalcount, 'fieldcount': fieldcount, 'fieldlist': fieldlist}
    #
    #     if objecttype == 'hoteldetail':
    #         hotelhtml = render_to_string('hotelreport/unloadedhotels.html', contextlDict)
    #         return HttpResponse(hotelhtml)
    #     elif objecttype == 'roomdetail':
    #         roomhtml = render_to_string('hotelreport/unloadedrooms.html', contextlDict)
    #         return HttpResponse(roomhtml)
    #     elif objecttype == 'rateplan':
    #         rateplanhtml = render_to_string('hotelreport/unloadedrateplans.html', contextlDict)
    #         return HttpResponse(rateplanhtml)
    # except Exception, e:
    #     logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
    #     'hotels', 'hotelreports', 'getUnloadedData', '', str(e), repr(traceback.format_exc())))
    # return HttpResponse(json.dumps(traceback.format_exc()))


@custom_required(token_required, login_required)
def get_terms_and_condition(request):
    try:
        return render_to_response('hotelmails/digital_terms_and_condition.html')
    except Exception, e:
        logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'hotelreports', 'get_terms_and_condition', ' ', str(e),
            repr(traceback.format_exc())))
        return HttpResponse(str(e) + str(traceback.format_exc()))


def get_terms_and_condition_consumer(request):
    try:
        return render_to_response('hotelmails/digital_terms_and_condition_consumer.html')
    except Exception, e:
        logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
            'hotels', 'hotelreports', 'get_terms_and_condition_consumer', ' ', str(e),
            repr(traceback.format_exc())))
        return HttpResponse(str(e) + str(traceback.format_exc()))


@staff_member_required
def get_tax_invoice(request, bid):
    from communication.tasks import send_service_tax_invoice_hotel
    booking = HotelBooking.objects.get(confirmbookingid=bid)
    return send_service_tax_invoice_hotel(booking, show=True)


def get_hotel_tax_invoice(request):
    from api.v1.reports.views.download import InvoiceDownloadViewSet
    response = InvoiceDownloadViewSet.as_view({'get': 'retrieve'})(request=request)
    return response


def send_hotel_contract_alert(hotels):
    from communication.hotelMailsSMS import sendMail
    for hotel in hotels:
        subject = ("Important: Updated T&Cs for selling on InGoibibo for your hotel:%s") %(hotel.hotelname)
        renderHtml = 'hotelmails/hotelier_seller_contract_alert.html'
        mail_data = render_to_string(renderHtml, {'data': locals(), 'projectpath': settings.HOST, 'hotelcode': hotel.hotelcode})
        if not settings.HOST in settings.PROD_HOSTS:
            send_to = settings.INGOIBIBO_ERROR_ALERT
        else:
            send_to = hotel.createSendToEmailList(
                email_filter=['non-staff'])
        email_list = send_to
        emailid = email_list[0]
        temp_id = '60.063'
        sendMail(emailid, None, mail_data, subject, temp_id, email_list, None, [], content_obj=hotel)


def sent_hotel_gst_alert(hotels):
    from communication.hotelMailsSMS import sendMail
    for hotel in hotels:
        try:
            subject = ("Important: Changes needed regarding GST for your hotel: %s") % (hotel.hotelname)
            renderHtml = 'hotelmails/gst/gst_emailer.html'
            mail_data = render_to_string(renderHtml,{'data': locals(), 'projectpath': settings.HOST,
                                          'hotelcode': hotel.hotelcode})
            if not settings.HOST in settings.PROD_HOSTS:
                send_to = settings.INGOIBIBO_ERROR_ALERT
            else:
                send_to = hotel.createSendToEmailList(
                    email_filter=['non-staff'])
            email_list = send_to
            emailid = email_list[0]
            temp_id = '60.064'
            sendMail(emailid, None, mail_data, subject, temp_id, email_list, None, [], content_obj=hotel)
        except Exception,e:
            pass


def get_hotel_tax_invoice_info(booking,invoice_obj,invoice_soln_obj):
    try:
        hotel_breakup = json.loads(booking.hotelbreakup)
        bookingbreakup = json.loads(booking.bookingbreakup)
    except:
        hotel_breakup = ast.literal_eval(booking.hotelbreakup)
        bookingbreakup = ast.literal_eval(booking.bookingbreakup)
    gst_tax_info = bookingbreakup['tax_breakup_daywise']
    gst_tax_percent_info = bookingbreakup['tax_percent_daywise']
    booking_info = {'tax_info': []}
    for night in range(0, len(gst_tax_percent_info)):
        room_night = []
        for room in range(0, len(gst_tax_info[night])):
            taxable_value = '{0:.2f}'.format(bookingbreakup['baseprice'][night][room] + bookingbreakup['extraguest'][night][room])
            gst_tax_percent = gst_tax_percent_info[night][room]
            gst_tax_value =  '{0:.2f}'.format(float(taxable_value) * float(gst_tax_percent/float(100)))
            room_night.append([float(gst_tax_percent)/2, float(gst_tax_value)/2])
        booking_info['tax_info'].append(room_night)
    booking_info['declared_tariff'] = hotel_breakup.get('original')
    booking_info['declared_tariff_agg'] = sum(sum(x) for x in booking_info['declared_tariff'])
    booking_info['gst_tax_info_agg'] = sum(sum(x) for x in gst_tax_info)
    hotel = booking.hotel
    booking_info['hotel_name'] = hotel.hotelname
    booking_info['hotel_legal_name'] = invoice_soln_obj.legal_entity_name
    booking_info['hotel_address'] = hotel.address
    booking_info['hotel_phone'] = invoice_soln_obj.phone if invoice_soln_obj.phone else hotel.hotelphone
    booking_info['cin'] = invoice_soln_obj.cin if invoice_soln_obj.cin else ''
    booking_info['gstin'] = hotel.gstn
    hotel_account = hotel.accounts.filter(isactive=True).last()
    booking_info['pan'] = hotel_account.pannumber if hotel_account else ''
    booking_info['traveller_name'] = booking.bookingname
    booking.bookingemail = booking.getGuestEmail()
    booking_info['traveller_email'] = booking.bookingemail
    booking_info['traveller_phone'] = booking.getGuestPhone()
    booking_info['hotelier_email'] = invoice_soln_obj.email
    booking.customer_gst = get_customer_gst(booking.misc, booking.booking_vendor_name)
    customer_gst = booking.customer_gst
    booking_info['company_name'] = customer_gst.get('gst_cn', '') if customer_gst else ''
    booking_info['company_phone'] = customer_gst.get('gst_ph', '') if customer_gst else ''
    booking_info['company_email'] = customer_gst.get('gst_ce', '') if customer_gst else ''
    booking_info['company_gstin'] = customer_gst.get('gstn', '') if customer_gst else ''
    booking_info['company_address'] = customer_gst.get('gst_ca', '') if customer_gst else ''
    booking_info['invoice_serial_no'] = invoice_obj.invoice_no
    booking_info['advanced_receipt_no'] = invoice_obj.advance_receipt_no
    booking_info['booking_date'] = str(booking.createdon)
    booking_info['invoice_date'] = str(invoice_obj.created_on)
    booking_info['place_of_supply'] = hotel.city_id.cityname
    booking_info['description_of_service'] = 'Room or unit accommodation services provided by hotels, inn, guest house, clubs, etc'
    booking_info['hsn'] = '996311'
    booking_info['booking_id'] = booking.id
    booking_info['checkin'] = str(booking.checkin)
    booking_info['checkout'] = str(booking.checkout)
    booking_info['room_type'] = str(booking.roomtype)
    booking_info['no_of_rooms'] = booking.noofrooms
    booking_info['no_of_nights'] = booking.noofnights
    booking_info['room_charges'] = '{0:.2f}'.format(booking.sell_without_tax)
    tax_breakup = bookingbreakup.get('taxbreakup',{})
    service_charge = '{0:.2f}'.format(tax_breakup.get('service_charge',0.00))
    booking_info['service_charge'] = service_charge
    booking_info['extra'] = '{0:.2f}'.format(booking.hotelnewbreakup['extraguest_sell'])
    booking_info['hotel_discount'] = '{0:.2f}'.format(booking.hotelnewbreakup['totalDiscountOnSell'])
    booking_info['taxable_value'] = '{0:.2f}'.format(booking.sell_without_tax + booking.hotelnewbreakup['extraguest_sell'])
    booking_info['net_amount'] = booking.base_currency + ' ' + '{0:.2f}'.format(booking.bookingamount)
    booking_info['total_amount'] = '{0:.2f}'.format(booking.bookingamount)
    booking_info['total_in_words'] = " ".join(num_to_words(booking.bookingamount)).title()
    invoice_obj.context = json.dumps(booking_info)
    invoice_obj.save()
    return booking_info

def get_tax_invoice_context(booking):
    invoice_obj, invoice_soln_obj = booking.generate_tax_invoice_number()
    booking_info = formatBookingVoucherData(booking)
    invoice_info = get_hotel_tax_invoice_info(booking_info, invoice_obj, invoice_soln_obj)
    return invoice_info
