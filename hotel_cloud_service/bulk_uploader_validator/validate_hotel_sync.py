from django.core.exceptions import ValidationError

def validate_hotel_sync(kwargs):
    """
    Validates the input data for hotel sync bulk uploader
    Args:
        kwargs: Dictionary containing the data for hotel sync
    Returns:
        True if validation passes
    Raises:
        ValidationError: If any mandatory field is missing or empty
    """
    mandatory_fields = [
        'IngoHotelId',
        'MMTHotelId'
    ]

    # Validate mandatory fields
    for field in kwargs.keys():
        if field in mandatory_fields and kwargs.get(field, '') == '':
            raise ValidationError("Field {} is mandatory".format(field))

    # Validate that IngoHotelId is a valid string
    ingo_hotel_id = kwargs.get('IngoHotelId', '')
    if ingo_hotel_id and not isinstance(ingo_hotel_id, (str, unicode)):
        raise ValidationError("IngoHotelId must be a valid string")

    # Validate that MMTHotelId is a valid string
    mmt_hotel_id = kwargs.get('MMTHotelId', '')
    if mmt_hotel_id and not isinstance(mmt_hotel_id, (str, unicode)):
        raise ValidationError("MMTHotelId must be a valid string")

    return True 