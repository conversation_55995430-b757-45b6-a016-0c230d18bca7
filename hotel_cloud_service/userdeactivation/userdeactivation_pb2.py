# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: userdeactivation.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='userdeactivation.proto',
  package='',
  syntax='proto3',
  serialized_pb=_b('\n\x16userdeactivation.proto\"(\n\x15\x44\x65\x61\x63tivateUserRequest\x12\x0f\n\x07\x61uth_id\x18\x01 \x01(\t\"\\\n\x16\x44\x65\x61\x63tivateUserResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x31\n\x05\x65rror\x18\x02 \x01(\x0b\x32\".DeactivationStandardErrorResponse\"w\n!DeactivationStandardErrorResponse\x12\x11\n\terrorCode\x18\x01 \x01(\t\x12\x14\n\x0c\x65rrorMessage\x18\x02 \x01(\t\x12\x11\n\terrorType\x18\x03 \x01(\t\x12\x16\n\x0e\x64isplayMessage\x18\x04 \x01(\t2Z\n\x15UserManagementService\x12\x41\n\x0e\x44\x65\x61\x63tivateUser\x12\x16.DeactivateUserRequest\x1a\x17.DeactivateUserResponseB\x0eZ\x0c/protomodelsb\x06proto3')
)




_DEACTIVATEUSERREQUEST = _descriptor.Descriptor(
  name='DeactivateUserRequest',
  full_name='DeactivateUserRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='auth_id', full_name='DeactivateUserRequest.auth_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=26,
  serialized_end=66,
)


_DEACTIVATEUSERRESPONSE = _descriptor.Descriptor(
  name='DeactivateUserResponse',
  full_name='DeactivateUserResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='DeactivateUserResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='error', full_name='DeactivateUserResponse.error', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=68,
  serialized_end=160,
)


_DEACTIVATIONSTANDARDERRORRESPONSE = _descriptor.Descriptor(
  name='DeactivationStandardErrorResponse',
  full_name='DeactivationStandardErrorResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='errorCode', full_name='DeactivationStandardErrorResponse.errorCode', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='errorMessage', full_name='DeactivationStandardErrorResponse.errorMessage', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='errorType', full_name='DeactivationStandardErrorResponse.errorType', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='displayMessage', full_name='DeactivationStandardErrorResponse.displayMessage', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=162,
  serialized_end=281,
)

_DEACTIVATEUSERRESPONSE.fields_by_name['error'].message_type = _DEACTIVATIONSTANDARDERRORRESPONSE
DESCRIPTOR.message_types_by_name['DeactivateUserRequest'] = _DEACTIVATEUSERREQUEST
DESCRIPTOR.message_types_by_name['DeactivateUserResponse'] = _DEACTIVATEUSERRESPONSE
DESCRIPTOR.message_types_by_name['DeactivationStandardErrorResponse'] = _DEACTIVATIONSTANDARDERRORRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

DeactivateUserRequest = _reflection.GeneratedProtocolMessageType('DeactivateUserRequest', (_message.Message,), dict(
  DESCRIPTOR = _DEACTIVATEUSERREQUEST,
  __module__ = 'userdeactivation_pb2'
  # @@protoc_insertion_point(class_scope:DeactivateUserRequest)
  ))
_sym_db.RegisterMessage(DeactivateUserRequest)

DeactivateUserResponse = _reflection.GeneratedProtocolMessageType('DeactivateUserResponse', (_message.Message,), dict(
  DESCRIPTOR = _DEACTIVATEUSERRESPONSE,
  __module__ = 'userdeactivation_pb2'
  # @@protoc_insertion_point(class_scope:DeactivateUserResponse)
  ))
_sym_db.RegisterMessage(DeactivateUserResponse)

DeactivationStandardErrorResponse = _reflection.GeneratedProtocolMessageType('DeactivationStandardErrorResponse', (_message.Message,), dict(
  DESCRIPTOR = _DEACTIVATIONSTANDARDERRORRESPONSE,
  __module__ = 'userdeactivation_pb2'
  # @@protoc_insertion_point(class_scope:DeactivationStandardErrorResponse)
  ))
_sym_db.RegisterMessage(DeactivationStandardErrorResponse)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('Z\014/protomodels'))

_USERMANAGEMENTSERVICE = _descriptor.ServiceDescriptor(
  name='UserManagementService',
  full_name='UserManagementService',
  file=DESCRIPTOR,
  index=0,
  options=None,
  serialized_start=283,
  serialized_end=373,
  methods=[
  _descriptor.MethodDescriptor(
    name='DeactivateUser',
    full_name='UserManagementService.DeactivateUser',
    index=0,
    containing_service=None,
    input_type=_DEACTIVATEUSERREQUEST,
    output_type=_DEACTIVATEUSERRESPONSE,
    options=None,
  ),
])
_sym_db.RegisterServiceDescriptor(_USERMANAGEMENTSERVICE)

DESCRIPTOR.services_by_name['UserManagementService'] = _USERMANAGEMENTSERVICE

# @@protoc_insertion_point(module_scope)