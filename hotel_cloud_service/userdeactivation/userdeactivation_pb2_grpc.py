# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

import userdeactivation_pb2 as userdeactivation__pb2


class UserManagementServiceStub(object):
  """Define the gRPC service.
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.DeactivateUser = channel.unary_unary(
        '/UserManagementService/DeactivateUser',
        request_serializer=userdeactivation__pb2.DeactivateUserRequest.SerializeToString,
        response_deserializer=userdeactivation__pb2.DeactivateUserResponse.FromString,
        )


class UserManagementServiceServicer(object):
  """Define the gRPC service.
  """

  def DeactivateUser(self, request, context):
    """RPC method to deactivate a user.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_UserManagementServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'DeactivateUser': grpc.unary_unary_rpc_method_handler(
          servicer.DeactivateUser,
          request_deserializer=userdeactivation__pb2.DeactivateUserRequest.FromString,
          response_serializer=userdeactivation__pb2.DeactivateUserResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'UserManagementService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))