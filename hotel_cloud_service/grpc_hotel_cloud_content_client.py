import json
import grpc
import uuid
from google.protobuf.json_format import Parse
from protobuf_to_dict import protobuf_to_dict
from utils.logger import Logger
from django.conf import settings

from hotel_cloud_service.hotelhostmapping.hotelhostmapping_pb2_grpc import HotelHostMappingServiceStub
from hotel_cloud_service.hotelhostmapping.hotelhostmapping_pb2 import AssignHotelsToUsersRequest
from hotel_cloud_service.content_sync.content_sync_pb2_grpc import SyncHotelServiceStub
from hotel_cloud_service.content_sync.content_sync_pb2 import SyncHotelRequest

inventory_logger = Logger(logger="inventoryLogger")
api_logger = Logger(logger='inventoryAPILogger')

certificate_file = settings.PROJECT_PATH + '/goibibo_inventory/settings/san-aws-ecs-mmt.crt'

class HotelCloudContentClient(object):
    
    def __init__(self):
        self.host = settings.HTLCLD_CONTENT_SERVICE_URL
        self.server_port = settings.HTLCLD_CONTENT_SERVICE_PORT

        if settings.HOST in settings.PROD_HOSTS:
            with open(certificate_file, 'rb') as f:
                trusted_certs = f.read()

            credentials = grpc.ssl_channel_credentials(root_certificates=trusted_certs)
            self.channel = grpc.secure_channel(
                '{}:{}'.format(self.host, self.server_port), credentials=credentials,
                options=(('grpc.enable_http_proxy', 0),
                         ('grpc.client_idle_timeout_ms', 5000)))
        else:
            self.channel = grpc.insecure_channel(
                '{}:{}'.format(self.host, self.server_port), 
                options=(('grpc.enable_http_proxy', 0),)
            )
        self.hotel_host_mapping_stub = HotelHostMappingServiceStub(self.channel)
        self.sync_hotel_service_stub = SyncHotelServiceStub(self.channel)
    
    def assign_hotels_to_users(self, payload, data_dict):
        """
        Assign or unassign hotels to users using gRPC.

        Args:
            payload: Dictionary containing hotel user assignment data
            data_dict: Original data dictionary for logging

        Returns:
            dict: Response from the service
        """
        response = {}
        try:
            userid = data_dict.get('user_id', '0')
            requestid = data_dict.get('request_id', str(uuid.uuid4()))
            
            json_request = json.dumps(payload)
            inventory_logger.info(
                message='AssignHotelsToUsers request hit: %s' % str(json_request),
                log_type='ingoibibo', 
                bucket='bulk_uploader', 
                stage='assign_hotels_to_users'
            )

            metadata = (
                ('userid', userid), 
                ('requestid', requestid), 
                ('clientip', '0.0.0.0'),
                ('source', 'ingoadmin'), 
                ('language', 'eng'), 
                ('country', 'in'),
                ('platform', 'admin'), 
                ('x-calling-service', 'ingo-web')
            )

            # Create proto request
            proto_request = Parse(json_request, AssignHotelsToUsersRequest())
            
            # Call gRPC service
            resp_proto = self.hotel_host_mapping_stub.AssignHotelsToUsers(
                proto_request, 
                metadata=metadata
            )

            response = protobuf_to_dict(resp_proto)
            inventory_logger.info(
                message='AssignHotelsToUsers response received: %s' % str(response),
                log_type='ingoibibo', 
                bucket='bulk_uploader', 
                stage='assign_hotels_to_users'
            )

        except Exception as e:
            api_logger.critical(
                message='AssignHotelsToUsers failed with Exception {}, payload: {}'.format(
                    str(e), payload
                ),
                log_type='ingoibibo', 
                bucket='bulk_uploader', 
                stage='assign_hotels_to_users'
            )
            raise e

        return response

    def manage_hotel_sync(self, payload, data_dict):
        """
        Sync hotel data using gRPC.

        Args:
            payload: Dictionary containing hotel sync data
            data_dict: Original data dictionary for logging

        Returns:
            dict: Response from the service
        """
        response = {}
        try:
            userid = data_dict.get('user_id', '0')
            requestid = data_dict.get('request_id', str(uuid.uuid4()))
            
            json_request = json.dumps(payload)
            inventory_logger.info(
                message='SyncHotel request hit: %s' % str(json_request),
                log_type='ingoibibo', 
                bucket='bulk_uploader', 
                stage='manage_hotel_sync'
            )

            metadata = (
                ('userid', userid), 
                ('requestid', requestid), 
                ('clientip', '0.0.0.0'),
                ('source', 'ingoadmin'), 
                ('language', 'eng'), 
                ('country', 'in'),
                ('platform', 'admin'), 
                ('x-calling-service', 'ingo-web')
            )

            # Create proto request
            proto_request = Parse(json_request, SyncHotelRequest())
            
            # Call gRPC service
            resp_proto = self.sync_hotel_service_stub.SyncHotel(
                proto_request, 
                metadata=metadata
            )

            response = protobuf_to_dict(resp_proto)
            inventory_logger.info(
                message='SyncHotel response received: %s' % str(response),
                log_type='ingoibibo', 
                bucket='bulk_uploader', 
                stage='manage_hotel_sync'
            )

        except Exception as e:
            api_logger.critical(
                message='SyncHotel failed with Exception {}, payload: {}'.format(
                    str(e), payload
                ),
                log_type='ingoibibo', 
                bucket='bulk_uploader', 
                stage='manage_hotel_sync'
            )
            raise e

        return response 