syntax = "proto3";

package contentSync;
option go_package = "pkg/generated_proto_stubs/contentSync";

service SyncHotelService{
  rpc SyncHotel(SyncHotelRequest) returns (SyncHotelResponse);
}

message SyncHotelRequest {
  string mmtHotelId = 1;
  string ingoHotelId = 2;
}

message SyncHotelResponse {
  ErrorWarningDetail error = 1;
  HotelDataDetail data = 2;
}

message HotelDataDetail {
  string hotelCloudHotelId = 1;
}

message ErrorWarningDetail {
  string type = 1;
  string code = 2;
  repeated Field field = 3;
  string message = 4;
}

message Field {
  string name = 1;
  string value = 2;
  string Message = 3;
} 