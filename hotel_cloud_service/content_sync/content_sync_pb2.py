# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: content_sync.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='content_sync.proto',
  package='contentSync',
  syntax='proto3',
  serialized_pb=_b('\n\x12\x63ontent_sync.proto\x12\x0b\x63ontentSync\";\n\x10SyncHotelRequest\x12\x12\n\nmmtHotelId\x18\x01 \x01(\t\x12\x13\n\x0bingoHotelId\x18\x02 \x01(\t\"o\n\x11SyncHotelResponse\x12.\n\x05\x65rror\x18\x01 \x01(\x0b\x32\x1f.contentSync.ErrorWarningDetail\x12*\n\x04\x64\x61ta\x18\x02 \x01(\x0b\x32\x1c.contentSync.HotelDataDetail\",\n\x0fHotelDataDetail\x12\x19\n\x11hotelCloudHotelId\x18\x01 \x01(\t\"d\n\x12\x45rrorWarningDetail\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\x0c\n\x04\x63ode\x18\x02 \x01(\t\x12!\n\x05\x66ield\x18\x03 \x03(\x0b\x32\x12.contentSync.Field\x12\x0f\n\x07message\x18\x04 \x01(\t\"5\n\x05\x46ield\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\x12\x0f\n\x07Message\x18\x03 \x01(\t2^\n\x10SyncHotelService\x12J\n\tSyncHotel\x12\x1d.contentSync.SyncHotelRequest\x1a\x1e.contentSync.SyncHotelResponseB\'Z%pkg/generated_proto_stubs/contentSyncb\x06proto3')
)




_SYNCHOTELREQUEST = _descriptor.Descriptor(
  name='SyncHotelRequest',
  full_name='contentSync.SyncHotelRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='mmtHotelId', full_name='contentSync.SyncHotelRequest.mmtHotelId', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='ingoHotelId', full_name='contentSync.SyncHotelRequest.ingoHotelId', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=35,
  serialized_end=94,
)


_SYNCHOTELRESPONSE = _descriptor.Descriptor(
  name='SyncHotelResponse',
  full_name='contentSync.SyncHotelResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='error', full_name='contentSync.SyncHotelResponse.error', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='data', full_name='contentSync.SyncHotelResponse.data', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=96,
  serialized_end=207,
)


_HOTELDATADETAIL = _descriptor.Descriptor(
  name='HotelDataDetail',
  full_name='contentSync.HotelDataDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='hotelCloudHotelId', full_name='contentSync.HotelDataDetail.hotelCloudHotelId', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=209,
  serialized_end=253,
)


_ERRORWARNINGDETAIL = _descriptor.Descriptor(
  name='ErrorWarningDetail',
  full_name='contentSync.ErrorWarningDetail',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='contentSync.ErrorWarningDetail.type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='code', full_name='contentSync.ErrorWarningDetail.code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='field', full_name='contentSync.ErrorWarningDetail.field', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='message', full_name='contentSync.ErrorWarningDetail.message', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=255,
  serialized_end=355,
)


_FIELD = _descriptor.Descriptor(
  name='Field',
  full_name='contentSync.Field',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='contentSync.Field.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='value', full_name='contentSync.Field.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='Message', full_name='contentSync.Field.Message', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=357,
  serialized_end=410,
)

_SYNCHOTELRESPONSE.fields_by_name['error'].message_type = _ERRORWARNINGDETAIL
_SYNCHOTELRESPONSE.fields_by_name['data'].message_type = _HOTELDATADETAIL
_ERRORWARNINGDETAIL.fields_by_name['field'].message_type = _FIELD
DESCRIPTOR.message_types_by_name['SyncHotelRequest'] = _SYNCHOTELREQUEST
DESCRIPTOR.message_types_by_name['SyncHotelResponse'] = _SYNCHOTELRESPONSE
DESCRIPTOR.message_types_by_name['HotelDataDetail'] = _HOTELDATADETAIL
DESCRIPTOR.message_types_by_name['ErrorWarningDetail'] = _ERRORWARNINGDETAIL
DESCRIPTOR.message_types_by_name['Field'] = _FIELD
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

SyncHotelRequest = _reflection.GeneratedProtocolMessageType('SyncHotelRequest', (_message.Message,), dict(
  DESCRIPTOR = _SYNCHOTELREQUEST,
  __module__ = 'content_sync_pb2'
  # @@protoc_insertion_point(class_scope:contentSync.SyncHotelRequest)
  ))
_sym_db.RegisterMessage(SyncHotelRequest)

SyncHotelResponse = _reflection.GeneratedProtocolMessageType('SyncHotelResponse', (_message.Message,), dict(
  DESCRIPTOR = _SYNCHOTELRESPONSE,
  __module__ = 'content_sync_pb2'
  # @@protoc_insertion_point(class_scope:contentSync.SyncHotelResponse)
  ))
_sym_db.RegisterMessage(SyncHotelResponse)

HotelDataDetail = _reflection.GeneratedProtocolMessageType('HotelDataDetail', (_message.Message,), dict(
  DESCRIPTOR = _HOTELDATADETAIL,
  __module__ = 'content_sync_pb2'
  # @@protoc_insertion_point(class_scope:contentSync.HotelDataDetail)
  ))
_sym_db.RegisterMessage(HotelDataDetail)

ErrorWarningDetail = _reflection.GeneratedProtocolMessageType('ErrorWarningDetail', (_message.Message,), dict(
  DESCRIPTOR = _ERRORWARNINGDETAIL,
  __module__ = 'content_sync_pb2'
  # @@protoc_insertion_point(class_scope:contentSync.ErrorWarningDetail)
  ))
_sym_db.RegisterMessage(ErrorWarningDetail)

Field = _reflection.GeneratedProtocolMessageType('Field', (_message.Message,), dict(
  DESCRIPTOR = _FIELD,
  __module__ = 'content_sync_pb2'
  # @@protoc_insertion_point(class_scope:contentSync.Field)
  ))
_sym_db.RegisterMessage(Field)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('Z%pkg/generated_proto_stubs/contentSync'))

_SYNCHOTELSERVICE = _descriptor.ServiceDescriptor(
  name='SyncHotelService',
  full_name='contentSync.SyncHotelService',
  file=DESCRIPTOR,
  index=0,
  options=None,
  serialized_start=412,
  serialized_end=506,
  methods=[
  _descriptor.MethodDescriptor(
    name='SyncHotel',
    full_name='contentSync.SyncHotelService.SyncHotel',
    index=0,
    containing_service=None,
    input_type=_SYNCHOTELREQUEST,
    output_type=_SYNCHOTELRESPONSE,
    options=None,
  ),
])
_sym_db.RegisterServiceDescriptor(_SYNCHOTELSERVICE)

DESCRIPTOR.services_by_name['SyncHotelService'] = _SYNCHOTELSERVICE

# @@protoc_insertion_point(module_scope)