# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

import content_sync_pb2 as content__sync__pb2


class SyncHotelServiceStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.SyncHotel = channel.unary_unary(
        '/contentSync.SyncHotelService/SyncHotel',
        request_serializer=content__sync__pb2.SyncHotelRequest.SerializeToString,
        response_deserializer=content__sync__pb2.SyncHotelResponse.FromString,
        )


class SyncHotelServiceServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def SyncHotel(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_SyncHotelServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'SyncHotel': grpc.unary_unary_rpc_method_handler(
          servicer.SyncHotel,
          request_deserializer=content__sync__pb2.SyncHotelRequest.FromString,
          response_serializer=content__sync__pb2.SyncHotelResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'contentSync.SyncHotelService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))