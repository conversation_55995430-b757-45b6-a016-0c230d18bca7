# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: upsert_prebuy_config.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import common_pb2 as common__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='upsert_prebuy_config.proto',
  package='prebuyconfig',
  syntax='proto3',
  serialized_pb=_b('\n\x1aupsert_prebuy_config.proto\x12\x0cprebuyconfig\x1a\x0c\x63ommon.proto\"\x8d\x01\n\x19UpsertPrebuyConfigRequest\x12\x16\n\x0e\x63orrelationKey\x18\x01 \x01(\t\x12\x0f\n\x07hotelId\x18\x02 \x01(\t\x12\x31\n\rprebuyConfigs\x18\x03 \x03(\x0b\x32\x1a.prebuyconfig.PrebuyConfig\x12\x14\n\x0c\x61\x63tionSource\x18\x04 \x01(\t\"\xb3\x03\n\x0cPrebuyConfig\x12\x32\n\x0eprebuyEntities\x18\x01 \x03(\x0b\x32\x1a.prebuyconfig.PrebuyEntity\x12\x17\n\x0ftotalRoomNights\x18\x02 \x01(\r\x12\r\n\x05price\x18\x03 \x01(\x02\x12\x19\n\x11orderValidityDays\x18\x04 \x01(\r\x12\x14\n\x0ctriggerOrder\x18\x05 \x01(\x08\x12\x14\n\x0creorderPoint\x18\x06 \x01(\x05\x12\x16\n\x0e\x64\x61ilySaleLimit\x18\x07 \x01(\r\x12\x1a\n\x12prebuyContractType\x18\x08 \x01(\t\x12\x14\n\x0c\x61pprovalType\x18\t \x01(\r\x12\x16\n\x0e\x61pprovalEmails\x18\n \x03(\t\x12\x10\n\x08isActive\x18\x0b \x01(\x08\x12\x10\n\x08\x63onfigId\x18\x0c \x01(\x03\x12\x12\n\napprovedBy\x18\r \x01(\t\x12,\n\norderDates\x18\x0e \x01(\x0b\x32\x18.prebuyconfig.OrderDates\x12\x19\n\x11maxGuestOccupancy\x18\x0f \x01(\r\x12\x1d\n\x15\x61utoApprovalInMinutes\x18\x10 \x01(\x05\"\xa1\x01\n\x1aUpsertPrebuyConfigResponse\x12\x16\n\x0e\x63orrelationKey\x18\x01 \x01(\t\x12\x0f\n\x07success\x18\x02 \x01(\x08\x12\"\n\x05\x65rror\x18\x03 \x01(\x0b\x32\x13.prebuyconfig.Error\x12\x36\n\x0c\x63onfigStatus\x18\x04 \x03(\x0b\x32 .prebuyconfig.PrebuyConfigStatus\"\xb5\x01\n\x12PrebuyConfigStatus\x12\x10\n\x08\x63onfigId\x18\x01 \x01(\x03\x12\x14\n\x0c\x63onfigStatus\x18\x02 \x01(\t\x12\x13\n\x0borderStatus\x18\x03 \x01(\t\x12\x1b\n\x13rateplanFlagUpdated\x18\x04 \x01(\x08\x12\x30\n\x0cprebuyConfig\x18\x05 \x01(\x0b\x32\x1a.prebuyconfig.PrebuyConfig\x12\x13\n\x0borderNumber\x18\x06 \x01(\tb\x06proto3')
  ,
  dependencies=[common__pb2.DESCRIPTOR,])




_UPSERTPREBUYCONFIGREQUEST = _descriptor.Descriptor(
  name='UpsertPrebuyConfigRequest',
  full_name='prebuyconfig.UpsertPrebuyConfigRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='correlationKey', full_name='prebuyconfig.UpsertPrebuyConfigRequest.correlationKey', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hotelId', full_name='prebuyconfig.UpsertPrebuyConfigRequest.hotelId', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='prebuyConfigs', full_name='prebuyconfig.UpsertPrebuyConfigRequest.prebuyConfigs', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='actionSource', full_name='prebuyconfig.UpsertPrebuyConfigRequest.actionSource', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=59,
  serialized_end=200,
)


_PREBUYCONFIG = _descriptor.Descriptor(
  name='PrebuyConfig',
  full_name='prebuyconfig.PrebuyConfig',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='prebuyEntities', full_name='prebuyconfig.PrebuyConfig.prebuyEntities', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='totalRoomNights', full_name='prebuyconfig.PrebuyConfig.totalRoomNights', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='price', full_name='prebuyconfig.PrebuyConfig.price', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orderValidityDays', full_name='prebuyconfig.PrebuyConfig.orderValidityDays', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='triggerOrder', full_name='prebuyconfig.PrebuyConfig.triggerOrder', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='reorderPoint', full_name='prebuyconfig.PrebuyConfig.reorderPoint', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='dailySaleLimit', full_name='prebuyconfig.PrebuyConfig.dailySaleLimit', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='prebuyContractType', full_name='prebuyconfig.PrebuyConfig.prebuyContractType', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approvalType', full_name='prebuyconfig.PrebuyConfig.approvalType', index=8,
      number=9, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approvalEmails', full_name='prebuyconfig.PrebuyConfig.approvalEmails', index=9,
      number=10, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='isActive', full_name='prebuyconfig.PrebuyConfig.isActive', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='configId', full_name='prebuyconfig.PrebuyConfig.configId', index=11,
      number=12, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='approvedBy', full_name='prebuyconfig.PrebuyConfig.approvedBy', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orderDates', full_name='prebuyconfig.PrebuyConfig.orderDates', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='maxGuestOccupancy', full_name='prebuyconfig.PrebuyConfig.maxGuestOccupancy', index=14,
      number=15, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='autoApprovalInMinutes', full_name='prebuyconfig.PrebuyConfig.autoApprovalInMinutes', index=15,
      number=16, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=203,
  serialized_end=638,
)


_UPSERTPREBUYCONFIGRESPONSE = _descriptor.Descriptor(
  name='UpsertPrebuyConfigResponse',
  full_name='prebuyconfig.UpsertPrebuyConfigResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='correlationKey', full_name='prebuyconfig.UpsertPrebuyConfigResponse.correlationKey', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='success', full_name='prebuyconfig.UpsertPrebuyConfigResponse.success', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='error', full_name='prebuyconfig.UpsertPrebuyConfigResponse.error', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='configStatus', full_name='prebuyconfig.UpsertPrebuyConfigResponse.configStatus', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=641,
  serialized_end=802,
)


_PREBUYCONFIGSTATUS = _descriptor.Descriptor(
  name='PrebuyConfigStatus',
  full_name='prebuyconfig.PrebuyConfigStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='configId', full_name='prebuyconfig.PrebuyConfigStatus.configId', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='configStatus', full_name='prebuyconfig.PrebuyConfigStatus.configStatus', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orderStatus', full_name='prebuyconfig.PrebuyConfigStatus.orderStatus', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='rateplanFlagUpdated', full_name='prebuyconfig.PrebuyConfigStatus.rateplanFlagUpdated', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='prebuyConfig', full_name='prebuyconfig.PrebuyConfigStatus.prebuyConfig', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='orderNumber', full_name='prebuyconfig.PrebuyConfigStatus.orderNumber', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=805,
  serialized_end=986,
)

_UPSERTPREBUYCONFIGREQUEST.fields_by_name['prebuyConfigs'].message_type = _PREBUYCONFIG
_PREBUYCONFIG.fields_by_name['prebuyEntities'].message_type = common__pb2._PREBUYENTITY
_PREBUYCONFIG.fields_by_name['orderDates'].message_type = common__pb2._ORDERDATES
_UPSERTPREBUYCONFIGRESPONSE.fields_by_name['error'].message_type = common__pb2._ERROR
_UPSERTPREBUYCONFIGRESPONSE.fields_by_name['configStatus'].message_type = _PREBUYCONFIGSTATUS
_PREBUYCONFIGSTATUS.fields_by_name['prebuyConfig'].message_type = _PREBUYCONFIG
DESCRIPTOR.message_types_by_name['UpsertPrebuyConfigRequest'] = _UPSERTPREBUYCONFIGREQUEST
DESCRIPTOR.message_types_by_name['PrebuyConfig'] = _PREBUYCONFIG
DESCRIPTOR.message_types_by_name['UpsertPrebuyConfigResponse'] = _UPSERTPREBUYCONFIGRESPONSE
DESCRIPTOR.message_types_by_name['PrebuyConfigStatus'] = _PREBUYCONFIGSTATUS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

UpsertPrebuyConfigRequest = _reflection.GeneratedProtocolMessageType('UpsertPrebuyConfigRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPSERTPREBUYCONFIGREQUEST,
  __module__ = 'upsert_prebuy_config_pb2'
  # @@protoc_insertion_point(class_scope:prebuyconfig.UpsertPrebuyConfigRequest)
  ))
_sym_db.RegisterMessage(UpsertPrebuyConfigRequest)

PrebuyConfig = _reflection.GeneratedProtocolMessageType('PrebuyConfig', (_message.Message,), dict(
  DESCRIPTOR = _PREBUYCONFIG,
  __module__ = 'upsert_prebuy_config_pb2'
  # @@protoc_insertion_point(class_scope:prebuyconfig.PrebuyConfig)
  ))
_sym_db.RegisterMessage(PrebuyConfig)

UpsertPrebuyConfigResponse = _reflection.GeneratedProtocolMessageType('UpsertPrebuyConfigResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPSERTPREBUYCONFIGRESPONSE,
  __module__ = 'upsert_prebuy_config_pb2'
  # @@protoc_insertion_point(class_scope:prebuyconfig.UpsertPrebuyConfigResponse)
  ))
_sym_db.RegisterMessage(UpsertPrebuyConfigResponse)

PrebuyConfigStatus = _reflection.GeneratedProtocolMessageType('PrebuyConfigStatus', (_message.Message,), dict(
  DESCRIPTOR = _PREBUYCONFIGSTATUS,
  __module__ = 'upsert_prebuy_config_pb2'
  # @@protoc_insertion_point(class_scope:prebuyconfig.PrebuyConfigStatus)
  ))
_sym_db.RegisterMessage(PrebuyConfigStatus)


# @@protoc_insertion_point(module_scope)
