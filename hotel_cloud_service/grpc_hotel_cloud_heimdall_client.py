import traceback
import grpc

from hotel_cloud_service.group.group_pb2_grpc import GroupServiceStub
from hotel_cloud_service.permission.permission_pb2_grpc import PermissionServiceStub
from hotel_cloud_service.screenpermission.screen_permission_pb2_grpc import ScreenPermissionServiceStub
from hotel_cloud_service.userdeactivation.userdeactivation_pb2_grpc import UserManagementServiceStub
from hotel_cloud_service.screenpermission.screen_permission_pb2 import UpsertScreenRegistryRequest as UpsertScreenRegistryRequest
from hotel_cloud_service.screenpermission.screen_permission_pb2 import (
    AssignScreenToUserRequest,
    AssignScreenToUserResponse,
    AssignScreenToGroupRequest,
    AssignScreenToGroupResponse,
    ApiRegistryRequest,
    ScreenApiMapperRequest
)
from hotel_cloud_service.userdeactivation.userdeactivation_pb2 import DeactivateUserRequest
from hotel_cloud_service.userdeactivation.userdeactivation_pb2_grpc import UserManagementServiceStub
from hotel_cloud_service.permission.permission_pb2 import UpsertAuthPermissionRequest
from hotel_cloud_service.permission.permission_pb2 import AssignPermissionToUserRequest as AssignPermissionToUserRequest
from hotel_cloud_service.permission.permission_pb2 import AssignPermissionToGroupRequest as AssignPermissionToGroupRequest
from hotel_cloud_service.group.group_pb2 import UpsertAuthGroupRequest as UpsertAuthGroupRequest
from hotel_cloud_service.group.group_pb2 import UpsertAuthUserGroupRequest as UpsertAuthUserGroupRequest
from proto_utils.dict_to_proto3_to_dict import protobuf_to_dict
from utils.logger import Logger
from google.protobuf.json_format import *
from django.conf import settings
import json
import uuid
from rest_framework.authtoken.models import Token

api_logger = Logger(logger='inventoryAPILogger')
inventory_logger = Logger(logger='inventoryLogger')

certificate_file = settings.PROJECT_PATH + '/goibibo_inventory/settings/san-aws-ecs-mmt.crt'


class HotelCloudHeimdallClient:
    def __init__(self):
        self.host = settings.HTLCLD_HEIMDALL_HOST
        self.server_port = settings.HTLCLD_HEIMDALL_PORT

        if settings.HOST in settings.PROD_HOSTS:
            with open(certificate_file, 'rb') as f:
                trusted_certs = f.read()

            credentials = grpc.ssl_channel_credentials(root_certificates=trusted_certs)
            self.channel = grpc.secure_channel(
                '{}:{}'.format(self.host, self.server_port), credentials=credentials,
                options=(('grpc.enable_http_proxy', 0),
                         ('grpc.client_idle_timeout_ms', 5000)))
        else:
            self.channel = grpc.insecure_channel(
                '{}:{}'.format(self.host, self.server_port), options=(('grpc.enable_http_proxy', 0),))

        # bind the client to the server channel
        self.permission_service_stub = PermissionServiceStub(self.channel)
        self.upsert_group_stub = GroupServiceStub(self.channel)
        self.screen_permission_service_stub = ScreenPermissionServiceStub(self.channel)
        self.user_management_service_stub = UserManagementServiceStub(self.channel)

    def upsert_permission(self, payload, data_dict):
        """
        Upsert permission using gRPC

        Args:
            payload: Dictionary containing permission data
            data_dict: Original data dictionary for logging

        Returns:
            dict: Response from the service
        """
        response = {}
        try:
            userid = data_dict.get('user_id', '0')
            # Log request payload before JSON conversion
            inventory_logger.info(
                message='UpsertAuthPermission payload before conversion: %s' % str(payload),
                log_type='ingoibibo', bucket='bulk_uploader', stage='upsert_permission'
            )

            # authToken = "Token " + Token.objects.get(user=userid).key
            json_request = json.dumps(payload)
            proto_request = Parse(json_request, UpsertAuthPermissionRequest())
            metadata = (("userid", userid),("source", "ingoadmin"),("language", "eng"),("country", "in"),("platform", "admin"),("x-calling-service", "ingo-web"))
            resp_proto = self.permission_service_stub.UpsertAuthPermission(proto_request, metadata=metadata)
            response = protobuf_to_dict(resp_proto)

            # Log response after conversion
            inventory_logger.info(
                message='UpsertAuthPermission response: %s' % str(response),
                log_type='ingoibibo', bucket='bulk_uploader', stage='upsert_permission'
            )

        except Exception as e:
            correlation_key = payload.get('correlationKey', 'UNKNOWN')
            # Fix string formatting issue by using %s instead of {} format method
            api_logger.critical(
                message='UpsertAuthPermission got failed with Exception %s, correlation: %s' % (str(e), correlation_key),
                log_type='ingoibibo', bucket='bulk_uploader', stage='upsert_permission'
            )
            raise e

        return response

    def upsert_group(self, payload, data_dict):
        """
        Upsert a group using gRPC based on UpsertAuthGroupRequest.

        Args:
            payload: Dictionary containing group upsert data matching
                     UpsertAuthGroupRequest fields (Correlation_key, Name, IsActive).
            data_dict: Original data dictionary for logging.

        Returns:
            dict: Response from the service.
        """
        response = {}
        try:
            userid = data_dict.get('user_id', '0')
            # authToken = "Token " + Token.objects.get(user=userid).key
            # Log request payload before JSON conversion
            inventory_logger.info(message='UpsertAuthGroup payload before conversion: %s' % str(payload),
                                log_type='ingoibibo', bucket='bulk_uploader', stage='upsert_group')

            json_request = json.dumps(payload)
            proto_request = Parse(json_request, UpsertAuthGroupRequest())

            metadata = (("userid", userid),("source", "ingoadmin"),("language", "eng"),("country", "in"),("platform", "admin"),("x-calling-service", "ingo-web"))

            resp_proto = self.upsert_group_stub.UpsertAuthGroup(proto_request, metadata=metadata)
            response = protobuf_to_dict(resp_proto)

            inventory_logger.info(message='UpsertAuthGroup response: %s' % str(response),
                                log_type='ingoibibo', bucket='bulk_uploader', stage='upsert_group')

        except Exception as e:
            correlation_key = payload.get('Correlation_key', 'UNKNOWN')
            api_logger.critical(message='UpsertAuthGroup failed with Exception {0}, correlation: {1}'.format(str(e), correlation_key),
                                log_type='ingoibibo', bucket='bulk_uploader', stage='upsert_group')
            response = {'Success': False, 'Error': {'message': str(e)}}

        return response

    def upsert_user_group(self, payload, data_dict):
        """
        Upsert a user-group association using gRPC based on UpsertAuthUserGroupRequest.

        Args:
            payload: Dictionary containing user-group upsert data matching
                     UpsertAuthUserGroupRequest fields (Correlation_key, Name, UserID, IsActive).
            data_dict: Original data dictionary for logging.

        Returns:
            dict: Response from the service.
        """
        response = {}
        try:
            userid = data_dict.get('user_id', '0') # Assuming 'user_id' in data_dict is the admin performing the action
            # authToken = "Token " + Token.objects.get(user=userid).key
            json_request = json.dumps(payload)
            
            # Log request payload
            inventory_logger.info(
                message='UpsertAuthUserGroup request payload: %s' % str(payload),
                log_type='ingoibibo', bucket='bulk_uploader', stage='upsert_user_group'
            )
            
            # Ensure payload keys match UpsertAuthUserGroupRequest proto fields
            proto_request = Parse(json_request, UpsertAuthUserGroupRequest())

            metadata = (("userid", userid),("source", "ingoadmin"),("language", "eng"),("country", "in"),("platform", "admin"),("x-calling-service", "ingo-web"))

            # Call the correct RPC method defined in auth.proto
            resp_proto = self.upsert_group_stub.UpsertAuthUserGroup(proto_request, metadata=metadata)
            response = protobuf_to_dict(resp_proto)
            
            # Log response
            inventory_logger.info(
                message='UpsertAuthUserGroup response: %s' % str(response),
                log_type='ingoibibo', bucket='bulk_uploader', stage='upsert_user_group'
            )

        except Exception as e:
            # Log using the correct correlation key from the payload
            correlation_key = payload.get('Correlation_key', 'UNKNOWN')
            api_logger.critical(message='UpsertAuthUserGroup failed with Exception %s, correlation: %s' % (str(e), correlation_key),
                                log_type='ingoibibo', bucket='bulk_uploader', stage='upsert_user_group')
            # It's often better to return a structured error or re-raise a custom exception
            # For now, returning a dictionary indicating failure
            response = {'success': False, 'error': {'msg': str(e)}}
            # Depending on desired behavior, you might want to re-raise: raise e

        return response

    def manage_permission_assignment(self, payload, data_dict):
        """
        Assign or unassign permissions to users or groups using gRPC.

        Args:
            payload: Dictionary containing permission assignment data
            data_dict: Original data dictionary for logging

        Returns:
            dict: Response from the service
        """
        response = {}
        try:
            userid = data_dict.get('user_id', '0')
            # authToken = "Token " + Token.objects.get(user=userid).key

            json_request = json.dumps(payload)
            inventory_logger.info(
                message='ManagePermissionAssignment request hit: %s' % str(json_request),
                log_type='ingoibibo', bucket='bulk_uploader', stage='manage_permission_assignment'
            )

            metadata = (("userid", userid),("source", "ingoadmin"),("language", "eng"),("country", "in"),("platform", "admin"),("x-calling-service", "ingo-web"))

            # Choose appropriate request type based on entity type
            if data_dict.get('ContentType', '').upper() == 'USER':
                proto_request = Parse(json_request, AssignPermissionToUserRequest())
                resp_proto = self.permission_service_stub.AssignPermissionToUser(proto_request, metadata=metadata)
            else:  # GROUP
                proto_request = Parse(json_request, AssignPermissionToGroupRequest())
                resp_proto = self.permission_service_stub.AssignPermissionToGroup(proto_request, metadata=metadata)

            response = protobuf_to_dict(resp_proto)
            inventory_logger.info(
                message='ManagePermissionAssignment response received: %s' % str(response),
                log_type='ingoibibo', bucket='bulk_uploader', stage='manage_permission_assignment'
            )

        except Exception as e:
            api_logger.critical(message='ManagePermissionAssignment got failed with Exception {}, correlation: {}'.format(str(e), payload.get('correlationKey')),
                               log_type='ingoibibo', bucket='bulk_uploader', stage='manage_permission_assignment')
            raise e

        return response

    def upsert_screen_registry(self, payload, data_dict):
        """
        Upsert a screen registry entry using gRPC.

        Args:
            payload: Dictionary containing screen registry data matching UpsertScreenRegistryRequest fields.
            data_dict: Original data dictionary, used for extracting metadata like user_id.

        Returns:
            dict: Response from the gRPC service, converted to a dictionary.
        """
        response = {}
        try:
            userid = data_dict.get('user_id', '0')
            json_request = json.dumps(payload)
            requestid = str(uuid.uuid4())
            inventory_logger.info(
                message='UpsertScreenRegistry request payload: %s' % str(json_request),
                log_type='ingoibibo', bucket='bulk_uploader', stage='upsert_screen_registry'
            )

            proto_request = Parse(json_request, UpsertScreenRegistryRequest())
            metadata = (("userid", userid),("requestid", requestid),("clientip", "0.0.0.0"),
                        ("source", "ingoadmin"), ("language", "eng"), ("country", "in"),
                        ("platform", "admin"), ("x-calling-service", "ingo-admin"))

            resp_proto = self.screen_permission_service_stub.UpsertScreenRegistry(proto_request, metadata=metadata)
            response = protobuf_to_dict(resp_proto)

            inventory_logger.info(
                message='UpsertScreenRegistry response: %s' % str(response),
                log_type='ingoibibo', bucket='bulk_uploader', stage='upsert_screen_registry'
            )

        except Exception as e:
            screen_name_for_log = payload.get('screenName', 'UNKNOWN_SCREEN')
            client_for_log = payload.get('client', 'UNKNOWN_CLIENT')
            api_logger.critical(
                message='UpsertScreenRegistry failed for screen: %s, client: %s. Exception: %s' % (screen_name_for_log, client_for_log, str(e)),
                log_type='ingoibibo', bucket='bulk_uploader', stage='upsert_screen_registry'
            )
            # Re-raise the exception to be caught by the calling save function in external_functions3
            raise e

        return response

    def manage_screen_assignment(self, payload, data_dict, entity_type):
        response = {}
        try:
            userid = data_dict.get('user_id', '0')
            json_request = json.dumps(payload)
            requestid = str(uuid.uuid4()) # Added for consistency, can be part of metadata or logged
            inventory_logger.info(
                message='ManageScreenAssignment request hit (request_id: %s): %s' % (requestid, str(json_request)),
                log_type='ingoibibo', bucket='bulk_uploader', stage='manage_screen_assignment'
            )

            metadata = (("userid", userid), ("requestid", requestid), ("clientip", "0.0.0.0"),
                        ("source", "ingoadmin"), ("language", "eng"), ("country", "in"),
                        ("platform", "admin"), ("x-calling-service", "ingo-admin"))

            if entity_type.upper() == 'USER':
                proto_request = Parse(json_request, AssignScreenToUserRequest())
                resp_proto = self.screen_permission_service_stub.AssignScreenToUser(proto_request, metadata=metadata)
            elif entity_type.upper() == 'GROUP':
                proto_request = Parse(json_request, AssignScreenToGroupRequest())
                resp_proto = self.screen_permission_service_stub.AssignScreenToGroup(proto_request, metadata=metadata)
            else:
                # Should not happen if validated upstream, but good to have a safeguard
                raise ValueError("Invalid entity_type provided to manage_screen_assignment: %s" % entity_type)

            response = protobuf_to_dict(resp_proto)
            inventory_logger.info(
                message='ManageScreenAssignment response received (request_id: %s): %s' % (requestid, str(response)),
                log_type='ingoibibo', bucket='bulk_uploader', stage='manage_screen_assignment'
            )

        except Exception as e:
            correlation_key = payload.get('correlationKey', 'UNKNOWN')
            api_logger.critical(
                message='ManageScreenAssignment failed with Exception %s, correlation_key: %s, request_id: %s' % (str(e), correlation_key, requestid),
                log_type='ingoibibo', bucket='bulk_uploader', stage='manage_screen_assignment'
            )
            raise e

        return response

    def upsert_api_registry(self, payload, data_dict):
        """
        Upsert an API registry entry using gRPC.
        Args:
            payload: Dictionary containing API registry data matching ApiRegistryRequest fields.
            data_dict: Original data dictionary, used for extracting metadata like user_id.
        Returns:
            dict: Response from the gRPC service, converted to a dictionary.
        """
        response = {}
        try:
            userid = data_dict.get('user_id', '0')
            requestid = str(uuid.uuid4())
            json_request = json.dumps(payload)
            inventory_logger.info(
                message='UpsertApiRegistry request hit: %s' % str(json_request),
                log_type='ingoibibo', bucket='bulk_uploader', stage='upsert_api_registry'
            )
            metadata = (('userid', userid), ('requestid', requestid), ('clientip', '0.0.0.0'),
                        ('source', 'ingoadmin'), ('language', 'eng'), ('country', 'in'),
                        ('platform', 'admin'), ('x-calling-service', 'ingo-admin'))
            proto_request = Parse(json_request, ApiRegistryRequest())
            resp_proto = self.screen_permission_service_stub.UpsertApiRegistry(proto_request, metadata=metadata)
            response = protobuf_to_dict(resp_proto)
            inventory_logger.info(
                message='UpsertApiRegistry response received: %s' % str(response),
                log_type='ingoibibo', bucket='bulk_uploader', stage='upsert_api_registry'
            )
        except Exception as e:
            api_logger.critical(message='UpsertApiRegistry failed with Exception {}, correlation: {}'.format(str(e), payload.get('correlationKey')),
                               log_type='ingoibibo', bucket='bulk_uploader', stage='upsert_api_registry')
            raise e
        return response

    def manage_screen_api_assignment(self, payload, data_dict):
        """
        Assign or unassign an API to a screen using gRPC.
        Args:
            payload: Dictionary containing screen API assignment data
            data_dict: Original data dictionary for logging
        Returns:
            dict: Response from the service
        """
        response = {}
        try:
            userid = data_dict.get('user_id', '0')
            requestid = str(uuid.uuid4())
            json_request = json.dumps(payload)
            inventory_logger.info(
                message='ManageScreenApiAssignment request hit: %s' % str(json_request),
                log_type='ingoibibo', bucket='bulk_uploader', stage='manage_screen_api_assignment'
            )
            metadata = (('userid', userid), ('requestid', requestid), ('clientip', '0.0.0.0'),
                        ('source', 'ingoadmin'), ('language', 'eng'), ('country', 'in'),
                        ('platform', 'admin'), ('x-calling-service', 'ingo-admin'))
            proto_request = Parse(json_request, ScreenApiMapperRequest())
            resp_proto = self.screen_permission_service_stub.UpsertScreenApiMapper(proto_request, metadata=metadata)
            response = protobuf_to_dict(resp_proto)
            inventory_logger.info(
                message='ManageScreenApiAssignment response received: %s' % str(response),
                log_type='ingoibibo', bucket='bulk_uploader', stage='manage_screen_api_assignment'
            )
        except Exception as e:
            api_logger.critical(message='ManageScreenApiAssignment failed with Exception {}, correlation: {}'.format(str(e), payload.get('correlationKey')),
                               log_type='ingoibibo', bucket='bulk_uploader', stage='manage_screen_api_assignment')
            raise e
        return response

    def deactivate_user(self, payload, data_dict):
        """
        Deactivate a user using gRPC.

        Args:
            payload: Dictionary containing user deactivation data
            data_dict: Original data dictionary for logging

        Returns:
            dict: Response from the service
        """
        response = {}
        try:
            userid = data_dict.get('user_id', '0')
            requestid = str(uuid.uuid4())
            
            json_request = json.dumps(payload)
            inventory_logger.info(
                message='DeactivateUser request hit: %s' % str(json_request),
                log_type='ingoibibo', 
                bucket='bulk_uploader', 
                stage='deactivate_user'
            )

            metadata = (('userid', userid), ('requestid', requestid), ('clientip', '0.0.0.0'),
                        ('source', 'ingoadmin'), ('language', 'eng'), ('country', 'in'),
                        ('platform', 'admin'), ('x-calling-service', 'ingo-admin'))

            # Import the request message


            # Create proto request
            proto_request = Parse(json_request, DeactivateUserRequest())
            
            # Call gRPC service
            resp_proto = self.user_management_service_stub.DeactivateUser(
                proto_request, 
                metadata=metadata
            )

            response = protobuf_to_dict(resp_proto)
            inventory_logger.info(
                message='DeactivateUser response received: %s' % str(response),
                log_type='ingoibibo', 
                bucket='bulk_uploader', 
                stage='deactivate_user'
            )

        except Exception as e:
            api_logger.critical(
                message='DeactivateUser failed with Exception {}, payload: {}'.format(
                    str(e), payload
                ),
                log_type='ingoibibo', 
                bucket='bulk_uploader', 
                stage='deactivate_user'
            )
            raise e

        return response
