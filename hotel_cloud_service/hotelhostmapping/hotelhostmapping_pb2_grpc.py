# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
import grpc

import hotelhostmapping_pb2 as hotelhostmapping__pb2


class HotelHostMappingServiceStub(object):
  # missing associated documentation comment in .proto file
  pass

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.AssignHotelsToUsers = channel.unary_unary(
        '/HotelHostMappingService/AssignHotelsToUsers',
        request_serializer=hotelhostmapping__pb2.AssignHotelsToUsersRequest.SerializeToString,
        response_deserializer=hotelhostmapping__pb2.AssignHotelsToUsersResponse.FromString,
        )


class HotelHostMappingServiceServicer(object):
  # missing associated documentation comment in .proto file
  pass

  def AssignHotelsToUsers(self, request, context):
    # missing associated documentation comment in .proto file
    pass
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_HotelHostMappingServiceServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'AssignHotelsToUsers': grpc.unary_unary_rpc_method_handler(
          servicer.AssignHotelsToUsers,
          request_deserializer=hotelhostmapping__pb2.AssignHotelsToUsersRequest.FromString,
          response_serializer=hotelhostmapping__pb2.AssignHotelsToUsersResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'HotelHostMappingService', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))