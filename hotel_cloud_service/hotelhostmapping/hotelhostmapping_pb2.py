# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: hotelhostmapping.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='hotelhostmapping.proto',
  package='',
  syntax='proto3',
  serialized_pb=_b('\n\x16hotelhostmapping.proto\"c\n\x1a\x41ssignHotelsToUsersRequest\x12\x0e\n\x06UserId\x18\x01 \x01(\x03\x12\x12\n\nHotelCodes\x18\x02 \x03(\t\x12\x0e\n\x06\x41\x63tion\x18\x03 \x01(\t\x12\x11\n\tUserEmail\x18\x04 \x01(\t\"q\n\x1b\x41ssignHotelsToUsersResponse\x12\x30\n\x06\x45rrors\x18\x01 \x01(\x0b\x32 .AssignmentStandardErrorResponse\x12\x0f\n\x07Message\x18\x02 \x01(\t\x12\x0f\n\x07Success\x18\x03 \x01(\x08\"u\n\x1f\x41ssignmentStandardErrorResponse\x12\x11\n\terrorCode\x18\x01 \x01(\t\x12\x14\n\x0c\x65rrorMessage\x18\x02 \x01(\t\x12\x11\n\terrorType\x18\x03 \x01(\t\x12\x16\n\x0e\x64isplayMessage\x18\x04 \x01(\t2k\n\x17HotelHostMappingService\x12P\n\x13\x41ssignHotelsToUsers\x12\x1b.AssignHotelsToUsersRequest\x1a\x1c.AssignHotelsToUsersResponseB\x0eZ\x0c/protomodelsb\x06proto3')
)




_ASSIGNHOTELSTOUSERSREQUEST = _descriptor.Descriptor(
  name='AssignHotelsToUsersRequest',
  full_name='AssignHotelsToUsersRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='UserId', full_name='AssignHotelsToUsersRequest.UserId', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='HotelCodes', full_name='AssignHotelsToUsersRequest.HotelCodes', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='Action', full_name='AssignHotelsToUsersRequest.Action', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='UserEmail', full_name='AssignHotelsToUsersRequest.UserEmail', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=26,
  serialized_end=125,
)


_ASSIGNHOTELSTOUSERSRESPONSE = _descriptor.Descriptor(
  name='AssignHotelsToUsersResponse',
  full_name='AssignHotelsToUsersResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='Errors', full_name='AssignHotelsToUsersResponse.Errors', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='Message', full_name='AssignHotelsToUsersResponse.Message', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='Success', full_name='AssignHotelsToUsersResponse.Success', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=127,
  serialized_end=240,
)


_ASSIGNMENTSTANDARDERRORRESPONSE = _descriptor.Descriptor(
  name='AssignmentStandardErrorResponse',
  full_name='AssignmentStandardErrorResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='errorCode', full_name='AssignmentStandardErrorResponse.errorCode', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='errorMessage', full_name='AssignmentStandardErrorResponse.errorMessage', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='errorType', full_name='AssignmentStandardErrorResponse.errorType', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='displayMessage', full_name='AssignmentStandardErrorResponse.displayMessage', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=242,
  serialized_end=359,
)

_ASSIGNHOTELSTOUSERSRESPONSE.fields_by_name['Errors'].message_type = _ASSIGNMENTSTANDARDERRORRESPONSE
DESCRIPTOR.message_types_by_name['AssignHotelsToUsersRequest'] = _ASSIGNHOTELSTOUSERSREQUEST
DESCRIPTOR.message_types_by_name['AssignHotelsToUsersResponse'] = _ASSIGNHOTELSTOUSERSRESPONSE
DESCRIPTOR.message_types_by_name['AssignmentStandardErrorResponse'] = _ASSIGNMENTSTANDARDERRORRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

AssignHotelsToUsersRequest = _reflection.GeneratedProtocolMessageType('AssignHotelsToUsersRequest', (_message.Message,), dict(
  DESCRIPTOR = _ASSIGNHOTELSTOUSERSREQUEST,
  __module__ = 'hotelhostmapping_pb2'
  # @@protoc_insertion_point(class_scope:AssignHotelsToUsersRequest)
  ))
_sym_db.RegisterMessage(AssignHotelsToUsersRequest)

AssignHotelsToUsersResponse = _reflection.GeneratedProtocolMessageType('AssignHotelsToUsersResponse', (_message.Message,), dict(
  DESCRIPTOR = _ASSIGNHOTELSTOUSERSRESPONSE,
  __module__ = 'hotelhostmapping_pb2'
  # @@protoc_insertion_point(class_scope:AssignHotelsToUsersResponse)
  ))
_sym_db.RegisterMessage(AssignHotelsToUsersResponse)

AssignmentStandardErrorResponse = _reflection.GeneratedProtocolMessageType('AssignmentStandardErrorResponse', (_message.Message,), dict(
  DESCRIPTOR = _ASSIGNMENTSTANDARDERRORRESPONSE,
  __module__ = 'hotelhostmapping_pb2'
  # @@protoc_insertion_point(class_scope:AssignmentStandardErrorResponse)
  ))
_sym_db.RegisterMessage(AssignmentStandardErrorResponse)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('Z\014/protomodels'))

_HOTELHOSTMAPPINGSERVICE = _descriptor.ServiceDescriptor(
  name='HotelHostMappingService',
  full_name='HotelHostMappingService',
  file=DESCRIPTOR,
  index=0,
  options=None,
  serialized_start=361,
  serialized_end=468,
  methods=[
  _descriptor.MethodDescriptor(
    name='AssignHotelsToUsers',
    full_name='HotelHostMappingService.AssignHotelsToUsers',
    index=0,
    containing_service=None,
    input_type=_ASSIGNHOTELSTOUSERSREQUEST,
    output_type=_ASSIGNHOTELSTOUSERSRESPONSE,
    options=None,
  ),
])
_sym_db.RegisterServiceDescriptor(_HOTELHOSTMAPPINGSERVICE)

DESCRIPTOR.services_by_name['HotelHostMappingService'] = _HOTELHOSTMAPPINGSERVICE

# @@protoc_insertion_point(module_scope)