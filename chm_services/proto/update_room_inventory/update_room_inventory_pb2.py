# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: update_room_inventory.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='update_room_inventory.proto',
  package='update_room_inventory',
  syntax='proto3',
  serialized_pb=_b('\n\x1bupdate_room_inventory.proto\x12\x15update_room_inventory\"\x8a\x02\n\x1aUpdateRoomInventoryRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x14\n\x0croomtypecode\x18\x02 \x01(\t\x12\x11\n\tstartdate\x18\x03 \x01(\t\x12\x0f\n\x07\x65nddate\x18\x04 \x01(\t\x12\x0f\n\x07\x64\x61ylist\x18\x05 \x03(\t\x12\x13\n\x0bminnonights\x18\x06 \x01(\t\x12\x0e\n\x06\x63utoff\x18\x07 \x01(\t\x12\x11\n\tavailable\x18\x08 \x01(\t\x12\x10\n\x08stopsell\x18\t \x01(\t\x12\x12\n\nhotel_code\x18\n \x01(\t\x12\x14\n\x0c\x63hannel_name\x18\x0b \x01(\t\x12\x0f\n\x07user_id\x18\x0c \x01(\t\x12\x10\n\x08username\x18\r \x01(\t\"_\n\x1bUpdateRoomInventoryResponse\x12\n\n\x02id\x18\x01 \x01(\t\x12\x12\n\nerror_code\x18\x02 \x01(\t\x12\x0f\n\x07message\x18\x03 \x01(\t\x12\x0f\n\x07success\x18\x04 \x01(\x08\x32\xa0\x01\n\x1eUpdateRoomInventoryDataService\x12~\n\x13UpdateRoomInventory\x12\x31.update_room_inventory.UpdateRoomInventoryRequest\x1a\x32.update_room_inventory.UpdateRoomInventoryResponse\"\x00\x62\x06proto3')
)




_UPDATEROOMINVENTORYREQUEST = _descriptor.Descriptor(
  name='UpdateRoomInventoryRequest',
  full_name='update_room_inventory.UpdateRoomInventoryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='update_room_inventory.UpdateRoomInventoryRequest.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='roomtypecode', full_name='update_room_inventory.UpdateRoomInventoryRequest.roomtypecode', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='startdate', full_name='update_room_inventory.UpdateRoomInventoryRequest.startdate', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='enddate', full_name='update_room_inventory.UpdateRoomInventoryRequest.enddate', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='daylist', full_name='update_room_inventory.UpdateRoomInventoryRequest.daylist', index=4,
      number=5, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='minnonights', full_name='update_room_inventory.UpdateRoomInventoryRequest.minnonights', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='cutoff', full_name='update_room_inventory.UpdateRoomInventoryRequest.cutoff', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='available', full_name='update_room_inventory.UpdateRoomInventoryRequest.available', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='stopsell', full_name='update_room_inventory.UpdateRoomInventoryRequest.stopsell', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='hotel_code', full_name='update_room_inventory.UpdateRoomInventoryRequest.hotel_code', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='channel_name', full_name='update_room_inventory.UpdateRoomInventoryRequest.channel_name', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='update_room_inventory.UpdateRoomInventoryRequest.user_id', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='username', full_name='update_room_inventory.UpdateRoomInventoryRequest.username', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=55,
  serialized_end=321,
)


_UPDATEROOMINVENTORYRESPONSE = _descriptor.Descriptor(
  name='UpdateRoomInventoryResponse',
  full_name='update_room_inventory.UpdateRoomInventoryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='update_room_inventory.UpdateRoomInventoryResponse.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='error_code', full_name='update_room_inventory.UpdateRoomInventoryResponse.error_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='message', full_name='update_room_inventory.UpdateRoomInventoryResponse.message', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='success', full_name='update_room_inventory.UpdateRoomInventoryResponse.success', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=323,
  serialized_end=418,
)

DESCRIPTOR.message_types_by_name['UpdateRoomInventoryRequest'] = _UPDATEROOMINVENTORYREQUEST
DESCRIPTOR.message_types_by_name['UpdateRoomInventoryResponse'] = _UPDATEROOMINVENTORYRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

UpdateRoomInventoryRequest = _reflection.GeneratedProtocolMessageType('UpdateRoomInventoryRequest', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEROOMINVENTORYREQUEST,
  __module__ = 'update_room_inventory_pb2'
  # @@protoc_insertion_point(class_scope:update_room_inventory.UpdateRoomInventoryRequest)
  ))
_sym_db.RegisterMessage(UpdateRoomInventoryRequest)

UpdateRoomInventoryResponse = _reflection.GeneratedProtocolMessageType('UpdateRoomInventoryResponse', (_message.Message,), dict(
  DESCRIPTOR = _UPDATEROOMINVENTORYRESPONSE,
  __module__ = 'update_room_inventory_pb2'
  # @@protoc_insertion_point(class_scope:update_room_inventory.UpdateRoomInventoryResponse)
  ))
_sym_db.RegisterMessage(UpdateRoomInventoryResponse)



_UPDATEROOMINVENTORYDATASERVICE = _descriptor.ServiceDescriptor(
  name='UpdateRoomInventoryDataService',
  full_name='update_room_inventory.UpdateRoomInventoryDataService',
  file=DESCRIPTOR,
  index=0,
  options=None,
  serialized_start=421,
  serialized_end=581,
  methods=[
  _descriptor.MethodDescriptor(
    name='UpdateRoomInventory',
    full_name='update_room_inventory.UpdateRoomInventoryDataService.UpdateRoomInventory',
    index=0,
    containing_service=None,
    input_type=_UPDATEROOMINVENTORYREQUEST,
    output_type=_UPDATEROOMINVENTORYRESPONSE,
    options=None,
  ),
])
_sym_db.RegisterServiceDescriptor(_UPDATEROOMINVENTORYDATASERVICE)

DESCRIPTOR.services_by_name['UpdateRoomInventoryDataService'] = _UPDATEROOMINVENTORYDATASERVICE

# @@protoc_insertion_point(module_scope)
