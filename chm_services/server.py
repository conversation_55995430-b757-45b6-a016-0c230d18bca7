import grpc
from concurrent import futures
import time
from grpc._server import _ServerStage

from chm_services.proto.health_check import healthcheck_pb2_grpc
from chm_services.proto.get_room_rates import get_room_rates_pb2_grpc
from chm_services.proto.get_hotel_detail import get_hotel_detail_pb2_grpc
from chm_services.proto.get_room_inventory import get_room_inventory_pb2_grpc
from chm_services.proto.get_cancellation_listing import get_cancellation_listing_pb2_grpc
from chm_services.proto.update_booking_status import update_booking_status_pb2_grpc
from chm_services.proto.get_booking_listing import get_booking_listing_pb2_grpc
from chm_services.proto.price_recommendation_config import price_recommendation_config_pb2_grpc
from chm_services.proto.price_recommendation_config_v2 import price_recommendation_config_v2_pb2_grpc
from chm_services.proto.price_recommendation import price_recommendation_pb2_grpc
from chm_services.proto.get_channel_manager import get_channel_manager_pb2_grpc
from chm_services.proto.update_room_inventory import update_room_inventory_pb2_grpc
from chm_services.proto.update_room_rates import update_room_rates_pb2_grpc
from chm_services.proto.update_offer import update_offer_pb2_grpc
from chm_services.proto.create_offers import create_offers_pb2_grpc
from chm_services.proto.get_offer_listing import get_offer_listing_pb2_grpc
from chm_services.proto.get_offer_detail import get_offer_detail_pb2_grpc
from chm_services.proto.get_booking_detail import get_booking_detail_pb2_grpc
from chm_services.proto.booking_confirmation import booking_confirmation_pb2_grpc

from chm_services.services.healthcheck import HealthCheck
from chm_services.services.get_room_rates_service import GetRoomRates
from chm_services.services.get_hotel_detail_service import GetHotelDetail
from chm_services.services.get_room_inventory_service import GetRoomInventory
from chm_services.services.get_cancellation_listing_service import GetCancellationListing
from chm_services.services.update_booking_status_service import UpdateBookingStatus
from chm_services.services.get_booking_listing_service import GetBookingListing
from chm_services.services.price_recommendation_config import PriceRecommendationConfig
from chm_services.services.price_recommendation_config_v2 import PriceRecommendationConfigV2
from chm_services.services.price_recommendation import PriceRecommendation
from chm_services.services.get_channel_manager import GetChannelManager
from chm_services.services.update_room_inventory_service import UpdateRoomInventory
from chm_services.services.update_room_rates import UpdateRoomRates
from chm_services.services.update_offer_service import UpdateOffer
from chm_services.services.create_offers_service import CreateOffers
from chm_services.services.get_offer_listing_service import GetOfferListing
from chm_services.services.get_offer_detail_service import GetOfferDetail
from chm_services.services.get_booking_detail_service import GetBookingDetail
from chm_services.services.booking_confirmation_service import BookingConfirmation

from utils.logger import Logger

inventory_logger = Logger(logger="inventoryLogger")
_HEALTH_CHECK_INTERVAL_SECONDS = 10
_PORT_NUMBER = 8073
_MAX_RETRY_COUNT = 10


def start_server():
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=30))
    healthcheck_pb2_grpc.add_HealthServicer_to_server(HealthCheck(), server)

    get_hotel_detail_pb2_grpc.add_HotelDataServiceServicer_to_server(GetHotelDetail(), server)
    get_offer_detail_pb2_grpc.add_OfferDetailServiceServicer_to_server(GetOfferDetail(), server)
    get_room_inventory_pb2_grpc.add_RoomInventoryServiceServicer_to_server(GetRoomInventory(), server)
    get_room_rates_pb2_grpc.add_RoomRatesServiceServicer_to_server(GetRoomRates(), server)
    get_cancellation_listing_pb2_grpc.add_CancellationDataServiceServicer_to_server(GetCancellationListing(), server)
    update_booking_status_pb2_grpc.add_UpdateBookingStatusServiceServicer_to_server(UpdateBookingStatus(), server)
    get_booking_listing_pb2_grpc.add_BookingListingServiceServicer_to_server(GetBookingListing(), server)
    price_recommendation_config_pb2_grpc.add_PriceRecommendationConfigServiceServicer_to_server(
        PriceRecommendationConfig(), server)
    price_recommendation_config_v2_pb2_grpc.add_PriceRecommendationConfigServiceServicer_to_server(
        PriceRecommendationConfigV2(), server)
    price_recommendation_pb2_grpc.add_PriceRecommendationServiceServicer_to_server(PriceRecommendation(), server)
    get_channel_manager_pb2_grpc.add_ChannelManagerDetailsServiceServicer_to_server(GetChannelManager(), server)
    update_room_inventory_pb2_grpc.add_UpdateRoomInventoryDataServiceServicer_to_server(UpdateRoomInventory(), server)
    update_room_rates_pb2_grpc.add_UpdateRoomRatesDataServiceServicer_to_server(UpdateRoomRates(), server)
    update_offer_pb2_grpc.add_UpdateOfferServiceServicer_to_server(UpdateOffer(), server)
    create_offers_pb2_grpc.add_CreateOffersServiceServicer_to_server(CreateOffers(), server)
    get_offer_listing_pb2_grpc.add_GetOfferListingServiceServicer_to_server(GetOfferListing(), server)
    get_booking_detail_pb2_grpc.add_BookingDetailServiceServicer_to_server(GetBookingDetail(), server)

    booking_confirmation_pb2_grpc.add_BookingConfirmationServiceServicer_to_server(BookingConfirmation(), server)

    output_port_number = server.add_insecure_port('[::]:%s' % _PORT_NUMBER)
    server.start()
    if output_port_number == _PORT_NUMBER and server._state.stage is _ServerStage.STARTED:
        return server
    return None


def serve():
    retry_count = 0
    try:
        while retry_count <= _MAX_RETRY_COUNT:
            server = start_server()
            retry_count += 1
            if server is not None and server._state.stage is _ServerStage.STARTED:
                inventory_logger.info(message="GRPC server has started on %s retry" % retry_count,
                                      log_type="ingoibibo", bucket="serve", stage="start_server")
                retry_count = 0
                while server._state.stage is _ServerStage.STARTED:
                    inventory_logger.info(message="GRPC server is running",
                                          log_type="ingoibibo", bucket="serve", stage="start_server")
                    time.sleep(_HEALTH_CHECK_INTERVAL_SECONDS)
        inventory_logger.critical(message="Error in starting GRPC server",
                                  log_type="ingoibibo", bucket="serve", stage="start_server")
    except Exception as ex:
        inventory_logger.info(message="GRPC server is stopped after trying %s retries, exception: %s"
                                      % (retry_count, str(ex)),
                              log_type="ingoibibo", bucket="serve", stage="start_server")
    finally:
        server.stop(0)


if __name__ == '__main__':
    serve()
