import uuid
import re

from common.cassandra_models.base import CassandraBaseModel
from django.conf import settings
from cassandra.query import BatchStatement, ConsistencyLevel


keyspace = settings.CASSANDRA_CONFIG.get('KEYSPACE_NAME', 'goibibo_inventory')

CREATE_ACTION_DATA = \
    'CREATE TABLE IF NOT EXISTS action_data (id uuid, data text, PRIMARY KEY(id));'

CREATE_FUTURE_ACTION_DATA = 'CREATE TABLE IF NOT EXISTS future_action_data (action_item text, ' \
                            'execution_time timestamp, action_data_uuid uuid,' \
                            ' PRIMARY KEY (action_item, execution_time));'


def validate_table_name(table_name):
    """
    Validate table name to prevent SQL injection.
    Only allow alphanumeric characters, underscores, and dots.
    """
    if not re.match(r'^[a-zA-Z0-9_\.]+$', table_name):
        raise ValueError("Invalid table name: contains unsafe characters")
    return table_name


class ActionData(CassandraBaseModel):

    table_name = validate_table_name('%s.action_data' % keyspace)

    def insert_data(self, action_data):
        cql = "INSERT INTO %s (id, data) VALUES (?, ?)" % self.table_name
        data_id = uuid.uuid4()
        self.session.execute(cql, (data_id, action_data))
        return data_id

    def get_data(self, action_data_id):
        cql = "SELECT data FROM %s WHERE id=? LIMIT 1" % self.table_name
        results = self.session.execute(cql, (action_data_id,))
        ## sql_injection id: 17 32-42 R
        return results[0].data

    def update_data(self, action_uuid, action_data):
        cql = "UPDATE %s SET data=? WHERE id=? IF EXISTS" % self.table_name
        ## sql_injection id: 16 38-48 R
        self.session.execute(cql, (action_data, action_uuid))


class FutureActionData(CassandraBaseModel):
    table_name = validate_table_name('%s.future_action_data' % keyspace)

    def insert_future_data(self, action_data):
        query = self.session.prepare("INSERT INTO future_action_data (action_item, execution_time, action_data_uuid) VALUES (?, ?, ?)")
        batch = BatchStatement(consistency_level=ConsistencyLevel.QUORUM)
        for data in action_data:
            action_item = data['action_item']
            execution_time = data['execution_time']
            action_data_uuid = data['action_data_uuid']
            batch.add(query, (action_item, execution_time, uuid.UUID(action_data_uuid)))
        self.session.execute(batch)

    def get_action_data_uuid(self, action_item, execution_time):
        table_name = validate_table_name('%s.future_action_data' % keyspace)
        cql = "SELECT action_data_uuid, execution_time FROM %s WHERE action_item=? AND execution_time>=?" % table_name
        ## sql_injection id: 15 59-69 R
        results = self.session.execute(cql, (action_item, execution_time))
        return results
