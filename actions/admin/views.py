import traceback

from lib.custom_decorators import token_required, custom_required

from utils.logger import Logger
from django.http.response import Http<PERSON><PERSON>ponse, HttpResponseServerError
from django.shortcuts import render_to_response
from django.template.context import RequestContext
from django.views.decorators.http import require_http_methods
from django.conf import settings
from django.contrib.auth.decorators import login_required


from actions.models import ActionJob, JOB_PENDING, JOB_REJECTED
from actions.cassandra_models.action_data import ActionData
from actions.action_settings.action_map import ACTIONTYPEMAP
from actions.tasks import manage_actions

action_data_model = ActionData()
logger = Logger(logger='inventoryLogger')


@custom_required(token_required, login_required)
def pending_actions(request):
    try:
        template = 'admin/actions/approve-actions-index.html'

        actions = ActionJob.objects.filter(status=JOB_PENDING)

        context = {'actions': actions, 'action_map': ACTIONTYPEMAP}
        return render_to_response(template, context, context_instance=RequestContext(request))
    except Exception, e:
        logger.critical(message="action_approval_pending_actions error %s\t %s\t" %
                                (str(e), repr(traceback.format_exc())),
                        log_type='ingoibibo', bucket='action_approval_system',
                        stage='actions.admin.views.pending_actions')
        return HttpResponseServerError('Error Occured')


@custom_required(token_required, login_required)
def get_action_data(request):
    try:
        action = ActionJob.objects.get(action_uuid=request.GET.get('data_id'))
        action_data = action_data_model.get_data(action.action_data_id)
        return HttpResponse(action_data)
    except Exception, e:
        logger.critical(message="action_approval_get_action_data error %s\t %s\t" %
                                (str(e), repr(traceback.format_exc())),
                        log_type='ingoibibo', bucket='action_approval_system',
                        stage='actions.admin.views.get_action_data')
        return HttpResponseServerError('Error Occured')


@custom_required(token_required, login_required)
@require_http_methods('POST')
def approve_actions(request):
    ## sql_injection id: 15, 16 NR
    action_uuids = request.POST.get('action_ids')
    try:
        action_uuids = action_uuids.split(',')
        actions = ActionJob.objects.filter(action_uuid__in=action_uuids, status=JOB_PENDING)
        if settings.DEBUG:
            manage_actions(actions)
        else:
            manage_actions.apply_async(args=(actions, ))

        logger.info(message="action_approval_approve_action ACTIONS SUCCESSFULLY MANAGED",
                    log_type='ingoibibo', bucket='action_approval_system',
                    stage='actions.admin.views.approve_actions')
        actions.update(approved_by=request.user)

        return HttpResponse('Success')
    except Exception, e:
        logger.critical(message="action_approval_approve_actions error %s\t %s\t" %
                                (str(e), repr(traceback.format_exc())),
                        log_type='ingoibibo', bucket='action_approval_system',
                        stage='actions.admin.views.approve_actions')
        return HttpResponseServerError('Error Occured')


@custom_required(token_required, login_required)
@require_http_methods('POST')
def reject_actions(request):
    action_uuids = request.POST.get('action_ids')
    try:
        action_uuids = action_uuids.split(',')
        ActionJob.objects.filter(action_uuid__in=action_uuids, status=JOB_PENDING).update(status=JOB_REJECTED)
        logger.info(message="action_approval_approve_action ACTIONS SUCCESSFULLY REJECTED",
                    log_type='ingoibibo', bucket='action_approval_system',
                    stage='actions.admin.views.reject_actions')

        return HttpResponse('Success')
    except Exception, e:
        logger.critical(message="action_approval_reject_actions error %s\t %s\t" %
                                (str(e), repr(traceback.format_exc())),
                        log_type='ingoibibo', bucket='action_approval_system',
                        stage='actions.admin.views.reject_actions')
        return HttpResponseServerError('Error Occured')


@custom_required(token_required, login_required)
def show_all_actions(request):
    try:
        template = 'admin/actions/all-action-status.html'

        actions = ActionJob.objects.all().only(*['action_type']).order_by("-createdon")

        context = {'actions': actions, 'action_map': ACTIONTYPEMAP}
        return render_to_response(template, context, context_instance=RequestContext(request))
    except Exception, e:
        logger.critical(message="action_approval_pending_actions error %s\t %s\t" %
                                (str(e), repr(traceback.format_exc())),
                        log_type='ingoibibo', bucket='action_approval_system',
                        stage='actions.admin.views.pending_actions')
        return HttpResponseServerError('Error Occured')
