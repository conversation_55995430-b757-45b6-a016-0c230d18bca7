import uuid

from django.db import models
from ingouser.models import User
from django.utils.translation import ugettext_lazy as _
from json_field import <PERSON><PERSON><PERSON><PERSON>

from actions.action_settings.action_types import ACTIONTYPECHOICE


JOB_PENDING = 'pending'
JOB_COMPLETED = 'completed'
JOB_SCHEDULED = 'scheduled'
JOB_RUNNING = 'executing'
JOB_REJECTED = 'rejected'
JOB_FAILED = 'failed'

ACTION_JOB_STATUS = (
    (JOB_PENDING, 'Job Pending'),
    (JOB_COMPLETED, 'Job Completed'),
    (JOB_SCHEDULED, 'Job Scheduled'),
    (JOB_RUNNING, 'Executing Job'),
    (JOB_REJECTED, 'Job Rejected'),
    (JOB_FAILED, 'Job Falied'),
)


ACTIONDATA_PER_JOB = 500


class ActionJob(models.Model):
    action_uuid = models.CharField(_('Action UUID'), max_length=36, unique=True, default=uuid.uuid4)
    createdon = models.DateTimeField(_('Created On'), auto_now_add=True, db_index=True)
    modifiedon = models.DateTimeField(_('Modified On'), auto_now_add=True, db_index=True)
    created_by = models.ForeignKey(User, related_name='created_by')
    approved_by = models.ForeignKey(User, related_name='approved_by',  null=True)
    status = models.CharField(_('Job Status'), max_length=50, choices=ACTION_JOB_STATUS, default=JOB_PENDING)
    kwargs = JSONField(default={}, verbose_name='Keyword Arguments')
    action_type = models.PositiveIntegerField(_('Action Type'), choices=ACTIONTYPECHOICE)
    action_data_id = models.CharField(_('Action Data Provided'), max_length=36)
    expiry_time = models.DateTimeField(_('Job Expiry Time'), null=True)
    approval_expiry_time = models.DateTimeField(_('Approval Expiry Time'))
    execution_time = models.DateTimeField(_('Job Execution Time'), null=True)
    execution_start = models.DateTimeField(_('Job Started at'), null=True)
    execution_end = models.DateTimeField(_('Job Finished at'), null=True)
    attempt_limit = models.PositiveIntegerField(_('Retry Upto'), default=1)
    attempt_count = models.PositiveIntegerField(_('No of Retries'), null=True, default=0)
