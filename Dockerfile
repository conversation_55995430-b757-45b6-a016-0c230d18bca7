# Pulling the inventory base docker image from ECR (dependencies already installed on this base image.)
FROM 524881529748.dkr.ecr.ap-south-1.amazonaws.com/ingo-inventory-base:v5

EXPOSE 80

WORKDIR /usr/local/goibibo/source/goibibo_inventory

ADD dockerconfig/newrelic/* /etc/newrelic/
COPY dockerconfig/httpd-default.conf /etc/httpd/conf/extra/httpd-default.conf
COPY dockerconfig/httpd-info.conf /etc/httpd/conf/extra/httpd-info.conf
COPY dockerconfig/httpd-mpm.conf /etc/httpd/conf/extra/httpd-mpm.conf
COPY dockerconfig/httpd.conf /etc/httpd/conf/httpd.conf
COPY dockerconfig/mime.types /etc/httpd/conf/mime.types
COPY dockerconfig/supervisord /etc/rc.d/init.d/
ADD dockerconfig/supervisor-service/* /etc/supervisord.d/
COPY ./requirements.txt /usr/local/goibibo/source/goibibo_inventory
RUN /usr/local/goibibo/python2.7/bin/pip install -r requirements.txt
COPY dockerconfig/star.mmt.go.crt /etc/pki/ca-trust/source/anchors/star.mmt.go.crt
COPY woof_kafka/conf/krb5.conf /etc/
RUN update-ca-trust

COPY ./ /usr/local/goibibo/source/goibibo_inventory
RUN mkdir -p /usr/local/goibibo/source/goibibo_inventory/coverage

RUN cp -r  dockerconfig/vhosts/* /etc/httpd/conf/extra/

ARG env
ENV CURR_ENV=$env
ENV C_FORCE_ROOT="true"
ENV GRPC_POLL_STRATEGY="epoll1"

VOLUME ["/opt/logs/"]

RUN chmod 0777 /usr/local/goibibo/source/goibibo_inventory/analytics_data/credentials.json && \
 chmod 0777 /usr/local/goibibo/source/goibibo_inventory/api/v1/gstn_invoice/ocr/visionAPI.json

RUN mkdir -p /usr/local/goibibo/source/goibibo_inventory/vision_data && chmod 1777 /usr/local/goibibo/source/goibibo_inventory/vision_data

RUN \
ln -sf dict_to_proto3_to_dict/src proto_utils && \
ln -sf goibibo_saml_service_provider saml_service_provider && \
if [ "${env}" != "dev" ] && [ "${env}" != "pp" ] ; then echo yes | /usr/local/goibibo/python2.7/bin/python2.7 manage.py collectstatic ; fi && \
if [ "${env}" == "dev" ] ; then /usr/local/goibibo/python2.7/bin/pip install django-extensions==2.2.9; fi && \
if [ "${env}" != "dev" ] ; then chmod 1777 /usr/local/goibibo/source/goibibo_inventory/static; fi

# Set timezone to Asia/Kolkata
RUN ln -snf /usr/share/zoneinfo/Asia/Kolkata /etc/localtime && echo "Asia/Kolkata" > /etc/timezone
ENV TZ=Asia/Kolkata

ENTRYPOINT ["./docker-entrypoint.sh"]