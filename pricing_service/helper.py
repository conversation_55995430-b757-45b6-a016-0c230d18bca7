# will put related helper functions in this file


def deserialize_room_stay(roomstay, request_dict):
    for idx, val in enumerate(roomstay):
        room_num = idx + 1
        adult_str = '%s%s' % ('adultroom', str(room_num))
        child_str = '%s%s' % ('childroom', str(room_num))
        request_dict[adult_str] = val[0]
        request_dict[child_str] = val[1]
        for child_idx, age in enumerate(val[2]):
            age_str = "%s%s%s%s" % ('ageroom', str(room_num), 'child', str(child_idx + 1))
            request_dict[age_str] = age
