syntax = "proto3" ;

package migrate_child_rates;

option go_package= './';


service MigrateChildRatesService {
    rpc MigrateChildRates (MigrateChildRatesRequest) returns (MigrateChildRatesResponse) {
    }
}

message MigrateChildRatesRequest {
    MigrateChildRatesRequestData data = 1;
    string hotel_code = 2;
    int32 user_id = 3;
    string user_name = 4;
    string source = 5;
}

message MigrateChildRatesRequestData {
    repeated string code_list = 1;
    repeated string contract_type_list = 2;
    string level = 3;
}

message MigrateChildRatesResponse {
    MigrateChildRatesResponseData data = 1;
    bool success = 2;
    string message = 3;
}

message MigrateChildRatesResponseData {
    repeated string level_code_list = 1;
    repeated string product_code_list = 2;
    bool success = 3;
    repeated MigrateChildRatesError errors = 4;
    string level = 5;
    string product_level = 6;
}

message MigrateChildRatesError {
    repeated string product_code_list = 1;
    string message = 2;
    string error_code = 3;
    string product_level = 4;
}
