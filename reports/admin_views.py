from django.db.models import Q, Count
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import render_to_response
from django.template.loader import render_to_string
from django.template import RequestContext
from django.contrib.admin.views.decorators import staff_member_required
from django.template.defaultfilters import slugify
from django.core.exceptions import PermissionDenied
from django.core.cache import cache
from django.conf import settings
from django.contrib import messages
from django.http import HttpResponseForbidden
from rest_framework.decorators import detail_route, api_view
from rest_framework.response import Response
from rest_framework import status

import datetime as dt
import time, datetime, traceback
import logging
import csv
import json
import socket
import os
from collections import defaultdict
from django.views.decorators.http import require_http_methods
from django.contrib.contenttypes.models import ContentType

from api.v1.cancellationrules.resources.migration_helper_free_cancellation import rev_checkin_weekday
from api.v1.common_resources import validate_bool, is_member
from api.v2.cancellation_rules.resources.constants import priority_map
from common.task import validate_and_create_cal_linkage_task, validate_and_remove_cal_linkage_task
from corporate_gstn.resources import RELEASE_AMOUNT_BULK_UPLOADER_GROUP
from hotels.constants.constants import ROLE_SUPPLY_IND_AUDIT_ID, ROLE_SUPPLY_IND_ANALYTICS_ID
from hotels.constants.hotel_constants import HotelABSOReason
from hotels.models.user_flag_configuration import HOST_FLAG_ONE_DICT
from hotels.models.user_management import HostProfile
from hotels.services.hotel_service import HotelService
from models import ExportModel
from common.models import Templates, User
from common.object_guardian import assign_permissions, remove_permissions
from reports import Export_Choose_List
from exporttools import ExportTools
from expiryreports import get_email_list
from reports.tasks import newReportOnEmailTask
from extranet import extranetvariables
from hotels import hotelchoice
from hotels.forms import (getContractManagers, get_contract_bdos,\
                          get_contract_market_coordinator, get_contract_adms,
                          get_pricing_specialist, get_chain_users, get_regional_heads)
from lib.MDBStorage import MDBStorage
from lib.check_permission import has_permission
from scripts.hostapp_migration.migrate_service import execute_child_related_migration, get_child_pricing_admin_user
from scripts.hostweb_migration.run import run_create_kitchen_space
from utils.logger import Logger
from hotels.models import HotelDetail, RatePlan, CancellationRules
from hotels.hotelchoice import hotel_tags
from hotels.methods import HotelMethods
from hotels.tasks import upload_mapping_csv, update_lat_long_task, upload_gostay_task, \
    vendor_sync_task, vendor_create_task, modify_pah_state_func, enable_pah_flag, upload_gst_csv, \
    create_add_ons_task, task_pending_hotel_tnc_mailer, map_existing_vendor_task, \
    update_vendor_field_task, create_offer_task, task_hotel_usp_create_from_input_file, \
    create_free_cancellation_rule_task, \
    update_hotel_flag_value_task, create_linked_rateplan_task, create_text_based_promotion_task, \
    update_rateplan_comission_task, update_free_cancellation_rule_task_new, vcc_update_bulk_task, \
    update_rateplan_flag_value_task, \
    update_host_flag_value_task, update_vendor_tan_status, upload_advantage_flag_task, \
    upload_hotel_remove_homestay_funnel_task, upload_fake_hotels_task, create_update_manager_mapping, \
    update_social_media_status, create_cug_task_promotionv2, create_cug_task, create_coupon_task_promotionv2
from reports.tasks import mail_finance_hotel_invoice, hotel_ledger_report_task
from reports.upload_reports import get_username_from_field_value
from lib.redis_cache_backend import RedisSearchCache
from communication.common_comms.communications import sendMail
from common.commonhelper import fetch_client_details
from corporate_gstn.tasks import release_gst_task, release_amounts_task
from hotels.hotelchoice import HotelCategoryDict, HotelSubCategory
from hotels.models.hotel_flag_configuration import FLAG_ONE_DICT, FLAG_THREE_DICT, RATE_PLAN_FLAG_DICT, CANCELLATION_RULES_FLAG_ONE_DICT, FLAG_FOUR_DICT
from hotels.tasks import create_coupon_task
from bulk_uploader.common_helpers import convert_truthy
from django.core.exceptions import PermissionDenied
from api.v2.cug.resources.cug_constants import CUG_SEGMENT_CUG1,CUG_SEGMENT_CUG10
logger = logging.getLogger('inventoryLogger')
logger_stats = Logger(logger="inventoryLogger")
inventory_logger = Logger(logger='inventoryLogger')

mdb = MDBStorage()
hostname = socket.gethostname()
hm = HotelMethods()
mdb_storage = MDBStorage()

DEFAULT_CACHE = settings.CACHES['default']
REDIS_SERVER = DEFAULT_CACHE['LOCATION']
REDIS_OPTIONS = DEFAULT_CACHE['OPTIONS']

ERROR_MESSAGE = "Total number of rows in CSV file should be less then {row_count}"
ROW_COUNT = 1000
DAY_ROW_COUNT = 500
UPDATE_VENDOR_MAX_ROW_ALLOWED = 10000

try:
    redis_server = RedisSearchCache(server=REDIS_SERVER, params={'OPTIONS': REDIS_OPTIONS})
except Exception:
    redis_server = None
    logger_stats.critical(message='redis connection failure', log_type='Ingoibibo', bucket='redis', stage='admin_views')

CREATE_VENDOR_FIELDS = (
    'Hotel Code', 'Terms Code', 'Method Code', 'Account Number', 'Account Name', 'Branch Name', 'Branch Code', 'IFSC',
    'Bank Name', 'Bank Code', 'PAN Number', 'Name on PAN', 'Vendor Code', 'Vendor', 'Corporate GST Number',
    'Corporate GST Address', 'Corporate GST Name', 'Vendor Name', 'Approval Status', 'TAN Number', 'Search Name',
    'Name 2', 'Vendor Address', 'Vendor Address 2', 'City', 'Contact', 'Phone No', 'Post Code',
    'Vendor GEN Posting Group', 'Email', 'Primary Contact Number', 'State Code', 'Full Name', 'Reservation Mobile No',
    'Sales Email', 'Sales Phone Number', 'Sales Mobile Number', 'Finance Email', 'Finance Phone Number',
    'Finance Mobile Number', 'Non Adjustment Vendor', 'Hotel Credit MEMO Application', 'TDS Application',
    'Account Address', 'Account Post Code', 'Account Email', 'IBAN', 'Adjustment Day Of Month', 'Vendor Posting Group',
    'Account Type', 'VAT Registration Number', 'VAT Bus Posting Group', 'Standard Vendor Purchase Code'
)
CREATE_VCC_FIELDS = ['IngoBooking']

UPDATE_VENDOR_FIELDS = (
    'Vendor Code', 'Terms Code', 'Method Code', 'Corporate GST Number', 'Corporate GST Name', 'Corporate GST Address',
    'Vendor Name', 'TAN Number', 'Search Name', 'Name 2', 'Vendor Address', 'Vendor Address 2', 'Contact',
    'Phone No', 'Post Code', 'Vendor GEN Posting Group', 'Email', 'Primary Contact Number', 'Full Name',
    'Reservation Mobile No', 'Sales Email', 'Sales Phone Number', 'Sales Mobile Number', 'Finance Email',
    'Finance Phone Number', 'Finance Mobile Number', 'Non Adjustment Vendor', 'Hotel Credit MEMO Application',
    'TDS Application', 'Adjustment Day Of Month', 'Blocked', 'City', 'Pay To Vendor', 'Vendor Posting Group',
    'VAT Registration Number', 'VAT Bus Posting Group', 'Standard Vendor Purchase Code', 'LDC Lower Percentage',
    'LDC Number', 'LDC Limit In INR','IFSC','Account Number','Account Type','Bank Name','Bank Code', 'Account Name',
    'Bank City','Branch Name','Branch Code'
)

RELEASE_GST = ("Booking ID",)
RELEASE_AMOUNTS = ("Booking ID",)

@staff_member_required
def index(request):
    if request.hotelusertype == 'contentoutsource':
        raise PermissionDenied
    context = {}
    savedExports = list(ExportModel.objects.all())

    context['savedExports'] = savedExports
    if request.GET:
        exporttype = request.GET.get('exportform', None)
        context.update(getExportForm(request, exporttype, ajax=False))
    return render_to_response('reports/index.html', context, context_instance=RequestContext(request))


@staff_member_required
def getExportForm(request, exporttype, ajax=True):
    context = {}
    context['exporttype'] = exporttype
    if exporttype == 'new':
        context['export_models'] = Export_Choose_List
    else:
        context['export_object'] = getExportObjectData(exporttype)
    if ajax:
        resp = render_to_string('reports/exportform.html', context, context_instance=RequestContext(request))
        return HttpResponse(resp)
    else:
        return context


def getExportObjectData(slugvalue):
    export_obj = None
    try:
        export_obj = ExportModel.objects.get(slugvalue=slugvalue)
        export_fields = export_obj.exportfields.split(',')
        export_filters = export_obj.exportfilters.split(',')
        exporttool = ExportTools(export_obj.exportmodel)
        export_obj.fieldlist = exporttool.get_fields_for_fieldlist(export_fields)
        filterfieldlist = exporttool.get_fields_for_fieldlist(export_filters)
        filterdict = {
            'datefilters': [],
            'numericfilter': [],
            'optionsfilter': [],
            'textfilter': [],
            'booleanfilters': []
        }
        for filterfield in filterfieldlist:
            if filterfield['db_type'] in ['date', 'datetime']:
                filterdict['datefilters'].append(filterfield)
            elif filterfield['db_type'] in ['bool']:
                filterdict['booleanfilters'].append(filterfield)
            elif len(filterfield['lookup_codes']) > 0:
                filterdict['optionsfilter'].append(filterfield)
            elif filterfield['db_type'] in ['integer', 'double precision']:
                filterdict['numericfilter'].append(filterfield)
            else:
                filterdict['textfilter'].append(filterfield)
        export_obj.filters = filterdict
        export_obj.exporttool = exporttool
    except Exception, e:
        print e
        logger.critical(str(e))

    return export_obj

@staff_member_required
def getNewExportForm(request, exportmodel):
    context = {}
    context['exportmodel'] = exportmodel
    exporttool = ExportTools(exportmodel)
    fieldlist = exporttool.get_general_fields()
    context['fieldlist'] = fieldlist
    resp = render_to_string('reports/newexportform.html', context, context_instance=RequestContext(request))
    return HttpResponse(resp)


@staff_member_required
def getForeignKeyFields(request):
    resp = ''
    if request.GET:
        context = dict(request.GET.items())
        exporttool = ExportTools(context['exportmodel'])
        fk_fieldlist = exporttool.get_fk_fields(context['fieldname'])
        context['fieldlist'] = fk_fieldlist
        context['fieldclass'] = context['fieldname']
        resp = render_to_string('reports/newexportfields.html', context, context_instance=RequestContext(request))

    else:
        resp = "<script>alert('Not valid foreign key');</script>"
    return HttpResponse(resp)


@staff_member_required
def submitNewExportForm(request):
    url = '/admin/reports/'
    if request.POST:
        exportname = request.POST['exportname']
        exportmodel = request.POST['exportmodel']
        exportfields = request.POST.getlist('exportfields')
        exportfilters = request.POST.getlist('exportfilters')
        fieldstr = ','.join(exportfields)
        filterstr = ','.join(exportfilters)
        requser = request.user
        exp_mod = ExportModel(exportname=exportname, exportmodel=exportmodel, exportfields=fieldstr,
                              exportfilters=filterstr, user=requser)
        exp_mod.slugvalue = slugify(exportname + str(time.time()))
        exp_mod.save()
        url += '?exportform=' + str(exp_mod.slugvalue)

    return HttpResponseRedirect(url)


@staff_member_required
def submitExportForm(request):
    filterStr = ''
    resp = HttpResponse('Some Error Occurred.')
    if request.POST:
        emailid = request.POST.get('emailid', '')
        slugvalue = request.POST['exportslug']
        export_object = getExportObjectData(slugvalue)
        exporttool = export_object.exporttool
        # get filters 1. datefitlers
        for field in export_object.filters['datefilters']:
            fname = field['name']
            try:
                if request.POST.get(fname + '_gte', None):
                    filterStr += fname + '__gte="' + request.POST[fname + '_gte'] + '", '
                if request.POST.get(fname + '_lte', None):
                    filterStr += fname + '__lte="' + request.POST[fname + '_lte'] + '", '
            except Exception, e:
                print e
        for field in export_object.filters['optionsfilter']:
            fname = field['name']
            options = request.POST.getlist(fname)
            try:
                if options:
                    filterStr += fname + '__in=' + str(options) + ', '
            except Exception, e:
                print e
        for field in export_object.filters['textfilter']:
            fname = field['name']
            textvalue = request.POST.get(fname, '')
            try:
                if textvalue:
                    filterStr += fname + '__icontains="' + str(textvalue) + '", '
            except Exception, e:
                print e

        for field in export_object.filters['booleanfilters']:
            fname = field['name']
            textvalue = request.POST.get(fname, '')
            try:
                if textvalue == 'True':
                    filterStr += fname + '=True, '
                elif textvalue == 'False':
                    filterStr += fname + '=False, '
            except Exception, e:
                print e

        for field in export_object.filters['numericfilter']:
            fname = field['name']
            try:
                if request.POST.get(fname + '_greater', None) != 'minimum':
                    filterStr += fname + '__gte=' + float(request.POST[fname + '_greater']) + ', '
            except:
                pass

            try:
                if request.POST.get(fname + '_less', None) != 'maximum':
                    filterStr += fname + '__lte=' + float(request.POST[fname + '_less']) + ', '
            except Exception, e:
                pass

        filename = slugify(
            export_object.exportname) + '-' + export_object.exportmodel + '-' + datetime.datetime.now().strftime(
            "%Y%m%d_%H%M%S") + '.csv'
        msg = "Export Report: %s: %s" %(export_object.exportname, request.user.username)
        sendMail('', from_email=settings.EMAIL_ALERT_SENDER, body=filterStr, subject=msg, template_id='',
                 cc_emails=settings.TECH_TEAM, attached_file=None, bcc_emails=[])
        if emailid:
            from reports.tasks import mail_export_data
            if settings.DEBUG:
                mail_export_data(exporttool, filterStr[:-2], export_object, filename, emailid)
            else:
                mail_export_data.apply_async(args=(exporttool, filterStr[:-2], export_object, filename, emailid), )
            messages.add_message(request, messages.INFO,
                                 "You will get report on only Offical Ibibogroup email id : " + str(emailid))
            return HttpResponseRedirect('/admin/reports/')
        else:
            messages.add_message(request, messages.ERROR,
                                 "Email id is mandatory. Report(s) will be sent to"
                                 " a particular email id only!")
            return HttpResponseRedirect('/admin/reports/')


@staff_member_required
def getBookingExportForm(request):
    context = {}
    context['chmList'] = list(extranetvariables.ChannelManagersDict.values())
    context['report_choice'] = hotelchoice.report_choice
    context['booking_status'] = hotelchoice.bookingstatus
    context['confirm_status'] = hotelchoice.confirmstatus
    return render_to_response('reports/exportreports.html', context, context_instance=RequestContext(request))


@staff_member_required
def submitBookingExportForm(request):
    url = '/admin/reports/newhotelreports/'
    try:
        if request.POST:
            post_data = request.POST.dict()
            fromdate = post_data.get('startdate', None)
            todate = post_data.get('enddate', None)
            reporttype = post_data.get('reporttype', None)
            sortby = post_data.get('sortby', None)
            reportEmail = post_data.get('emailreport', None)
            chmname = post_data.get('chmname', None)
            date_based = post_data.get('datebased', None)
            bstatus = request.POST.getlist('bstatus[]')
            cstatus = request.POST.getlist('cstatus[]')
            username = request.user.username
            if fromdate and todate:
                noofdays = (datetime.datetime.strptime(todate, '%Y-%m-%d') -
                            datetime.datetime.strptime(fromdate, '%Y-%m-%d')).days
                if noofdays > 31:
                    messages.add_message(request, messages.INFO,
                                         "Start/End Date difference should be less than and equal to 31 days.")
                    return HttpResponseRedirect(url, messages)

            paramDict = {'fromdate': fromdate, 'todate': todate, 'reporttype': reporttype, 'sortby': sortby,
                         'reportEmail': reportEmail, 'chmname': chmname,
                         'reportStartTime': str(datetime.datetime.now()),
                         'date_based': date_based, 'booking_status': bstatus,
                         'confirm_status': cstatus, 'username': username}
            reportEmail = get_email_list(reportEmail)
            if reportEmail:
                if settings.DEBUG:
                    resp = newReportOnEmailTask([paramDict])
                else:
                    resp = newReportOnEmailTask.apply_async(args=([paramDict]), )
                if resp:
                    messages.add_message(request, messages.INFO,
                                         "You will get report on only Offical Ibibogroup or Goibibo email id : "
                                         "" + str(reportEmail) + "," + str(resp))
                else:
                    messages.add_message(request, messages.ERROR, "Please fill all required field.")
            else:
                messages.add_message(request, messages.INFO, "Please enter email id (, separted ).")

    except Exception, e:
        messages.add_message(request, messages.ERROR, "Please consult with tech team : " + str(e))
        pass
    return HttpResponseRedirect(url, messages)


@staff_member_required
def upload_reports(request):
    from hotels.constants.hotel_constants import HotelABSOReason
    context = {'user_email': request.user.email, 'abso_reasons': HotelABSOReason.get_reasons_choices()}
    request.user.has_user_mapping_permission = has_permission(request.user, 'custom_upload_hotel_user_mapping')
    request.user.show_rateplan_commission_update_uploader = request.user.is_superuser or has_permission(request.user, 'bulk_update_commission')
    request.user.show_free_cancellation_rule_create_uploader = request.user.is_superuser or has_permission(request.user, 'custom_user_type_category')
    request.user.show_flag_bit_uploader = request.user.is_superuser or has_permission(request.user, 'custom_user_type_category')
    request.user.show_create_linked_rateplan_uploader = request.user.is_superuser or has_permission(request.user, 'custom_user_type_category')
    #request.user.show_create_text_based_promotion = request.user.is_superuser or has_permission(request.user, 'custom_user_type_category')
    request.user.show_generic_hotel_records_updation = request.user.is_superuser or has_permission(request.user, 'custom_user_generic_hotel_records_updation')
    request.user.show_hostapp_migration_upload = request.user.is_superuser or has_permission(request.user, 'custom_hostapp_migration_upload')
    request.user.show_manager_mapping_uploader = request.user.is_superuser or request.user.groups.filter(
        id__in=[ROLE_SUPPLY_IND_AUDIT_ID, ROLE_SUPPLY_IND_ANALYTICS_ID]).exists()
    return render_to_response('reports/uploadreports.html', context,
                              context_instance=RequestContext(request))


@staff_member_required
def update_records(request):
    """After report file submitting this function will call"""
    logger.info("bulk update records data task")
    url = request.META.get('HTTP_REFERER', 'admin/reports/uploadreports/')
    emailid = request.POST.get('emailid', '')
    user = request.user
    if request.FILES and emailid and validate_file_type(request.FILES['updatereports'],['.csv']):
        upload_file = request.FILES[request.FILES.keys()[0]]
        upload_file_name = mdb.save("", upload_file)
        username = request.user
        if settings.DEBUG:
            bulk_update_report_data(upload_file_name, emailid, user)
        else:
            from reports.tasks import bulk_update_report_data_task
            bulk_update_report_data_task.apply_async(args=(upload_file_name, emailid, user,))

        messages.info(request, 'Record updation process has been inititated, we will mail you after task completion.')
    else:
        messages.error(request, 'Please upload valid csv file and email id to process record updation.')
    logger.info("bulk update records data task complete")
    return HttpResponseRedirect(url)

def bulk_update_report_data(filename, emailid, user):
    emailids = get_email_list(emailid)
    user_id = 0 if user is None else user.id
    logger.info("bulk update report data task started by user: " + str(user_id))
    try:
        saved_file = mdb.open(filename)
        fileContent = saved_file.read()
        dbData = [row for row in csv.reader(fileContent.splitlines(), quotechar='"', quoting=csv.QUOTE_ALL)]
        hour = datetime.datetime.now().hour
        chunkSize = settings.OFFHOUR_HOTEL_CHUNK_SIZE
        if hour >= settings.BUSINESS_HOUR_START and hour <= settings.BUSINESS_HOUR_END:
            chunkSize = settings.BUSINESS_HOUR_HOTEL_CHUNK_SIZE
        parallelChunkCount = settings.PARALLEL_REPORT_CHUNK_COUNT
        runningChunkCount = 0
        chunkCount = 0
        chunkExecutionTime = 0
        for i in range(1, len(dbData), chunkSize):
            chunkCount += 1
            runningChunkCount += 1
            chunk = dbData[i:i + chunkSize]
            chunk_file = '/tmp/chunk_update_data_report_' + str(chunkCount) + '_' + str(datetime.datetime.now()) + '.csv'
            writefile = open(chunk_file, 'w')
            data_writer = csv.writer(writefile, dialect='excel')
            data_writer.writerow(dbData[0])
            for row in chunk:
                data_writer.writerow(row)
            writefile.close()
            readfile = open(chunk_file, 'r')
            chunk_filename = mdb.save("", readfile)
            if settings.DEBUG:
                update_report_data(chunk_filename, emailid, user)
            else:
                from reports.tasks import update_report_data_task
                update_report_data_task.apply_async(args=(chunk_filename, emailid, user), countdown=chunkExecutionTime)
            if runningChunkCount >= parallelChunkCount:
                runningChunkCount = 0
                chunkExecutionTime += 60 * 60  # 1 hour
            os.remove(chunk_file)
        mdb.delete(filename)
        logger.info("bulk update report data task completed started by user: " + str(user_id))
    except Exception as e:
        logger.critical(str("update report data :") + str(e))
        sendMail('', from_email=settings.EMAIL_ALERT_SENDER, body=str(emailid) + str(e),
                 subject='ERROR - uploading report sheet ', template_id='',
                 cc_emails=settings.INGOIBIBO_ERROR_ALERT.extend(emailids), attached_file=None, bcc_emails=[])
        
def update_report_data(filename, emailid, user):
    """Update database records calling from update_records function"""
    emailids = get_email_list(emailid)
    try:
        saved_file = mdb.open(filename)
        fileContent = saved_file.read()
        dbData = [row for row in csv.reader(fileContent.splitlines(), quotechar='"', quoting=csv.QUOTE_ALL)]
        message_file = '/tmp/update_data_report_' + str(datetime.datetime.now()) + '.csv'
        writefile = open(message_file, 'w')
        data_writer = csv.writer(writefile, dialect='excel')
        field_names = dbData[0]
        no_of_column = len(field_names)
        hoteltags = [x[0] for x in hotel_tags]

        contractmanagers = getContractManagers()[1:]
        contractmanagers = [cm[0] for cm in contractmanagers]
        contractbdos = get_contract_bdos()[1:]
        contractbdos = [cb[0] for cb in contractbdos]
        marketcoordinators = get_contract_market_coordinator()[1:]
        marketcoordinators = [mc[0] for mc in marketcoordinators]
        contractadm = get_contract_adms()[1:]
        contractadm = [adm[0] for adm in contractadm]
        chain_managers = get_chain_users()[1:]
        chain_managers = [chain_manager[0] for chain_manager in chain_managers]
        pricing_specialists = get_pricing_specialist()[1:]
        pricing_specialists = [specialist[0] for specialist in pricing_specialists]
        regional_heads = get_regional_heads()[1:]
        regional_heads = [regional_head[0] for regional_head in regional_heads]
        subcategories = [x[0] for x in HotelSubCategory]


        if field_names[0] != 'hotelcode':
            sendMail('', from_email=settings.EMAIL_ALERT_SENDER, body=str(emailid) + str(field_names),
                     subject='hotelcode should be in first column', template_id='',
                     cc_emails=settings.INGOIBIBO_ERROR_ALERT.extend(emailids), attached_file=None, bcc_emails=[])
        else:
            for row in dbData[1:]:
                hcode = row[0]
                hlist = HotelDetail.objects.filter(hotelcode=hcode)
                remove_from_user = []
                update_column = dict()
                if hlist:
                    hobj = hlist[0]
                    is_enable_save = False
                    for i in range(1, no_of_column):
                        try:
                            if row[i]:
                                verify_user = False
                                if field_names[i] == 'contractmanager':
                                    row[i] = get_username_from_field_value(row[i])
                                    verify_user = row[i] in contractmanagers
                                if field_names[i] == 'contractbdo':
                                    row[i] = get_username_from_field_value(row[i])
                                    verify_user = row[i] in contractbdos
                                if field_names[i] == 'market_coordinator':
                                    row[i] = get_username_from_field_value(row[i])
                                    verify_user = row[i] in marketcoordinators
                                if field_names[i] == 'contractadm':
                                    verify_user = row[i] in contractadm
                                if field_names[i] == 'chain_manager':
                                    verify_user = row[i] in chain_managers
                                if field_names[i] == 'pricing_specialist':
                                    row[i] = get_username_from_field_value(row[i])
                                    verify_user = row[i] in pricing_specialists
                                if field_names[i] == 'regional_head':
                                    row[i] = get_username_from_field_value(row[i])
                                    verify_user = row[i] in regional_heads
                                if field_names[i] == 'hotel_category':
                                    if row[i] in HotelCategoryDict.keys():
                                        setattr(hobj, field_names[i], row[i])
                                        update_column[field_names[i]] = row[i]
                                if field_names[i] == 'hotels_subcategory':
                                    if row[i] in subcategories:
                                        setattr(hobj, field_names[i], row[i])
                                        update_column[field_names[i]] = row[i]
                                elif field_names[i] in ['tags']:
                                    if row[i].lower().replace(' ', '') in hoteltags:
                                        is_enable_save = True
                                        setattr(hobj, field_names[i], str(row[i]).strip())
                                        update_column[field_names[i]] = str(row[i]).strip()
                                if row[i] == 'None':
                                    row[i], verify_user = None, True
                                    remove_from_user.append(getattr(hobj, field_names[i]))
                                if verify_user:
                                    setattr(hobj, field_names[i], row[i])
                                    update_column[field_names[i]] = row[i]
                                elif field_names[i] not in ['tags']:
                                    data_writer.writerow(['User {} is not a {} for this hotel {}' \
                                                              .format(row[i], field_names[i], hcode)])
                        except Exception, e:
                            sendMail('', from_email=settings.EMAIL_ALERT_SENDER, body=str(emailid) + str(e),
                                     subject='uploading column is mismatch with original column', template_id='',
                                     cc_emails=settings.INGOIBIBO_ERROR_ALERT.extend(emailids), attached_file=None,
                                     bcc_emails=[])
                            break
                    if is_enable_save:
                        hobj.save(bulk=True)
                    elif update_column:
                        if 'pannumber' in update_column:
                            pan_val = update_column['pannumber']
                            is_encrypted = pan_val is not None and len(pan_val) > 10
                            log_data = {'api_specific_identifiers': {}, 'error': {}, 'request_id': ''}
                            log_message = "Update pannumber via admin_views. HotelCode: {}, Value: {}, Encrypted: {}".format(hobj.hotelcode, pan_val, is_encrypted)
                            log_data["api_specific_identifiers"]["message"] = log_message
                            inventory_logger.info(
                                message=log_message,
                                log_type="ingoibibo", bucket="update_report_data", stage="update_report_data", identifier='{}'.format(log_data)
                            )
                        # TODO: change to update, since voyager, hotelstore doesn't store this
                        # call save if tags in update_column
                        HotelDetail.objects.filter(hotelcode=hobj.hotelcode).update(**update_column)
                        users = User.objects.filter(username__in=update_column.values(), is_staff=True)
                        for user in users:
                            user.hotels.add(hobj)
                            assign_permissions(user, hobj)
                            refresh_hotel_assignment_cache(user)
                        if remove_from_user:
                            users = User.objects.filter(username__in=remove_from_user)
                            for user in users:
                                user.hotels.remove(hobj)
                                remove_permissions(user, hobj)
                                refresh_hotel_assignment_cache(user)
                        hm.updateLogMsg(user, hobj, str(", ".join(update_column.keys())) + ' Updated')
                    else:
                        data_writer.writerow(['Error for this row {}'.format(row)])
                else:
                    data_writer.writerow(['Hotel Code is not found in our database {}'.format(hcode)])

        writefile.close()
        subject = "report data has been updated"
        attached_file = open(message_file,'r')
        sendMail('', from_email=settings.EMAIL_ALERT_SENDER, body="Report data has been updated successfully",
                 subject=subject, template_id='', cc_emails=emailids, attached_file=attached_file, bcc_emails=[])
        attached_file.close()
        os.remove(message_file)
        mdb.delete(filename)
    except Exception, e:
        logger_stats.info(str("update report data") + str(e), log_type="hotelreport", bucket="update_report_data",
                          stage="update_report_data")

        sendMail('', from_email=settings.EMAIL_ALERT_SENDER, body=str(emailid) + str(e),
                 subject='ERROR - uploading report sheet ', template_id='',
                 cc_emails=settings.INGOIBIBO_ERROR_ALERT.extend(emailids), attached_file=None, bcc_emails=[])


def validate_file_type(file, extension):
    filename, ext = os.path.splitext(file.name)
    if ext.lower() in extension:
        return True
    else:
        return False


def validatecsv(file, needed_fields=()):
    if validate_file_type(file, ['.csv']):
        try:
            header = set(csv.reader(file).next())
            return len(header) == len(needed_fields) and \
                   all(x in header for x in needed_fields) and not (set(needed_fields) - header)
        except:
            return False
    else:
        return False


def valid_rows_count(file, row_count=ROW_COUNT):
    try:
        reader = csv.reader(file, delimiter = ',')
        data = list(reader)
        inventory_logger.info(message='count_rows | total rows={count}'.format(count=len(data)),
                              log_type='ingoibibo', bucket='valid_rows_count', stage='valid_rows_count')
        return len(data) < row_count
    except Exception as e:
        inventory_logger.critical(message='count_rows | error={error}'.format(error=repr(traceback.format_exc())),
                                  log_type='ingoibibo', bucket='valid_rows_count', stage='valid_rows_count')
        return False



def isNowInTimePeriod(startTime, endTime, nowTime):
    if startTime < endTime :
        return nowTime >= startTime and nowTime <= endTime
    else :
        return False




def validate_term_code_csv(file, needed_fields=()):
    try:
        header = set(csv.reader(file).next())
        if 'Vendor Code' not in header:
            return False
        return len(header) <= len(needed_fields) and all(x in needed_fields for x in header) and not (set(header) - set(needed_fields))
    except:
        return False

def validate_hotel_csv(file):
    """
    :param file:
    :return:
    hotelcode is necessary and fields that are present in hoteldetail.
    """
    # Since this function validates the column names for hotel detail table. A requirement is there to update data in bulk for hotel metadata.
    # Hence offline_agreement_flag has also been added as custom allowed flag.
    custom_fields = ['offline_agreement_flag', 'insurmount_tnc_flag', 'deactivation_reason', 'deactivation_comment',
                     'deflaging_reason', 'deflaging_comment', 'pms_name', 'source_config_id']
    try:
        header = set(csv.reader(file).next())
        hoteldetail_fields = set([field.name for field in HotelDetail._meta.fields])
        hoteldetail_fields.update(custom_fields)
        return 'hotelcode' in header and not(header - hoteldetail_fields)
    except:
        return False


def validate_csv_pah_uploader(file, needed_fields=None):
    try:
        header = set(csv.reader(file).next())
        return all(x in header for x in needed_fields)
    except:
        return False


## sql_injection id: 1 /admin/reports/uploadreports/updatemapping/ R
def update_hotel_user_mapping(request):
    if request.FILES and request.FILES.get('mappingcsv', '') and \
            validatecsv(request.FILES['mappingcsv'], needed_fields=("username", "hotelcodes", "action")):
        f = request.FILES['mappingcsv']
        save_to_mdb = mdb.save("mappingcsv", f)
        emailid = [request.user.email] if request.user else settings.INGOIBIBO_ERROR_ALERT
        upload_mapping_csv.apply_async(args=(save_to_mdb, emailid))
        messages.success(request, "The file is uploaded successfully.")
    else:
        messages.error(request, 'Please upload a valid csv file.')
    return HttpResponseRedirect('/admin/reports/uploadreports/')


@staff_member_required
def mail_hotel_invoice(request):
    fields = ("invoiceno", "refno", "invoicedate", "duedate",
              "vendorname", "address", "mobile", "city", "qty", "rate",
              "commision", "servicetax", "total", "amount_in_word",
              "description", "emailid")
    if request.user.username in settings.STAFF_MEMBER_INVOICE_UPLOAD:
        if request.FILES and request.FILES.get('uploadinvoice', '') and validatecsv(request.FILES['uploadinvoice'], fields):
            f = request.FILES['uploadinvoice']
            save_to_mdb = mdb.save("uploadinvoice", f)
            if settings.DEBUG:
                from reports.hotelierreports import send_finance_invoice_to_hotelier
                send_finance_invoice_to_hotelier(save_to_mdb)
            else:
                mail_finance_hotel_invoice.apply_async(args=(save_to_mdb,))
            messages.success(request, "The file has been uploaded successfully.")
        else:
            messages.error(request, 'Please upload a valid csv file.')
    else:
        messages.error(request, 'You do not have permission to upload invoice file.')
    return HttpResponseRedirect('/admin/reports/uploadreports/')


@staff_member_required
def hotel_ledger_reports_view(request):
    context = {}
    if request.user.is_superuser:
        return render_to_response('reports/hotel_ledger_reports_view.html', context,
                                  context_instance=RequestContext(request))
    else:
        return HttpResponseForbidden('Access Denied')

def validate_hotel_ledger_csv(file):
    required_fields = ("hotelcode", "startdate", "enddate", "adjustmentflag")
    try:
        header = set(csv.reader(file).next())
        return all(x in header for x in required_fields)
    except:
        return False

@staff_member_required
def get_hotel_ledger_records(request):
    if (request.user.username in settings.HOTEL_LEDGER_REPORT_USERNAME or
            request.user.is_superuser):
       if (request.FILES and request.FILES.get('hotelledgers', '') and
               validate_hotel_ledger_csv(request.FILES['hotelledgers'])):
          uploaded_file = request.FILES['hotelledgers']
          email_id = request.user.email
          save_to_mdb = mdb.save("hotelledgers", uploaded_file)
          if settings.HOST in settings.PROD_HOSTS:
              hotel_ledger_report_task.apply_async(args=(save_to_mdb, email_id))
          else:
              from reports.hotel_ledger_reports import get_hotel_ledgers
              get_hotel_ledgers(save_to_mdb, email_id)

          messages.success(request, "The file has been uploaded successfully.")
       else:
          messages.error(request, 'Please upload a valid csv file.')
    else:
       messages.error(request, 'You do not have permission to upload invoice file.')

    return HttpResponseRedirect('/admin/reports/hotel-ledger-reports/')


@staff_member_required
@require_http_methods(['POST'])
def upload_lat_long(request):
    if request.FILES and validate_file_type(request.FILES['lat_long_file'], ['.csv']):
        lat_long_file = request.FILES.get('lat_long_file', None)
        notifier_mail = settings.LAT_LONG_UPDATE_WATCHERS + [request.POST.get('notifier_mail', None)]
        if not (request.user.is_superuser or has_permission(request.user, 'custom_upload_hotel_lat_long')):
            messages.error(request, 'Permission Denied')
            return HttpResponseRedirect('/admin/reports/uploadreports/')

        if not lat_long_file:
            messages.error(request, 'File is Required')
            return HttpResponseRedirect('/admin/reports/uploadreports/')

        response = {'success': False, 'error': 'Error Occurred', 'message': '', 'queued': False}
        temp_filename = '/tmp/lat_long_file.csv'
        try:
            lat_long = dict()
            with open(temp_filename, 'w') as temp_file:
                temp_file.writelines(lat_long_file.file.readlines())
            with open(temp_filename, 'rU') as temp_file:
                csv_reader = csv.DictReader(temp_file)
                for row in csv_reader:
                    if row['hotelcode'] and row['latitude'] and row['longitude']:
                        lat_long[row['hotelcode']] = {'latitude': row['latitude'], 'longitude': row['longitude']}

            batch_size = 100
            file_name = lat_long_file._name
            if len(lat_long.keys()) < batch_size:
                update_lat_long_task(lat_long, file_name, notifier_mail)
            else:
                response['queued'] = True
                items = lat_long.items()
                start = 0
                stop = len(items)
                for offset in xrange(start, stop, batch_size):
                    update_lat_long_task.apply_async(args=(dict(items[offset:offset+batch_size]),
                                                           file_name, notifier_mail))

            response['success'] = True
            response['error'] = ''
            response['message'] = 'Successfully Completed'
            os.remove(temp_filename)
        except KeyError:
            response['error'] = 'Invalid CSV File Refer the Sample CSV'

        except Exception, e:
            logger.error('%s\t%s\t%s\t%s\t' % ('reports.admin_view', 'upload_lat_long', str(e),
                                               repr(traceback.format_exc())))
            response['error'] = 'Error Occurred'

        if response['success']:
            message = response['message']
            if response['queued']:
                message += ' But will take some time to get reflected'

            messages.success(request, message)

        else:
            messages.error(request, response['error'])
    else:
        messages.error(request, 'Please upload a valid csv file.')

    return HttpResponseRedirect('/admin/reports/uploadreports')


@staff_member_required
@require_http_methods(['POST'])
def upload_gostays(request):
    go_stay_file = request.FILES.get('gostay-file', None)
    show_error = False
    message = 'We will mail you after task completion.'
    if not (request.user.is_superuser or has_permission(request.user,
                                                        'custom_activate_hotel_gostay_flag') or is_member(request.user, ['ROLE_SUPPLY_IND_ALTACCO_ZM'])):
        message = 'Permission Denied'
        show_error = True
    if not(go_stay_file and validatecsv(go_stay_file, needed_fields=('hotelcode', 'gostayflag', 'approver', 'commission'))):
        message = 'Please upload a valid CSV file. Refer sample CSV.'
        show_error = True

    email_id = request.user.email
    if show_error:
        messages.error(request, message)
    else:
        messages.success(request, message)
        go_stay_file = mdb.save('gostayfile', go_stay_file)
        if settings.HOST in settings.PROD_HOSTS:
            upload_gostay_task.apply_async(args=(go_stay_file, email_id, request.user))
        else:
            upload_gostay_task(go_stay_file, email_id, request.user)
    return HttpResponseRedirect('/admin/reports/uploadreports')


@staff_member_required
@require_http_methods(['POST'])
def upload_advantage_flag(request):
    advantage_file = request.FILES.get('advantage-file', None)
    show_error = False
    message = 'We will mail you after task completion.'
    if not (request.user.is_superuser or has_permission(request.user,
                                                        'custom_activate_hotel_gostay_flag') or is_member(request.user, ['ROLE_SUPPLY_IND_ALTACCO_ZM'])):
        message = 'Permission Denied'
        show_error = True
    if not(advantage_file and validatecsv(advantage_file, needed_fields=('hotelcode', 'advantageflag', 'approver', 'commission'))):
        message = 'Please upload a valid CSV file. Refer sample CSV.'
        show_error = True
    if not valid_rows_count(file=advantage_file):
        message = ERROR_MESSAGE.format(row_count=ROW_COUNT)
        show_error = True

    if show_error:
        messages.error(request, message)
    else:
        messages.success(request, message)
        advantage_file = mdb.save('advantagefile', advantage_file)
        if settings.HOST in settings.PROD_HOSTS:
            upload_advantage_flag_task.apply_async(args=(request.user, advantage_file,))
        else:
            upload_advantage_flag_task(request.user, advantage_file)
    return HttpResponseRedirect('/admin/reports/uploadreports')

@staff_member_required
@require_http_methods(['POST'])
def upload_fake_hotels(request):
    fake_hotels_file = request.FILES.get('fake-hotels-file', None)
    show_error = False
    message = 'Task will be completed successfully.'
    # We can give permission to specific people
    # if not (request.user.is_superuser or has_permission(request.user,
    #                                                     'custom_activate_hotel_gostay_flag')):
    #     message = 'Permission Denied'
    #     show_error = True
    if not(fake_hotels_file and validatecsv(fake_hotels_file, needed_fields=('hotelcode', 'fakeflag'))):
        message = 'Please upload a valid CSV file. Refer sample CSV.'
        show_error = True
    if not valid_rows_count(file=fake_hotels_file):
        message = ERROR_MESSAGE.format(row_count=ROW_COUNT)
        show_error = True

    if show_error:
        messages.error(request, message)
    else:
        messages.success(request, message)
        fake_hotels_file = mdb.save('fakehotelsfile', fake_hotels_file)
        if settings.HOST in settings.PROD_HOSTS:
            upload_fake_hotels_task.apply_async(args=(request.user, fake_hotels_file,))
        else:
            upload_fake_hotels_task(request.user, fake_hotels_file)
    return HttpResponseRedirect('/admin/reports/uploadreports')

@staff_member_required
@require_http_methods(['POST'])
def upload_hotel_remove_homestay_funnel(request):
    remove_homestay_funnel_file = request.FILES.get('hotel-remove-homestay-funnel-file', None)
    show_error = False
    message = 'Task will be completed successfully.'
    # if not (request.user.is_superuser or has_permission(request.user,
    #                                                     'custom_activate_hotel_gostay_flag')):
    #     message = 'Permission Denied'
    #     show_error = True
    if not(remove_homestay_funnel_file and validatecsv(remove_homestay_funnel_file, needed_fields=('hotelcode', 'removehomestayfunnel'))):
        message = 'Please upload a valid CSV file. Refer sample CSV.'
        show_error = True
    if not valid_rows_count(file=remove_homestay_funnel_file):
        message = ERROR_MESSAGE.format(row_count=ROW_COUNT)
        show_error = True

    if show_error:
        messages.error(request, message)
    else:
        messages.success(request, message)
        remove_homestay_funnel_file = mdb.save('remove_homestay_funnel_file', remove_homestay_funnel_file)
        if settings.HOST in settings.PROD_HOSTS:
            upload_hotel_remove_homestay_funnel_task.apply_async(args=(request.user, remove_homestay_funnel_file,))
        else:
            upload_hotel_remove_homestay_funnel_task(request.user, remove_homestay_funnel_file)
    return HttpResponseRedirect('/admin/reports/uploadreports')


@staff_member_required
@require_http_methods(['POST'])
def change_pah_status(request):
    request_data = fetch_client_details(request)
    try:
        modify_pah_state_file = request.FILES.get('alter-pah-state', None)
        user = request.user
        show_error = False
        start_date = request.POST['start_date']
        end_date = request.POST['end_date']
        based_on = request.POST['based_on']
        action = request.POST['action']

        key = str(user.id) + '_' + 'pah_uploader'
        UPLOAD_RESTRICTED_TIME = 240  # in minutes

        if redis_server:
            value = redis_server.get(key)
            if value:
                diff = datetime.datetime.now() - datetime.datetime.strptime(value, "%Y-%m-%d-%H-%M")
                minutes = UPLOAD_RESTRICTED_TIME - diff.seconds / 60;
                messages.error(request, 'User can upload only one sheet in %s minutes. Try again after %s minutes' % (
                                    UPLOAD_RESTRICTED_TIME, minutes))
                return HttpResponseRedirect('/admin/reports/uploadreports')

        query_dict = {}
        if start_date:
            s_date = start_date.split('/')
            start_date = datetime.date(int(s_date[2]), int(s_date[0]), int(s_date[1]))
        if end_date:
            e_date = end_date.split('/')
            end_date = datetime.date(int(e_date[2]), int(e_date[0]), int(e_date[1]))
        query_dict['start_date'] = start_date
        query_dict['end_date'] = end_date
        query_dict['based_on'] = based_on
        query_dict['action'] = action

        today = datetime.date.today()
        message = 'We will mail you after task completion.'

        if action != 'make_pah':
            if not start_date or not end_date:
                message = 'Please fill dates'
                show_error = True

            if start_date < today:
                message = 'Please select date greater or equal to today'
                show_error = True

            if end_date < start_date:
                message = 'End date must be greater or equal to start date'
                show_error = True

        if not (request.user.is_superuser or has_permission(request.user, 'custom_upload_pah_file')):
            message = 'Permission Denied'
            show_error = True

        csv_validated = False
        if based_on == 'hotel_code':
            csv_validated = validate_csv_pah_uploader(modify_pah_state_file, needed_fields=['hotelcode'])
        elif based_on == 'state':
            csv_validated = validate_csv_pah_uploader(modify_pah_state_file, needed_fields=['state'])
        else:
            csv_validated = validate_csv_pah_uploader(modify_pah_state_file, needed_fields=['state', 'city'])
        if not modify_pah_state_file or not csv_validated:
            message = 'Please upload a valid CSV file. state and city  or hotelcode field are required.'
            show_error = True

        if show_error:
            messages.error(request, message)
            return HttpResponseRedirect('/admin/reports/uploadreports')
        else:
            messages.success(request, message)
            modify_pah_state_file = mdb.save('modifypahstate', modify_pah_state_file)
            # Logging in inventory to keep a record of file data
            inventory_logger.info(message='Make PAH rateplan bulk uploader file name: %s by user id %s for action %s'
                                          % (str(modify_pah_state_file), user.id, query_dict['action']),
                                        log_type='ingoibibo', bucket='change_pah_status', stage='change_pah_status')
            if settings.DEBUG:
                modify_pah_state_func(modify_pah_state_file, query_dict, request.user, request_data)
            else:
                modify_pah_state_func.apply_async(args=(modify_pah_state_file, query_dict, request.user, request_data),
                                                  soft_timeout=7200, timeout=9000)

        if redis_server:
            redis_server.set(key, datetime.datetime.now().strftime("%Y-%m-%d-%H-%M"), timeout=UPLOAD_RESTRICTED_TIME * 60)

    except Exception, e:
        message = 'Please try again'
        messages.error(request, message)

    return HttpResponseRedirect('/admin/reports/uploadreports')


@staff_member_required
@require_http_methods(['POST'])
def activate_pah_flag(request):
    try:
        modify_pah_flag_file = request.FILES.get('pah-file', None)
        show_error = False
        pah_inventory_threshold = request.POST['thr-value']

        message = 'We will mail you after task completion.'

        if not (request.user.is_superuser or has_permission(request.user, 'custom_upload_pah_file')):
            message = 'Permission Denied'
            show_error = True

        csv_validated = validate_csv_pah_uploader(modify_pah_flag_file, needed_fields=['Hotel Code'])

        if not modify_pah_flag_file or not csv_validated:
            message = 'Please upload a valid CSV file. Hotel Code field is required.'
            show_error = True

        email_id = request.user.email
        if show_error:
            messages.error(request, message)
        else:
            messages.success(request, message)
            modify_pah_flag_file = mdb.save('modifypahstate', modify_pah_flag_file)
            if settings.DEBUG:
                enable_pah_flag(modify_pah_flag_file, pah_inventory_threshold, email_id)
            else:
                enable_pah_flag.apply_async(args=(modify_pah_flag_file, pah_inventory_threshold, email_id))

    except Exception, e:
        message = 'Please try again'
        messages.error(request, message)

    return HttpResponseRedirect('/admin/reports/uploadreports')


def update_hotel_level_fields(request):
    from hotels.tasks import parent_upload_hotel_fields_task
    if not (request.user.is_superuser or has_permission(request.user,
                                                        'custom_upload_hotel_fields')):
        messages.error(request, 'Permission Denied')
        return HttpResponseRedirect('/admin/reports/uploadreports/')
    CSV_ROW_COUNT_LIMIT = 5*ROW_COUNT
    if request.FILES and request.FILES.get('hotelcsv', '') and validate_hotel_csv(request.FILES['hotelcsv']):
        f = request.FILES['hotelcsv']
        if not valid_rows_count(f, row_count=CSV_ROW_COUNT_LIMIT):
            messages.error(request, ERROR_MESSAGE.format(row_count=CSV_ROW_COUNT_LIMIT))
            return HttpResponseRedirect('/admin/reports/uploadreports/')
        if cache.get(request.user.username + ':generic_hotel_update_' + f.name):
            messages.error(request, "User uploaded same file again")
            return HttpResponseRedirect('/admin/reports/uploadreports/')
        else:
            # 4 Hour cool off period
            cache.set(request.user.username + ':generic_hotel_update_' + f.name, True, 60 * 60 * 4)
        save_to_mdb = mdb.save('hotelcsv', f)
        if settings.DEBUG:
            parent_upload_hotel_fields_task(save_to_mdb, request.user)
        else:
            parent_upload_hotel_fields_task.apply_async(args=(save_to_mdb, request.user, ))

        messages.success(request, "The file is uploaded successfully.")
    else:
        messages.error(request, 'Please upload a valid csv file.')
    return HttpResponseRedirect('/admin/reports/uploadreports/')


def refresh_hotel_assignment_cache(user):
    cache_key = 'all_child_hotels_list_' + user.username
    code_cache = 'all_hotelcodelist_' + user.username
    cache.set(cache_key, [])
    cache.set(code_cache, [])


def update_gst_details(request):
    fields = ('hotelcode', 'gstn', 'address', 'zipcode', 'hotelemail', 'taxee',
              'designation', 'contactno')
    if request.FILES and request.FILES.get('hotelcsv', '') and \
            validatecsv(request.FILES['hotelcsv'], fields):
        f = request.FILES['hotelcsv']
        save_to_mdb = mdb.save('hotelcsv', f)
        if not settings.DEBUG:
            upload_gst_csv.apply_async(args=(save_to_mdb, request.user))
        else:
            upload_gst_csv(save_to_mdb, request.user)
        messages.success(request, "The file is uploaded successfully.")
    else:
        messages.error(request, 'Please upload a valid csv file.')
    return HttpResponseRedirect('/admin/reports/uploadreports/')


@staff_member_required
@require_http_methods(['POST'])
def create_add_ons(request):
    add_on_file = request.FILES.get('addoncsv', None)
    notifier_mail = [request.POST.get('notifier_mail', None)]
    user = request.user
    #if not (request.user.is_superuser or request.user.groups.filter(name='SPOORS_LAT_LONG_UPDATE').exists()):
    #    messages.error(request, 'Permission Denied')
    #     return HttpResponseRedirect('/admin/reports/uploadreports/')

    if not add_on_file:
        messages.error(request, 'File is Required')
        return HttpResponseRedirect('/admin/reports/uploadreports/')

    if not validate_addon_csv(add_on_file):
        messages.error(request, 'Please input valid csv file')
        return HttpResponseRedirect('/admin/reports/uploadreports/')

    response = {'success': False, 'error': 'Error Occurred', 'message': '', 'queued': False}
    save_to_mdb = mdb.save('addoncsv', add_on_file)
    try:
        if settings.HOST in settings.PROD_HOSTS:
            create_add_ons_task.apply_async(args=(save_to_mdb, notifier_mail, user))
        else:
            create_add_ons_task(save_to_mdb, notifier_mail, user)
        response['success'] = True
        response['error'] = ''
        response['message'] = 'Successfully Completed'
    except KeyError:
        response['error'] = 'Invalid CSV File Refer the Sample CSV'

    except Exception, e:
        logger.error('%s\t%s\t%s\t%s\t' % ('reports.admin_view', 'upload_lat_long', str(e),
                                           repr(traceback.format_exc())))
        response['error'] = 'Error Occurred'

    if response['success']:
        message = response['message']
        if response['queued']:
            message += ' But will take some time to get reflected'

        messages.success(request, message)

    else:
        messages.error(request, response['error'])

    return HttpResponseRedirect('/admin/reports/uploadreports')


def validate_addon_csv(file):
    required_fields = ('addonType', 'addonLevel', 'relatedCode', 'chargeType', 'chargeValue', 'margin',
                        'cancellationRule', 'inventoryCutoff', 'startTime', 'endTime')
    try:
        header = set(csv.reader(file).next())
        return all(x in header for x in required_fields)
    except:
        return False

def pending_hotel_tnc_mailer(request):
    fields = ('hotelcode', 'hotelemail')
    if request.FILES and request.FILES.get('hotelmailerfile', '') and \
            validatecsv(request.FILES['hotelmailerfile'], fields):
        f = request.FILES['hotelmailerfile']
        save_to_mdb = mdb.save('hotelmailerfile', f)
        result = task_pending_hotel_tnc_mailer.apply_async(args=(save_to_mdb, request.user))
        job_id = result.id

        messages.success(request, "The file is uploaded successfully. Job Id to Track the status - %s"%job_id)
    else:
        messages.error(request, 'Please upload a valid csv file.')
    return HttpResponseRedirect('/admin/reports/uploadreports/')

# existing vendor update
@staff_member_required
@require_http_methods(['POST'])
def map_existing_vendor(request):
    vendor_file = request.FILES.get('updateVendorReports', None)
    show_error = False
    message = 'We will mail you after task completion.'
    inventory_logger.info(message='map_existing_vendor api file name %s' % (vendor_file), log_type='ingoibibo', bucket='ClientAPI',
                              stage='map_existing_vendor')
    if not (request.user.is_superuser or has_permission(request.user, 'custom_upload_vendor_vcc')):
        message = 'Permission Denied'
        show_error = True
    if cache.get(request.user.username+'map_existing_vendor_'+vendor_file.name):
        message = "User upload same file with in 5 hours, Please upload diffent file or change file name"
        show_error = True
    if not(vendor_file and validatecsv(vendor_file, needed_fields=('Hotel Code', 'Company', 'Vendor Code'))):
        message = 'Please upload a valid CSV file. Refer sample CSV.'
        show_error = True

    if not valid_rows_count(file=vendor_file):
        message = ERROR_MESSAGE.format(row_count=ROW_COUNT)
        show_error = True

    email_id = request.user.email
    if show_error:
        messages.error(request, message)
    else:
        cache.set(request.user.username+'map_existing_vendor'+vendor_file.name, True, 60*60*5)
        inventory_logger.info(message='redis key: %s' % (request.user.username+'map_existing_vendor'+vendor_file.name), log_type='ingoibibo', bucket='ClientAPI',
                              stage='map_existing_vendor')
        messages.success(request, message)
        vendor_file = mdb.save('vendorfile', vendor_file)
        if settings.HOST in settings.PROD_HOSTS: #TODO: disable celery for testing
            map_existing_vendor_task.apply_async(args=(vendor_file, email_id, request.user))
        else:
            map_existing_vendor_task(vendor_file, email_id, request.user)
    return HttpResponseRedirect('/admin/reports/uploadreports')

# term code update
@staff_member_required
@require_http_methods(['POST'])
def vendor_sync(request):
    vendor_file = request.FILES.get('updateVendorReports', None)
    show_error = False
    message = 'We will mail you after task completion.'
    inventory_logger.info(message='vendor_sync api file name %s' % (vendor_file), log_type='ingoibibo', bucket='ClientAPI',
                              stage='vendor_sync')
    if not (request.user.is_superuser or has_permission(request.user, 'custom_upload_vendor_vcc')):
        message = 'Permission Denied'
        show_error = True
    # if cache.get(request.user.username+ 'vendor_sync' +vendor_file.name):
    #     message = "User upload same file with in 5 hours, Please upload diffent file or change file name"
    #     show_error = True
    if not(vendor_file and validatecsv(vendor_file, needed_fields=(['Vendor Code']))):
        message = 'Please upload a valid CSV file. Refer sample CSV.'
        show_error = True

    if not valid_rows_count(file=vendor_file):
        message = ERROR_MESSAGE.format(row_count=ROW_COUNT)
        show_error = True

    email_id = request.user.email
    if show_error:
        messages.error(request, message)
    else:
        cache.set(request.user.username + 'vendor_sync' + vendor_file.name, True, 60*60*5)
        inventory_logger.info(message='redis key: %s' % (request.user.username+vendor_file.name), log_type='ingoibibo', bucket='ClientAPI',
                              stage='vendor_sync')
        messages.success(request, message)
        vendor_file = mdb.save('vendorfile', vendor_file)
        if settings.HOST in settings.PROD_HOSTS: #TODO: disable celery for testing
            vendor_sync_task.apply_async(args=(vendor_file, email_id, request.user))
        else:
            vendor_sync_task(vendor_file, email_id, request.user)
    return HttpResponseRedirect('/admin/reports/uploadreports')

# vendor sync
@staff_member_required
@require_http_methods(['POST'])
def update_termcode_methodcode(request):
    vendor_file = request.FILES.get('updateVendorReports', None)
    show_error = False
    message = 'We will mail you after task completion.'
    inventory_logger.info(message='Upload_vendor api file name %s' % vendor_file, log_type='ingoibibo',
                          bucket='ClientAPI', stage='update_termcode_methodcode')
    if not (request.user.is_superuser or has_permission(request.user, 'custom_upload_vendor_vcc')):
        message = 'Permission Denied'
        show_error = True
    if cache.get(request.user.username + 'update_termcode_methodcode' + vendor_file.name):
        message = "User upload same file with in 5 hours, Please upload diffent file or change file name"
        show_error = True
    if not(vendor_file and validate_term_code_csv(vendor_file, needed_fields=UPDATE_VENDOR_FIELDS)):
        message = 'Please upload a valid CSV file. Refer sample CSV.'
        show_error = True
    # This count has been temporally increased to support vendor cleanup task. Can be removed in future if required.
    if not valid_rows_count(vendor_file, row_count=UPDATE_VENDOR_MAX_ROW_ALLOWED ):
        message = ERROR_MESSAGE.format(row_count=UPDATE_VENDOR_MAX_ROW_ALLOWED)
        show_error = True

    email_id = request.user.email
    if show_error:
        messages.error(request, message)
    else:
        cache.set(request.user.username+vendor_file.name, True, 60*60*5)
        inventory_logger.info(message='redis key: %s' % (request.user.username + 'update_termcode_methodcode' +
                                                         vendor_file.name),
                              log_type='ingoibibo', bucket='ClientAPI', stage='update_termcode_methodcode')
        messages.success(request, message)
        vendor_file = mdb.save('vendorfile', vendor_file)
        if settings.HOST in settings.PROD_HOSTS:
            update_vendor_field_task.apply_async(args=(vendor_file, email_id, request.user))
        else:
            update_vendor_field_task(vendor_file, email_id, request.user)
    return HttpResponseRedirect('/admin/reports/uploadreports')

# vendor create
@staff_member_required
@require_http_methods(['POST'])
def new_upload_vendor(request):
    vendor_file = request.FILES.get('updateVendorReports', None)
    show_error = False
    message = 'We will mail you after task completion.'
    inventory_logger.info(message='Upload_vendor api file name %s' % (vendor_file), log_type='ingoibibo',
                          bucket='ClientAPI', stage='upload_vendor')
    if not (request.user.is_superuser or has_permission(request.user, 'custom_upload_vendor_vcc')):
        message = 'Permission Denied'
        show_error = True
    if cache.get(request.user.username + 'upload_vendor' + vendor_file.name):
        message = "User upload same file with in 5 hours, Please upload diffent file or change file name"
        show_error = True
    if not(vendor_file and validatecsv(vendor_file, needed_fields=CREATE_VENDOR_FIELDS)):
        message = 'Please upload a valid CSV file. Refer sample CSV.'
        show_error = True

    if not valid_rows_count(file=vendor_file):
        message = ERROR_MESSAGE.format(row_count=ROW_COUNT)
        show_error = True

    email_id = request.user.email
    if show_error:
        messages.error(request, message)
    else:
        cache.set(request.user.username+vendor_file.name, True, 60*60*5)
        inventory_logger.info(message='redis key: %s' % (request.user.username + 'upload_vendor' + vendor_file.name),
                              log_type='ingoibibo', bucket='ClientAPI', stage='upload_vendor')
        messages.success(request, message)
        vendor_file = mdb.save('vendorfile', vendor_file)
        if settings.HOST in settings.PROD_HOSTS:
            vendor_create_task.apply_async(args=(vendor_file, email_id, request.user))
        else:
            vendor_create_task(vendor_file, email_id, request.user)
    return HttpResponseRedirect('/admin/reports/uploadreports')


@staff_member_required
@require_http_methods(['POST'])
def vendor_tan_status_update(request):
    vendor_file = request.FILES.get('updateVendorReports', None)
    show_error = False
    message = 'We will mail you after task completion.'
    inventory_logger.info(message='Upload_vendor api file name %s' % (vendor_file), log_type='ingoibibo',
                          bucket='ClientAPI', stage='vendor_tan_status_update')
    email_id = request.user.email
    if not(vendor_file and validatecsv(vendor_file, needed_fields=(['id']))):
        message = 'Please upload a valid CSV file. Refer sample CSV.'
        show_error = True
    if not valid_rows_count(vendor_file, row_count=UPDATE_VENDOR_MAX_ROW_ALLOWED ):
        message = ERROR_MESSAGE.format(row_count=UPDATE_VENDOR_MAX_ROW_ALLOWED)
        show_error = True
    if show_error:
        messages.error(request, message)
    else:
        cache.set(request.user.username+vendor_file.name, True, 60*60*5)
        inventory_logger.info(message='redis key: %s' % (request.user.username + 'upload_vendor_tan_status' + vendor_file.name),
                              log_type='ingoibibo', bucket='ClientAPI', stage='vendor_tan_status_update')
        messages.success(request, message)
        vendor_file = mdb.save('vendorfile', vendor_file)
        if settings.HOST in settings.PROD_HOSTS:
            update_vendor_tan_status.apply_async(args=(vendor_file, email_id, request.user))
        else:
            update_vendor_tan_status(vendor_file, email_id, request.user)
    return HttpResponseRedirect('/admin/reports/uploadreports')


@staff_member_required
@require_http_methods(['POST'])
def update_vcc_payment(request):
    vcc_file = request.FILES.get('updateVCCReports', None)
    show_error = False
    message = 'We will mail you after task completion.'
    inventory_logger.info(message='update_vcc_payment api file name %s' % (vcc_file), log_type='ingoibibo',
                          bucket='ClientAPI', stage='update_vcc_payment')
    if not (request.user.is_superuser or has_permission(request.user, 'custom_update_vcc')):
        message = 'Permission Denied'
        show_error = True
    if cache.get(request.user.username + 'update_vcc' + vcc_file.name):
        message = "User upload same file with in 5 hours, Please upload different file or change file name"
        show_error = True
    if not(vcc_file and validatecsv(vcc_file, needed_fields=CREATE_VCC_FIELDS)):
        message = 'Please upload a valid CSV file. Refer sample CSV.'
        show_error = True
    email_id = request.user.email
    if show_error:
        messages.error(request, message)
    else:
        # 5 hours as TTL
        cache.set(request.user.username + 'update_vcc' + vcc_file.name, True, 60*60*5)
        inventory_logger.info(message='redis key: %s' % (request.user.username + 'update_vcc' + vcc_file.name),
                              log_type='ingoibibo', bucket='ClientAPI', stage='upload_vendor')
        messages.success(request, message)
        vendor_file = mdb.save('vccfile', vcc_file)
        if False and settings.HOST in settings.PROD_HOSTS:
            vcc_update_bulk_task.apply_async(args=(vendor_file, email_id, request.user))
        else:
            vcc_update_bulk_task(vendor_file, email_id, request.user)
    return HttpResponseRedirect('/admin/reports/uploadreports')


@staff_member_required
@require_http_methods(['POST'])
def create_bulk_offer(request):
    try:
        data = request.POST
        offer_condition_data = dict()
        content_type = ContentType.objects.get(id=27)
        checkin_start_date = None
        checkin_end_date = None
        booking_start_date = None
        booking_end_date = None
        if data['checkin_start_date']:
            checkin_start_date = datetime.datetime.strptime(data['checkin_start_date'], '%m/%d/%Y')
        if data['checkin_end_date']:
            checkin_end_date = datetime.datetime.strptime(data['checkin_end_date'], '%m/%d/%Y')
        if data['booking_start_date']:
            booking_start_date = datetime.datetime.strptime(data['booking_start_date'], '%m/%d/%Y')
        if data['booking_end_date']:
            booking_end_date = datetime.datetime.strptime(data['booking_end_date'], '%m/%d/%Y')

        if booking_start_date and booking_end_date and booking_end_date < booking_start_date:
            messages.error(request, 'booking end date should be greater than booking start date')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        if checkin_start_date and checkin_end_date and checkin_end_date < checkin_start_date:
            messages.error(request, 'checkin end date should be greater than checkin start date')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        list_value = dict(data.lists())
        if data.has_key('checkin_daylist'):
            offer_condition_data['checkinweekday'] = list_value['checkin_daylist']
        else:
            messages.error(request, 'at least one checkin day should be selected')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        if data.has_key('booking_daylist'):
            offer_condition_data['bookingweekday'] = list_value['booking_daylist']
        else:
            messages.error(request, 'at least one booking day should be selected')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        if data.has_key('segment_list'):
            segment_list = list_value['segment_list']
        else:
            messages.error(request, 'at least one segment should be selected')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        offer_condition_data['checkindatestart'] = checkin_start_date
        offer_condition_data['checkoutdateend'] = checkin_end_date
        offer_condition_data['bookingdatestart'] = booking_start_date
        offer_condition_data['bookingdateend'] = booking_end_date + datetime.timedelta(days=1)
        offer_condition_data['description'] = data['offer_description']
        offer_condition_data['content_type'] = content_type
        offer_condition_data['offercategory'] = 'customised'
        offer_condition_data['offercondition'] = 'all'
        offer_condition_data['isactive'] = 1
        offer_condition_data['pah_applicable'] = int(data['pah_applicable'])
        offer_condition_data['offer_type_flags'] = {"only_pah_applicable": int(data.get('only_pah_applicable', False)),
                                                    "advantage_program": int(data.get('advantage_program', False))}
        offer_condition_data['user'] = request.user

        offer_value_data = dict()
        offer_value_data['channel'] = data['channel']
        offer_value_data['segment'] = segment_list
        offer_value_data['offer_basis'] = 'commission'
        offer_value_data['offer_type'] = 'percentage'
        offer_value_data['isactive'] = 1
        offer_value_data['description'] = offer_condition_data['description']
        offer_value_data['user'] = request.user

        user_id = request.user.id
        offer_file = request.FILES.get('offercsv', None)

        if not offer_file:
            messages.error(request, 'Please upload a csv file')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        if not validatecsv(offer_file, needed_fields=('hotelcode', 'offervalue', 'approver')):
            messages.error(request, 'Please upload valid csv file')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        if redis_server:
            key = str(user_id)+'_'+str(offer_file)
            value = redis_server.get(key)
            if value:
                messages.error(request, 'processing previously uploaded file. please try after some time.')
                return HttpResponseRedirect('/admin/reports/uploadreports')

        save_to_mdb = mdb.save('offercsv', offer_file)

        if settings.HOST in settings.PROD_HOSTS:
            create_offer_task.apply_async(args=(save_to_mdb, offer_condition_data, offer_value_data, ))
        else:
            create_offer_task(save_to_mdb, offer_condition_data, offer_value_data)

        if redis_server:
            key = str(user_id)+'_'+str(offer_file)
            redis_server.set(key, 1, timeout=10*60)
        messages.success(request, "The file is uploaded successfully")
    except Exception, e:
        messages.error(request, 'Exception Occured')
    return HttpResponseRedirect('/admin/reports/uploadreports')

def hotel_usp_records(request):
    fields = ('hotelcode', 'tag', 'usp_text')
    if request.FILES and request.FILES.get('hotel_usp_file', '') and \
            validatecsv(request.FILES['hotel_usp_file'], fields):
        f = request.FILES['hotel_usp_file']
        save_to_mdb = mdb.save('hotel_usp_file', f, params=None, prepend_file_name='hotel_usp_file')
        result = task_hotel_usp_create_from_input_file.apply_async(args=(save_to_mdb, request.user))
        job_id = result.id

        messages.success(request, "The file is uploaded successfully. Job Id to Track the status - %s"%job_id)
    else:
        messages.error(request, 'Please upload a valid csv file.')
    return HttpResponseRedirect('/admin/reports/uploadreports/')


@staff_member_required
@require_http_methods(['POST'])
def update_rateplan_commission(request):
    request_data = fetch_client_details(request)
    MAX_LINE_COUNT = 5000
    UPLOAD_RESTRICTED_TIME = 30 # in minutes
    try:
        user_id = request.user.id
        email_id = request.user.email
        rateplan_commission_file = request.FILES.get('rateplan_commission_csv', None)

        if not rateplan_commission_file or not validatecsv(rateplan_commission_file, needed_fields=('rateplancode', 'commission_value')):
            messages.error(request, 'Please upload valid csv file')
            inventory_logger.error(message='Invalid csv file uploaded by user %s for Rateplan commission bulk uploader' % (user_id),
                              log_type='ingoibibo', bucket='reports.admin_views', stage='update_rateplan_commission')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        if redis_server:
            key = str(user_id) + '_' + 'rateplan_commission_csv'
            value = redis_server.get(key)
            if value:
                diff = datetime.datetime.now() - datetime.datetime.strptime(value, "%Y-%m-%d-%H-%M")
                minutes = UPLOAD_RESTRICTED_TIME - diff.seconds/60
                messages.error(request, 'User can upload only one sheet in %s minutes. Try again after %s minutes' %(UPLOAD_RESTRICTED_TIME, minutes))
                return HttpResponseRedirect('/admin/reports/uploadreports')

        save_to_mdb = mdb.save('rateplan_commission_csv', rateplan_commission_file)
        validation_result = validate_rateplan_commission_uploader_file(save_to_mdb, MAX_LINE_COUNT)
        if not validation_result['success']:
            messages.error(request, validation_result['result']);
            inventory_logger.error(message='Some exception occured %s, User: %s' % (str(ex), user_id), log_type='ingoibibo',
                               bucket='reports.admin_views', stage='update_rateplan_commission')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        #Logging in inventory to keep a record of file data
        inventory_logger.info(message='Rateplan commission bulk uploader file name: %s by user id %s' % (str(save_to_mdb), user_id),
                              log_type='ingoibibo', bucket='reports.admin_views', stage='update_rateplan_commission')

        rateplan_commission_value_map = validation_result['result']
        for rateplan in rateplan_commission_value_map.keys():
            if settings.HOST in settings.PROD_HOSTS:
                update_rateplan_comission_task.apply_async(args=(rateplan, rateplan_commission_value_map[rateplan],
                                                                 request.user, str(save_to_mdb), request_data, ))
            else:
                update_rateplan_comission_task(rateplan, rateplan_commission_value_map[rateplan],
                                               request.user, str(save_to_mdb), request_data)

        if redis_server:
            key = str(user_id) + '_' + 'rateplan_commission_csv'
            redis_server.set(key, datetime.datetime.now().strftime("%Y-%m-%d-%H-%M"), timeout=UPLOAD_RESTRICTED_TIME * 60)
        messages.success(request, "The file is uploaded successfully")
        try:
            sendMail(email_id, from_email=settings.EMAIL_ALERT_SENDER,
                     body='Rateplan Commission Uploader file is uploaded successfully',
                     subject='Rateplan Commission Uploader file is uploaded successfully', template_id='', cc_emails='',
                     attached_file=None, bcc_emails=[])
        except Exception as ex:
            inventory_logger.error(message='Exception occured while sending mail %s, User: %s' %(str(ex), user_id), log_type='ingoibibo',
                                   bucket='reports.admin_views', stage='update_rateplan_commission')
    except Exception as ex:
        inventory_logger.error(message='Some exception occured %s, User: %s' % (str(ex), user_id), log_type='ingoibibo',
                               bucket='reports.admin_views', stage='update_rateplan_commission')
        messages.error(request, 'Exception Occured')
    return HttpResponseRedirect('/admin/reports/uploadreports/')


@staff_member_required
@require_http_methods(['POST'])
def create_free_cancellation_rule(request):
    MAX_LINE_COUNT = 1000
    UPLOAD_RESTRICTED_TIME = 30  # in minutes
    try:
        user = request.user
        email_id = user.email
        free_cancellation_rule = dict()
        data = request.POST
        free_cancellation_uploader_file = request.FILES.get('free_cancellation_uploader_file', None)

        # Validating that upload is not done within the restricted time
        if redis_server:
            value = redis_server.get('free_cancellation_uploader_file')
            if value:
                diff = datetime.datetime.now() - datetime.datetime.strptime(value, "%Y-%m-%d-%H-%M")
                minutes = UPLOAD_RESTRICTED_TIME - diff.seconds / 60
                messages.error(request, 'Free cancellation uploader can be used once in %s minutes. Try again after %s minutes' %(UPLOAD_RESTRICTED_TIME, minutes))
                return HttpResponseRedirect('/admin/reports/uploadreports')

        # Validating csv file has needed fields
        if not free_cancellation_uploader_file or not validatecsv(free_cancellation_uploader_file,
                                                                  needed_fields=['hotelcode','blackoutdates']):
            messages.error(request, 'Please upload valid csv file')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        # Validating checkout and checkin dates
        if data['free_cancellation_checkin_date']:
            checkin_date = datetime.datetime.strptime(data['free_cancellation_checkin_date'], '%m/%d/%Y')
        else:
            messages.error(request, 'Checkin date cannot be empty')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        if data['free_cancellation_checkout_date']:
            checkout_date = datetime.datetime.strptime(data['free_cancellation_checkout_date'], '%m/%d/%Y')
        else:
            messages.error(request, 'Checkout date cannot be empty')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        if checkin_date and checkout_date and checkout_date < checkin_date:
            messages.error(request, 'checkout date should be greater than checkin date')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        if not 'checkin_daylist' in data:
            messages.error(request, "Checkin weekday cannot be empty")
            return HttpResponseRedirect('/admin/reports/uploadreports')

        is_special_policy = convert_truthy(data.get('is_special_policy', ''))

        save_to_mdb = mdb.save('free_cancellation_uploader_file', free_cancellation_uploader_file)

        # Validating csv file content
        validation_result = validate_create_free_cancellation_uploader_file(save_to_mdb, MAX_LINE_COUNT, is_special_policy)
        if not validation_result['success']:
            messages.error(request, validation_result['result'])
            return HttpResponseRedirect('/admin/reports/uploadreports')

        # Logging in inventory to keep a record of file data
        inventory_logger.info(message='Free cancellation rule bulk uploader file name: %s by user id %s' % (str(save_to_mdb), request.user.id),
                              log_type='ingoibibo', bucket='create_free_cancellation_rule', stage='create_free_cancellation_rule')

        hotelcode_blackouts_map = validation_result['result']
        free_cancellation_rule['stay_start'] = checkin_date
        free_cancellation_rule['stay_end'] = checkout_date
        free_cancellation_rule['penalty_rules'] = hotelchoice.free_cancel_days_penalty_rules[int(data['free_cancellation_rules'])]
        checkin_weekday = ",".join(dict(data.lists())['checkin_daylist'])
        free_cancellation_rule['na_checkin_weekday'] = rev_checkin_weekday(checkin_weekday)
        free_cancellation_rule['is_special_policy'] = is_special_policy


        if settings.HOST in settings.PROD_HOSTS:
            create_free_cancellation_rule_task.apply_async(args=(hotelcode_blackouts_map, free_cancellation_rule, user, str(save_to_mdb),))
        else:
            create_free_cancellation_rule_task(hotelcode_blackouts_map, free_cancellation_rule, user, str(save_to_mdb))

        if redis_server:
            key = 'free_cancellation_uploader_file'
            redis_server.set(key, datetime.datetime.now().strftime("%Y-%m-%d-%H-%M"), timeout=UPLOAD_RESTRICTED_TIME * 60)

        if is_special_policy and validation_result.get('blackout_exist', False):
            messages.success(request, "The file is uploaded successfully without blackout dates as blackout is not supported for special policy")
        else:
            messages.success(request, "The file is uploaded successfully")
        try:
            sendMail(email_id, from_email=settings.EMAIL_ALERT_SENDER,
                     body='Free Cancellation uploader file is uploaded successfully',
                     subject='Free Cancellation uploader file is uploaded successfully', template_id='', cc_emails='',
                     attached_file=None, bcc_emails=[])
        except Exception as ex:
            inventory_logger.error(message='Exception occured while sending mail %s' %(str(ex)), log_type='ingoibibo',
                                   bucket='create_free_cancellation_rule', stage='create_free_cancellation_rule')

    except Exception as ex:
        inventory_logger.error(message='Some exception occured %s' %(str(ex)), log_type='ingoibibo',
                               bucket='create_free_cancellation_rule', stage='create_free_cancellation_rule')
        messages.error(request, 'Exception Occured')
    return HttpResponseRedirect('/admin/reports/uploadreports/')


def validate_create_free_cancellation_uploader_file(free_cancellation_uploader_file, MAX_LINE_COUNT, is_special_policy):
    hotelcode_blackout_map = {}
    line_count = 0
    blackout_exist = False
    try:
        saved_file = mdb_storage.open(free_cancellation_uploader_file)
        file_content = saved_file.read()
    except Exception as ex:
        inventory_logger.error(message='Exception occured while opening/reading sheet %s' % (str(ex)), log_type='ingoibibo',
                       bucket='validate_create_free_cancellation_rule_uploader', stage='opening_free_cancellation_sheet')
        return {"success": False, "result": "Error in opening/reading sheet from mdb storage"}
    try:
        for row in csv.DictReader(file_content.splitlines(), quotechar='"', quoting=csv.QUOTE_ALL):
            hotel_code = row.get('hotelcode')
            blackout_dates_str = row.get('blackoutdates')
            blackout_dates = []
            if blackout_dates_str:
                if not is_special_policy:
                    blackout_dates, error = validate_blackout_dates(blackout_dates_str.split("#"))
                    if error:
                        raise Exception(error)
                blackout_exist = True
            hotelcode_blackout_map[hotel_code] = blackout_dates
            line_count += 1

        if line_count >= MAX_LINE_COUNT:
            return {"result": "Error: Total hotelcode is %s. Maximum %s entries in a sheet" %(line_count, MAX_LINE_COUNT), "success": False}

        input_hotel_codes = set(hotelcode_blackout_map.keys())
        valid_hotel_codes = HotelDetail.objects.values_list('hotelcode', flat=True).filter(hotelcode__in=input_hotel_codes)

        invalid_hotelcodes = input_hotel_codes.difference(set(valid_hotel_codes))
        if invalid_hotelcodes:
            return {"result": "Error: These hotelcodes does not exist : " + ",".join(invalid_hotelcodes), "success": False}
        return {"result": hotelcode_blackout_map, "success": True, "blackout_exist": blackout_exist}

    except Exception as ex:
        inventory_logger.error(message='Exception occured while validating sheet %s' % (str(ex)), log_type='ingoibibo',
                               bucket='validate_create_free_cancellation_uploader_file', stage='validate_free_cancellation_sheet')
        return {"success": False, "result": "Error: Exception occured while validating sheet"}


def validate_blackout_dates(blackout_dates_list, blackout_start_date=None, blackout_end_date=None):
    final_blackout_dates = []
    error_msg = None

    try:
        if not blackout_dates_list:
            if not blackout_start_date:
                raise Exception('specify range')
            blackout_end_date = blackout_end_date or blackout_start_date
            blackout_start_date = datetime.datetime.strptime(blackout_start_date, "%Y-%m-%d").date()
            blackout_end_date = datetime.datetime.strptime(blackout_end_date, "%Y-%m-%d").date()
            while blackout_start_date <= blackout_end_date:
                final_blackout_dates.append(blackout_start_date)
                blackout_start_date += datetime.timedelta(days=1)
        else:
            for blackout_date in blackout_dates_list:
                if ':' in blackout_date:
                    start_date_str, end_date_str = blackout_date.split(":")
                    start_date = datetime.datetime.strptime(start_date_str, "%Y-%m-%d").date()
                    end_date = datetime.datetime.strptime(end_date_str, "%Y-%m-%d").date()
                    if end_date < start_date:
                        raise Exception("end date should be greater than start date")

                    temp_date = start_date
                    while temp_date <= end_date:
                        final_blackout_dates.append(temp_date)
                        temp_date += datetime.timedelta(days=1)
                else:
                    blackout_date_obj = datetime.datetime.strptime(blackout_date, "%Y-%m-%d").date()
                    final_blackout_dates.append(blackout_date_obj)

    except Exception, e:
        error_msg = "Blackout date validation error %s" % e

    return final_blackout_dates, error_msg


@staff_member_required
@require_http_methods(['POST'])
def update_free_cancellation_rule(request):
    MAX_LINE_COUNT = 1000
    UPLOAD_RESTRICTED_TIME = 30  # in minutes
    try:
        user = request.user
        email_id = user.email
        free_cancellation_rule = dict()
        update_free_cancellation_uploader_file = request.FILES.get('update_free_cancellation_uploader_file', None)

        # Validating that upload is not done within the restricted time
        if redis_server:
            value = redis_server.get('update_free_cancellation_uploader_file')
            if value:
                diff = datetime.datetime.now() - datetime.datetime.strptime(value, "%Y-%m-%d-%H-%M")
                minutes = UPLOAD_RESTRICTED_TIME - diff.seconds / 60
                messages.error(request, 'Free cancellation uploader can be used once in %s minutes. Try again after %s minutes' %(UPLOAD_RESTRICTED_TIME, minutes))
                return HttpResponseRedirect('/admin/reports/uploadreports')

        # Validating csv file has needed fields
        if not update_free_cancellation_uploader_file or not validatecsv(update_free_cancellation_uploader_file,
                                                                  needed_fields=['rule id', 'blackoutstartdate', 'blackoutenddate', 'action']):
            messages.error(request, 'Please upload valid csv file')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        save_to_mdb = mdb.save('free_cancellation_update_file', update_free_cancellation_uploader_file)

        # Validating csv file content
        validation_result = validate_update_free_cancellation_uploader_file(save_to_mdb, MAX_LINE_COUNT)
        if not validation_result['success']:
            messages.error(request, validation_result['result'])
            return HttpResponseRedirect('/admin/reports/uploadreports')

        # Logging in inventory to keep a record of file data
        inventory_logger.info(message='Free cancellation rule bulk uploader file name: %s by user id %s' % (str(save_to_mdb), request.user.id),
                              log_type='ingoibibo', bucket='update_free_cancellation_rule', stage='update_free_cancellation_rule')

        if settings.HOST in settings.PROD_HOSTS:
            update_free_cancellation_rule_task_new.apply_async(args=(validation_result['result'], user, str(save_to_mdb)))
        else:
            update_free_cancellation_rule_task_new(validation_result['result'], user, str(save_to_mdb))

        if redis_server:
            key = 'update_free_cancellation_uploader_file'
            redis_server.set(key, datetime.datetime.now().strftime("%Y-%m-%d-%H-%M"), timeout=UPLOAD_RESTRICTED_TIME * 60)
        messages.success(request, "Update Free Cancellation file is uploaded successfully")
        try:
            sendMail(email_id, from_email=settings.EMAIL_ALERT_SENDER,
                     body='Update Free Cancellation uploader file is uploaded successfully',
                     subject='Update Free Cancellation uploader file is uploaded successfully', template_id='',
                     cc_emails='', attached_file=None, bcc_emails=[])
        except Exception as ex:
            inventory_logger.error(message='Exception occured while sending mail %s' %(str(ex)), log_type='ingoibibo',
                                   bucket='update_free_cancellation_rule', stage='update_free_cancellation_rule')

    except Exception as ex:
        inventory_logger.error(message='Some exception occured %s' %(str(ex)), log_type='ingoibibo',
                               bucket='update_free_cancellation_rule', stage='update_free_cancellation_rule')
        messages.error(request, 'Exception Occured')
    return HttpResponseRedirect('/admin/reports/uploadreports/')


def convert_old_validation_result_to_new(validation_map):
    new_rule_map = {}
    for rule_id, action_blackout_map in validation_map.items():
        pattern = ',' + str(rule_id) + ','
        row = CancellationRules.objects.filter(
            Q(old_ids__startswith=pattern[1:]) | Q(old_ids__endswith=pattern[:-1]) |
            Q(old_ids__contains=pattern) | Q(old_ids=pattern[1:-1])).first()
        if row:
            new_rule_map[row.id]=action_blackout_map

    return new_rule_map


def validate_update_free_cancellation_uploader_file(free_cancellation_uploader_file, MAX_LINE_COUNT):
    rule_action_blackout_map = defaultdict(lambda: defaultdict(list))
    line_count = 0
    try:
        saved_file = mdb_storage.open(free_cancellation_uploader_file)
        file_content = saved_file.read()
    except Exception as ex:
        inventory_logger.error(message='Exception occured while opening/reading sheet %s' % (str(ex)), log_type='ingoibibo',
                       bucket='validate_update_free_cancellation_rule_uploader', stage='opening_free_cancellation_sheet')
        return {"success": False, "result": "Error in opening/reading sheet from mdb storage"}

    try:
        for row in csv.DictReader(file_content.splitlines(), quotechar='"', quoting=csv.QUOTE_ALL):
            free_cancellation_rule_id = int(row.get('rule id'))
            blackout_startdate_str, blackout_enddate_str= row.get('blackoutstartdate'), row.get('blackoutenddate')
            blackout_dates, error = validate_blackout_dates([], blackout_startdate_str, blackout_enddate_str)
            if error:
                raise Exception(error)
            action = row.get('action').lower()
            if action not in ["add", "delete"]:
                raise Exception("invalid action, action can be add or delete")
            rule_action_blackout_map[free_cancellation_rule_id][action].extend(blackout_dates)
            line_count += 1

        if line_count >= MAX_LINE_COUNT:
            return {"result": "Error: Total hotelcode is %s. Maximum %s entries in a sheet" %(line_count, MAX_LINE_COUNT), "success": False}

        free_cancellation_ids = set(rule_action_blackout_map.keys())
        valid_free_cancel_ids = CancellationRules.objects.values_list('id', flat=True).filter(id__in=free_cancellation_ids, priority=priority_map['FC'])

        invalid_free_cancel_ids = free_cancellation_ids.difference(set(valid_free_cancel_ids))
        if invalid_free_cancel_ids:
            return {"result": "Error: These cancellation ids does not exist : " + ",".join(str(invalid_free_cancel_id) for invalid_free_cancel_id in invalid_free_cancel_ids),
                    "success": False}

        return {"result": rule_action_blackout_map, "success": True}

    except Exception as ex:
        inventory_logger.error(message='Exception occured while validating sheet %s error traceback: %s' % (str(ex), repr(traceback.format_exc())), log_type='ingoibibo',
                               bucket='validate_update_free_cancellation_uploader_file', stage='validate_free_cancellation_sheet')
        return {"success": False, "result": "Error: Exception occured while validating sheet"}


def validate_rateplan_commission_uploader_file(rateplan_commission_file, MAX_LINE_COUNT):
    rateplan_commission_value_map = dict()
    line_count = 0;
    try:
        saved_file = mdb_storage.open(rateplan_commission_file)
        file_content = saved_file.read()
    except Exception as ex:
        inventory_logger.error(message='Exception occured while opening/reading sheet %s' % (str(ex)), log_type='ingoibibo',
                               bucket='validate_rateplan_commission_sheet', stage='opening_rateplan_commission_sheet')
        return {"success": False, "result": "Error in opening/reading sheet from mdb storage"}
    try:
        for row in csv.DictReader(file_content.splitlines(), quotechar='"', quoting=csv.QUOTE_ALL):
            rateplancode = row.get('rateplancode')
            try:
                commission_value = float(row.get('commission_value'))
            except:
                return {"result": "Error: Commission is not correct for rateplancode %s" % (rateplancode), "success": False}
            if commission_value < 0:
                return { "result": "Error: Commission is negative for rateplancode %s" % (rateplancode), "success": False}
            rateplan_commission_value_map[rateplancode] = commission_value
            line_count += 1

        if line_count >=MAX_LINE_COUNT:
            return {"result": "Error: Total rateplancode is %s. Maximum %s entries in a sheet" %(line_count, MAX_LINE_COUNT), "success": False}

        valid_rateplan_codes = [rateplan['rateplancode']
                                for rateplan in RatePlan.objects.filter(rateplancode__in=rateplan_commission_value_map.keys()).values('rateplancode')]

        invalid_rateplan_codes = [rateplancode for rateplancode in rateplan_commission_value_map.keys() if rateplancode not in valid_rateplan_codes]
        if invalid_rateplan_codes:
            return {"result": "Error: These rateplancodes does not exist : " + ",".join(invalid_rateplan_codes), "success": False}
        return {"result": rateplan_commission_value_map, "success": True }

    except Exception as ex:
        inventory_logger.error( message='Exception occured while validating sheet %s' % (str(ex)), log_type='ingoibibo',
                                bucket='validate_rateplan_commission_sheet', stage='validate_rateplan_commission_sheet')
        return {"success": False, "result": "Error: Exception occured while validating sheet"}


@api_view(['GET'])
@detail_route(url_path='get_data_choices_free_cancellation_uploader')
def get_data_choices_free_cancellation_uploader(request):
    response = {
        "free_cancellation_rules": {}
    }
    templates = get_free_cancellation_templates()
    for template in templates:
        title = template[0]
        fcp = json.loads(template[1]).get("fcp").get("value")
        response["free_cancellation_rules"][fcp] = title
    return Response(response, status=status.HTTP_200_OK)


@api_view(['GET'])
@detail_route(url_path='get_hotel_flag_bit_choices')
def get_hotel_flag_bit_choices(request):
    response = dict()

    admin_uploader_flag = request.query_params.get('admin_uploader', False)

    flag_bit_choices = dict()
    final_flag_dict = dict()
    final_flag_dict.update(FLAG_ONE_DICT)
    final_flag_dict.update(FLAG_THREE_DICT)
    final_flag_dict.update(FLAG_FOUR_DICT)
    for flag in final_flag_dict:
        flag_bit_choices[flag] = flag.replace("_", " ").title()
    if 'fraud_hotel' in FLAG_ONE_DICT:
        flag_bit_choices['fraud_hotel'] = 'Active But Sold Out'
    response['hotel_flag_bit_choices'] = flag_bit_choices

    # remove gstn_assured and gstn_penalty_on from option
    # because those are not allow to change using
    # flag bit uploader
    if admin_uploader_flag:
        flag_bit_choices.pop('gstn_assured')
        flag_bit_choices.pop('gstn_penalty_on')

    return Response(response, status=status.HTTP_200_OK)


@api_view(['GET'])
@detail_route(url_path='get_host_flag_bit_choices')
def get_host_flag_bit_choices(request):
    response = dict()

    admin_uploader_flag = request.query_params.get('admin_uploader', False)

    flag_bit_choices = dict()
    final_flag_dict = dict()
    final_flag_dict.update(HOST_FLAG_ONE_DICT)
    for flag in final_flag_dict:
        flag_bit_choices[flag] = flag.replace("_", " ").title()
    response['host_flag_bit_choices'] = flag_bit_choices

    # remove is_custom_about_host
    # because those are not allow to change using
    # flag bit uploader
    # is_custom_about_host is used as a field present in host profile
    if admin_uploader_flag:
        flag_bit_choices.pop('is_custom_about_host')

    return Response(response, status=status.HTTP_200_OK)


@api_view(['GET'])
@detail_route(url_path='get_mealplan_name_choices')
def get_mealplan_name_choices(request):
    from hotels.hotelchoice import MEAL_PLAN_CHOICES
    from collections import OrderedDict
    response = dict()
    mealplan_name_choices = OrderedDict()
    for mealplan_name in MEAL_PLAN_CHOICES:
        mealplan_name_choices[mealplan_name[0]] = mealplan_name[0]
    response['mealplan_name_choices'] = mealplan_name_choices
    return Response(response, status=status.HTTP_200_OK)


def validate_hotel_flag_bit_uploader_file(hotel_flag_bit_uploader_file, MAX_LINE_COUNT):
    hotel_code_list = []
    line_count = 0;
    try:
        saved_file = mdb_storage.open(hotel_flag_bit_uploader_file)
        file_content = saved_file.read()
    except Exception as ex:
        inventory_logger.error(message='Exception occured while opening/reading sheet %s' % (str(ex)), log_type='ingoibibo',
                       bucket='validate_hotel_flag_bit_uploader_file', stage='opening_hotel_flag_bit_uploader_file')
        return {"success": False, "result": "Error in opening/reading sheet from mdb storage"}
    try:
        for row in csv.DictReader(file_content.splitlines(), quotechar='"', quoting=csv.QUOTE_ALL):
            hotel_code = row.get('hotelcode')
            hotel_code_list.append(hotel_code)
            line_count += 1

        if line_count >=MAX_LINE_COUNT:
            return {"result": "Total hotelcode is %s. Maximum %s entries is allowed in a file" %(line_count, MAX_LINE_COUNT), "success": False}

        valid_hotel_codes = [hotel.hotelcode
                        for hotel in HotelDetail.objects.only('hotelcode').filter(hotelcode__in=hotel_code_list)]

        invalid_hotel_codes = [hotelcode for hotelcode in hotel_code_list if hotelcode not in valid_hotel_codes]
        if invalid_hotel_codes:
            return {"result": "These hotelcodes does not exist : " + ",".join(invalid_hotel_codes), "success": False}
        return {"result": hotel_code_list, "success": True}

    except Exception as ex:
        inventory_logger.error( message='Exception occurred while validating file %s' % (str(ex)), log_type='ingoibibo',
                                bucket='validate_hotel_flag_bit_uploader_file',
                                stage='validate_hotel_flag_bit_uploader_file')
        return {"success": False, "result": "Exception occurred while validating sheet"}


def validate_host_flag_bit_uploader_file(host_flag_bit_uploader_file, MAX_LINE_COUNT):
    user_id_list = []
    line_count = 0;
    try:
        saved_file = mdb_storage.open(host_flag_bit_uploader_file)
        file_content = saved_file.read()
    except Exception as ex:
        inventory_logger.error(message='Exception occured while opening/reading sheet %s' % (str(ex)), log_type='ingoibibo',
                       bucket='validate_host_flag_bit_uploader_file', stage='opening_host_flag_bit_uploader_file')
        return {"success": False, "result": "Error in opening/reading sheet from mdb storage"}
    try:
        for row in csv.DictReader(file_content.splitlines(), quotechar='"', quoting=csv.QUOTE_ALL):
            user_id = long(row.get('user_id'))
            user_id_list.append(user_id)
            line_count += 1

        if line_count >=MAX_LINE_COUNT:
            return {"result": "Total user_id is %s. Maximum %s entries is allowed in a file" %(line_count, MAX_LINE_COUNT), "success": False}

        valid_user_id = []
        valid_user_id = [host.user_id
                        for host in HostProfile.objects.only('user_id').filter(user_id__in=user_id_list)]

        invalid_user_id = [str(user_id) for user_id in user_id_list if user_id not in valid_user_id]
        if invalid_user_id:
            return {"result": "Host profiles corresponding to these user_ids does not exist : " + ",".join(invalid_user_id), "success": False}
        return {"result": user_id_list, "success": True}

    except Exception as ex:
        inventory_logger.error( message='Exception occurred while validating file %s' % (str(ex)), log_type='ingoibibo',
                                bucket='validate_host_flag_bit_uploader_file',
                                stage='validate_host_flag_bit_uploader_file')
        return {"success": False, "result": "Exception occurred while validating sheet"}


@staff_member_required
@require_http_methods(['POST'])
def update_hotel_flag_bit_value(request):
    MAX_LINE_COUNT = 1000
    UPLOAD_RESTRICTED_TIME = 30  # in minutes
    batch_size = 100
    try:
        user_id = request.user.id
        key = str(user_id) + '_' + 'hotel_flag_bit_uploader'
        email_id = request.user.email
        data = request.POST
        hotel_flag_bit_uploader_file = request.FILES.get('hotel_flag_bit_uploader_file', None)
        flag_value = True if int(data['hotel_flag_operation']) == 1 else False
        flag_name = data['hotel_flag_bit_choices']
        flag_extra_data = dict()

        # validate abso reason input
        abso_reason = data.get(HotelService.FIELD_ABSO_REASON, None)
        if len(abso_reason) == 0:
            abso_reason = None

        flag_extra_data[HotelService.FIELD_ABSO_REASON] = abso_reason

        if flag_name == HotelService.FIELD_ABSO and flag_value:
            if not abso_reason:
                messages.error(request, HotelService.ERROR_ABSO_REASON_UNAVAILABLE)
                return HttpResponseRedirect('/admin/reports/uploadreports')
            elif HotelABSOReason.is_automatic_only_reason(abso_reason):
                messages.error(request, HotelService.ERROR_ABSO_AUTOMATION_ONLY)
                return HttpResponseRedirect('/admin/reports/uploadreports')

        # Validating that upload is not done within the restricted time
        if redis_server and not settings.DEBUG:
            value = redis_server.get(key)
            if value:
                diff = datetime.datetime.now() - datetime.datetime.strptime(value, "%Y-%m-%d-%H-%M")
                minutes = UPLOAD_RESTRICTED_TIME - diff.seconds / 60
                messages.error(request, 'User can upload only one sheet in %s minutes. Try again after %s minutes'
                               % (UPLOAD_RESTRICTED_TIME, minutes))
                return HttpResponseRedirect('/admin/reports/uploadreports')

        # Validating csv file has needed fields
        if not hotel_flag_bit_uploader_file or not validatecsv(hotel_flag_bit_uploader_file, needed_fields=['hotelcode']):
            messages.error(request, 'Please upload valid csv file')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        # Validating csv file content
        save_to_mdb = mdb.save('hotel_flag_bit_uploader_file', hotel_flag_bit_uploader_file)
        validation_result = validate_hotel_flag_bit_uploader_file(save_to_mdb, MAX_LINE_COUNT)
        if not validation_result['success']:
            messages.error(request, validation_result['result']);
            return HttpResponseRedirect('/admin/reports/uploadreports')

        # Logging in inventory to keep a record of file data
        inventory_logger.info(message='Update Hotel flag bit bulk uploader file name: %s by user id %s'
                                      % (str(save_to_mdb), user_id),
                              log_type='ingoibibo', bucket='update_hotel_flag_bit_value',
                              stage='update_hotel_flag_bit_value')

        hotel_code_list = validation_result['result']

        start_index = 0;
        data_size = len(hotel_code_list)
        while start_index < data_size:
            hotel_code_chunks = hotel_code_list[start_index:start_index+batch_size]
            start_index = start_index + batch_size
            if settings.HOST in settings.PROD_HOSTS:
                update_hotel_flag_value_task.apply_async(args=(hotel_code_chunks, flag_name, flag_value, request.user,
                                                               str(save_to_mdb), flag_extra_data))
            else:
                update_hotel_flag_value_task(hotel_code_chunks, flag_name, flag_value, request.user,
                                             str(save_to_mdb), flag_extra_data)

        if redis_server:
            redis_server.set(key, datetime.datetime.now().strftime("%Y-%m-%d-%H-%M"), timeout=UPLOAD_RESTRICTED_TIME * 60)
        try:
            sendMail(email_id, from_email=settings.EMAIL_ALERT_SENDER,
                     body='Hotel Flag bit uploader file is uploaded successfully',
                     subject='Hotel Flag bit uploader file is uploaded successfully', template_id='', cc_emails='',
                     attached_file=None, bcc_emails=[])
        except Exception as ex:
            inventory_logger.error(message='Exception occurred while sending mail %s' % (str(ex)), log_type='ingoibibo',
                                   bucket='update_hotel_flag_bit_value', stage='update_hotel_flag_bit_value')

        messages.success(request, "The file is uploaded successfully")
    except Exception as ex:
        inventory_logger.error(message='Some exception occurred %s' % (str(ex)), log_type='ingoibibo',
                               bucket='update_hotel_flag_bit_value', stage='update_hotel_flag_bit_value')
        messages.error(request, 'Exception Occurred')
    return HttpResponseRedirect('/admin/reports/uploadreports/')


@staff_member_required
@require_http_methods(['POST'])
def update_host_flag_bit_value(request):
    MAX_LINE_COUNT = 1000
    UPLOAD_RESTRICTED_TIME = 30  # in minutes
    batch_size = 100
    try:
        user_id = request.user.id
        key = str(user_id) + '_' + 'host_flag_bit_uploader'
        email_id = request.user.email
        data = request.POST
        host_flag_bit_uploader_file = request.FILES.get('host_flag_bit_uploader_file', None)

        # Validating that upload is not done within the restricted time
        if redis_server and not settings.DEBUG:
            value = redis_server.get(key)
            if value:
                diff = datetime.datetime.now() - datetime.datetime.strptime(value, "%Y-%m-%d-%H-%M")
                minutes = UPLOAD_RESTRICTED_TIME - diff.seconds / 60
                messages.error(request, 'User can upload only one sheet in %s minutes. Try again after %s minutes'
                               % (UPLOAD_RESTRICTED_TIME, minutes))
                return HttpResponseRedirect('/admin/reports/uploadreports')

        # Validating csv file has needed fields
        if not host_flag_bit_uploader_file or not validatecsv(host_flag_bit_uploader_file, needed_fields=['user_id']):
            messages.error(request, 'Please upload valid csv file')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        # Validating csv file content
        save_to_mdb = mdb.save('host_flag_bit_uploader_file', host_flag_bit_uploader_file)
        validation_result = validate_host_flag_bit_uploader_file(save_to_mdb, MAX_LINE_COUNT)
        if not validation_result['success']:
            messages.error(request, validation_result['result'])
            return HttpResponseRedirect('/admin/reports/uploadreports')

        # Logging in inventory to keep a record of file data
        inventory_logger.info(message='Update Host flag bit bulk uploader file name: %s by user id %s'
                                      % (str(save_to_mdb), user_id),
                              log_type='ingoibibo', bucket='update_host_flag_bit_value',
                              stage='update_host_flag_bit_value')

        user_id_list = validation_result['result']
        flag_name = data['host_flag_bit_choices']
        flag_value = True if int(data['host_flag_operation']) == 1 else False

        start_index = 0
        data_size = len(user_id_list)
        while start_index < data_size:
            user_id_chunks = user_id_list[start_index:start_index+batch_size]
            start_index = start_index + batch_size
            if settings.HOST in settings.PROD_HOSTS:
                update_host_flag_value_task.apply_async(args=(user_id_chunks, flag_name, flag_value, request.user, str(save_to_mdb),))
            else:
                update_host_flag_value_task(user_id_chunks, flag_name, flag_value, request.user, str(save_to_mdb))

        if redis_server:
            redis_server.set(key, datetime.datetime.now().strftime("%Y-%m-%d-%H-%M"), timeout=UPLOAD_RESTRICTED_TIME * 60)
        try:
            sendMail(email_id, from_email=settings.EMAIL_ALERT_SENDER,
                     body='Host Flag bit uploader file is uploaded successfully',
                     subject='Host Flag bit uploader file is uploaded successfully', template_id='', cc_emails='',
                     attached_file=None, bcc_emails=[])
        except Exception as ex:
            inventory_logger.error(message='Exception occurred while sending mail %s' % (str(ex)), log_type='ingoibibo',
                                   bucket='update_host_flag_bit_value', stage='update_host_flag_bit_value')

        messages.success(request, "The file is uploaded successfully")
    except Exception as ex:
        inventory_logger.error(message='Some exception occurred %s' % (str(ex)), log_type='ingoibibo',
                               bucket='update_host_flag_bit_value', stage='update_host_flag_bit_value')
        messages.error(request, 'Exception Occurred')
    return HttpResponseRedirect('/admin/reports/uploadreports/')


def validate_linked_rateplan_uploader_file(linked_rateplan_uploader_file, MAX_LINE_COUNT):
    hotel_code_list = []
    line_count = 0
    try:
        saved_file = mdb_storage.open(linked_rateplan_uploader_file)
        file_content = saved_file.read()
    except Exception as ex:
        inventory_logger.error(message='Exception occured while opening/reading sheet %s' % (str(ex)), log_type='ingoibibo',
                       bucket='validate_linked_rateplan_uploader_file', stage='opening_validate_linked_rateplan_uploader_file')
        return {"success": False, "result": "Error in opening/reading sheet from mdb storage"}
    try:
        for row in csv.DictReader(file_content.splitlines(), quotechar='"', quoting=csv.QUOTE_ALL):
            hotel_code = row.get('hotelcode')
            hotel_code_list.append(hotel_code)
            line_count += 1

        if line_count >=MAX_LINE_COUNT:
            return {"result": "Total hotelcode is %s. Maximum %s entries is allowed in a file" %(line_count, MAX_LINE_COUNT), "success": False}

        valid_hotel_codes = [hotel.hotelcode
                        for hotel in HotelDetail.objects.only('hotelcode').filter(hotelcode__in=hotel_code_list)]

        invalid_hotel_codes = [hotelcode for hotelcode in hotel_code_list if hotelcode not in valid_hotel_codes]
        if invalid_hotel_codes:
            return {"result": "These hotelcodes does not exist : " + ",".join(invalid_hotel_codes), "success": False}
        return {"result": hotel_code_list, "success": True}

    except Exception as ex:
        inventory_logger.error( message='Exception occured while validating file %s' % (str(ex)), log_type='ingoibibo',
                                bucket='validate_linked_rateplan_uploader_file', stage='validate_linked_rateplan_uploader_file')
        return {"success": False, "result": "Exception occured while validating sheet"}

@staff_member_required
@require_http_methods(['POST'])
def create_linked_rateplan(request):
    request_data = fetch_client_details(request)
    MAX_LINE_COUNT = 1000
    UPLOAD_RESTRICTED_TIME = 240  # in minutes
    batch_size = 100;
    try:
        user_id = request.user.id
        key = str(user_id) + '_' + 'linked_rateplan_uploader'
        email_id = request.user.email
        data = request.POST
        linked_rateplan_uploader_file = request.FILES.get('linked_rateplan_uploader_file', None)

        # Validating that upload is not done within the restricted time
        if redis_server:
            value = redis_server.get(key)
            if value:
                diff = datetime.datetime.now() - datetime.datetime.strptime(value, "%Y-%m-%d-%H-%M")
                minutes = UPLOAD_RESTRICTED_TIME - diff.seconds / 60;
                messages.error(request, 'User can upload only one sheet in %s minutes. Try again after %s minutes' % (
                                    UPLOAD_RESTRICTED_TIME, minutes))
                return HttpResponseRedirect('/admin/reports/uploadreports')

        if not linked_rateplan_uploader_file or not validatecsv(linked_rateplan_uploader_file, needed_fields=['hotelcode']):
            messages.error(request, 'Please upload valid csv file')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        # Validating csv file content
        save_to_mdb = mdb.save('linked_rateplan_uploader_file', linked_rateplan_uploader_file)
        validation_result = validate_linked_rateplan_uploader_file(save_to_mdb, MAX_LINE_COUNT)
        if not validation_result['success']:
            messages.error(request, validation_result['result']);
            return HttpResponseRedirect('/admin/reports/uploadreports')

        # Logging in inventory to keep a record of file data
        inventory_logger.info(message='Create Linked Rateplan bulk uploader file name: %s by user id %s' % (str(save_to_mdb), user_id),
                            log_type='ingoibibo', bucket='create_linked_rateplan', stage='create_linked_rateplan')

        existing_mealplan = data['existing_mealplan_name_choices']
        new_mealplan = data['new_mealplan_name_choices']
        if existing_mealplan == new_mealplan:
            messages.error(request, "Existing and new mealplan name cannot be same");
            return HttpResponseRedirect('/admin/reports/uploadreports')

        linkage_basis = 'percent' if int(data['linkage_basis']) == 0 else 'fixed'
        linkage_type = 'low' if int(data['linkage_type']) == 0 else 'high'
        linkage_amount = float(data['linkage_amount'])
        extra_guest_linkage_amount = float(data['extra_guest_linkage_amount'])
        sell_commission = float(data['sell_commission'])

        linkage_rule_map = {'linkage_basis': linkage_basis,
                            'linkage_type': linkage_type,
                            'linkage_amount': linkage_amount,
                            'extra_guest_linkage_amount': extra_guest_linkage_amount}

        hotel_code_list = validation_result['result']
        start_index = 0;
        data_size = len(hotel_code_list)
        while start_index < data_size:
            hotel_code_chunks = hotel_code_list[start_index:start_index + batch_size]
            start_index = start_index + batch_size
            if settings.HOST in settings.PROD_HOSTS:
                create_linked_rateplan_task.apply_async(args=(hotel_code_chunks, existing_mealplan, new_mealplan, sell_commission,
                                                              linkage_rule_map, request.user, str(save_to_mdb), request_data,))
            else:
                create_linked_rateplan_task(hotel_code_chunks, existing_mealplan, new_mealplan, sell_commission,
                                            linkage_rule_map, request.user, str(save_to_mdb), request_data)
        if redis_server:
            redis_server.set(key, datetime.datetime.now().strftime("%Y-%m-%d-%H-%M"), timeout=UPLOAD_RESTRICTED_TIME * 60)
        try:
            sendMail(email_id, from_email=settings.EMAIL_ALERT_SENDER,
                     body='Linked Rateplan uploader file is uploaded successfully',
                     subject='Linked Rateplan uploader file is uploaded successfully', template_id='', cc_emails='',
                     attached_file=None, bcc_emails=[])
        except Exception as ex:
            inventory_logger.error(message='Exception occured while sending mail %s' % (str(ex)), log_type='ingoibibo',
                                   bucket='create_linked_rateplan', stage='create_linked_rateplan')

        messages.success(request, "The file is uploaded successfully")

    except Exception as ex:
        inventory_logger.error(message='Some exception occured %s' % (str(ex)), log_type='ingoibibo',
                               bucket='create_linked_rateplan', stage='create_linked_rateplan')
        messages.error(request, 'Exception Occured')
    return HttpResponseRedirect('/admin/reports/uploadreports/')



def validate_text_based_promotion_uploader_file(text_based_promotion_uploader_file, MAX_LINE_COUNT):
    line_count = 0;
    input_hotel_codes = []
    try:
        saved_file = mdb_storage.open(text_based_promotion_uploader_file)
        file_content = saved_file.read()
    except Exception as ex:
        inventory_logger.error(message='Exception occured while opening/reading sheet %s' % (str(ex)), log_type='ingoibibo',
                       bucket='validate_text_based_promotion_uploader_file', stage='opening_text_based_promotion_uploader_file')
        return {"success": False, "result": "Error in opening/reading sheet from mdb storage"}

    try:
        for row in csv.DictReader(file_content.splitlines(), quotechar='"', quoting=csv.QUOTE_ALL):
            hotel_code = row.get('hotelcode')
            input_hotel_codes.append(hotel_code)
            line_count += 1

        if line_count >= MAX_LINE_COUNT:
            return {"result": "Error: Total hotelcode is %s. Maximum %s entries in a sheet" %(line_count, MAX_LINE_COUNT), "success": False}

        valid_hotel_codes = HotelDetail.objects.values_list('hotelcode', flat=True).filter(hotelcode__in=input_hotel_codes)
        difference = [hotel_code for hotel_code in input_hotel_codes if hotel_code not in valid_hotel_codes]

        if difference:
            return {"result": "Error: These hotelcodes does not exist : " + ",".join(difference), "success": False}

        return {"result": input_hotel_codes, "success": True}

    except Exception as ex:
        inventory_logger.error(message='Exception occured while validating sheet %s' % (str(ex)), log_type='ingoibibo',
                               bucket='validate_text_based_promotion_uploader_file', stage='validate_text_based_promotion_uploader_file')
        return {"success": False, "result": "Error: Exception occured while validating sheet"}




@staff_member_required
@require_http_methods(['POST'])
def bulk_create_text_based_promotions(request):
    MAX_LINE_COUNT = 1000
    UPLOAD_RESTRICTED_TIME = 30  # in minutes
    try:
        user = request.user
        email_id = user.email
        text_based_promotion_data = dict()
        post_data = request.POST
        text_based_promotion_uploader_file = request.FILES.get('create_text_based_promotion_uploader_file', None)

        # Validating that upload is not done within the restricted time
        if redis_server:
            value = redis_server.get('text_based_promotion_uploader_file')
            if value:
                diff = datetime.datetime.now() - datetime.datetime.strptime(value, "%Y-%m-%d-%H-%M")
                minutes = UPLOAD_RESTRICTED_TIME - diff.seconds / 60
                messages.error(request, 'Text based promotion uploader can be used once in %s minutes. Try again after %s minutes' %(UPLOAD_RESTRICTED_TIME, minutes))
                return HttpResponseRedirect('/admin/reports/uploadreports')

        # Validating csv file has needed fields
        if not text_based_promotion_uploader_file or not validatecsv(text_based_promotion_uploader_file, needed_fields=['hotelcode']):
            messages.error(request, 'Please upload valid csv file')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        if post_data['text_offers_template_map']=='':
            messages.error(request, 'Please choose a template')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        # Validating bookin start and end date
        if post_data['text_promotion_bookin_start_date']:
            bookin_start_date = datetime.datetime.strptime(post_data['text_promotion_bookin_start_date'], '%m/%d/%Y')
        if post_data['text_promotion_bookin_end_date']:
            bookin_end_date = datetime.datetime.strptime(post_data['text_promotion_bookin_end_date'], '%m/%d/%Y')

        # Validating Checkin start and end date
        if post_data['text_promotion_checkin_start_date']:
            checkin_start_date = datetime.datetime.strptime(post_data['text_promotion_checkin_start_date'], '%m/%d/%Y')
        if post_data['text_promotion_checkin_end_date']:
            checkin_end_date = datetime.datetime.strptime(post_data['text_promotion_checkin_end_date'], '%m/%d/%Y')


        if bookin_start_date and bookin_end_date and bookin_end_date < bookin_start_date:
            messages.error(request, 'Booking end date should be greater than Booking start date')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        if checkin_start_date and checkin_end_date and checkin_end_date < checkin_start_date:
            messages.error(request, 'Checkin end date should be greater than Checkin start date')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        save_to_mdb = mdb.save('text_based_promotion_uploader_file', text_based_promotion_uploader_file)

        # Validating csv file content
        validation_result = validate_text_based_promotion_uploader_file(save_to_mdb, MAX_LINE_COUNT)
        if not validation_result['success']:
            messages.error(request, validation_result['result'])
            return HttpResponseRedirect('/admin/reports/uploadreports')


        # Logging in inventory to keep a record of file data
        inventory_logger.info(message='Text Based Promotion uploader file name: %s by user id %s' % (str(save_to_mdb), request.user.id),
                              log_type='ingoibibo', bucket='create_text_based_promotions', stage='create_text_based_promotions')

        input_hotelcodes = validation_result['result']
        text_based_promotion_data['bookingdatestart'] = bookin_start_date
        text_based_promotion_data['bookingdateend'] = bookin_end_date
        text_based_promotion_data['checkindatestart'] = checkin_start_date.date()
        text_based_promotion_data['checkoutdateend'] = checkin_end_date.date()
        text_based_promotion_data['content_type'] = 27

        if settings.HOST in settings.PROD_HOSTS:
            create_text_based_promotion_task.apply_async(args=(input_hotelcodes, text_based_promotion_data, post_data, user,))
        else:
            create_text_based_promotion_task(input_hotelcodes, text_based_promotion_data, post_data, user)

        if redis_server:
            key = 'text_based_promotion_uploader_file'
            redis_server.set(key, datetime.datetime.now().strftime("%Y-%m-%d-%H-%M"), timeout=UPLOAD_RESTRICTED_TIME * 60)
        messages.success(request, "The file is uploaded successfully")
        try:
            sendMail(email_id, from_email=settings.EMAIL_ALERT_SENDER,
                     body='Text Based Promotion uploader file is uploaded successfully',
                     subject='Text Based Promotion uploader file is uploaded successfully', template_id='',
                     cc_emails='', attached_file=None, bcc_emails=[])
        except Exception as ex:
            inventory_logger.error(message='Exception occured while sending mail %s' %(str(ex)), log_type='ingoibibo',
                                   bucket='create_text_based_promotions', stage='create_text_based_promotions')

    except Exception as ex:
        inventory_logger.error(message='Some exception occured %s' %(str(ex)), log_type='ingoibibo',
                               bucket='create_text_based_promotions', stage='create_text_based_promotions')
        messages.error(request, 'Exception Occured')
    return HttpResponseRedirect('/admin/reports/uploadreports/')

@staff_member_required
@require_http_methods(['POST'])
def create_bulk_cug(request):
    try:
        data = request.POST
        list_value = dict(data.lists())
        if data.has_key('segment_list'):
            segment = list_value.get('segment_list', '')[0]
            if segment == 'MMTSELECT':
                segment = CUG_SEGMENT_CUG10    
            
            if segment == CUG_SEGMENT_CUG1  :
                messages.error(request, 'MMT BLACK segment cug update is not allowed')
                return HttpResponseRedirect('/admin/reports/uploadreports')                    
        else:
            messages.error(request, 'at least one segment should be selected')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        user_id = request.user.id
        offer_file = request.FILES.get('cugcsv', None)

        if not offer_file:
            messages.error(request, 'Please upload a csv file')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        if isNowInTimePeriod(dt.time(8,00), dt.time(22,00), dt.datetime.now().time()) :
            row_count = DAY_ROW_COUNT
        else:
            row_count = ROW_COUNT


        if not valid_rows_count(file=offer_file, row_count=row_count):
            messages.error(request, ERROR_MESSAGE.format(row_count=row_count))
            return HttpResponseRedirect('/admin/reports/uploadreports')

        if redis_server:
            key = str(user_id)+'_'+str(offer_file)
            value = redis_server.get(key)
            if value:
                messages.error(request, 'processing previously uploaded file. please try after some time.')
                return HttpResponseRedirect('/admin/reports/uploadreports')

        save_to_mdb = mdb.save('offercsv', offer_file)
        promo_genie_enabled = settings.PROMO_GENIE_CONFIG["promo_genie_enabled"]

        if settings.HOST in settings.PROD_HOSTS:
            if promo_genie_enabled:
                create_cug_task_promotionv2.apply_async(args=(save_to_mdb, request.user, segment))
            else:
                create_cug_task.apply_async(args=(save_to_mdb, request.user, segment))
        else:
            if promo_genie_enabled:
                create_cug_task_promotionv2(save_to_mdb, request.user, segment)
            else:
                create_cug_task(save_to_mdb, request.user, segment)

        if redis_server:
            key = str(user_id)+'_'+str(offer_file)
            redis_server.set(key, 1, timeout=10*60)
        messages.success(request, "The file is uploaded successfully")
    except Exception, e:
        messages.error(request, 'Exception Occured')

    return HttpResponseRedirect('/admin/reports/uploadreports')

@staff_member_required
@require_http_methods(['POST'])
def create_bulk_coupon(request):
    try:
        user_id = request.user.id
        offer_file = request.FILES.get('couponcsv', None)

        if not offer_file:
            messages.error(request, 'Please upload a csv file')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        if isNowInTimePeriod(dt.time(8,00), dt.time(22,00), dt.datetime.now().time()) :
            row_count = DAY_ROW_COUNT
        else :
            row_count = ROW_COUNT

        if not valid_rows_count(file=offer_file, row_count=row_count):
            messages.error(request, ERROR_MESSAGE.format(row_count=row_count))
            return HttpResponseRedirect('/admin/reports/uploadreports')

        if redis_server:
            key = str(user_id)+'_'+str(offer_file)
            value = redis_server.get(key)
            if value:
                messages.error(request, 'processing previously uploaded file. please try after some time.')
                return HttpResponseRedirect('/admin/reports/uploadreports')

        save_to_mdb = mdb.save('couponcsv', offer_file)
        promo_genie_enabled = settings.PROMO_GENIE_CONFIG["promo_genie_enabled"]

        if settings.HOST in settings.PROD_HOSTS:
            if promo_genie_enabled:
                create_coupon_task_promotionv2.apply_async(args=(save_to_mdb, request.user))
            else:
                create_cug_task.apply_async(args=(save_to_mdb, request.user))
        else:
            if promo_genie_enabled:
                create_coupon_task_promotionv2(save_to_mdb, request.user)
            else:
                create_coupon_task(save_to_mdb, request.user)

        if redis_server:
            key = str(user_id)+'_'+str(offer_file)
            redis_server.set(key, 1, timeout=10*60)
        messages.success(request, "The file is uploaded successfully")
    except Exception, e:
        messages.error(request, 'Exception Occured')

    return HttpResponseRedirect('/admin/reports/uploadreports')

@staff_member_required
@require_http_methods(['POST'])
def release_amounts(request):
    uploader_file = request.FILES.get('releaseAmount', None)
    show_error = False
    message = 'We will mail you after task completion.'
    inventory_logger.info(message='Release Amount {file_name}'.format(file_name=uploader_file),
                          log_type='ingoibibo', bucket='ClientAPI', stage='release_amounts')
    if not request.user.groups.filter(name__in=RELEASE_AMOUNT_BULK_UPLOADER_GROUP).annotate(num_tags=Count('name')).exists():
        message = 'Permission Denied'
        show_error = True

    if cache.get(request.user.username + 'upload_vendor' + uploader_file.name):
        message = "User upload same file with in 5 hours, Please upload diffent file or change file name"
        show_error = True

    if not(uploader_file and validatecsv(uploader_file, needed_fields=RELEASE_AMOUNTS)):
        message = 'Please upload a valid CSV file. Refer sample CSV.'
        show_error = True

    if not valid_rows_count(file=uploader_file):
        message = ERROR_MESSAGE.format(row_count=ROW_COUNT)
        show_error = True

    if show_error:
        messages.error(request, message)
    else:
        cache.set(request.user.username+uploader_file.name, True, 60*60*5)
        inventory_logger.info(message='redis key: %s' % (request.user.username + 'release_amount' + uploader_file.name),
                              log_type='ingoibibo', bucket='ClientAPI', stage='release_amounts')
        messages.success(request, message)
        uploader_file = mdb.save('bookingfile', uploader_file)
        if settings.HOST in settings.PROD_HOSTS:
            release_amounts_task.apply_async(args=(uploader_file, request.user))
        else:
            release_amounts_task(uploader_file, request.user)
    return HttpResponseRedirect('/admin/reports/uploadreports')


# release GST amount after threshold date
@staff_member_required
@require_http_methods(['POST'])
def release_gst(request):
    uploader_file = request.FILES.get('releaseGST', None)
    show_error = False
    message = 'We will mail you after task completion.'
    inventory_logger.info(message='Release GST amount {file_name}'.format(file_name=uploader_file),
                          log_type='ingoibibo', bucket='ClientAPI', stage='release_gst')
    if not has_permission(request.user, 'can_upload_invoice_without_check'):
        message = 'Permission Denied'
        show_error = True

    if cache.get(request.user.username + 'upload_vendor' + uploader_file.name):
        message = "User upload same file with in 5 hours, Please upload diffent file or change file name"
        show_error = True

    if not(uploader_file and validatecsv(uploader_file, needed_fields=RELEASE_GST)):
        message = 'Please upload a valid CSV file. Refer sample CSV.'
        show_error = True

    if not valid_rows_count(file=uploader_file):
        message = ERROR_MESSAGE.format(row_count=ROW_COUNT)
        show_error = True

    if show_error:
        messages.error(request, message)
    else:
        cache.set(request.user.username+uploader_file.name, True, 60*60*5)
        inventory_logger.info(message='redis key: %s' % (request.user.username + 'release_gst' + uploader_file.name),
                              log_type='ingoibibo', bucket='ClientAPI', stage='release_gst')
        messages.success(request, message)
        uploader_file = mdb.save('bookingfile', uploader_file)
        if settings.HOST in settings.PROD_HOSTS:
            release_gst_task.apply_async(args=(uploader_file, request.user))
        else:
            release_gst_task(uploader_file, request.user)
    return HttpResponseRedirect('/admin/reports/uploadreports')


def validate_rateplan_flag_bit_uploader_file(rateplan_flag_bit_uploader_file, MAX_LINE_COUNT):
    rateplan_code_list = []
    line_count = 0
    try:
        saved_file = mdb_storage.open(rateplan_flag_bit_uploader_file)
        file_content = saved_file.read()
    except Exception as ex:
        inventory_logger.error(message='Exception occurred while opening/reading sheet %s' % (str(ex)),
                               log_type='ingoibibo', bucket='validate_rateplan_flag_bit_uploader_file',
                               stage='opening_rateplan_flag_bit_uploader_file')
        return {"success": False, "result": "Error in opening/reading sheet from mdb storage"}
    try:
        for row in csv.DictReader(file_content.splitlines(), quotechar='"', quoting=csv.QUOTE_ALL):
            rateplan_code = row.get('rateplancode')
            rateplan_code_list.append(rateplan_code)
            line_count += 1

        if line_count >= MAX_LINE_COUNT:
            return {"result": "Total count of rateplancode is %s. Maximum %s entries is allowed in a file"
                              % (line_count, MAX_LINE_COUNT), "success": False}

        valid_rateplan_codes = [ratePlan.rateplancode for ratePlan in
                                RatePlan.objects.only('rateplancode').filter(rateplancode__in=rateplan_code_list)]

        invalid_rateplan_codes = [rateplancode for rateplancode in rateplan_code_list
                                  if rateplancode not in valid_rateplan_codes]
        if invalid_rateplan_codes:
            return {"result": "These rateplancodes don't exist : " + ",".join(invalid_rateplan_codes), "success": False}
        return {"result": rateplan_code_list, "success": True}

    except Exception as ex:
        inventory_logger.error( message='Exception occurred while validating file %s' % (str(ex)), log_type='ingoibibo',
                                bucket='validate_rateplan_flag_bit_uploader_file',
                                stage='validate_rateplan_flag_bit_uploader_file')
        return {"success": False, "result": "Exception occurred while validating sheet"}


@api_view(['GET'])
@detail_route(url_path='get_rateplan_flag_bit_choices')
def get_rateplan_flag_bit_choices(request):
    response = dict()
    flag_bit_choices = dict()
    final_flag_dict = dict()
    final_flag_dict.update(RATE_PLAN_FLAG_DICT)

    for flag in final_flag_dict:
        flag_bit_choices[flag] = flag.replace("_", " ").title()

    flag_bit_choices.pop('is_day_zero')
    flag_bit_choices.pop('is_only_rateplan_cug')
    flag_bit_choices.pop('is_only_rateplan_hcp')
    flag_bit_choices.pop('is_net_rate_model')
    flag_bit_choices.pop('apply_commission_on_post_tax')

    response['rateplan_flag_bit_choices'] = flag_bit_choices

    return Response(response, status=status.HTTP_200_OK)


@staff_member_required
@require_http_methods(['POST'])
def update_rateplan_flag_bit_value(request):
    MAX_LINE_COUNT = 5000
    UPLOAD_RESTRICTED_TIME = 30  # in minutes
    batch_size = 1000
    try:
        user_id = request.user.id
        key = str(user_id) + '_' + 'rateplan_flag_bit_uploader'
        email_id = request.user.email
        data = request.POST
        rateplan_flag_bit_uploader_file = request.FILES.get('rateplan_flag_bit_uploader_file', None)

        # Validating that upload is not done within the restricted time
        if redis_server and not settings.DEBUG:
            value = redis_server.get(key)
            if value:
                diff = datetime.datetime.now() - datetime.datetime.strptime(value, "%Y-%m-%d-%H-%M")
                minutes = UPLOAD_RESTRICTED_TIME - diff.seconds / 60
                messages.error(request, 'User can upload only one sheet in %s minutes. '
                                        'Try again after %s minutes' % (UPLOAD_RESTRICTED_TIME, minutes))
                return HttpResponseRedirect('/admin/reports/uploadreports')

        # Validating csv file has needed fields
        if not rateplan_flag_bit_uploader_file or not validatecsv(
                rateplan_flag_bit_uploader_file, needed_fields=['rateplancode']):
            messages.error(request, 'Please upload valid csv file')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        # Validating csv file content
        save_to_mdb = mdb.save('rateplan_flag_bit_uploader_file', rateplan_flag_bit_uploader_file)
        validation_result = validate_rateplan_flag_bit_uploader_file(save_to_mdb, MAX_LINE_COUNT)
        if not validation_result['success']:
            messages.error(request, validation_result['result'])
            return HttpResponseRedirect('/admin/reports/uploadreports')

        # Logging in inventory to keep a record of file data
        inventory_logger.info(message='Update Rate Plan flag bit bulk uploader file name: %s by user id %s' % (
            str(save_to_mdb), user_id), log_type='ingoibibo', bucket='update_rateplan_flag_bit_value',
                              stage='update_rateplan_flag_bit_value')

        rateplan_code_list = validation_result['result']
        flag_name = data['rateplan_flag_bit_choices']
        flag_value = True if int(data['rateplan_flag_operation']) == 1 else False

        start_index = 0
        data_size = len(rateplan_code_list)
        while start_index < data_size:
            rateplan_code_chunks = rateplan_code_list[start_index:start_index+batch_size]
            start_index = start_index + batch_size
            if settings.HOST in settings.PROD_HOSTS:
                update_rateplan_flag_value_task.apply_async(args=(
                    rateplan_code_chunks, flag_name, flag_value, request.user, str(save_to_mdb),))
            else:
                update_rateplan_flag_value_task(rateplan_code_chunks, flag_name, flag_value,
                                                request.user, str(save_to_mdb))

        if redis_server:
            redis_server.set(key, datetime.datetime.now().strftime("%Y-%m-%d-%H-%M"),
                             timeout=UPLOAD_RESTRICTED_TIME * 60)
        try:
            sendMail(email_id, from_email=settings.EMAIL_ALERT_SENDER,
                     body='Rate Plan Flag bit uploader file is uploaded successfully',
                     subject='Rate Plan Flag bit uploader file is uploaded successfully', template_id='', cc_emails='',
                     attached_file=None, bcc_emails=[])
        except Exception as ex:
            inventory_logger.error(message='Exception occurred while sending mail %s' % (str(ex)), log_type='ingoibibo',
                                   bucket='update_rateplan_flag_bit_value', stage='update_rateplan_flag_bit_value')

        messages.success(request, "The file is uploaded successfully")
    except Exception as ex:
        inventory_logger.error(message='Some exception occurred %s' % (str(ex)), log_type='ingoibibo',
                               bucket='update_rateplan_flag_bit_value', stage='update_rateplan_flag_bit_value')
        messages.error(request, 'Exception Occurred')
    return HttpResponseRedirect('/admin/reports/uploadreports/')

@staff_member_required
@require_http_methods(['POST'])
def start_hostapp_migration(request):
    from scripts.hostapp_migration.run import execute_migration_script

    try:
        user_id = request.user.id
        csvfile = request.FILES.get('csvfile', None)
        key_prefix = 'hostapp_migration_'
        if not csvfile:
            messages.error(request, 'Please upload a csv file')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        if redis_server:
            key = key_prefix + str(user_id) + '_' + str(csvfile)
            value = redis_server.get(key)
            if value:
                messages.error(request, 'processing previously uploaded file. please try after some time.')
                return HttpResponseRedirect('/admin/reports/uploadreports')

        save_to_mdb = mdb.save('csvfile', csvfile)

        if settings.DEBUG:
            execute_migration_script(csvfile, user_id=user_id)
        else:
            execute_migration_script(csvfile, user_id=user_id)

        if redis_server:
            key = key_prefix + str(user_id) + '_' + str(csvfile)
            redis_server.set(key, 1, timeout=10*60)

        try:
            email_id = request.user.email
            body = 'Hostapp property migration uploader file is uploaded successfully.'
            if 'Error' not in save_to_mdb:
                body += ' Uploaded file url: {}'.format(settings.CDN_DOMAIN + save_to_mdb)

            sendMail(email_id, from_email=settings.EMAIL_ALERT_SENDER,
                     body=body,
                     subject='Hostapp property migration uploader file is uploaded successfully', template_id='', cc_emails='',
                     attached_file=None, bcc_emails=[])
        except Exception as ex:
            inventory_logger.error(message='Exception occurred while sending mail %s' % (str(ex)), log_type='ingoibibo',
                                   bucket='start_hostapp_migration', stage='start_hostapp_migration')

        messages.success(request, "The file is uploaded successfully")

    except Exception as e:
        messages.error(request, 'Exception Occured')

    return HttpResponseRedirect('/admin/reports/uploadreports')

@staff_member_required
@require_http_methods(['POST'])
def start_hostweb_upgradation(request):
    from scripts.hostweb_migration.run import execute_upgradation_script

    try:
        user_id = request.user.id
        csvfile = request.FILES.get('csvfile', None)
        key_prefix = 'hostweb_migration_'
        if not csvfile:
            messages.error(request, 'Please upload a csv file')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        if redis_server:
            key = key_prefix + str(user_id) + '_' + str(csvfile)
            value = redis_server.get(key)
            if value:
                messages.error(request, 'processing previously uploaded file. please try after some time.')
                return HttpResponseRedirect('/admin/reports/uploadreports')

        save_to_mdb = mdb.save('csvfile', csvfile)

        if settings.DEBUG:
            execute_upgradation_script(csvfile, user_id=user_id)
        else:
            execute_upgradation_script(csvfile, user_id=user_id)

        if redis_server:
            key = key_prefix + str(user_id) + '_' + str(csvfile)
            redis_server.set(key, 1, timeout=10*60)

        try:
            email_id = request.user.email
            body = 'Hostweb migration property list file is uploaded successfully.'
            if 'Error' not in save_to_mdb:
                body += ' Uploaded file url: {}'.format(settings.CDN_DOMAIN + save_to_mdb)

            sendMail(email_id, from_email=settings.EMAIL_ALERT_SENDER,
                     body=body,
                     subject='Hostweb migration property list file is uploaded successfully', template_id='', cc_emails='',
                     attached_file=None, bcc_emails=[])
        except Exception as ex:
            inventory_logger.error(message='Exception occurred while sending mail %s' % (str(ex)), log_type='ingoibibo',
                                   bucket='start_hostweb_upgradation', stage='start_hostweb_upgradation')

        messages.success(request, "The file is uploaded successfully")

    except Exception as e:
        messages.error(request, 'Exception Occured')

    return HttpResponseRedirect('/admin/reports/uploadreports')

@staff_member_required
@require_http_methods(['POST'])
def start_hostweb_flag_setup(request):
    from scripts.hostweb_migration.run import run_hostweb_migration_flag_setup

    try:
        user_id = request.user.id
        csvfile = request.FILES.get('csvfile', None)
        key_prefix = 'flag_upgradation_'
        if not csvfile:
            messages.error(request, 'Please upload a csv file')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        if redis_server:
            key = key_prefix + str(user_id) + '_' + str(csvfile)
            value = redis_server.get(key)
            if value:
                messages.error(request, 'processing previously uploaded file. please try after some time.')
                return HttpResponseRedirect('/admin/reports/uploadreports')

        save_to_mdb = mdb.save('csvfile', csvfile)

        run_hostweb_migration_flag_setup(csvfile)


        if redis_server:
            key = key_prefix + str(user_id) + '_' + str(csvfile)
            redis_server.set(key, 1, timeout=10*60)

        try:
            email_id = request.user.email
            body = 'display_space_detail has been run successfully for all the input hotels.'
            if 'Error' not in save_to_mdb:
                body += ' Uploaded file url: {}'.format(settings.CDN_DOMAIN + save_to_mdb)

            sendMail(email_id, from_email=settings.EMAIL_ALERT_SENDER,
                     body=body,
                     subject='set hostweb upgradation flag file is uploaded successfully', template_id='', cc_emails='',
                     attached_file=None, bcc_emails=[])
        except Exception as ex:
            inventory_logger.error(message='Exception occurred while sending mail %s' % (str(ex)), log_type='ingoibibo',
                                   bucket='start_hostweb_flag_setup', stage='start_hostweb_flag_setup')

        messages.success(request, "The file is uploaded successfully")

    except Exception as e:
        messages.error(request, 'Exception Occured')

    return HttpResponseRedirect('/admin/reports/uploadreports')


@staff_member_required
@require_http_methods(['POST'])
def create_kitchen_space_uploader(request):
    try:
        user_id = request.user.id
        csvfile = request.FILES.get('csvfile', None)
        form_data = dict()
        form_data['request_user'] = request.user
        created_ids = run_create_kitchen_space(csvfile, form_data, request)
        messages.success(request, "Created ids for space : {}".format(str(created_ids)))
    except Exception as ex:
        messages.error(request, "error in this")
    return HttpResponseRedirect('/admin/reports/uploadreports')


def get_free_cancellation_templates():
    templates = list(Templates.objects.filter(segment_id=hotelchoice.COMMON_TEMPLATE_SEGMENTS[1][0],
                                              is_active=True, category=hotelchoice.CANCELLATION_TEMPLATE_CATEGORY[0][0])
                     .values_list("title", "fields"))
    return templates


@staff_member_required
@require_http_methods(['POST'])
def create_internal_cal_linkages(request):

    try:
        user_id = request.user.id
        csvfile = request.FILES.get('csvfile', None)
        key_prefix = 'calsync-create-link_'
        if not csvfile:
            messages.error(request, 'Please upload a csv file')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        if redis_server and not settings.DEBUG:
            key = key_prefix + str(user_id) + '_' + str(csvfile)
            value = redis_server.get(key)
            if value:
                messages.error(request, 'processing previously uploaded file. please try after some time.')
                return HttpResponseRedirect('/admin/reports/uploadreports')

        save_to_mdb = mdb.save('csvfile', csvfile)

        csv_reader = csv.DictReader(csvfile)
        csv_list_data = list(csv_reader)

        hotel_data_list = []
        for row_dict in csv_list_data:
            hotel_dict = {'hotelcode': row_dict['hotelcode'], 'roomtypecode': row_dict['roomtypecode']}
            hotel_data_list.append(hotel_dict)

        if settings.DEBUG:
            # execute_migration_script(csvfile, user_id=user_id)
            validate_and_create_cal_linkage_task(hotel_data_list, request.user)
        else:
            validate_and_create_cal_linkage_task.apply_async(args=(hotel_data_list, request.user), )

        if redis_server and not settings.DEBUG:
            key = key_prefix + str(user_id) + '_' + str(csvfile)
            redis_server.set(key, 1, timeout=10*60)

        try:
            email_id = request.user.email
            body = 'Internal Cal Sync Create Linkages uploader file is uploaded successfully.'
            if 'Error' not in save_to_mdb:
                body += ' Uploaded file url: {}'.format(settings.CDN_DOMAIN + save_to_mdb)

            sendMail(email_id, from_email=settings.EMAIL_ALERT_SENDER,
                     body=body,
                     subject='Internal Cal Sync Create Linkages uploader file is uploaded successfully', template_id='', cc_emails='',
                     attached_file=None, bcc_emails=[])
        except Exception as ex:
            inventory_logger.error(message='Exception occurred while sending mail %s' % (str(ex)), log_type='ingoibibo',
                                   bucket='calsync-create-link', stage='create_internal_cal_linkages')

        messages.success(request, "The file is uploaded successfully")

    except Exception as e:
        inventory_logger.critical(message='Exception occurred %s' % (str(e)),
                                  log_type='ingoibibo', bucket='calsync-create-link',
                                  stage='create_internal_cal_linkages')
        messages.error(request, 'Exception Occurred')

    return HttpResponseRedirect('/admin/reports/uploadreports')



@staff_member_required
@require_http_methods(['POST'])
def remove_internal_cal_linkages(request):

    try:
        user_id = request.user.id
        csvfile = request.FILES.get('csvfile', None)
        key_prefix = 'calsync-remove-link_'
        if not csvfile:
            messages.error(request, 'Please upload a csv file')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        if redis_server and not settings.DEBUG:
            key = key_prefix + str(user_id) + '_' + str(csvfile)
            value = redis_server.get(key)
            if value:
                messages.error(request, 'processing previously uploaded file. please try after some time.')
                return HttpResponseRedirect('/admin/reports/uploadreports')

        save_to_mdb = mdb.save('csvfile', csvfile)

        csv_reader = csv.DictReader(csvfile)
        csv_list_data = list(csv_reader)

        hotel_data_list = []
        for row_dict in csv_list_data:
            hotel_dict = {}
            hotel_dict['hotelcode'] = row_dict['hotelcode']
            hotel_dict['roomtypecode'] = row_dict['roomtypecode']
            hotel_data_list.append(hotel_dict)

        if settings.DEBUG:
            validate_and_remove_cal_linkage_task(hotel_data_list, request.user)
        else:
            validate_and_remove_cal_linkage_task.apply_async(args=(hotel_data_list, request.user), )

        if redis_server and not settings.DEBUG:
            key = key_prefix + str(user_id) + '_' + str(csvfile)
            redis_server.set(key, 1, timeout=10*60)

        try:
            email_id = request.user.email
            body = 'Internal Cal Sync Remove Linkages uploader file is uploaded successfully.'
            if 'Error' not in save_to_mdb:
                body += ' Uploaded file url: {}'.format(settings.CDN_DOMAIN + save_to_mdb)

            sendMail(email_id, from_email=settings.EMAIL_ALERT_SENDER,
                     body=body,
                     subject='Internal Cal Sync Remove Linkages uploader file is uploaded successfully', template_id='', cc_emails='',
                     attached_file=None, bcc_emails=[])
        except Exception as ex:
            inventory_logger.error(message='Exception occurred while sending mail %s' % (str(ex)), log_type='ingoibibo',
                                   bucket='calsync-remove-link', stage='')

        messages.success(request, "The file is uploaded successfully")

    except Exception as e:
        messages.error(request, 'Exception Occurred')

    return HttpResponseRedirect('/admin/reports/uploadreports')

@require_http_methods(['POST'])
def push_services_to_kafka(request):
    from kafka_helper.tasks.dynamic.update_search_meta_pipelines import push_hotelcodes_to_debezium_hotel_services_topic
    from api.v2.common.permissions.common_permisssion import DEV_GROUP
    from django.db.models import Count
    try:

        if not (request.user.is_superuser or request.user.groups.filter(name__in=DEV_GROUP).annotate(num_tags=Count('name')).exists()):
            raise PermissionDenied

        user_id = request.user.id
        csvfile = request.FILES.get('csvfile', None)
        key_prefix = 'services_sync_link_'

        if not csvfile:
            messages.error(request, 'Please upload a csv file')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        if redis_server and not settings.DEBUG:
            key = key_prefix + str(user_id) + '_' + str(csvfile)
            value = redis_server.get(key)
            if value:
                messages.error(request, 'processing previously uploaded file. please try after some time.')
                return HttpResponseRedirect('/admin/reports/uploadreports')

        save_to_mdb = mdb.save('csvfile', csvfile)

        csv_reader = csv.DictReader(csvfile)
        csv_list_data = list(csv_reader)

        ingo_hotelcode_list = []
        for row_dict in csv_list_data:
            ingo_hotelcode_list.append(row_dict['ingoHotelCode'])

        email_body = None

        # verify hotel mmt ids
        verified_ingo_hotelcode_list = HotelDetail.objects.filter(hotelcode__in=ingo_hotelcode_list).values_list("hotelcode", flat=True)

        if len(verified_ingo_hotelcode_list) >= 1:
            if not settings.DEBUG:
                push_hotelcodes_to_debezium_hotel_services_topic.apply_async(args=(verified_ingo_hotelcode_list,))
            else:
                push_hotelcodes_to_debezium_hotel_services_topic(verified_ingo_hotelcode_list)

            if redis_server and not settings.DEBUG:
                key = key_prefix + str(user_id) + '_' + str(csvfile)
                redis_server.set(key, 1, timeout=10 * 60)

            inventory_logger.info(message='Pushed all hotel codes to Hotel Services Debezium: {}'.format(verified_ingo_hotelcode_list),
                                  log_type='ingoibibo', bucket='push_services_to_kafka', stage='push_services_to_kafka')
        else:
            email_body = "Zero valid Hotel MMT Ids found, Please upload again with valid ids."

        try:
            email_id = request.user.email
            if email_body is None:
                email_body = 'Push services for given hotel file is uploaded successfully.'

                if len(ingo_hotelcode_list) != 0 and  len(ingo_hotelcode_list) != len(verified_ingo_hotelcode_list):
                    email_body += 'Few of the ingo hotel codes were incorrect, please check : {}'.format(
                        list(set(ingo_hotelcode_list)-set([str(i) for i in verified_ingo_hotelcode_list])))
            if 'Error' not in save_to_mdb:
                email_body += ' Uploaded file url: {}'.format(settings.CDN_DOMAIN + save_to_mdb)

            sendMail(email_id, from_email=settings.EMAIL_ALERT_SENDER,
                     body=email_body,
                     subject='Push Services to Kafka file is uploaded successfully', template_id='',
                     cc_emails='',
                     attached_file=None, bcc_emails=[])

        except Exception as ex:
            inventory_logger.error(message='Exception occurred while sending mail %s' % (str(ex)), log_type='ingoibibo',
                                   bucket='push_services_to_kafka', stage='push_services_to_kafka')

        messages.success(request, "The file is uploaded successfully")

    except Exception as e:
        inventory_logger.critical(message='Exception occurred %s' % (str(e)),
                                  log_type='ingoibibo', bucket='push_services_to_kafka',
                                  stage='push_services_to_kafka')
        messages.error(request, 'Exception Occurred')

    return HttpResponseRedirect('/admin/reports/uploadreports')


@staff_member_required
@require_http_methods(['POST'])
def run_child_pricing_migrator(request):
    user_id = request.user.id
    try:
        child_pricing_user_list = get_child_pricing_admin_user()
        if user_id not in child_pricing_user_list:
            messages.error(request, 'Not authorized to run uploader')
            return HttpResponseRedirect('/admin/reports/uploadreports')
        csvfile = request.FILES.get('csvfile', None)
        operation_type = request.POST.get('operation_type', 'migration')
        key_prefix = 'child-pricing-file_'
        if not csvfile:
            messages.error(request, 'Please upload a csv file')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        if redis_server and not settings.DEBUG:
            key = key_prefix + str(user_id) + '_' + str(csvfile)
            value = redis_server.get(key)
            if value:
                messages.error(request, 'processing previously uploaded file. please try after some time.')
                return HttpResponseRedirect('/admin/reports/uploadreports')

        save_to_mdb = mdb.save('csvfile', csvfile)

        execute_child_related_migration(save_to_mdb, operation_type, user_id)

        if redis_server and not settings.DEBUG:
            key = key_prefix + str(user_id) + '_' + str(csvfile)
            redis_server.set(key, 1, timeout=10*60)

        try:
            email_id = request.user.email
            body = 'Child pricing migration uploader file is uploaded successfully.'
            if 'Error' not in save_to_mdb:
                body += ' Uploaded file url: {}'.format(settings.CDN_DOMAIN + save_to_mdb)

            sendMail(email_id, from_email=settings.EMAIL_ALERT_SENDER,
                     body=body,
                     subject='Child pricing migration uploader file is uploaded successfully', template_id='', cc_emails='',
                     attached_file=None, bcc_emails=[])
        except Exception:
            inventory_logger.critical(
                message='Exception occurred while sending mail {} user id {}'.format(repr(traceback.format_exc()),
                                                                                     user_id), log_type='ingoibibo',
                bucket='run_child_pricing_migrator', stage='')
            messages.error(request, 'File upload failed')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        messages.success(request, "The file is uploaded successfully")

    except Exception:
        inventory_logger.critical(
            message='Exception occurred {} user id {}'.format(repr(traceback.format_exc()), user_id),
            log_type='ingoibibo',
            bucket='run_child_pricing_migrator', stage='')
        messages.error(request, 'Exception Occurred')

    return HttpResponseRedirect('/admin/reports/uploadreports')

@staff_member_required
@require_http_methods(['POST'])
def run_manager_mapping_uploader(request):
    user_id = request.user.id
    try:
        csvfile = request.FILES.get('csvfile', None)
        key_prefix = 'manager-mapping_'
        if not csvfile:
            messages.error(request, 'Please upload a csv file')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        if redis_server and not settings.DEBUG:
            key = key_prefix + str(user_id)
            value = redis_server.get(key)
            if value:
                messages.error(request, 'processing previously uploaded file. please try after some time.')
                return HttpResponseRedirect('/admin/reports/uploadreports')

        save_to_mdb = mdb.save('csvfile', csvfile, prepend_file_name='bulk_upload_csvs/')
        is_prod_env = os.environ.get("CURR_ENV") in ("prod")
        if is_prod_env:
            create_update_manager_mapping.apply_async(args=(csvfile, request.user))
        else:
            create_update_manager_mapping(csvfile, request.user, False)

        if redis_server and not settings.DEBUG:
            key = key_prefix + str(user_id)
            redis_server.set(key, 1, timeout=10*60)

        try:
            email_id = request.user.email
            body = 'Manager Mapping uploader file is uploaded successfully.'
            if 'Error' not in save_to_mdb:
                body += ' Uploaded file url: {}'.format(settings.CDN_DOMAIN + save_to_mdb)

            sendMail(email_id, from_email=settings.EMAIL_ALERT_SENDER,
                     body=body,
                     subject='Manager Mapping uploader file is uploaded successfully', template_id='', cc_emails='',
                     attached_file=None, bcc_emails=[])
        except Exception:
            inventory_logger.critical(
                message='Exception occurred while sending mail {} user id {}'.format(repr(traceback.format_exc()),
                                                                                     user_id), log_type='ingoibibo',
                bucket='run_manager_mapping_uploader', stage='')
            messages.error(request, 'File upload failed')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        messages.success(request, "The file is uploaded successfully")

    except Exception:
        inventory_logger.critical(
            message='Exception occurred {} user id {}'.format(repr(traceback.format_exc()), user_id),
            log_type='ingoibibo',
            bucket='run_manager_mapping_uploader', stage='')
        messages.error(request, 'Exception Occurred')

    return HttpResponseRedirect('/admin/reports/uploadreports')

@staff_member_required
@require_http_methods(['POST'])
def update_social_media_hotelcode(request):
    from api.v2.common.permissions.common_permisssion import SOCIAL_MEDIA_UPDATE_PERMISSIONS
    from social_media_service.grpc_social_media_client import SocialMediaClient
    import grpc
    try:

        #check if user is in BDM groups
        if not request.user.groups.filter(name__in=SOCIAL_MEDIA_UPDATE_PERMISSIONS).annotate(num_tags=Count('name')).exists():
            raise PermissionDenied

        user_id = request.user.id
        csvfile = request.FILES.get('csvfile', None)
        key_prefix = 'update_social_media_hotelcode_'

        if not csvfile:
            messages.error(request, 'Please upload a csv file')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        if redis_server and not settings.DEBUG:
            key = key_prefix + str(user_id) + '_' + str(csvfile)
            value = redis_server.get(key)
            if value:
                messages.error(request, 'processing previously uploaded file. please try after some time.')
                return HttpResponseRedirect('/admin/reports/uploadreports')

        save_to_mdb = mdb.save('csvfile', csvfile)

        update_social_media_status.apply_async(args=(csvfile, request.user))

    except PermissionDenied as pe:
        inventory_logger.critical(message='Exception occurred %s' % (str(pe)),
                                  log_type='ingoibibo', bucket='update_social_media_hotelcode')
        messages.error(request, "Permission denied")

    except Exception as e:
        inventory_logger.critical(message='Exception occurred %s' % (str(e)),
                                  log_type='ingoibibo', bucket='update_social_media_hotelcode',
                                  stage='update_social_media_hotelcode')
        messages.error(request, 'Exception Occurred')

    return HttpResponseRedirect('/admin/reports/uploadreports')

@staff_member_required
@require_http_methods(['POST'])
def create_update_inclusions(request):
    from hotels.tasks import inclusions_bulk_upload
    from api.v2.common.permissions.common_permisssion import INCLUSIONS_CREATE_UPDATE_PERMISSIONS
    try:
        if not request.user.groups.filter(name__in=INCLUSIONS_CREATE_UPDATE_PERMISSIONS).annotate(num_tags=Count('name')).exists():
            raise PermissionDenied

        user_id = request.user.id
        csvfile = request.FILES.get('csvfile', None)
        key_prefix = 'create_update_inclusions'

        if not csvfile:
            messages.error(request, 'Please upload a csv file')
            return HttpResponseRedirect('/admin/reports/uploadreports')

        if redis_server and not settings.DEBUG:
            key = key_prefix + str(user_id) + '_' + str(csvfile)
            value = redis_server.get(key)
            if value:
                messages.error(request, 'processing previously uploaded file. please try after some time.')
                return HttpResponseRedirect('/admin/reports/uploadreports')

        save_to_mdb = mdb.save('csvfile', csvfile)

        inclusions_bulk_upload.apply_async(args=(csvfile, request.user))

    except PermissionDenied as pe:
        inventory_logger.critical(message='Exception occurred %s' % (str(pe)),
                                  log_type='ingoibibo', bucket='create_update_inclusions')
        messages.error(request, "Permission denied")

    except Exception as e:
        inventory_logger.critical(message='Exception occurred %s' % (str(e)),
                                  log_type='ingoibibo', bucket='create_update_inclusions',
                                  stage='create_update_inclusions')
        messages.error(request, 'Exception Occurred')

    return HttpResponseRedirect('/admin/reports/uploadreports')