# Script to identify those cms ids whose data isn't present in hindi
# currently written for policy / hotelname, but this can be extended by looking at resp and accordingly make amendments

from hotels.aerospike_connector import get_lingo_records_data
from hotels.models import HotelDetail
from lingo.helpers.model_references import create_cms_id_from_modelobj, get_doc_pkey_for_model
from utils.logger import Logger
import csv

api_logger = Logger(logger="inventoryAPILogger")


# template ids which have no grammar present in them
exclude_template_ids = [7, 8, 10, 11, 12, 21, 22, 23, 24, 28, 33, 35, 36, 37, 39, 40, 41, 42, 43, 53, 58, 61, 62,
                        63, 69, 71, 76, 91, 97,
                        98, 101, 102, 103, 106, 109, 111, 112, 113, 114, 116, 117, 118, 119, 120, 121, 122, 123,
                        124, 125, 127, 129, 130, 132, 133, 134, 136, 137, 138, 139, 140, 141, 142]

from hotels.models.policy_rules_models import HotelPolicyMappingV2


def get_blank_crms_ids(resp, hotelcode):
    '''
    modify this function to get node wise data - like for hoteldetail/rateplan/etc
    further scope - make this generic by accepting params
    '''
    grammar_nodes = resp.get('htl_plc_mpv2', {})
    crms_id_list = []
    if grammar_nodes:
        for key in grammar_nodes:
            grammar = grammar_nodes[key]
            if grammar['grammar'] == "":
                policy = HotelDetail.objects.get(hotelcode=key)
                if policy.template_id not in exclude_template_ids:
                    crms_id_list.append(create_cms_id_from_modelobj(policy, hotelcode))
    return crms_id_list


def get_crms_ids(model_name='HotelDetail', lang_code='hin', file_path='/opt/logs/hindi-list.csv'):
    crms_ids = []
    with open(file_path, 'rb') as csv_file:
        rows = csv.DictReader(csv_file)
        fields = rows.fieldnames
        print(fields)
        for row in rows:
            object_id = long(row['id'])
            hotel = HotelDetail.objects.get(id=object_id)
            hotelcode = hotel.hotelcode
            if model_name and object_id:
                document_name, p_key = get_doc_pkey_for_model(model_name, object_id)
                try:
                    resp = get_lingo_records_data(document_name, lang_code, p_key, [])  # can specify bin here if require
                    if resp:
                        crms_ids = crms_ids + get_blank_crms_ids(resp, hotelcode)
                        print(hotelcode)
                    else:
                        api_logger.critical(
                            message='Data not available for model {} and id {}'.format(model_name, p_key),
                            stage='api.lingo.views.LingoViewSet',
                            bucket='lingo', log_type='ingoibibo')

                except Exception as e:
                    api_logger.critical(message='exception occured for hotel', stage='api.lingo.views.LingoViewSet',
                                        bucket='lingo', log_type='ingoibibo')
        print(crms_ids)
    api_logger.info(message='--------CRMS IDS-------- {}'.format(crms_ids),
                    stage='api.lingo.views.LingoViewSet',
                    bucket='lingo', log_type='ingoibibo')
