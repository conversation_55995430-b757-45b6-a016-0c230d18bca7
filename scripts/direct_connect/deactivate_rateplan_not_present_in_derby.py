
from nexus_partner_services.dc_content_pipeline import api_helper
from hotels.models import HotelDetail, RoomDetail, RatePlan
from scripts.direct_connect.django_override_script import *

from utils.logger import Logger
from goibibo_inventory.settings import app

inventory_logger = Logger(logger="inventoryLogger")

INGO_TO_DERBY_CHAINCODE_MAP = {"radisson": "RADISSON",
                                "radissonnt": "RADISSON",
                                "bestwstrn": "BESTWESTERN",
                                "bestwstrnn": "BESTWESTERN",
                                "mariottntr": "MARRIOTT"}

@app.task(name = "scripts.direct_connect.deactivate_rateplan_not_present_in_derby")
def deactivate_derby_rps_not_present_in_derby(hotels, token):
    success_rps, failed_rps = deactivate_rateplan_not_present_in_derby(hotels, token)
    inventory_logger.info(message="Rateplan Successfully Deactivated: %s" % success_rps,
                          bucket='prep_for_derby_migration')

    inventory_logger.info(message="Rateplan Deactivation Failed for: %s" % failed_rps,
                          bucket='prep_for_derby_migration')

def get_products_not_present_in_derby_but_present_in_ingo(hotel_codes):
    """
    Script to get products which are not present in derby Product API but created at INGO:
    success_list, failure_list, room_not_present_in_derby, rp_not_present_in_derby = get_products_not_present_in_derby_but_present_in_ingo([], 'radisson')
    """

    url = "http://ingo-nexus-partner.ecs.mmt/api/ingo-direct/derby/v1/content"
    headers = {'Content-Type': 'application/json'}

    success_list = []
    failure_list = []
    room_not_present_in_derby = []
    rp_not_present_in_derby = []
    for hotel_code in hotel_codes:
        hotel_obj = HotelDetail.objects.get(hotelcode=hotel_code)
        cm_hotel_code = hotel_obj.source_hotelcode
        if cm_hotel_code is None or cm_hotel_code == "":
            inventory_logger.info(message="No Vendor Hotel Code for: %s" % hotel_code, bucket='prep_for_derby_migration')
            continue

        if hotel_obj.chainname is None or hotel_obj.chainname.chaincode is None:
            inventory_logger.info(message="No Chain Code Added for : %s" % hotel_code, bucket='prep_for_derby_migration')
            continue
        
        ingo_chain_code = hotel_obj.chainname.chaincode
        cm_chain_code = INGO_TO_DERBY_CHAINCODE_MAP.get(ingo_chain_code)
        ingo_room_list = []
        cm_room_list = []
        ingo_rp_list = []
        cm_rp_list = []
       
        ingo_rooms = RoomDetail.objects.filter(hotel_id=hotel_obj.id, source_config_id=3, isactive=True)
        for room in ingo_rooms:
            if room.source_roomtypecode:
                ingo_room_list.append(cm_hotel_code+"__"+room.source_roomtypecode)

        # print("rooms",ingo_room_list)

        ingo_rps = RatePlan.objects.filter(roomtype__hotel_id=hotel_obj.id, source_config_id=3, isactive=True)
        for rp in ingo_rps:
            if rp.roomtype.source_roomtypecode and rp.source_rateplancode:
                ingo_rp_list.append(cm_hotel_code+"__"+rp.roomtype.source_roomtypecode+"__"+rp.source_rateplancode)

        # print("rateplans",ingo_rp_list)

        api_body = {
            "hotelDetails": [
                {
                    "chainCode": cm_chain_code,
                    "hotelCode": cm_hotel_code,
                    "vendorAdditionalDetail": {
                        "isMaxResponseRequired": True,
                        "isProductResponseRequired": True
                    }
                }
            ]
        }
        try:
            api_success, json_response = api_helper.post_request(url, headers, api_body)
            if api_success:
                is_success = json_response['success']
                if is_success:
                    content_details = json_response.get('contentDetails')
                    content = content_details[0]
                    room_details = content.get("roomDetails")
                    for room_detail in room_details:
                        room_code = room_detail.get("roomCode")
                        cm_room_list.append(cm_hotel_code+"__"+room_code)

                    #print(cm_room_list)

                    room_mismatch_list = set(ingo_room_list).difference(set(cm_room_list))
                    room_not_present_in_derby.extend(room_mismatch_list)

                    room_wise_rateplans = content.get("rateplanDetails")

                    for room_wise_rateplan in room_wise_rateplans:
                        rateplans = room_wise_rateplan.get("rateplans")
                        for rateplan in rateplans:
                            room_code = rateplan.get("roomCode")
                            rp_code = rateplan.get("rateplanCode")
                            cm_rp_list.append(cm_hotel_code+"__"+room_code+"__"+rp_code)

                    #print(cm_rp_list)

                    rp_mismatch_list = set(ingo_rp_list).difference(set(cm_rp_list))
                    rp_not_present_in_derby.extend(rp_mismatch_list)

                    if len(room_mismatch_list) > 0 or len(rp_mismatch_list) > 0:
                        failure_list.append(cm_hotel_code)
                    else:
                        success_list.append(cm_hotel_code)

                    inventory_logger.info(message="Method completed for: %s" % hotel_code, bucket='prep_for_derby_migration')
                else:
                    failure_list.append(hotel_code)
                    inventory_logger.error(message="Api Request Unsuccessful for: %s" % hotel_code, bucket='prep_for_derby_migration')
            else:
                failure_list.append(hotel_code)
                inventory_logger.error(message="Api Request Failed for: %s" % hotel_code, bucket='prep_for_derby_migration')
        except Exception as e:
            failure_list.append(hotel_code)
            inventory_logger.error(message='Exception in fetching hotel content: %s '% str(e), bucket='prep_for_derby_migration')

    inventory_logger.info(message="Success_list: %s" % success_list, bucket='prep_for_derby_migration')
    inventory_logger.info(message="Failure_list: %s" % failure_list, bucket='prep_for_derby_migration')
    inventory_logger.info(message="room_not_present_in_derby: %s" % room_not_present_in_derby, bucket='prep_for_derby_migration')
    inventory_logger.info(message="rp_not_present_in_derby: %s" % rp_not_present_in_derby, bucket='prep_for_derby_migration')

    return success_list, failure_list, room_not_present_in_derby, rp_not_present_in_derby


def get_ingo_rateplan_code_from_cm_product_codes(product_code_list):
    mysql_cm_credential = {"host": "mysql-ingo-inventory-slave.mmt.mmt", "port": "3306", "user": "inventory",
                           "password": "g0!b!b0in", "db": "channel_manager"}
    # mysql_cm_credential = {"host": "mysql-ingo.alpha.mmt", "port": "3306", "user": "ingo_mysql_alpha",
    #                        "password": "ingo_mysql_alpha", "db": "channel_manager"}
    product_codes = []
    db_connection = connect(mysql_cm_credential)
    for cm_product_code in product_code_list:
        cm_hotel_code, cm_room_code, cm_rateplan_code = cm_product_code.split('__')

        query = (
            'SELECT ingo_hotel_code, ingo_room_code, ingo_rateplan_code '
            'FROM channel_manager.cm_hotel_mapping '
            'INNER JOIN channel_manager.cm_room_mapping ON cm_room_mapping.cm_hotel_code = cm_hotel_mapping.cm_hotel_code '
            'INNER JOIN channel_manager.cm_rate_plan_mapping ON cm_rate_plan_mapping.cm_hotel_code = cm_room_mapping.cm_hotel_code '
            'WHERE cm_rate_plan_mapping.cm_room_code = cm_room_mapping.cm_room_code '
            'AND channel_manager.cm_hotel_mapping.cm_hotel_code = %s '
            'AND channel_manager.cm_room_mapping.cm_room_code = %s '
            'AND cm_rateplan_code = %s '
            'AND channel_manager.cm_rate_plan_mapping.is_active = True'
        )
        
        cursor = db_connection.cursor()
        cursor.execute(query, (cm_hotel_code, cm_room_code, cm_rateplan_code))
        row = cursor.fetchone()
        cursor.close()
        if row is not None:
            product_codes.append((str(row[0]), str(row[1]), str(row[2])))
    return product_codes

def deactivate_rateplan_not_present_in_derby(hotel_codes, token):
    failed_rps, success_rps = set(), set()

    success_list, failure_list, room_not_present_in_derby, rp_not_present_in_derby = get_products_not_present_in_derby_but_present_in_ingo(hotel_codes)
    if len(rp_not_present_in_derby) == 0:
        inventory_logger.info(message="No rateplans to deactivate",stage='deactivate_rateplan_not_present_in_derby', bucket='prep_for_derby_migration')
        return success_rps, failed_rps

    product_codes = get_ingo_rateplan_code_from_cm_product_codes(rp_not_present_in_derby)

    inventory_logger.info(message="Product Codes To Be Deactivated: %s" % product_codes, bucket='prep_for_derby_migration')

    headers = {'Content-Type': 'application/json', 'Authorization': 'Token ' + token}
    for ingo_hotel_code, ingo_room_code, ingo_rateplan_code in product_codes:
        update_rateplan_url = "http://in.goibibo.com/api/v2/rateplan/%s/" %ingo_rateplan_code
        api_body = {
            "hotel_id": ingo_hotel_code,
            "room_id": ingo_room_code,
            "is_active": 0,
            "RateplanCode": ingo_rateplan_code
        }

        try:
            api_body["source_config_id"] = "derby"
            api_success, json_response = api_helper.put_request(update_rateplan_url, headers, api_body)

            if api_success:
                success = json_response["success"]
                if success:
                    success_rps.add(ingo_rateplan_code)
                    inventory_logger.info(message="Derby Packet of Rateplan Successfully Deactivated: %s" % ingo_rateplan_code, bucket='prep_for_derby_migration')
                else:
                    failed_rps.add(ingo_rateplan_code)
                    inventory_logger.info(message="Derby Packet of Rateplan Not Updated: %s" % ingo_rateplan_code, bucket='prep_for_derby_migration')

            api_body["source_config_id"] = "ingo"
            api_success_ingo, json_response_ingo = api_helper.put_request(update_rateplan_url, headers, api_body)

            if api_success_ingo:
                success = json_response_ingo["success"]
                if success:
                    if ingo_rateplan_code not in failed_rps: #if rateplan failed in updated in derby packet call  but failed in derby packet call remove that rateplan from success list
                        success_rps.add(ingo_rateplan_code)
                    inventory_logger.info(message="Ingo Packet of Rateplan Successfully Deactivated: %s" % ingo_rateplan_code, bucket='prep_for_derby_migration')
                else:
                    if ingo_rateplan_code in success_rps: #if rateplan got updated in derby packet call  but failed in derby packet call remove that rateplan from success list
                        success_rps.remove(ingo_rateplan_code)
                    failed_rps.add(ingo_rateplan_code)
                    inventory_logger.info(message="Ingo Packet of Rateplan Not Updated: %s" % ingo_rateplan_code, bucket='prep_for_derby_migration')
        except Exception as e:
            failed_rps.add(ingo_rateplan_code)
            inventory_logger.error(message='Exception in updating rateplan active status: %s ' % str(e), bucket='prep_for_derby_migration')

    return success_rps, failed_rps