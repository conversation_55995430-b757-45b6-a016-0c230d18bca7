'''
import time

from django.db import close_old_connections

from scripts.direct_connect.ratecheck import *
from hotels.models import HotelDetail, RatePlan
from utils.logger import Logger

inventory_logger = Logger(logger="inventoryLogger")

mysql_rates_master_credential = {"host": "mysql-ingo-ari-master.mmt.mmt", "port": "3306", "user": "inventory",
                    "password": "g0!b!b0in", "db": "ingommt_ari"}

mysql_rates_credential = {"host": "mysql-ingo-ari-slave.mmt.mmt", "port": "3306", "user": "inventory",
                          "password": "g0!b!b0in", "db": "ingommt_ari"}

mysql_inventory_credential = {"host": "mysql-ingo-ari-slave.mmt.mmt", "port": "3306", "user": "inventory",
                              "password": "g0!b!b0in", "db": "ingommt_ari_inv"}

CONTRACTTYPE_INTID_MAP ={
	"b2c":       "0",
	"b_2_b":     "1",
	"corporate": "2",
	"mobile":    "3",
	"bundled":       "5",
	"hbc":           "6",
	"corporate_rfp": "7",
	"gcc":           "8",
	"b2a":           "9",
	"loggedin":      "10",
	"MMT BLACK":     "11",
	"MMT BLACK1":    "12",
	"MMT BLACK2":    "13",
	"dlo":           "14",
	"IN-POS":        "15",
	"grp":           "16",
}

year_date_all = [
    "001","002","003","004","005","006","007","008","009","010","011","012","013","014","015","016","017","018","019","020","021","022","023","024","025","026","027","028","029","030",
    "031","032","033","034","035","036","037","038","039","040","041","042","043","044","045","046","047","048","049","050","051","052","053","054","055","056","057","058","059","060",
    "061","062","063","064","065","066","067","068","069","070","071","072","073","074","075","076","077","078","079","080","081","082","083","084","085","086","087","088","089","090",
    "091","092","093","094","095","096","097","098","099","100","101","102","103","104","105","106","107","108","109","110","111","112","113","114","115","116","117","118","119","120",
    "121","122","123","124","125","126","127","128","129","130","131","132","133","134","135","136","137","138","139","140","141","142","143","144","145","146","147","148","149","150",
    "151","152","153","154","155","156","157","158","159","160","161","162","163","164","165","166","167","168","169","170","171","172","173","174","175","176","177","178","179","180",
    "181","182","183","184","185","186","187","188","189","190","191","192","193","194","195","196","197","198","199","200","201","202","203","204","205","206","207","208","209","210",
    "211","212","213","214","215","216","217","218","219","220","221","222","223","224","225","226","227","228","229","230","231","232","233","234","235","236","237","238","239","240",
    "241","242","243","244","245","246","247","248","249","250","251","252","253","254","255","256","257","258","259","260","261","262","263","264","265","266","267","268","269","270",
    "271","272","273","274","275","276","277","278","279","280","281","282","283","284","285","286","287","288","289","290","291","292","293","294","295","296","297","298","299","300",
    "301","302","303","304","305","306","307","308","309","310","311","312","313","314","315","316","317","318","319","320","321","322","323","324","325","326","327","328","329","330",
    "331","332","333","334","335","336","337","338","339","340","341","342","343","344","345","346","347","348","349","350","351","352","353","354","355","356","357","358","359","360",
    "361","362","363","364","365"
]
month_days_in_year = [31,28,31,30,31,30,31,31,30,31,30,31]

connection = None

def init_db():
    return mysql.connector.connect(**mysql_rates_master_credential)

def get_db_connection():
    global connection
    if not connection:
        connection = init_db()
    try:
        connection.ping(reconnect=True, attempts=3, delay=5)
    except mysql.connector.Error as err:
        connection = init_db()

    return connection, connection.cursor()

def close_connection(cnx):
    if cnx:
        cnx.close()

def close_cursor(cursor):
    if cursor:
        cursor.close()

def close_open_connections():
    global connection
    close_connection(connection)


# This script will remove extra rates for contract types which are not set at rateplan from the specified month of a start year to end year
# For Example: remove_rateplans_rates(["1000000394"], 2, 2023, 2023, 1)
def remove_rateplans_rates(hotelcode_list, source_config, start_year, end_year, month):
    i = 0
    for hotelcode in hotelcode_list:
        rateplan_list = RatePlan.objects.filter(roomtype__hotel__hotelcode =hotelcode).filter(source_config=source_config).values_list("rateplancode","contracttype")
        if source_config==2: #Synxis rateplans source_config
            synxis_rateplan_rates_remove(rateplan_list,start_year,end_year,month)
        elif source_config==3:# Derby rateplans source_config
            derby_rateplan_rates_remove(rateplan_list,start_year,end_year,month)
        i+=1
        inventory_logger.info(message="Script completed for : %s" % hotelcode, stage='remove_rateplans_rates')
        close_old_connections()

        if i%100 == 0:
            time.sleep(10)


########################################################################################################################
#                                   SYNXIS METHODS                                                                     #
########################################################################################################################

def fetch_and_remove_synxis_rateplans_rates(start_year, end_year, month):
    close_old_connections()
    synxis_rateplans=RatePlan.objects.filter(source_config=2).values_list("rateplancode","contracttype")
    batch_size = 500
    current_batch = 0
    for i in range(0, len(synxis_rateplans), batch_size):
        if current_batch + batch_size > len(synxis_rateplans):
            synxis_rateplans_batch = synxis_rateplans[current_batch:]
        synxis_rateplans_batch = synxis_rateplans[current_batch:current_batch + batch_size]
        current_batch += batch_size
        synxis_rateplan_rates_remove(synxis_rateplans_batch, start_year, end_year, month)

def synxis_rateplan_rates_remove(synxis_rateplans, start_year, end_year, month):
    rp_list = []
    for rateplan in synxis_rateplans:
        rateplancode=rateplan[0]
        hotelcode = RatePlan.objects.values_list('roomtype__hotel__hotelcode').get(rateplancode=rateplancode)
        hotel_obj = HotelDetail.objects.get(hotelcode=hotelcode[0])
        hotel_internal_model_codes=hotel_obj.internal_model_codes.split(",")
        contract_type_list=rateplan[1]

        non_existing_cts = ""
        for contract_type in CONTRACTTYPE_INTID_MAP:
            if contract_type not in contract_type_list:
                non_existing_cts += CONTRACTTYPE_INTID_MAP[contract_type] + ","

        if len(non_existing_cts) > 0:
            non_existing_cts = non_existing_cts[:-1]

        remove_synxis_rates(rateplancode,non_existing_cts,hotel_internal_model_codes[1],start_year,end_year, month)

    return "Completed"

def remove_synxis_rates(rateplancode, contract_type, rates_internal_model_code, start_year, end_year, month):
    basic_table='ingommt_ari.hotels_hotelrates_basic_synxis_v2'
    extended_table='ingommt_ari.hotels_hotelrates_extended_synxis'
    if rates_internal_model_code=='8100010':
        basic_table='ingommt_ari.hotels_hotelrates_basic_los_synxis'
        extended_table='ingommt_ari.hotels_hotelrates_extended_los_synxis'
    batch_size = 30
    start_date = 0

    try:
        cnx, cursor = get_db_connection()
        for i in range(0,month -1):
            start_date += month_days_in_year[i]

        for date_year in range (start_year,end_year+1):
            current_batch=start_date
            year_date=year_date_all
            date_year=str(date_year)
            for day in range(start_date, 366, batch_size):
                if current_batch +  batch_size > len(year_date):
                    date_batch=year_date[current_batch:]
                else:
                    date_batch=year_date[current_batch:current_batch+batch_size]
                idate_list=''
                for date in date_batch:
                    idate=date_year+str(date)
                    idate_list+=idate+','
                current_batch+=batch_size
                idate_list=idate_list[:-1]
                update_synxis_rates_basic(rateplancode, idate_list, contract_type,basic_table,cnx, cursor)
                update_synxis_rates_extended(rateplancode,idate_list,contract_type,extended_table,cnx, cursor)

        close_cursor(cursor)
        close_open_connections()

    except Exception as e:
        inventory_logger.error(message="Exception : %s" % str(e), stage='synxis_rateplan_rates_remove')

def update_synxis_rates_basic(rateplancode,idate,contract_type,basic_table,cnx, cursor):
    query_rates='select id from ' + basic_table + '  where product_code=31 and product_object_code="' + \
            rateplancode + '" and idate in (' +idate +') and contract_type in ('+contract_type+ ');'

    cursor.execute(query_rates)
    rows = cursor.fetchall()

    if len(rows)==0:
        return

    inventory_logger.info(message="Basic | RatePlanCode: %s Contract Type: %s No of rows: %d" % (
        rateplancode, contract_type, len(rows)), stage='synxis_rateplan_rates_remove')

    id_list=''
    for sql_row in rows:
        id_list+=str(sql_row[0])+','
    id_list=id_list[:-1]

    update_query='update  ' + basic_table + \
                ' set adult_price_one=-1,adult_price_two=-1,adult_price_three=-1,adult_price_four=-1,adult_price_five=-1,' + \
                'adult_price_six=-1,adult_price_seven=-1,adult_price_eight=-1,adult_price_one_with_tax=-1,adult_price_two_with_tax=-1,' + \
                'adult_price_three_with_tax=-1,adult_price_four_with_tax=-1,adult_price_five_with_tax=-1,adult_price_six_with_tax=-1,' + \
                'adult_price_seven_with_tax=-1,adult_price_eight_with_tax=-1,child_range_one_min_age=0,child_range_one_max_age=0,' + \
                'child_range_one_price_one=-1,child_range_one_price_two=-1,child_range_one_price_three=-1,child_range_one_price_one_with_tax=-1,' + \
                'child_range_one_price_two_with_tax=-1,child_range_one_price_three_with_tax=-1,child_range_two_min_age=0,child_range_two_max_age=0,' + \
                'child_range_two_price_one=-1,child_range_two_price_two=-1,child_range_two_price_three=-1,child_range_two_price_one_with_tax=-1,' + \
                'child_range_two_price_two_with_tax=-1,child_range_two_price_three_with_tax=-1,child_range_three_min_age=0,child_range_three_max_age=0,' + \
                'child_range_three_price_one=-1,child_range_three_price_two=-1,child_range_three_price_three=-1,child_range_three_price_one_with_tax=-1,' + \
                'child_range_three_price_two_with_tax=-1,child_range_three_price_three_with_tax=-1 ' + \
                'where  id in ('+ id_list+') ;'

    inventory_logger.info(message="Basic | Update Query: %s" % update_query, stage='synxis_rateplan_rates_remove')

    cursor.execute(update_query)
    cnx.commit()

def update_synxis_rates_extended(rateplancode,idate,contract_type,extended_table, cnx, cursor):
    query_rates='select id from ' + extended_table + '  where product_code=31 and product_object_code="' + \
            rateplancode + '" and idate in (' + idate  +') and contract_type in ('+ contract_type + ');'

    cursor.execute(query_rates)
    rows = cursor.fetchall()

    if len(rows)==0:
        return

    inventory_logger.info(message="Extended | RatePlanCode: %s Contract Type: %s No of rows: %d" % (
        rateplancode, contract_type, len(rows)), stage='synxis_rateplan_rates_remove')
    id_list=''
    for sql_row in rows:
        id_list+=str(sql_row[0])+','

    id_list=id_list[:-1]

    update_query='update  ' + extended_table + \
                ' set adult_price_and_price_with_tax_till_occ_twenty="#######################", ' + \
                'adult_price_and_price_with_tax_till_occ_y="###################" ,' + \
                'child_range_one_price_and_price_with_tax_till_twelve = "#################" ,' + \
                'child_range_two_price_and_price_with_tax_till_twelve = "#################" ,' + \
                'child_range_three_price_and_price_with_tax_till_twelve = "#################"' + \
                ' where  id in ('+ id_list +') ;'

    inventory_logger.info(message="Extended | Update Query: %s" % update_query, stage='synxis_rateplan_rates_remove')
    cursor.execute(update_query)
    cnx.commit()


########################################################################################################################
#                                   DERBY METHODS                                                                      #
########################################################################################################################

def fetch_and_remove_derby_rateplans_rates(start_year, end_year):
    close_old_connections()
    derby_rateplans=RatePlan.objects.filter(source_config=3).values_list("rateplancode","contracttype")
    batch_size=500
    current_batch=0
    for i in range(0,len(derby_rateplans), batch_size):
        if current_batch + batch_size >len(derby_rateplans):
            derby_rateplans_batch=derby_rateplans[current_batch:]
        derby_rateplans_batch=derby_rateplans[current_batch:current_batch + batch_size]
        current_batch+=batch_size
        derby_rateplan_rates_remove(derby_rateplans_batch,start_year,end_year)

def derby_rateplan_rates_remove(derby_rateplans, start_year, end_year, month):
    for rateplan in derby_rateplans:
        rateplancode=rateplan[0]
        hotelcode = RatePlan.objects.values_list('roomtype__hotel__hotelcode').get(rateplancode=rateplancode)
        hotel_obj = HotelDetail.objects.get(hotelcode=hotelcode[0])
        hotel_internal_model_codes=hotel_obj.internal_model_codes.split(",")
        contract_type_list=rateplan[1]

        non_existing_cts = ""
        for contract_type in CONTRACTTYPE_INTID_MAP:
            if contract_type not in contract_type_list:
                non_existing_cts += CONTRACTTYPE_INTID_MAP[contract_type] + ","

        if len(non_existing_cts) > 0:
            non_existing_cts = non_existing_cts[:-1]

        remove_derby_rates(rateplancode, non_existing_cts, hotel_internal_model_codes[1], start_year, end_year, month)

    return "Completed"

def remove_derby_rates(rateplancode, contract_type, rates_internal_model_code, start_year, end_year, month):
    basic_table='ingommt_ari.hotels_hotelrates_basic_derby'
    extended_table='ingommt_ari.hotels_hotelrates_extended_derby'
    if rates_internal_model_code=='8100013':
        basic_table='ingommt_ari.hotels_hotelrates_basic_los_derby'
        extended_table='ingommt_ari.hotels_hotelrates_extended_los_derby'

    start_date = 0
    batch_size = 30
    try:
        cnx, cursor = get_db_connection()
        for i in range(0,month -1):
            start_date += month_days_in_year[i]

        for date_year in range (start_year,end_year+1):
            current_batch=start_date
            year_date=year_date_all
            date_year=str(date_year)
            for day in range(start_date, 366, batch_size):
                if current_batch +  batch_size > len(year_date):
                    date_batch=year_date[current_batch:]
                else:
                    date_batch=year_date[current_batch:current_batch+batch_size]
                idate_list=''
                for date in date_batch:
                    idate=date_year+str(date)
                    idate_list+=idate+','
                current_batch+=batch_size
                idate_list=idate_list[:-1]
                update_derby_rates_basic(rateplancode, idate_list, contract_type, basic_table, cnx, cursor)
                update_derby_rates_extended(rateplancode, idate_list, contract_type, extended_table, cnx, cursor)

        close_cursor(cursor)
        close_open_connections()

    except Exception as e:
        inventory_logger.error(message="Exception : %s" % str(e), stage='derby_rateplan_rates_remove')

def update_derby_rates_basic(rateplancode, idate_list, contract_type_list, basic_table, cnx, cursor):
    query_rates='select id from ' + basic_table + '  where product_code=31 and product_object_code="' + \
            rateplancode + '" and idate in (' + idate_list +') and contract_type in ('+ contract_type_list + ');'

    cursor.execute(query_rates)
    rows = cursor.fetchall()

    if len(rows)==0:
        return

    inventory_logger.info(message="Basic | RatePlanCode: %s Contract Type: %s No of rows: %d" % (
        rateplancode, contract_type_list, len(rows)), stage='derby_rateplan_rates_remove')

    id_list=''
    for sql_row in rows:
        id_list += str(sql_row[0])+','
    id_list = id_list[:-1]

    update_query='update  ' + basic_table + \
                ' set adult_one_price=-1,adult_two_price=-1,adult_three_price=-1,adult_four_price=-1,adult_one_child_one_price=-1,' + \
                'adult_two_child_one_price=-1,adult_three_child_one_price=-1,adult_four_child_one_price=-1,adult_one_child_two_price=-1,' + \
                'adult_two_child_two_price=-1,adult_three_child_two_price=-1,adult_four_child_two_price=-1,adult_one_child_three_price=-1,' + \
                'adult_two_child_three_price=-1,adult_three_child_three_price=-1,adult_four_child_three_price=-1,' + \
                'adult_one_price_with_tax=-1,adult_two_price_with_tax=-1,adult_three_price_with_tax=-1,adult_four_price_with_tax=-1,' + \
                'adult_one_child_one_price_with_tax=-1,adult_two_child_one_price_with_tax=-1,adult_three_child_one_price_with_tax=-1,' + \
                'adult_four_child_one_price_with_tax=-1,adult_one_child_two_price_with_tax=-1,adult_two_child_two_price_with_tax=-1,' + \
                'adult_three_child_two_price_with_tax=-1,adult_four_child_two_price_with_tax=-1,adult_one_child_three_price_with_tax=-1,' + \
                'adult_two_child_three_price_with_tax=-1,adult_three_child_three_price_with_tax=-1,adult_four_child_three_price_with_tax=-1' + \
                ' where  id in ('+ id_list +') ;'

    inventory_logger.info(message="Basic | Update Query: %s" % update_query, stage='derby_rateplan_rates_remove')

    cursor.execute(update_query)
    cnx.commit()

def update_derby_rates_extended(rateplancode, idate_list, contract_type_list, extended_table, cnx, cursor):
    query_rates='select id from ' + extended_table + '  where product_code=31 and product_object_code="' + \
            rateplancode + '" and idate in (' + idate_list +') and contract_type in ('+ contract_type_list + ');'

    cursor.execute(query_rates)
    rows = cursor.fetchall()

    if len(rows)==0:
        return

    inventory_logger.info(message="Extended | RatePlanCode: %s Contract Type: %s No of rows: %d" % (
        rateplancode, contract_type_list, len(rows)), stage='derby_rateplan_rates_remove')

    id_list=''
    for sql_row in rows:
        id_list += str(sql_row[0])+','
    id_list = id_list[:-1]

    update_query='update  ' + extended_table + \
                ' set prices=NULL' + \
                ' where  id in ('+ id_list +') ;'

    inventory_logger.info(message="Extended | Update Query: %s" % update_query, stage='derby_rateplan_rates_remove')

    cursor.execute(update_query)
    cnx.commit()

'''