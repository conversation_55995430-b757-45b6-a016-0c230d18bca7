######################################################################
# Post Content Refresh Script
######################################################################

from scripts.direct_connect.derby_migration_scripts import *

from utils.logger import Logger

inventory_logger = Logger(logger="inventoryLogger")

INGO_TO_DERBY_CHAINCODE_MAP = {"radisson": "RADISSON",
                                "radissonnt": "RADISSON",
                                "bestwstrn": "BESTWESTERN",
                                "bestwstrnn": "BESTWESTERN",
                                "mariottntr": "MARRIOTT",
                                "wyndham": "WYNDXIS"
                               }

def post_content_refresh_script(hotel_codes, ingo_chain_code):
    existing_hotel_codes = HotelDetail.objects.filter(hotelcode__in=hotel_codes, source_hotelcode__isnull=False,
                                                      source_config__isnull=False).values_list('hotelcode', flat=True)
    if len(existing_hotel_codes) != len(hotel_codes):
        intersection_list = set(hotel_codes).intersection(existing_hotel_codes)
        missing_hotel_codes = set(hotel_codes).difference(intersection_list)
        inventory_logger.info(message="Missing Mappings for Hotel Codes: %s" % missing_hotel_codes,
                              stage='post_content_refresh_stage', bucket='prep_for_derby_migration')

    success_list, failure_list = update_currency_from_max_api(existing_hotel_codes, ingo_chain_code)

    inventory_logger.info(message="Currency Code Update Failed for: %s" % str(failure_list),
                          stage='post_content_refresh_stage', bucket='prep_for_derby_migration')

    inventory_logger.info(message="Currency Code Update Succeeded for: %s" % str(success_list),
                          stage='post_content_refresh_stage', bucket='prep_for_derby_migration')

    success_list_2, failure_list_2 = update_child_age_from_derby_content_api(existing_hotel_codes, ingo_chain_code)

    inventory_logger.info(message="Child Age Update Failed for: %s" % str(failure_list_2),
                          stage='post_content_refresh_stage', bucket='prep_for_derby_migration')

    inventory_logger.info(message="Child Age Update Succeeded for: %s" % str(success_list_2),
                          stage='post_content_refresh_stage', bucket='prep_for_derby_migration')

    update_mapping_at_nexus(existing_hotel_codes)
    inventory_logger.info(message="Nexus Mappings successfully Updated",
                          stage='post_content_refresh_stage', bucket='prep_for_derby_migration')


# Script to update max child age of hotel from Product API (BestWestern)
def update_child_age_from_derby_content_api(hotel_codes, ingo_chain_code):
    url = "http://ingo-nexus-partner.ecs.mmt/api/ingo-direct/derby/v1/content"
    headers = {'Content-Type': 'application/json'}

    success_list = []
    failure_list = []
    cm_chain_code = INGO_TO_DERBY_CHAINCODE_MAP.get(ingo_chain_code)
    for hotel_code in hotel_codes:
        hotel_obj = HotelDetail.objects.get(hotelcode=hotel_code)
        cm_hotel_code = hotel_obj.source_hotelcode
        if cm_hotel_code is None or cm_hotel_code == "":
            inventory_logger.info(message="No Vendor Hotel Code for: %s" % hotel_code,
                                  stage='update_child_age_from_derby_content_api',
                                  bucket='prep_for_derby_migration')
            continue

        api_body = {
            "hotelDetails": [
                {
                    "chainCode": cm_chain_code,
                    "hotelCode": cm_hotel_code,
                    "vendorAdditionalDetail": {
                        "isMaxResponseRequired": False,
                        "isProductResponseRequired": True
                    }
                }
            ]
        }
        try:
            api_success, json_response = api_helper.post_request(url, headers, api_body)
            if api_success:
                is_success = json_response['success']
                if is_success:
                    content_details = json_response.get('contentDetails')
                    content = content_details[0]
                    hotel_detail = content.get("hotelDetail")
                    max_child_age = hotel_detail.get('attributes', {}).get('maxChildAge', -1)

                    if max_child_age == -1:
                        failure_list.append(hotel_code)
                        inventory_logger.error(message="Max Child Age not available for: %s" % hotel_code,
                                              stage='update_child_age_from_derby_content_api',
                                              bucket='prep_for_derby_migration')
                        continue

                    hotel_obj.max_infant_age = max_child_age
                    hotel_obj.max_child_age = max_child_age
                    hotel_obj.max_adolescent_age = max_child_age
                    hotel_obj.save()

                    success_list.append(hotel_code)
                    inventory_logger.info(message="Max Child Age successfully updated for: %s" % hotel_code,
                                          stage='update_child_age_from_derby_content_api',
                                          bucket='prep_for_derby_migration')
                else:
                    failure_list.append(hotel_code)
                    inventory_logger.error(message="Api Request Unsuccessful for: %s" % hotel_code,
                                           stage='update_child_age_from_derby_content_api',
                                           bucket='prep_for_derby_migration')
            else:
                failure_list.append(hotel_code)
                inventory_logger.error(message="Api Request Failed for: %s" % hotel_code,
                                      stage='update_child_age_from_derby_content_api',
                                      bucket='prep_for_derby_migration')
        except Exception as e:
            failure_list.append(hotel_code)
            inventory_logger.error(message='Exception in fetching hotel content: %s '% str(e),
                                   stage='update_child_age_from_derby_content_api', bucket='prep_for_derby_migration')

    return success_list, failure_list


# Script to update base and VCC currency of hotel from Max API
def update_currency_from_max_api(hotel_codes, ingo_chain_code):
    url = "http://ingo-nexus-partner.ecs.mmt/api/ingo-direct/derby/v1/content"
    headers = {'Content-Type': 'application/json'}
    hotel_success = []
    hotel_failures = []
    cm_chain_code = INGO_TO_DERBY_CHAINCODE_MAP.get(ingo_chain_code)
    for hotel_code in hotel_codes:
        try:
            hotel_obj = HotelDetail.objects.get(hotelcode=hotel_code)
            cm_hotel_code = hotel_obj.source_hotelcode
            if cm_hotel_code is None or cm_hotel_code == "":
                inventory_logger.info(message="No Vendor Hotel Code for: %s" % hotel_code,
                                      stage='update_currency_from_max_api', bucket='prep_for_derby_migration')
                continue

            api_body = {
                "hotelDetails": [
                    {
                        "chainCode": cm_chain_code,
                        "hotelCode": cm_hotel_code,
                        "vendorAdditionalDetail": {
                            "isMaxResponseRequired": True,
                            "isProductResponseRequired": True
                        }
                    }
                ]
            }
            api_success, json_response = api_helper.post_request(url, headers, api_body)
            if api_success:
                is_success = json_response['success']
                if is_success:
                    content_details = json_response.get('contentDetails')
                    content = content_details[0]
                    currency_code = content.get("currencyCode")

                    if currency_code is None or currency_code == "":
                        hotel_failures.append(hotel_code)
                        inventory_logger.error(message="Currency not available for: %s" % hotel_code,
                                              stage='update_currency_from_max_api', bucket='prep_for_derby_migration')
                        continue

                    if hotel_obj.base_currency != currency_code or hotel_obj.vcc_currency != currency_code:
                        hotel_obj.base_currency= currency_code
                        hotel_obj.vcc_currency = currency_code
                        hotel_obj.save()
                        hotel_success.append(hotel_code)
                        inventory_logger.info(message="Currency successfully updated for: %s with currency: %s" %(hotel_code, currency_code),
                                               stage='update_currency_from_max_api', bucket='prep_for_derby_migration')
                    else:
                        inventory_logger.info(message="Currency update not needed", bucket='prep_for_derby_migration',
                                              stage='update_currency_from_max_api' )

        except Exception as e:
            inventory_logger.error(message='Exception in set_currencyCode with error: {}'.format(str(e)),
                                   stage='update_currency_from_max_api', bucket='prep_for_derby_migration')

    return hotel_success, hotel_failures



def enable_ari(hotel_codes):
    mysql_cm_credential = {"host": "ingoibibo.mysql.master.goibibo.com", "port": "3306", "user": "inventory",
                           "password": "g0!b!b0in", "db": "channel_manager"}
    # mysql_cm_credential = {"host": "mysql-ingo.alpha.mmt", "port": "3306", "user": "ingo_mysql_alpha",
    #                        "password": "ingo_mysql_alpha", "db": "channel_manager"}
    if not hotel_codes or len(hotel_codes) == 0:
        return []

    hotel_code_placeholders = ','.join(['%s'] * len(hotel_codes))

    query = (
        'UPDATE channel_manager.cm_hotel_mapping '
        'SET enable_ari = 1 '
        'WHERE ingo_hotel_code IN (' + hotel_code_placeholders + ');'
    )

    try:
        db_connection = mysql.connector.connect(
            host=mysql_cm_credential["host"],
            port=mysql_cm_credential["port"],
            user=mysql_cm_credential["user"],
            password=mysql_cm_credential["password"],
            database=mysql_cm_credential["db"]
        )
        cursor = db_connection.cursor()
        cursor.execute(query, hotel_codes)
        db_connection.commit()
    except Exception as e:
        inventory_logger.error(message='Exception in enable_ari with error: {}'.format(str(e)),
                                stage='enable_ari', bucket='post_content_refresh_script')
