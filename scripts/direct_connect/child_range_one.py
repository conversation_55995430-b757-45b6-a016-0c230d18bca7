from hotels.models import RoomDetail, HotelDetail, RatePlan
from django.db import close_old_connections
from utils.logger import Logger
import mysql.connector
from goibibo_inventory.settings import app
import mysql
import mysql.connector
inventory_logger = Logger(logger="inventoryLogger")


mysql_rates_master_credential = {"host": "mysql-ingo-ari-master.mmt.mmt", "port": "3306", "user": "inventory",
                    "password": "g0!b!b0in", "db": "ingommt_ari","connect_timeout":5}


# year_date_all = [
#     "001","002","003","004","005","006","007","008","009","010","011","012","013","014","015","016","017","018","019","020","021","022","023","024","025","026","027","028","029","030","031","032","033","034","035","036","037","038","039","040","041","042","043","044","045","046","047","048","049","050","051","052","053","054","055","056","057","058","059","060","061","062","063","064","065","066","067","068","069","070","071","072","073","074","075","076","077","078","079","080","081","082","083","084","085","086","087","088","089","090","091","092","093","094","095","096","097","098","099","100","101","102","103","104","105","106","107","108","109","110","111","112","113","114","115","116","117","118","119","120","121","122","123","124","125","126","127","128","129","130","131","132","133","134","135","136","137","138","139","140","141","142","143","144","145","146","147","148","149","150","151","152","153","154","155","156","157","158","159","160","161","162","163","164","165","166","167","168","169","170","171","172","173","174","175","176","177","178","179","180","181","182","183","184","185","186","187","188","189","190","191","192","193","194","195","196","197","198","199","200","201","202","203","204","205","206","207","208","209","210","211","212","213","214","215","216","217","218","219","220","221","222","223","224","225","226","227","228","229","230","231","232","233","234","235","236","237","238","239","240","241","242","243","244","245","246","247","248","249","250","251","252","253","254","255","256","257","258","259","260","261","262","263","264","265","266","267","268","269","270","271","272","273","274","275","276","277","278","279","280","281","282","283","284","285","286","287","288","289","290","291","292","293","294","295","296","297","298","299","300","301","302","303","304","305","306","307","308","309","310","311","312","313","314","315","316","317","318","319","320","321","322","323","324","325","326","327","328","329","330","331","332","333","334","335","336","337","338","339","340","341","342","343","344","345","346","347","348","349","350","351","352","353","354","355","356","357","358","359","360","361","362","363","364","365"
# ]

year_date_all = [
    "096","097","098","099","100","101","102","103","104","105","106","107","108","109","110","111","112","113","114","115","116","117","118","119","120",
    "121","122","123","124","125","126","127","128","129","130","131","132","133","134","135","136","137","138","139","140","141","142","143","144","145",
    "146","147","148","149","150","151","152","153","154","155","156","157","158","159","160","161","162","163","164","165","166","167","168","169","170",
    "171","172","173","174","175","176","177","178","179","180","181","182","183","184","185","186","187","188","189","190","191","192","193","194","195",
    "196","197","198","199","200","201","202","203","204","205","206","207","208","209","210","211","212","213","214","215","216","217","218","219","220",
    "221","222","223","224","225","226","227","228","229","230","231","232","233","234","235","236","237","238","239","240","241","242","243","244","245",
    "246","247","248","249","250","251","252","253","254","255","256","257","258","259","260","261","262","263","264","265","266","267","268","269","270",
    "271","272","273","274","275","276","277","278","279","280","281","282","283","284","285","286","287","288","289","290","291","292","293","294","295",
    "296","297","298","299","300","301","302","303","304","305","306","307","308","309","310","311","312","313","314","315","316","317","318","319","320",
    "321","322","323","324","325","326","327","328","329","330","331","332","333","334","335","336","337","338","339","340","341","342","343","344","345",
    "346","347","348","349","350","351","352","353","354","355","356","357","358","359","360","361","362","363","364","365"
]


connection = None


def init_db():
    return mysql.connector.connect(**mysql_rates_master_credential)

def get_db_connection():
    global connection
    if not connection:
        connection = init_db()

    try:
        connection.ping(reconnect=True, attempts=3, delay=5)
    except mysql.connector.Error as err:
        # reconnect your cursor as you did in __init__ or wherever
        connection = init_db()

    return connection, connection.cursor()


def close_connection(cnx):
    if cnx:
        cnx.close()


def close_cursor(cursor):
    if cursor:
        cursor.close()


def close_open_connections():
    global connection
    close_connection(connection)


def run_db_query(query, args):
    cnx, cursor = get_db_connection()
    if cnx and cursor:
        cursor.execute(query, args)
        cnx.commit()

hotelcode_list=[]
window = 60*60
batch_size = 25

# from scripts.direct_connect.child_range_one import remove_infant_rateplans_rates
# remove_infant_rateplans_rates.apply_async(args=(hotelcode_list,),countdown=1)

def run_in_batch(hotelcode_list, window, batch_size):
    current_batch=0
    countdown_time=10
    for hotel in range(0,len(hotelcode_list),batch_size):
        if current_batch+batch_size>len(hotelcode_list):
            hotelcode_list_batch=hotelcode_list[current_batch:]
        else:
            hotelcode_list_batch=hotelcode_list[current_batch:current_batch+batch_size]
        remove_infant_rateplans_rates.apply_async(args=(hotelcode_list_batch,),countdown=countdown_time)
        current_batch = current_batch + batch_size
        countdown_time = countdown_time + window

@app.task(name="nexus_partner_services.remove_infant_rateplans_rates")
def remove_infant_rateplans_rates(hotelcode_list):
    for hotelcode in hotelcode_list:
        try:
            inventory_logger.info( message="Script executed for hotel %s" % (str(hotelcode)), stage='remove_infant_rateplans_rates')
            close_old_connections()
            hotelid_list = HotelDetail.objects.filter(hotelcode=hotelcode).values("id")
            hotel_id = hotelid_list[0]['id']
            rateplan_list = RatePlan.objects.filter(roomtype__hotel_id = hotel_id, isactive = 1).values_list("rateplancode")
            for rateplan in rateplan_list:
                rateplancode=rateplan[0]
                inventory_logger.info( message="Before Updating hotel rates for hotelcode %s rateplancode: %s"  %(str(hotelcode),str(rateplancode)), stage='remove_infant_rateplans_rates')
                remove_daily_ingo_infant_rates(rateplancode)
                inventory_logger.info( message="After Updating hotel rates for hotelcode %s rateplancode: %s" % (str(hotelcode),str(rateplancode)), stage='remove_infant_rateplans_rates')
        except Exception as e:
            # close_old_connections()
            inventory_logger.error( message="Error occurred running for hotelcode  %s , Error %s" % (str(hotelcode), str(e)), stage='remove_infant_rateplans_rates')



def remove_daily_ingo_infant_rates(rateplancode):
    rates_table='ingommt_ari.hotels_hotelrates_basic'

    batch_size=30
    try:
        cnx, cursor = get_db_connection()
        for date_year in range (2023,2024):
            current_batch=0
            year_date=year_date_all
            date_year=str(date_year)
            for day in range(0,len(year_date),batch_size):
                if current_batch +  batch_size > len(year_date):
                    date_batch=year_date[current_batch:]
                else:
                    date_batch=year_date[current_batch:current_batch+batch_size]
                idate_list=''
                for date in date_batch:
                    idate=date_year+str(date)
                    idate_list+=idate+','
                current_batch+=batch_size
                idate_list=idate_list[:-1]
                update_infant_extra_price_zero(rates_table,rateplancode,cnx,cursor,idate_list)

        close_cursor(cursor)
        close_open_connections()
    except Exception as e:
        inventory_logger.error( message="Error occured running for rateplancode  %s , Error %s" % (str(rateplancode), str(e)), stage='remove_daily_ingo_infant_rates')

def update_infant_extra_price_zero(table,rateplancode,cnx,cursor,idate_list):
    try:
        if not idate_list:
            return

        idate_placeholders = ','.join(['%s'] * len(idate_list))

        query_rates = (
            'SELECT id FROM {table} '
            'WHERE product_code = %s AND extra_infant_price = %s AND product_object_code = %s '
            'AND idate IN ({idate_placeholders})'
        ).format(table=table, idate_placeholders=idate_placeholders)

        cursor.execute(query_rates, [31, -1, rateplancode] + idate_list)
        rows = cursor.fetchall()

        if not rows:
            return

        ids = [row[0] for row in rows]
        id_placeholders = ','.join(['%s'] * len(ids))

        update_query = (
            'UPDATE {table} SET extra_infant_price = 0 '
            'WHERE product_code = %s AND product_object_code = %s AND id IN ({id_placeholders})'
        ).format(table=table, id_placeholders=id_placeholders)

        cursor.execute(update_query, [31, rateplancode] + ids)
        cnx.commit()
        inventory_logger.info(message="Updated from Table %s, for rateplancode: %s and no of rows %d" %(str(table),rateplancode,len(rows)) ,stage='update_infant_extra_price_zero')
    except Exception as e:
        inventory_logger.error(message="Error occured running update query for rateplancode %s , Error %s " %(str(rateplancode),str(e)) ,stage='update_infant_extra_price_zero')

