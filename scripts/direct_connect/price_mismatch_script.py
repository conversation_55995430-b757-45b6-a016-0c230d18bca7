import json, requests
from datetime import timedelta

from ingouser.models import User
from django.db import close_old_connections

from scripts.direct_connect.direct_connect_script import *
from scripts.direct_connect.ratecheck import *

from hotels.models import RoomDetail, HotelDetail, RatePlan

from utils.logger import Logger

inventory_logger = Logger(logger="inventoryLogger")

right_rateplans = {}
dmc_segment_id_map = {"G": "1120"}

idate = 2022046
checkin, checkout = "2022-02-15", "2022-02-16"
dc_url = "http://hotels-connector-suppliers.ecs.mmt/dmc-connector/services/v2/connector/HOTEL/SNXX0002/SEARCH/PI01MULTIHOTELCR"
ingo_url = "http://***********:9098/api/ingo/v1/search"
vendor = "synxis"

# dict keys of hotels_with_issue
# "dmc_res" "conn_res"
# "dmc_extra_rateplan" "conn_extra_rateplan"
# "price_mismatch"

mysql_inv_credential = {"host": "ingoibibo.mysql.master.mmt.mmt", "port": "3306", "user": "inventory",
                    "password": "g0!b!b0in", "db": "goibibo_inventory"}

mysql_rates_credential = {"host": "mysql-ingo-ari-slave.mmt.mmt", "port": "3306", "user": "inventory",
                          "password": "g0!b!b0in", "db": "ingommt_ari"}

mysql_inventory_credential = {"host": "mysql-ingo-ari-slave.mmt.mmt", "port": "3306", "user": "inventory",
                              "password": "g0!b!b0in", "db": "ingommt_ari_inv"}

########################################################################################################################
# Steps to run this script:

# import: from scripts.direct_connect.price_mismatch_script import *
# 1. Run get_hotels function with the ingo hotel code list
# ingo_hotel_codes = [ingo_hotel_codes]
# ingo_hotel_list, mmt_hotel_list = get_hotels(ingo_hotel_codes)
# hotels_with_issue, hotels_without_issue = {}, {}
# Set the following values:
# vendor = "derby"
# idate = 2023075
# checkin, checkout = "2023-03-16", "2023-03-17"

# 2. Run con_map_with_ingo_hotels function, this prints a map which you will need to paste in config/base_config.json file
# con_map_with_ingo_hotels(ingo_hotel_list)
# TODO: Create an API to update hotelMapping in Hotel Nexus Connector Supplier

# 3. Run:
# compare_two_flows(ingo_hotel_list, mmt_hotel_list, checkin, checkout, idate, dc_url, ingo_url, vendor, hotels_with_issue, hotels_without_issue, is_phoenix_db_data_reqd=True, is_hotels_without_issue_data_reqd=False)
# This prints the data of hotels with issue for every pax item (there are total 5 kinds of pax items present)
# change checkin, checkout, idate, dc_url, vendor of your choice
# defaults are checkin= "2022-02-05", checkout = "2022-02-06", idate = 2022036
# dc_url = "http://hotels-connector-suppliers.ecs.mmt/dmc-connector/services/v2/connector/HOTEL/SNXX0002/SEARCH/PI01MULTIHOTELCR"
# vendor = "synxis", is_phoenix_db_data_reqd = False, is_hotels_without_issue_data_reqd = False

# 4. If you want to check the right hotels data, then simply pass is_hotels_without_issue_data_reqd = True
# For checking phoenix db data, pass is_phoenix_db_data_reqd = True

# 5. If you re-run the script, then re-initialize these values
# hotels_with_issue = {}
# hotels_without_issue = {}
# right_rateplans = {}

# 6. If we receive no rates from BE in Check rates api--  1. check if there are any rates in the ratecheck api
# if no rates in ratecheck then we will treat this as a success criteria
# 7. if there are rates in rate check api we will query our database for certain derby rateplans whose contract type is b2c
# and check if there are any entries. If there are entries we will mark as failure case

# After completion of the script, please don't forget to run
# vendor = "synxis" or vendor = "derby"
# move_to_content_only(ingo_hotel_list, vendor)
########################################################################################################################


# Returns Ingo and MMT hotel list
def get_hotels(ingo_hotel_codes):
    close_old_connections()
    hd_list = HotelDetail.objects.filter(hotelcode__in=ingo_hotel_codes, source_config_id__in=[2, 3]).values(
        "hotelcode", "mmt_id")

    ingo_hotel_list, mmt_hotel_list = [], []

    for hotel in hd_list:
        ingo_hotel_list.append(hotel["hotelcode"])
        mmt_hotel_list.append(str(hotel["mmt_id"]))
    close_old_connections()
    existing_user = User.objects.get(id=134366)
    convert_hotel_contentonly_to_contracted(ingo_hotel_list, existing_user)
    return ingo_hotel_list, mmt_hotel_list


def move_to_content_only(ingo_hotel_list, vendor):
    close_old_connections()
    existing_user = User.objects.get(id=134366)
    convert_hotel_contracted_to_contentonly(ingo_hotel_list, existing_user, vendor)


# Prints Map of MMT hotel with Ingo hotel for connector base_config (for more info, read above steps)
def con_map_with_ingo_hotels(ingo_hotel_list):
    close_old_connections()
    hd_list = HotelDetail.objects.filter(hotelcode__in=ingo_hotel_list).values("mmt_id", "hotelcode")
    for hotel in hd_list:
        str_map = ""
        str_map = '"' + str(hotel["mmt_id"]) + '": "' + str(hotel["hotelcode"]) + '",'
        print(str_map)


def connect(credential):
    try:
        db_connection = mysql.connector.connect(
            host=credential["host"],
            port=credential["port"],
            user=credential["user"],
            password=credential["password"],
            database=credential["db"])
    except Exception as ex:
        print(ex)
        print("not connected")
        inventory_logger.info(
            message="Can't connect to mysql db, error: %s" % (str(ex)), stage='django_override_script')
        return
    inventory_logger.info(message="connected to mysql db", stage='django_override_script')
    print("connected")
    print("\n")
    return db_connection


def query_execute(query, db_connection):
    try:
        cursor = db_connection.cursor()
    except Exception as ex:
        inventory_logger.info(
            message="New connection", stage='django_override_script')
        db_connection = connect(mysql_credential)
        cursor = db_connection.cursor()
    cursor.execute(query)
    myresult = cursor.fetchall()
    cursor.close()
    myresult_list = [i for i in myresult]
    return myresult_list, db_connection


# Returns response for Ingo-Connector and DMC-Connector APIS
def get_response(request_dict, mmt_hotel_list, pax_item, url):
    request_dict["paxItems"] = pax_item
    request_dict["hotelCodes"] = mmt_hotel_list
    response_dict = {}
    request_dump = json.dumps(request_dict)
    headers = {"cache-control": "no-cache", "content-type": "application/json"}
    response = requests.post(url, data=request_dump, headers=headers)
    if response.status_code == 200:
        response_dict = json.loads(response.content)
    else:
        print ("restart process, error occurred with status code: %d" % response.status_code)
    return response_dict


# Converts Dmc-Connector Product codes to Ingo Rateplan codes
def get_ingo_ratecodes_for_dmc(dmc_room_data, mmt_id):
    for idx in range(0, len(dmc_room_data)):
        dmc_room_data[idx]["rpcc"] = ""
        vendor_room_rate = dmc_room_data[idx]["ratePlanCode"].split("^^^")
        vendor_room = dmc_room_data[idx]["roomCode"]
        vendor_rate = vendor_room_rate[1]
        close_old_connections()
        ingo_hotel_id = HotelDetail.objects.filter(mmt_id=mmt_id).values("id")
        ingo_rp = RatePlan.objects.filter(roomtype__hotel_id=ingo_hotel_id[0]["id"],
                                          roomtype__source_roomtypecode=vendor_room,
                                          source_rateplancode=vendor_rate).values("rateplancode")
        if len(ingo_rp) > 0:
            dmc_room_data[idx]["rpcc"] = ingo_rp[0]["rateplancode"]
    return dmc_room_data


def get_unique_rateplancodes(room_data_list):
    unique_rateplan = {}
    for room in room_data_list:
        unique_rateplan[room["rpcc"]] = True
    return unique_rateplan


# Checks Phoenix DB data for provided rateplan code
def check_rateplan_code_is_active_and_updates_present(hotel_code, rateplan_code, vendor, idate):
    err_msg = ""
    close_old_connections()
    ingo_hotel = HotelDetail.objects.filter(hotelcode=hotel_code).values("pricing_model", "internal_model_codes")
    ingo_rp = RatePlan.objects.filter(rateplancode=rateplan_code).values("isactive", "roomtype_id", "pricing_model",
                                                                         "internal_model_codes")
    room_code = ""
    ingo_room = []
    if len(ingo_rp) < 0 or (len(ingo_rp) > 0 and ingo_rp[0]["isactive"] == 0):
        err_msg += "rateplan is not active, "

    if len(ingo_rp) > 0:
        close_old_connections()
        ingo_room = RoomDetail.objects.filter(id=ingo_rp[0]["roomtype_id"]).values("roomtypecode", "isactive",
                                                                                   "pricing_model",
                                                                                   "internal_model_codes")
        if len(ingo_room) > 0 and ingo_room[0]["isactive"] == 0:
            err_msg += "room is not active, "
        room_code = ingo_room[0]["roomtypecode"]
    else:
        err_msg += "rateplan not found"
        return

    hotel_internal_model_codes = ingo_hotel[0]["internal_model_codes"].split(",")
    rateplan_internal_model_codes = []
    if ingo_rp and ingo_rp[0].get("internal_model_codes", None):
        rateplan_internal_model_codes = ingo_rp[0]["internal_model_codes"].split(",")

    rate_db_connection = connect(mysql_rates_credential)
    if vendor == "synxis":
        query_rates = 'select id from ingommt_ari.hotels_hotelrates_basic_synxis_v2 where product_object_code="' + \
                      rateplan_code + '" and idate=%d;' % idate
        sql_response, rate_db_connection = query_execute(query_rates, rate_db_connection)
        if len(sql_response) == 0:
            err_msg += "rate update not present, "
        else:
            err_msg += "rate update present with id = %d ," % sql_response[0][0]
    else:

        if hotel_internal_model_codes[1] == "8100012" or (
                len(rateplan_internal_model_codes) > 0 and rateplan_internal_model_codes[1] == "8100012"):
            query_rates = 'select id from ingommt_ari.hotels_hotelrates_basic_derby where product_object_code="' + \
                          rateplan_code + '" and idate=%d;' % idate
            sql_response, rate_db_connection = query_execute(query_rates, rate_db_connection)
            if len(sql_response) == 0:
                err_msg += "rate update not present, "
            else:
                err_msg += "rate update present with id =%d " % sql_response[0][0]
        elif hotel_internal_model_codes[1] == "8100013" or (
                len(rateplan_internal_model_codes) > 0 and rateplan_internal_model_codes[1] == "8100013"):
            query_rates = 'select id from ingommt_ari.hotels_hotelrates_basic_los_derby where product_object_code="' + \
                          rateplan_code + '" and idate=%d;' % idate
            sql_response, rate_db_connection = query_execute(query_rates, rate_db_connection)
            if len(sql_response) == 0:
                err_msg += "rate update not present, "
            else:
                err_msg += "rate update present with id =%d " % sql_response[0][0]

    # Check if any restriction block is applied
    query_restriction = 'select block from ingommt_ari.hotels_restrictions where product_object_code="' + \
                        rateplan_code + '" and idate=%d;' % idate
    sql_response, rate_db_connection = query_execute(query_restriction, rate_db_connection)
    if len(sql_response) == 0:
        err_msg += "restriction update not present, "
    else:
        err_msg += "restriction block status %d, " % sql_response[0][0]

    if room_code != "":
        if vendor == "derby":
            # For Derby, inventory is stored at rateplan level
            room_code = rateplan_code
        if hotel_internal_model_codes[0] == "8100004" or (
                len(rateplan_internal_model_codes) > 0 and rateplan_internal_model_codes[0] == "8100004"):
            inv_db_connection = connect(mysql_inventory_credential)
            query_inventory = 'select available from ingommt_ari_inv.hotels_hotelinventory_basic where ' \
                              'product_object_code="' + room_code + '" and idate=%d;' % idate
            sql_response, rate_db_connection = query_execute(query_inventory, inv_db_connection)
            if len(sql_response) == 0:
                err_msg += "inventory update not present"
            else:
                err_msg += "inventory available status %d " % sql_response[0][0]
        elif hotel_internal_model_codes[0] == "8100011" or (
                len(rateplan_internal_model_codes) > 0 and rateplan_internal_model_codes[0] == "8100011"):
            inv_db_connection = connect(mysql_inventory_credential)
            query_inventory = 'select available from ingommt_ari_inv.hotels_hotelinventory_basic_los where ' \
                              'product_object_code="' + room_code + '" and idate=%d;' % idate
            sql_response, rate_db_connection = query_execute(query_inventory, inv_db_connection)
            if len(sql_response) == 0:
                err_msg += "inventory update not present"
            else:
                err_msg += "inventory available status %d " % sql_response[0][0]

    return err_msg


# Matches Prices are equal or differ by 1 or more for Ingo-Connector Response and DMC-Connector Response
def is_match_found(ingo_price, dc_price, is_match_found):
    if ingo_price != dc_price and abs(ingo_price - dc_price) > 1:
        if is_match_found is None:
            is_match_found = False
    elif ingo_price == dc_price or abs(ingo_price - dc_price) <= 1:
        is_match_found = True
    return is_match_found


# Matches Rate key for Ingo-Connector Response and DMC-Connector Response
def is_matching_ratekey_present(ingo_rate_key, dmc_connector_rate_key):
    if ingo_rate_key == "" and dmc_connector_rate_key == "":
        return True
    elif dmc_connector_rate_key == "":
        return False
    elif ingo_rate_key == "":
        return False
    ingo_rate_key_split = ingo_rate_key.split("#")
    dmc_connector_rate_key_split = dmc_connector_rate_key.split("#")
    if not "org#" in dmc_connector_rate_key:
        return False
    if len(ingo_rate_key_split) == len(dmc_connector_rate_key_split):
        for i in range(0, len(ingo_rate_key_split) - 1):
            if ingo_rate_key_split[i] != dmc_connector_rate_key_split[i]:
                return False
    return True


# Checks if Direct Connect Response and Ingo Response are same, if different, the it appends data in
# hotels_with_issue Map
def check_prices(dc_response, ingo_response, mmt_id, pax_idx, vendor, is_db_data_reqd, idate, hotels_with_issue,
                 hotels_without_issue):
    try:
        close_old_connections()
        hotel_data = HotelDetail.objects.filter(mmt_id=mmt_id).values("hotelcode")
        ingo_hotel = hotel_data[0]["hotelcode"]
        if dc_response.get("error", False) and ingo_response.get("error", False):
            success_dict = {"dmc_res": dc_response, 'ingo_res': ingo_response}
            if not hotels_without_issue.get(mmt_id, False):
                hotels_without_issue[mmt_id] = {pax_idx: []}
            elif not hotels_without_issue[mmt_id].get(pax_idx, False):
                hotels_without_issue[mmt_id][pax_idx] = []
            hotels_without_issue[mmt_id][pax_idx].append(success_dict)
            return

        if dc_response.get("error", False) and not ingo_response.get("error", False):
            inventory_logger.info(message="Hotel %s sellable for Ingo and Non Sellable for %s" % (mmt_id, vendor),
                                  stage='price_mismatch_script')
            error_dict = {"dmc_res": dc_response, 'ingo_extra_rps': []}
            if not hotels_with_issue.get(mmt_id, False):
                hotels_with_issue[mmt_id] = {pax_idx: []}
            elif not hotels_with_issue[mmt_id].get(pax_idx, False):
                hotels_with_issue[mmt_id][pax_idx] = []
            ingo_room_data = ingo_response["response"]["hotelData"][0]["roomData"]
            ingo_unique_rpc = get_unique_rateplancodes(ingo_room_data)
            for rateplancode in ingo_unique_rpc:
                err_msg = ""
                rp_obj  = RatePlan.objects.get(rateplancode=rateplancode)
                if rp_obj.source_config_id == 1 or rp_obj.source_config_id is None:
                    err_msg = "Ingo RatePlan"
                elif is_db_data_reqd:
                    err_msg = check_rateplan_code_is_active_and_updates_present(ingo_hotel, rateplancode, vendor, idate)
                    inventory_logger.info(
                        message="Phoenix DB data for Rateplan %s and error message: (%s)" % (rateplancode, err_msg),
                        stage='price_mismatch_script')
                rp_code_with_msg = {rateplancode: err_msg}
                error_dict['ingo_extra_rps'].append(rp_code_with_msg)

            hotels_with_issue[mmt_id][pax_idx].append(error_dict)

        elif ingo_response.get("error", False) and not dc_response.get("error", False):
            inventory_logger.info(message="Hotel %s sellable for %s and Non Sellable for Ingo" % (mmt_id, vendor),
                                  stage='price_mismatch_script')
            error_dict = {"ingo_res": ingo_response, 'dc_extra_rps': []}
            if not hotels_with_issue.get(mmt_id, False):
                hotels_with_issue[mmt_id] = {pax_idx: []}
            elif not hotels_with_issue[mmt_id].get(pax_idx, False):
                hotels_with_issue[mmt_id][pax_idx] = []
            if dc_response.get("response", False):
                dc_room_data = dc_response["response"]["hotelData"][0]["roomData"]
                dc_room_data = get_ingo_ratecodes_for_dmc(dc_room_data, mmt_id)
                dc_unique_rpc = get_unique_rateplancodes(dc_room_data)
                for rateplancode in dc_unique_rpc:
                    err_msg = ""
                    if is_db_data_reqd:
                        err_msg = check_rateplan_code_is_active_and_updates_present(ingo_hotel, rateplancode, vendor,
                                                                                    idate)
                        inventory_logger.info(
                            message="Phoenix DB data for Rateplan %s and error message: (%s)" % (rateplancode, err_msg),
                            stage='price_mismatch_script')
                    rp_code_with_msg = {rateplancode: err_msg}
                    error_dict['dc_extra_rps'].append(rp_code_with_msg)

            hotels_with_issue[mmt_id][pax_idx].append(error_dict)

        if dc_response.get("response", False) and ingo_response.get("response", False):
            inventory_logger.info(message="Hotel %s sellable for both %s and Ingo" % (mmt_id, vendor),
                                  stage='price_mismatch_script')
            dc_room_data = dc_response["response"]["hotelData"][0]["roomData"]
            ingo_room_data = ingo_response["response"]["hotelData"][0]["roomData"]

            dc_room_data = get_ingo_ratecodes_for_dmc(dc_room_data, mmt_id)

            dc_unique_rpc = get_unique_rateplancodes(dc_room_data)
            ingo_unique_rpc = get_unique_rateplancodes(ingo_room_data)

            dc_room_data_map = {}
            for dc_room in dc_room_data:
                dc_rpcc = dc_room["rpcc"]
                if not dc_room_data_map.get(dc_rpcc, False):
                    dc_room_data_map[dc_rpcc] = []
                dc_room_data_map[dc_rpcc].append(dc_room)

            for rateplancode in dc_unique_rpc:
                if not ingo_unique_rpc.get(rateplancode, False):
                    # check ratecode is active and both rates and restriction update is present in phoenix db
                    err_msg = ""
                    if is_db_data_reqd:
                        err_msg = check_rateplan_code_is_active_and_updates_present(ingo_hotel, rateplancode, vendor,
                                                                                    idate)
                        inventory_logger.info(
                            message="Phoenix DB data for Rateplan %s and error message: (%s)" % (rateplancode, err_msg),
                            stage='price_mismatch_script')
                    error_dict = {"dc_extra_rateplan": rateplancode, "error_message": err_msg}
                    if not hotels_with_issue.get(mmt_id, False):
                        hotels_with_issue[mmt_id] = {pax_idx: []}
                    elif not hotels_with_issue[mmt_id].get(pax_idx, False):
                        hotels_with_issue[mmt_id][pax_idx] = []

                    hotels_with_issue[mmt_id][pax_idx].append(error_dict)
            err_dict_list = {"ingo_extra_rateplan": []}
            for rateplancode in ingo_unique_rpc:
                if not dc_unique_rpc.get(rateplancode, False):
                    err_msg = ""
                    if is_db_data_reqd:
                        err_msg = check_rateplan_code_is_active_and_updates_present(ingo_hotel, rateplancode, vendor,
                                                                                    idate)
                        inventory_logger.info(
                            message="Phoenix DB data for Rateplan %s and error message: (%s)" % (rateplancode, err_msg),
                            stage='price_mismatch_script')
                    rp_code_with_msg = {rateplancode: err_msg}
                    err_dict_list["ingo_extra_rateplan"].append(rp_code_with_msg)

            if len(err_dict_list["ingo_extra_rateplan"]) > 0:
                if not hotels_with_issue.get(mmt_id, False):
                    hotels_with_issue[mmt_id] = {pax_idx: []}
                elif not hotels_with_issue[mmt_id].get(pax_idx, False):
                    hotels_with_issue[mmt_id][pax_idx] = []
                hotels_with_issue[mmt_id][pax_idx].append(err_dict_list)

            dmc_rateplan_segment_map = {}
            dc_extra_segment_nodes = []

            for ingo_room in ingo_room_data:
                is_match_found_no_tax = None
                is_match_found_with_tax = None
                ingo_sell_price_node = ingo_room["sellingPrice"]
                ingo_segment_id = ingo_room["segmentId"]
                ingo_rpcc = ingo_room["ratePlanCode"]
                ingo_rate_key = ingo_room["bookingMetaData"]["rateKey"]
                no_tax_ingo = 0
                tax_ingo = 0

                no_tax_dmc = 0
                tax_dmc = 0
                dc_nodes = []
                if not dc_room_data_map.get(ingo_rpcc, False):
                    continue

                for dc_room in dc_room_data_map[ingo_rpcc]:
                    dc_sell_price_node = dc_room["sellingPrice"]
                    dc_segment_id = dc_room["segmentId"]
                    dc_rpcc = dc_room["rpcc"]
                    dc_rate_key = ""
                    if dc_room.get("bookingMetaData", False) and dc_room["bookingMetaData"].get("rateKey", False):
                        dc_rate_key_split = dc_room["bookingMetaData"]["rateKey"].split("^^^")
                        dc_rate_key = dc_rate_key_split[len(dc_rate_key_split) - 1]

                    if (dc_segment_id == ingo_segment_id) or (
                            dc_segment_id == "G" and dmc_segment_id_map[dc_segment_id] == ingo_segment_id):
                        no_tax_ingo = int(float(ingo_sell_price_node["noTax"]))
                        tax_ingo = int(float(ingo_sell_price_node["tax"]))

                        no_tax_dmc = int(float(dc_sell_price_node["noTax"]))
                        tax_dmc = int(float(dc_sell_price_node["tax"]))

                        is_match_found_no_tax = is_match_found(no_tax_ingo, no_tax_dmc, is_match_found_no_tax)
                        is_match_found_with_tax = is_match_found(tax_ingo, tax_dmc, is_match_found_with_tax)

                        if not is_match_found_no_tax:
                            dc_data = {"no_tax_dmc_price": no_tax_dmc, "dmc_rate_key": dc_rate_key}
                            dc_nodes.append(dc_data)

                        if not is_match_found_with_tax:
                            dc_data = {"tax_dmc": tax_dmc, "dmc_rate_key": dc_rate_key}
                            dc_nodes.append(dc_data)

                        if is_match_found_no_tax and is_match_found_with_tax:
                            if not right_rateplans.get(mmt_id, False):
                                right_rateplans[mmt_id] = []
                            right_rateplans[mmt_id].append(ingo_rpcc)
                            if is_matching_ratekey_present(ingo_rate_key, dc_rate_key):
                                rateplan_segment_ratekey_str = ingo_rpcc + '_' + ingo_segment_id + '_' + dc_rate_key
                                dmc_rateplan_segment_map[rateplan_segment_ratekey_str] = True
                                success_dict = {
                                    "price_match": {"rateplancode": ingo_rpcc, "segment_id": ingo_segment_id,
                                                    "no_tax_dmc_price": no_tax_dmc, "no_tax_ingo_price": no_tax_ingo,
                                                    "with_tax_dmc_price": tax_dmc, "with_tax_ingo_price": tax_ingo,
                                                    "dmc_rate_key": dc_rate_key, "ingo_rate_key": ingo_rate_key}}
                                if not hotels_without_issue.get(mmt_id, False):
                                    hotels_without_issue[mmt_id] = {pax_idx: []}
                                elif not hotels_without_issue[mmt_id].get(pax_idx, False):
                                    hotels_without_issue[mmt_id][pax_idx] = []
                                hotels_without_issue[mmt_id][pax_idx].append(success_dict)

                if (not is_match_found_no_tax and is_match_found_no_tax is not None) or (not is_match_found_with_tax and
                                                                                         is_match_found_with_tax is not None):
                    error_dict_list = []
                    is_matching_ratekey_exist = False

                    for dc_detail in dc_nodes:
                        dc_rate_key = dc_detail["dmc_rate_key"]
                        rateplan_segment_ratekey_str = ingo_rpcc + '_' + ingo_segment_id + '_' + dc_rate_key
                        if dmc_rateplan_segment_map.get(rateplan_segment_ratekey_str, False):
                            continue
                        if is_matching_ratekey_present(ingo_rate_key, dc_rate_key):
                            is_matching_ratekey_exist = True
                            if dc_detail.get("no_tax_dmc_price", False):
                                error_dict = {
                                    "price_mismatch": {"rateplancode": ingo_rpcc, "segment_id": ingo_segment_id,
                                                       "no_tax_dmc_price": dc_detail["no_tax_dmc_price"],
                                                       "no_tax_ingo_price": no_tax_ingo,
                                                       "ingo_rate_key": ingo_rate_key, "dmc_rate_key": dc_rate_key}}
                                error_dict_list.append(error_dict)
                            if dc_detail.get("tax_dmc", False):
                                error_dict = {
                                    "price_mismatch": {"rateplancode": ingo_rpcc, "segment_id": ingo_segment_id,
                                                       "with_tax_dmc_price": dc_detail["tax_dmc"],
                                                       "with_tax_ingo_price": tax_ingo, "ingo_rate_key": ingo_rate_key,
                                                       "dmc_rate_key": dc_rate_key}}
                                error_dict_list.append(error_dict)

                    if not is_matching_ratekey_exist:
                        error_dict = {
                            "price_mismatch": {"rateplancode": ingo_rpcc, "segment_id": ingo_segment_id,
                                               "no_tax_ingo_price": no_tax_ingo,
                                               "ingo_rate_key_extra": ingo_rate_key,
                                               "rate_key_match": False}}
                        error_dict_list.append(error_dict)
                        for dc_detail in dc_nodes:
                            dc_rate_key = dc_detail["dmc_rate_key"]
                            rateplan_segment_ratekey_str = ingo_rpcc + '_' + ingo_segment_id + '_' + dc_rate_key
                            if dmc_rateplan_segment_map.get(rateplan_segment_ratekey_str, False):
                                continue
                            if dc_detail.get("no_tax_dmc_price", False):
                                error_dict = {
                                    "price_mismatch": {"rateplancode": ingo_rpcc, "segment_id": ingo_segment_id,
                                                       "no_tax_dmc_price": dc_detail["no_tax_dmc_price"],
                                                       "dmc_rate_key_extra": dc_rate_key,
                                                       "rate_key_match": False}}
                                dc_extra_segment_nodes.append(error_dict)
                                # error_dict_list.append(error_dict)
                            if dc_detail.get("tax_dmc", False):
                                error_dict = {
                                    "price_mismatch": {"rateplancode": ingo_rpcc, "segment_id": ingo_segment_id,
                                                       "with_tax_dmc_price": dc_detail["tax_dmc"],
                                                       "dmc_rate_key_extra": dc_rate_key, "rate_key_match": False}}
                                dc_extra_segment_nodes.append(error_dict)
                                # error_dict_list.append(error_dict)

                    if len(error_dict_list) > 0:
                        if not hotels_with_issue.get(mmt_id, False):
                            hotels_with_issue[mmt_id] = {pax_idx: []}
                        elif not hotels_with_issue[mmt_id].get(pax_idx, False):
                            hotels_with_issue[mmt_id][pax_idx] = []

                        hotels_with_issue[mmt_id][pax_idx].append(error_dict_list)

            for dc_exta in dc_extra_segment_nodes:
                ingo_rpcc = dc_exta["price_mismatch"]["rateplancode"]
                ingo_segment_id = dc_exta["price_mismatch"]["segment_id"]
                dc_rate_key = dc_exta["price_mismatch"]["dmc_rate_key_extra"]
                rateplan_segment_ratekey_str = ingo_rpcc + '_' + ingo_segment_id + '_' + dc_rate_key
                if dmc_rateplan_segment_map.get(rateplan_segment_ratekey_str, False):
                    continue
                if not hotels_with_issue.get(mmt_id, False):
                    hotels_with_issue[mmt_id] = {pax_idx: []}
                elif not hotels_with_issue[mmt_id].get(pax_idx, False):
                    hotels_with_issue[mmt_id][pax_idx] = []
                hotels_with_issue[mmt_id][pax_idx].append(dc_exta)
    except Exception as ex:
        inventory_logger.info(
            message="exception occurred in price mismatch script : %s" % (str(ex)),
            stage='price_mismatch_script')


# Call this function to start the price mismatch script
def compare_two_flows(ingo_hotel_list, mmt_hotel_list, checkin, checkout, idate, dc_url, ingo_url, vendor, hotels_with_issue,
                      hotels_without_issue,
                      is_phoenix_db_data_reqd=False, is_hotels_without_issue_data_reqd=False):
    inventory_logger.info(message="Price mismatch script started", stage='price_mismatch_script')
    dmc_req_file = open('scripts/direct_connect/request_files/dmc_request.json', )
    connector_req_file = open('scripts/direct_connect/request_files/connector_request.json', )
    pax_items_file = open('scripts/direct_connect/request_files/pax_items.json', )

    dmc_req = json.load(dmc_req_file)
    connector_req = json.load(connector_req_file)
    pax_items_json = json.load(pax_items_file)

    dmc_req["arrivalDate"] = checkin
    dmc_req["departureDate"] = checkout
    dmc_req["hotelCodes"] = mmt_hotel_list

    connector_req["arrivalDate"] = checkin
    connector_req["departureDate"] = checkout
    connector_req["hotelCodes"] = mmt_hotel_list

    pax_item_list = pax_items_json["paxItemsList"]

    pax_idx = 0
    if vendor.lower() == "derby":
        dc_url = "http://hotels-connector-suppliers.ecs.mmt/dmc-connector/services/v2/connector/HOTEL/DERBY_DOORWAY/SEARCH/PI01MULTIHOTELCR"
        dmc_req["vendorCode"] = "DERBY_DOORWAY"

    for pax_item in pax_item_list:
        for mmt_hotel in mmt_hotel_list:
            inventory_logger.info(message="Running hotel %s with pax item no %d" % (mmt_hotel, pax_idx),
                                  stage='price_mismatch_script')
            dc_response = get_response(dmc_req, [mmt_hotel], pax_item, dc_url)
            ingo_response = get_response(connector_req, [mmt_hotel], pax_item, ingo_url)
            check_prices(dc_response, ingo_response, mmt_hotel, pax_idx, vendor.lower(), is_phoenix_db_data_reqd, idate,
                         hotels_with_issue, hotels_without_issue)
            inventory_logger.info(message="Script completed for hotel %s with pax item no %d" % (mmt_hotel, pax_idx),
                                  stage='price_mismatch_script')
        pax_idx += 1
        json_dump_hotels_with_issue = json.dumps(hotels_with_issue)
        json_dump_hotels_without_issue = json.dumps(hotels_without_issue)
        print ("\n\n")
        print("hotels with issue \n")
        print(json_dump_hotels_with_issue)

        if is_hotels_without_issue_data_reqd:
            print("\n\n hotels without issue \n")
            print(json_dump_hotels_without_issue)
        print ("\n\n")

    # Moving the hotels back to content only after price comparison is completed
    move_to_content_only(ingo_hotel_list, vendor)

'''
def get_hotels_with_price_mismatch(hotels_with_issue):
    """
    TODO: Cases for failure:
    1. When ingo/derby rates are not found, handle such cases gracefully
    2. Check for different pax items if rate not found for previous pax item.
    3. Get List of hotels which have no rates from ingo & derby
    """
    is_error_occurred, is_rate_matched, is_price_available_from_both_vendor = False, False, False
    hotels_with_mismatch, hotels_with_updated_child_age, hotels_with_no_rates_from_both_vendors = [], [], []
    rateplan_code_list = get_price_mismatch_rateplans(hotels_with_issue)
    product_code_list = get_product_codes_from_rp_code(rateplan_code_list)
    for hotel_code, room_code, rp_code in product_code_list:
        close_old_connections()
        hotel_obj = HotelDetail.objects.get(hotelcode=hotel_code)
        max_child_age = hotel_obj.max_child_age
        for i in range(5):
            adult_child_ranges = [(1,0), (1,1), (2,0),(2,1),(3,0)]
            for x,y in adult_child_ranges:
                for j in [0, max_child_age]:
                    adult_count, child_count = x, y
                    child_ages = []
                    if y !=0:
                        child_ages = [j]

                    close_old_connections()
                    ingo_rates = get_ingo_rates_for_push_connectivity(rp_code, get_date_obj_after_x_days(30*i),
                                                                      get_date_obj_after_x_days(30*i+1), adult_count,
                                                                      child_count, child_ages, max_child_age, hotel_obj.pricing_model)
                    derby_rates = get_derby_rates(hotel_code, room_code, rp_code, get_date_obj_after_x_days(30*i).strftime("%Y-%m-%d"),
                                                  get_date_obj_after_x_days(30*i+1).strftime("%Y-%m-%d"), adult_count, child_count, child_ages)

                    if ingo_rates != 0 and derby_rates != 0:
                        is_price_available_from_both_vendor = True
                        if ingo_rates == derby_rates:
                            if max_child_age != j:
                                hotels_with_updated_child_age.append((hotel_code, j))
                            is_rate_matched = True
                        else:
                            print("Price Mismatch in Rate Plan {} : Ingo Rates : {} Derby Rates: {} Checkin: {} Checkout: {}".format(
                                str(rp_code), ingo_rates, derby_rates, str(get_date_obj_after_x_days(30 * i).strftime("%Y-%m-%d")),
                                str(get_date_obj_after_x_days(30 * i + 1).strftime("%Y-%m-%d"))))
                    elif ingo_rates == 0 and derby_rates == 0:
                        continue
                    else:
                        is_error_occurred = True
                        print("Price Mismatch in Rate Plan {} : Ingo Rates : {} Derby Rates: {} Checkin: {} Checkout: {}".format(
                              str(rp_code), ingo_rates, derby_rates, str(get_date_obj_after_x_days(30*i).strftime("%Y-%m-%d")),
                              str(get_date_obj_after_x_days(30*i+1).strftime("%Y-%m-%d"))))
                        break

                if is_error_occurred:
                    break

            if is_error_occurred:
                break
        if is_error_occurred or not is_rate_matched:
            hotels_with_mismatch.append(hotel_code)
        if not is_price_available_from_both_vendor:
            hotels_with_no_rates_from_both_vendors.append(hotel_code)
    return hotels_with_mismatch, hotels_with_no_rates_from_both_vendors, hotels_with_updated_child_age

def get_price_mismatch_rateplans(hotels_with_issue):
    rateplan_code_list = set()
    hotel_codes = list(hotels_with_issue.keys())
    for hotel_code in hotel_codes:
        try:
            for i in range(5):
                if len(rateplan_code_list) >0:
                    break
                price_mismatch_per_pax = hotels_with_issue[hotel_code]
                pax_key =  list(price_mismatch_per_pax.keys())[i]
                issues = price_mismatch_per_pax[pax_key]
                for issue in issues:
                    if type(issue) != type({}):
                        rateplan_code_list.add(str(issue[0]['price_mismatch']['rateplancode']))
        except Exception as e:
            print("Failed for %s with exception: %s", hotel_code, str(e))
    print("Final RatePlan Code List:" + str(rateplan_code_list))
    return list(rateplan_code_list)

def get_product_codes_from_rp_code(rp_code_list):
    db_connection = connect(mysql_inv_credential)

    rp_codes = ','.join(rp_code_list)
    query = 'select hotels_hoteldetail.hotelcode, hotels_roomdetail.roomtypecode, hotels_rateplan.rateplancode from ' \
            'hotels_hoteldetail join hotels_roomdetail on hotels_roomdetail.hotel_id=hotels_hoteldetail.id join ' \
            'hotels_rateplan on hotels_roomdetail.id=hotels_rateplan.roomtype_id where hotels_rateplan.rateplancode ' \
            'in (' + rp_codes + ');'

    cursor = db_connection.cursor()
    cursor.execute(query)
    rows = cursor.fetchall()

    product_codes = []
    for row in rows:
        product_codes.append((row[0],row[1], row[2]))
    cursor.close()
    print(product_codes)
    return product_codes

'''

def get_date_obj_after_x_days(days):
    return datetime.datetime.now() + timedelta(days=days)


