import xlrd
import time
from ingouser.models import User
from hotels.models.user_management import UserLdapIdMapping

def insert_data_from_excel(file_path):
    try:
        usernames = []
        username_ldapid_map = {}
        workbook = xlrd.open_workbook(file_path)
        sheet = workbook.sheet_by_index(0)

        for row in range(1, sheet.nrows):
            username = sheet.cell_value(row, 0)
            ldap_id = sheet.cell_value(row, 5)
            if username != 42 and ldap_id != 42:
                username_ldapid_map[username] = ldap_id
                usernames.append(username)

        # CHECKUSERCHANGE - DONE
        user_objs = User.objects.filter(username__in=usernames)

        data_to_insert = []
        for user in user_objs:

            obj = UserLdapIdMapping(user_id=user.id, ldap_id=username_ldapid_map[user.username])
            data_to_insert.append(obj)

            if len(data_to_insert) == 100:
                UserLdapIdMapping.objects.bulk_create(data_to_insert)
                time.sleep(5)
                data_to_insert = []

        UserLdapIdMapping.objects.bulk_create(data_to_insert)

        print('Data inserted successfully.')

    except Exception as e:
        print('An error occurred' )