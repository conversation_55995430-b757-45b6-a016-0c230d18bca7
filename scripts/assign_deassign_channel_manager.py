from pickletools import long1
from django.conf import settings
from hotels.models import HotelDetail, ChannelManager
from oauth2app.models import Client
from goibibo_inventory.settings import app
from utils.logger import Logger
from oauth2app.models import AccessToken, Client, Code
import datetime
import mysql.connector
from django.db import close_old_connections
from ingouser.models import User
import traceback

inventory_logger = Logger(logger = "inventoryLogger")

#mysql_credential = {"host": "ingoibibo.mysql.master.goibibo.com", "port": "3306", "user": "inventory",
        #            "password": "g0!b!b0in", "db": "goibibo_inventory"}
db_config = settings.DATABASES['default']
config = {
    'user': db_config['USER'],
    'password': db_config['PASSWORD'],
    'host': db_config['HOST'],
    'database': db_config['NAME'],
    'raise_on_warnings': True,
    'auth_plugin':'mysql_native_password'
}


def deassign_channel_manager(hotel_list):
    try:
        db_connection  =  connect(config)
        for hotel in hotel_list:
            update_query = "update hotels_hoteldetail set channel_manager_id = %s where hotelcode=%s"
            values = (None,hotel)
            cursor = db_connection.cursor()
            cursor.execute(update_query,values)
            cursor.close()
            db_connection.commit()

            client1 = Client.objects.filter(name=hotel)
            if client1:
                accesstoken1 = AccessToken.objects.get(client=client1)
                accesstoken1.delete()
                code1 = Code.objects.get(client=client1)
                code1.delete()
                client1.delete()

            inventory_logger.info(message = "successfully deassigned Channel Manager for hotel: {}".format(hotel), bucket = 'assign_deassign_channel_manager',
                        stage = 'scripts.assign_deassign_channel_manager')

        inventory_logger.info(message = "successfully deassigned Channel Manager for hotels_list: {}".format(hotel_list), bucket = 'assign_deassign_channel_manager',
                        stage = 'scripts.assign_deassign_channel_manager')

    except Exception  as e:
        inventory_logger.critical(message = "exception occured in deassign_channel_manager, error %s\t %s\t" %
                                (str(e), repr(traceback.format_exc())),
                        log_type = 'ingoibibo', bucket = 'assign_deassign_channel_manager',
                        stage = 'scripts.assign_deassign_channel_manager')

    

def deassign_particular_channel_manager(hotel_list,channel_manager_ID):
    try:
        db_connection  =  connect(config)
        for hotel_code in hotel_list:
            hotel_object = HotelDetail.objects.get(hotelcode=hotel_code)
            if hotel_object.channel_manager_id and hotel_object.channel_manager_id == channel_manager_ID:
                update_query = "update hotels_hoteldetail set  channel_manager_id = %s where hotelcode=%s"
                values = (None,hotel_code)
                cursor = db_connection.cursor()
                cursor.execute(update_query, values)
                cursor.close()
                db_connection.commit()

                client1 = Client.objects.filter(name=hotel_code)
                if client1:
                    accesstoken1 = AccessToken.objects.get(client=client1)
                    accesstoken1.delete()
                    code1 = Code.objects.get(client=client1)
                    code1.delete()
                    client1.delete()
                
                inventory_logger.info(message = "successfully deassigned Channel Manager:{} for hotel: {}".format(channel_manager_ID, hotel_code), bucket = 'assign_deassign_channel_manager',
                        stage = 'scripts.assign_deassign_channel_manager')

        inventory_logger.info(message = "successfully deassigned Channel Manager with id {} from the hotel_list {}".format(channel_manager_ID, hotel_list), bucket = 'assign_deassign_channel_manager',
                        stage = 'scripts.assign_deassign_channel_manager')
    
    except Exception  as e:
        inventory_logger.critical(message = "exception occured in deassign_perticular_channel_manager, error %s\t %s\t" %
                                (str(e), repr(traceback.format_exc())),
                        log_type = 'ingoibibo', bucket = 'assign_deassign_channel_manager',
                        stage = 'scripts.assign_deassign_channel_manager')
    

def assign_channel_manager(hotel_list,channel_manager_ID,userid):

    try:
        db_connection  =  connect(config)
        for hotel_code in hotel_list:
            update_query = "update hotels_hoteldetail set channel_manager_id = %s where hotelcode=%s"
            values = (channel_manager_ID,hotel_code)
            cursor = db_connection.cursor()
            cursor.execute(update_query, values)
            cursor.close()
            db_connection.commit()
            hotel_object = HotelDetail.objects.get(hotelcode=hotel_code)

    
            ch = ChannelManager.objects.get(id=channel_manager_ID)
            chm_name = ch.name

            user = User.objects.get(id=userid)

            key = ''
            secret = ''
            client1 = None
            code1 = None
            accesstoken1 = None
            codekey = ''
            at = ''
            creationdate = None
            status = ''
            deleteddate = None
            hotelname = ''
            username = ''

            if hotel_object:
                try:
                    client1 = Client.objects.filter(name=hotel_code).order_by('-id')
                    if client1:
                        client1 = client1[0]
                        if client1.description.lower() == chm_name.lower():
                            key= client1.key
                            secret= client1.secret
                            name= client1.name
                        else:
                            delete_accesstoken(user, client1)
                            delete_code(user, client1)
                            delete_client(user, hotel_code)
                
                    else:
                        client1 = Client.objects.create(user=user, name=hotel_code, description=chm_name)
                        client1.save()
                        key = client1.key
                        secret = client1.secret
                        name = client1.name
                except Exception as e:
                    pass
                try:
                    code1 = Code.objects.get(user=user, client=client1)
                    codekey = code1.key
                except Code.DoesNotExist:
                    code1 = Code.objects.create(user=user, client=client1)
                    code1.save()
                    codekey = code1.key
                try:
                    accesstoken1 = AccessToken.objects.get(user=user, client=client1)
                    at = accesstoken1.token
                    creationdate = datetime.datetime.fromtimestamp \
                        (float(accesstoken1.issue)).strftime("%B %d, %Y")
                    dt = datetime.datetime.fromtimestamp(float(accesstoken1.expire))

                    if accesstoken1.issue < accesstoken1.expire:
                        status = 'Active'
                    else:
                        status = 'Inactive'
                    if dt < datetime.datetime.today():
                        deleteddate = datetime.datetime.fromtimestamp \
                            (float(accesstoken1.expire)).strftime("%B %d, %Y")
                        status = 'Deleted'
                    else:
                        deleteddate = ''
                except AccessToken.DoesNotExist:
                    accesstoken1 = AccessToken.objects.create(user=user, client=client1)
                    accesstoken1.save()
                    at = accesstoken1.token
                    creationdate = datetime.datetime.fromtimestamp \
                        (float(accesstoken1.issue)).strftime("%B %d, %Y")
                    dt = datetime.datetime.fromtimestamp(float(accesstoken1.expire))

                    if dt > datetime.datetime.today():
                        status = 'Active'
                    else:
                        status = 'Inactive'
                try:
                    username = user.username
                except AttributeError:
                    username = ''
                returnDict = {
                    'createdby': username,
                    'success': True,
                    'key': key,
                    'hotelcode': str(hotel_code),
                    'hotelname': hotelname,
                    'chmname': chm_name,
                    'secret': secret,
                    'code': codekey,
                    'accesstoken': at,
                    'status': status,
                    'creationdate': creationdate,
                    'deleteddate': deleteddate
                }
                inventory_logger.info(message = "successfully deassigned Channel Manager for hotel: {}".format(hotel_code), bucket = 'assign_deassign_channel_manager',
                        stage = 'scripts.assign_deassign_channel_manager')

            else:
                inventory_logger.info(message = "successfully deassigned Channel Manager for hotel: {}".format(hotel_code), bucket = 'assign_deassign_channel_manager',
                        stage = 'scripts.assign_deassign_channel_manager')


        inventory_logger.info(message = "successfully assigned Channel Manager to the hotel_list {} ".format(hotel_list), bucket = 'assign_deassign_channel_manager',
                        stage = 'scripts.assign_deassign_channel_manager')

    except Exception  as e:
        inventory_logger.critical(message = "exception occured in assign_channel_manager, error %s\t %s\t" %
                                (str(e), repr(traceback.format_exc())),
                        log_type = 'ingoibibo', bucket = 'assign_deassign_channel_manager',
                        stage = 'scripts.assign_deassign_channel_manager')
            
    
def delete_accesstoken(user, client1):
    try:
        accesstoken1 = AccessToken.objects.filter(client=client1)
        for accesstoken in accesstoken1:
            accesstoken.delete()
            # accesstoken1 = AccessToken.objects.get(user=user,client=client1)
    except AccessToken.DoesNotExist:
        pass


def delete_code(user, client1):
    try:
        # code1 = Code.objects.get(user=user,client=client1)
        code1 = Code.objects.filter(client=client1)
        for code in code1:
            code.delete()
    except Code.DoesNotExist:
        pass


def delete_client(user, hotelcode):
    try:
        client1 = Client.objects.filter(name=hotelcode).order_by('-id')
        for client in client1:
            client.delete()
    except Client.DoesNotExist:
        pass

def connect(credential):
    try:
        close_old_connections()
        db_connection  =  mysql.connector.connect(**credential)
            
    except Exception as ex:
        inventory_logger.info(
            message = "Can't connect to mysql db, error: %s" % (str(ex)), stage = 'scripts.assign_deassign_channel_manager')
        return

    return db_connection


def query_execute(query , db_connection):
    try:
        cursor = db_connection.cursor()
    except Exception as ex:
        inventory_logger.info(
            message = "New connection", stage = 'scripts.assign_deassign_channel_manager')
        db_connection = connect(config)
        cursor = db_connection.cursor()
    cursor.execute(query)
    myresult = cursor.fetchall()
    cursor.close()
    myresult_list = [list(i) for i in myresult]
    return myresult_list, db_connection
