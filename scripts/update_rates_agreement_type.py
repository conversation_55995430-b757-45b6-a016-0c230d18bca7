import math
from hotels.models import RatePlan, RateBenefits


def update_rates_agreements_of_rateplans():
    rateplan_list = RatePlan.objects.all()
    for rateplan in rateplan_list:
        try:
            if rateplan.rates_agreement_type is None:
                if rateplan.sellcommission>=0:
                    rateplan.rates_agreement_type = 'sell_commission'
                else:
                    rateplan.rates_agreement_type = 'nett_markup'
                    rateplan.sellcommission = math.fabs(rateplan.sellcommission)
                #print rateplan.id, rateplan.rates_agreement_type, rateplan.sellcommission
                rateplan.save(hashChangeFunctionCall=False)
        except Exception, e:
            print str(e)


def update_rates_agreements_of_rate_benefits():
    rate_benefits_list = RateBenefits.objects.all()
    for rb in rate_benefits_list:
        try:
            if rb.rates_agreement_type is None:
                if rb.sellcommissionvalue>=0:
                    rb.rates_agreement_type = 'sell_commission'
                else:
                    rb.rates_agreement_type = 'nett_markup'
                    rb.sellcommissionvalue = math.fabs(rb.sellcommissionvalue)
                #print rb.id, rb.rates_agreement_type, rb.sellcommissionvalue
                rb.save(hashChangeFunctionCall=False)
        except Exception, e:
            print str(e)


def update_editable_only_from_admin():
    print "start"
    from hotels.methods import HotelMethods
    from hotels.models import RatePlan
    from django.db import close_old_connections
    from ingouser.models import User
    u = User.objects.get(id=1769)
    hm = HotelMethods()
    rateplan_list = RatePlan.objects.filter(editable_only_from_admin=True).only('editable_only_from_admin')
    print len(rateplan_list)
    for rateplan in rateplan_list:
        try:
            rateplan.editable_only_from_admin = False
            rateplan.save(update_fields=['editable_only_from_admin'])
            hm.updateLogMsg(u, rateplan, 'Editable flag True to False by Script')
        except Exception, e:
            close_old_connections()
    print "close"





#python run.py 'scripts.update_rates_agreement_type' 'update_rates_agreements_of_rateplans'
#python run.py 'scripts.update_rates_agreement_type' 'update_rates_agreements_of_rate_benefits'
