import csv, json, datetime

from common.models import User
from hotels.hotelchoice import ADJUSTMENT_STATUS_DICT
from hotels.methods import HotelMethods
from hotels.models import AdjustmentEntry, HotelOutwardPayment,\
    HotelPaymentLink, HotelDetail

hm = HotelMethods()

def run(filename):
    f = open(filename, 'r')
    reader = csv.reader(f)
    user = User.objects.get(username='sankalp.jain')
    for line in reader:
        try:
            hotel = HotelDetail.objects.get(hotelcode=line[1])
            adjst_obj = AdjustmentEntry.objects.get(adjustmentid=line[0])
            adjst_obj.paymentstatus = 'processed'
            adjst_obj.save()
            pdetail = json.dumps({'adjustment': [line[0]]})
            pdate = datetime.datetime.strptime(line[10], '%d/%m/%Y')
            accounts_list = hotel.accounts.filter(isactive=True)
            gbp = HotelOutwardPayment(**{
                'bankreference': line[9],
                'hotelname': hotel.hotelname,
                'hotel': hotel.hotelcode,
                'amount': line[7], 'paymentstatus': 'processed', 'paymode': 'NEFT',
                'paymentdetail': pdetail, 'paymentdate': pdate, 'user': user
            })
            if accounts_list:
                account = accounts_list[0]
                gbp.accno = account.accno
                gbp.ifsc = account.ifsc
                gbp.accname = account.accname
                gbp.bankcode = account.bankcode
                gbp.bankname = account.bankname
                gbp.branchcode = account.branchcode
                gbp.branchname = account.branchname
            gbp.save()
            gbp.paymentid = hm.update_code(gbp.id, 12, pref='GBP')
            gbp.save()
            hplink = HotelPaymentLink(payment=gbp, content_object=adjst_obj,
                                      amount=line[7])

            hplink.save()
            payment_action = ADJUSTMENT_STATUS_DICT[adjst_obj.adjustmentstatus][1]
            msg = ('initiated adjustment entry of Rs. %s for this %s adjustment '
                   'with payment action %s of adjustment id %s' %
                   (hplink.amount, adjst_obj.adjustmenttype, payment_action,
                    adjst_obj.adjustmentid))
            cmsg = ('initiated adjustment entry of Rs. %s with gbpid %s' %
                   (hplink.amount, gbp.paymentid))
            hm.updateLogMsg(user, gbp, msg)
            hm.updateLogMsg(user, adjst_obj, cmsg)
        except Exception as e:
            print(line)
            print(e)
