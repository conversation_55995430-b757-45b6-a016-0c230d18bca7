syntax = "proto3" ;
package getlmrconfig;
import "google/protobuf/wrappers.proto";

option go_package = "./;getlmrconfig";

service GetLMRService {
  rpc GetLMR (GetLMRRequest) returns (GetLMRResponse) {}
}

message GetLMRRequest {
  string correlationKey = 1;
  string ingoHotelId = 2;
  string lmrCode = 3;
  bool entityMappingRequired = 4;
}

message LmrConfig {
  google.protobuf.StringValue startTime = 1;
  google.protobuf.StringValue endTime = 2;
  google.protobuf.StringValue chargeType = 3;
  google.protobuf.DoubleValue chargeValue = 4;
  google.protobuf.BoolValue isActive = 5;
  google.protobuf.StringValue stayDateBlackouts = 6;
}

message ActiveEntity {
  string level = 1;
  repeated string entityList = 2;
}


message ResponseDetails {
  string ingoHotelId = 1;
  string lmrCode = 2;
  LmrConfig lmrConfig = 3;
  repeated ActiveEntity activeEntities = 4;
}

message GetLMRResponse {
  string correlationKey = 1;
  bool success = 2;
  string message = 3;
  repeated Error error = 4;
  ResponseDetails data = 5;
}


message Error {
  string code = 1;
  string message = 2;
}