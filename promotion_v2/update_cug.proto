syntax = "proto3" ;
package create_offer;
import "google/protobuf/wrappers.proto";

option go_package = "./;create_offer";

service UpdateCugService {
  rpc UpdateCug (UpdateCugRequest) returns (UpdateCugResponse) {}
}
message UpdateCugRequest {
  string mmtId = 1;
  string correlationKey = 2;
  repeated CugRequestData offerData = 3;
}
message CugRequestData {
  google.protobuf.StringValue checkinDateStart = 1;
  google.protobuf.StringValue  checkoutDateEnd = 2;
  google.protobuf.StringValue bookingDateStart = 3;
  google.protobuf.StringValue bookingDateEnd = 4;
  google.protobuf.StringValue offerCode = 5;
  google.protobuf.Int32Value earlyBirdMin = 6;
  google.protobuf.Int32Value earlyBirdMax = 7;
  repeated string checkinBlackoutDates = 8;
  repeated CugValues offerValueList = 9;
}


message CugValues {
  google.protobuf.BoolValue isActive = 1;
  google.protobuf.FloatValue offerValue = 2;
  string segment = 3;
}
message UpdateCugResponse {
  bool success = 1;
  string mmtId = 2;
  string correlationKey = 3;
  string message = 4;
  string errorCode = 5;
  repeated UpdateCugResponseData data = 6;
}
message UpdateCugResponseData {
  bool success = 1;
  string errorCode = 2;
  string message = 3;
  CugData offerData = 4;
}
message CugData {
  string offerCode = 1;
  string offerName = 2;
  string bookingDateStart = 3;
  string bookingDateEnd = 4;
  string checkinDateStart = 5;
  string checkoutDateEnd = 6;
  string checkinBlackoutDates = 7;
  repeated CugValues offerValueList = 8;
}