import uuid
import re

import boto3
from django.conf import settings
from django.core.exceptions import ValidationError

from utils.logger import Logger
from lib.redis_cache_backend import RedisCache

import datetime

import json
from bulk_uploader.constants import DATE_OCCOURS_IN_THE_PAST, NOT_TRUE_FALSE, END_DATE_BEFORE_CURRENT_DATE, CHECKOUT_GREATER_THAN_CHECKIN, \
    NOT_A_VALID_WEEKDAY_LIST, INVALID_OFFER_TYPE, INVALID_DATE_FORMAT, INVALID_RATEPLAN_ROOMCODE_FORMAT, CREATING_NEW_HOTEL_OFFER, \
    NOT_A_VALID_STRING, NOT_A_VALID_INTEGER, NOT_A_VALID_FLOAT, HOTEL_ALREADY_EXISTS, VALID_TRUTHY, NOT_A_VALID_TRUTHY, INVALID_CONDITION, \
    LAST_MINUTE_DAYS_INVALID, EARLY_BIRD_DAYS_INVALID, FREE_NIGHTS_INVALID, INVALID_MIN_DAY_COUNT, INVALID_MAX_DAY_COUNT, INVALID_DAY_PAIR,\
    VALID_DAY_PAIR, VALID_BOOKING_WINDOW_TYPE, INVALID_BOOKING_WINDOW_TYPE ,COMMA_SEPARATOR,COLON_SEPARATOR,HYPHEN_SEPARATOR,UNDERSCORE_SEPARATOR,HASH_SEPARATOR,AT_SEAPARATOR,\
    ROOM_UPGRADE_LEAF_CATEGORY_ID,MEAL_UPGRADE_LEAF_CATEGORY_ID,SPA_BENEFIT_LEAF_CATEGORY_ID,FNB_BENEFIT_LEAF_CATEGORY_ID,HOTEL_CREDIT_BENEFIT_LEAF_CATEGORY_ID,PLATINUM_ONLY,GOLD_AND_PLATINUM_BOTH,\
    INVALID_SRP_OFFER, INVALID_SEGMENT_FIELD, PROMO_GENIE_LMR_NOT_FOUND_ERROR_CODE, LMR_LEVEL_RATEPLAN, LMR_LEVEL_ROOM, \
    LMR_LEVEL_HOTEL
from hotels.models import RatePlan   
from hotels.models.hotel_flag_configuration import RATE_PLAN_FLAG_DICT,DAYUSE_ROOM_INFO_AVAILABLE_SLOT_FLAG
from common.constants import BLACK_MIN_DIFFERENCE_ALLOWED, ContentTypeIdForHotel
from scripts.update_gst_template import update
from reservation_service.cancellation_policy.constants import CP_TEMPLATE_ID,CP_POLICY_RULE_TO_TEMPLATE,CUSTOM_TEMPLATE,CP_POLICY_TYPE

SEARCH_CACHE = settings.SEARCH_CACHE
DEFAULT_CACHE = settings.CACHES['default']
REDIS_SERVER = DEFAULT_CACHE['LOCATION']
REDIS_OPTIONS = SEARCH_CACHE['OPTIONS']

logger = Logger(logger="inventoryLogger")


def upload_to_s3(file_name, extn, prepend_name=None, key_name=None):
    """
    Uploads File to a S3 Bucket.
    :param data: The file contents to be uploaded.
    :param file_name: The name of the csv file to be uploaded.
    :param prepend_name: The prefix to be added in s3_file_name(optional).
    :return: returns the s3 url path.
    """
    date = datetime.datetime.now().strftime('%Y-%m-%d')
    bucket = settings.STAGING_BUCKET_KEY
    if settings.HOST in settings.PROD_HOSTS:
        bucket = settings.PROD_BUCKET_KEY
    if not key_name:
        key = 'bulk-uploader/'
    else:
        key = key_name

    if prepend_name:
        key += prepend_name + '-'
    key += '%s_%s_%s.%s' % (date, file_name.rsplit('/', 1)[-1].rsplit('.', 1)[0],
                                                uuid.uuid1().urn[9:], extn)

    try:
        session = boto3.session.Session(region_name='ap-south-1')
        if settings.HOST in settings.PROD_HOSTS:
            s3client = session.client('s3', config=boto3.session.Config(signature_version='s3v4'))
        else:
            s3client = session.client('s3', config=boto3.session.Config(signature_version='s3v4'),
                                  aws_access_key_id=settings.AWS_BUCKETS_CONFIG[bucket]['access_key_id'],
                                  aws_secret_access_key=settings.AWS_BUCKETS_CONFIG[bucket]['secret_access_key'])
        s3client.upload_file(file_name, bucket, key, ExtraArgs={'ACL': 'public-read'})
        location = s3client.get_bucket_location(Bucket=bucket)['LocationConstraint']
        url = "https://s3-%s.amazonaws.com/%s/%s" % (location, bucket, key)
    except Exception, e:
        logger.critical(message='Error Uploading File to s3. Message: %s' % str(e),
                        log_type='Ingoibibo', bucket='bulk_uploader', stage='upload_to_s3')
        raise Exception(str(e))

    return url


def send_email(data, subject, to_email):
    from communication.common_comms.communications import sendMail
    sendMail(to_email, from_email=settings.EMAIL_ALERT_SENDER, body=data, subject=subject, template_id='',
             cc_emails=None, attached_file=None, bcc_emails=[])


def get_config(handle):
    from .config import BULK_UPLOADER_CONFIG
    try:
        return [x for x in BULK_UPLOADER_CONFIG if x['handle'] == handle][0]
    except:
        return None


redis_server = None


def get_redis_conn():
    global redis_server
    if redis_server:
        return redis_server
    try:
        redis_server = RedisCache(server=REDIS_SERVER, params={'OPTIONS': REDIS_OPTIONS})
    except Exception:
        redis_server = None
        logger.critical(message='redis connection failure', log_type='Ingoibibo', bucket='redis',
                        stage='get_redis_conn')
    return redis_server


def convert_truthy(truthy_string):
    if truthy_string.lower() in ['true', '1', 't', 'y', 'yes']:
        return True
    elif truthy_string.lower() in ['false', '0', 'f', 'n', 'no', '']:
        return False
    else:
        raise ValueError(NOT_TRUE_FALSE)

def validate_date(entered_date):
    now = datetime.datetime.now().strftime('%Y-%m-%d')
    cur_date = datetime.datetime.strptime(now, '%Y-%m-%d')

    valid = True
    reason = "valid"
    if cur_date > entered_date:
        valid = False
        reason = DATE_OCCOURS_IN_THE_PAST
        raise ValueError(DATE_OCCOURS_IN_THE_PAST)
    return (valid, reason)

def validate_date_pair(start_date, end_date):
    valid = True
    reason = "valid"
    # start_validity, start_reason = validate_date(start_date)
    end_validity, end_reason = validate_date(end_date)
    if end_validity:
        if start_date > end_date:
            valid = False
            reason = CHECKOUT_GREATER_THAN_CHECKIN
    else:
        valid = False
        reason = END_DATE_BEFORE_CURRENT_DATE
    return (valid, reason)


def validate_week_days(weekday_list):
    full_weekday_list = [0,1,2,3,4,5,6]
    final_set = set()
    new_weekday_list = []
    try:
        for weekday in weekday_list:
            new_weekday_list.append(int(weekday))
    except ValueError:
        raise Exception("WeekDays should be int or should not be empty")
    
    for weekday in new_weekday_list:
        if weekday in full_weekday_list:
            final_set.add(str(weekday))
        else:
            raise ValueError(NOT_A_VALID_WEEKDAY_LIST)
    
    return list(final_set)

def check_days_to_book_in_advance(no_of_days):
    days_int = int(no_of_days)
    if days_int <= 0:
        return False, EARLY_BIRD_DAYS_INVALID
    else:
        return True, 'valid'

def check_last_minute_booking_days_before(no_of_days_before):
    days_int = int(no_of_days_before)
    if days_int <= 0:
        return False, LAST_MINUTE_DAYS_INVALID
    else:
        return True, 'valid'

def check_free_nights(days_int):
    if  days_int < 0:
        return False, FREE_NIGHTS_INVALID
    else:
        return True, 'valid'

def check_day_pair(day_min,day_max):
    if int(day_min) < 0:
        return False, INVALID_MIN_DAY_COUNT
    elif int(day_max) < 0:
        return False, INVALID_MAX_DAY_COUNT
    elif day_min > day_max:
        return False, INVALID_DAY_PAIR
    else:
        return True, VALID_DAY_PAIR

def convert_int(str_value, default_value=0):
    try:
        int_value = int(str_value)
    except ValueError:
       int_value = default_value
    return int_value

def convert_offer_type_string(offer_type_string):
    if offer_type_string.lower() == "percentage":
        offer_type = "percentage" 
    elif offer_type_string.lower() == "fixed":
        offer_type = "flatdprpn"
    else:
        raise ValueError(INVALID_OFFER_TYPE)
    return offer_type

def check_booking_window_type(booking_window_type_string):
    if booking_window_type_string.lower() in ['lastminute','earlybird','']:
        return True, VALID_BOOKING_WINDOW_TYPE    
    return False, INVALID_BOOKING_WINDOW_TYPE

def convert_offer_condition(offer_condition_string):
    if offer_condition_string.lower() == 'any':
        offer_condition = 'any-x-nights'
    elif offer_condition_string.lower() == 'cheapest':
        offer_condition = 'cheapest-x-nights'
    elif offer_condition_string.lower() == 'last':
        offer_condition = 'last-x-nights'
    else:
        raise ValueError(INVALID_CONDITION)
    return offer_condition


def parse_applicable_for(parse_applicable_for_string):
    applicable_list = []
    parse_applicable_for_string_lower = parse_applicable_for_string.lower()
    if 'stay' in  parse_applicable_for_string_lower:
        applicable_list.append('stay')
    if 'booking' in  parse_applicable_for_string_lower:
        applicable_list.append('booking')
    return applicable_list

def parse_date(date_string, start_date=False, end_date=False):
    if start_date and date_string=='':
        datetime_obj = datetime.datetime.now()
        return datetime_obj
    if end_date and date_string=='':
        datetime_obj = datetime.datetime(2099, 1, 1)
        return datetime_obj
    try:
        datetime_obj = datetime.datetime.strptime(date_string, '%Y-%m-%d')
    except ValueError:
        # this is raised if the datetime parsing fails
        raise ValueError(INVALID_DATE_FORMAT)
    return datetime_obj

def build_offer_value(offer_type, discount_value, segment):
    # todo  make this modifiable for day based promotion as well
    try:
        offer_value = float(discount_value)
    except ValueError:
        raise Exception("Offer value should be a number")

    return {
        "channel": "all",
        "isactive": 1,
        "offer_basis": "discount",
        "offer_type": offer_type,
        "offer_value": offer_value,
        "segment": segment
    }

def parse_roomcode_rateplan(roomcode_json):
    # todo check for just the keys
    try:
        json_dict = json.loads(roomcode_json)
        roomcode_list = json_dict.get('room', [])
        rateplan_list = json_dict.get('rateplan', [])
        hotel_list = json_dict.get('hotel', [])
    except Exception as e:
        raise Exception(INVALID_RATEPLAN_ROOMCODE_FORMAT)
    rateplan_roomcode_dict = {
        'room': roomcode_list,
        'rateplan': rateplan_list,
        'hotel': hotel_list
    }
    return rateplan_roomcode_dict

def create_related_list(rateplan_json_dict, offer_data):
    from api.v1.offers.views.hotel_offer import  get_pah_linked_rateplan
    from hotels.models import RatePlan
    related_list = []
    if 'hotel' in rateplan_json_dict.keys() and len(rateplan_json_dict['hotel']) != 0:
        hotel_id = rateplan_json_dict['hotel']
        related_list.append({'relatedto': 'hotel', 'relatedcode': hotel_id})
        return related_list
    rateplan_related_list = rateplan_json_dict.get('rateplan', [])
    for rateplan_id in rateplan_related_list:
        if offer_data.get('pah_applicable') and not offer_data.get('applicable_for_all_rooms_rateplans'):
            rateplan = RatePlan.objects.only('id', 'pay_at_hotel').get(rateplancode=rateplan_id)
            if rateplan.pay_at_hotel == 0:
                linked_rateplan = get_pah_linked_rateplan(rateplan)
                if linked_rateplan:
                    related_list.append({'relatedto': 'Rateplan', 'relatedcode': linked_rateplan.rateplancode,
                                        'relatedname': linked_rateplan.rateplanname})
        else:
            related_list.append({'relatedto': 'Rateplan', 'relatedcode': rateplan_id})
    room_related_list = rateplan_json_dict.get('room', [])
    for room_id in room_related_list:
        related_list.append({'relatedto': 'Room', 'relatedcode': room_id})

    return related_list

def convert_offer_code(offer_code):
    if offer_code == '':
        return None
    else:
        return offer_code

def check_valid_offer_code(offer_code):
    from hotels.models import HotelOfferCondition
    if offer_code == '':
        return True, CREATING_NEW_HOTEL_OFFER
    else:
        try:
            promotion_obj = HotelOfferCondition.objects.get(offercode=offer_code)
        except Exception as e:
            return False, str(e)
        return True,HOTEL_ALREADY_EXISTS

def check_valid_srp_offer(show_nr_offer_on_funnel_srp_value, offer_non_refundable_value):
    offer_non_refundable = convert_truthy(offer_non_refundable_value)
    try:
        show_nr_offer_on_funnel_srp = convert_truthy(show_nr_offer_on_funnel_srp_value)
    except ValueError:
        return False, NOT_A_VALID_TRUTHY
    
    if not offer_non_refundable and not show_nr_offer_on_funnel_srp:
        return False, INVALID_SRP_OFFER
    return True, "valid"

def check_valid_segment(promotion_applied_for_b2c_bundled):
    promotion_applied_for_b2c_bundled_validity, promotion_applied_for_b2c_bundled_reason = check_string(promotion_applied_for_b2c_bundled)
    if not promotion_applied_for_b2c_bundled_validity:
        return promotion_applied_for_b2c_bundled_validity, promotion_applied_for_b2c_bundled_reason
    
    if promotion_applied_for_b2c_bundled.lower() not in ['b2c', 'bundled']:
        return False, INVALID_SEGMENT_FIELD
    return True, "valid"

def check_valid_hotel_code(hotel_code):
    from hotels.models import HotelDetail
    try:
        hotel_detail_obj = HotelDetail.objects.get(hotelcode=hotel_code)
        if hotel_detail_obj:
            return True, 'hotel exists'
    except Exception as e:
        return False, str(e)

def check_string(value_string):
    if isinstance(value_string, str):
        return True, 'valid string'
    else:
        return False, NOT_A_VALID_STRING 

def check_int(value_int):
    try:
        int(value_int)
    except:
        return False, NOT_A_VALID_INTEGER
    return True, 'valid integer'

def check_float(value_float):
    try:
        float(value_float)
    except:
        return False, NOT_A_VALID_FLOAT
    return True, 'valid float'

def check_bool(truthy_string):
    if truthy_string.lower() in ['true', '1', 't', 'y', 'yes'] or truthy_string.lower() in ['false', '0', 'f', 'n', 'no']:
        return True,  VALID_TRUTHY
    else:
        return False, NOT_A_VALID_TRUTHY 

def check_rateplan_json(rateplan_json_string):
    from api.v1.offers.views.hotel_offer import get_content_object, create_offer
    rateplan_validity, message = False, ''
    try:
        rateplan_json_dict = parse_roomcode_rateplan(rateplan_json_string)
        related_list = create_related_list(rateplan_json_dict, {})
        for related_to_obj in related_list:
            related_to = related_to_obj.get('relatedto')
            related_code_list = related_to_obj.get('relatedcode')
            for related_code in related_code_list:
                content_object = get_content_object(related_to, related_code)
                if content_object['object_id'] == None and content_object['content_type'] == None:
                    message += 'content type object does not exist related_to: {} related_code: {}'.format(related_to, related_code)
        valid = True
        message = 'rooms/rateplan/hotel content types are valid'
    except Exception as e:
        return False, str(e)
    return valid, message


def validate_checkin_checkout_time(check_in_time, check_out_time):
    if not check_in_time:
        return "Check-in time cannot be empty.", False
    if not check_out_time:
        return "Check-out time cannot be empty.", False
    
    try:
        check_in_obj = datetime.datetime.strptime(check_in_time, '%H:%M').time()
        check_out_obj = datetime.datetime.strptime(check_out_time, '%H:%M').time()
        
        if check_in_obj >= datetime.time(5, 0):
            if check_in_obj < check_out_obj:
                return "Valid input.", True
            else:
                return "Check-in time should be before check-out time.", False
        else:
            return "Check-in time should be after 5:00 AM", False
    except ValueError:
        return "Invalid time format. Please use HH:MM format.", False
    
def validate_available_slots(available_slots):
    if not available_slots:
        return "Available slots cannot be empty", False
    valid_available_slots_values = ['3', '6', '9']
    values = available_slots.split(',')

    for value in values:
        value = value.strip()  
        if value not in valid_available_slots_values:
            return 'Invalid avaliable slot values',False
    return 'Valid inputs.',True

def compute_avaliable_slot_duration_values(available_slots):
    input_values = available_slots.split(',')
    available_slots_duration = 0
    
    for value in input_values:
        value = value.strip()
        if value in DAYUSE_ROOM_INFO_AVAILABLE_SLOT_FLAG:
            available_slots_duration |= DAYUSE_ROOM_INFO_AVAILABLE_SLOT_FLAG[value]

    return available_slots_duration

def validate_hourly_dayuse_input_data(hourly_dayuse_rateplancode,check_in_time,check_out_time,available_slots,room_obj):
    try:
        if not hourly_dayuse_rateplancode:
            return  "Hourly Dayuse RatePlanCode is not provided",False
        is_valid_rateplancode = RatePlan.objects.filter(rateplancode=hourly_dayuse_rateplancode,roomtype_id=room_obj.id)
        if not is_valid_rateplancode:
            return  "Invalid hourly_dayuse_rateplancode provided.",False
        
        msg,is_valid_time_range=validate_checkin_checkout_time(check_in_time,check_out_time)
        if not is_valid_time_range:
            return msg,is_valid_time_range
        
        msg,is_valid_available_slots=validate_available_slots(available_slots)
        if not is_valid_available_slots:
            return msg,is_valid_available_slots
        
        return 'Valid inputs',True
    except Exception as e:
            return str(e),False
        
def update_dayuseroominfo_fields(update_dayuseroominfo_obj,check_in_time,check_out_time,available_slots=None):
    if update_dayuseroominfo_obj:
        check_in_time='{}:00'.format(check_in_time)
        check_out_time='{}:00'.format(check_out_time)
        update_dayuseroominfo_obj.checkin_start=check_in_time
        update_dayuseroominfo_obj.checkout_end=check_out_time
        if available_slots:
            computed_available_slot=compute_avaliable_slot_duration_values(available_slots)
            update_dayuseroominfo_obj.available_slots=computed_available_slot
        update_dayuseroominfo_obj.save(inventory_update_required=False)


def activate_hourly_dayuse_rateplan(hourly_dayuse_rateplancode):
        hourly_dayuse_rateplan_obj = RatePlan.objects.get(rateplancode=hourly_dayuse_rateplancode)
        if hourly_dayuse_rateplan_obj:
            hourly_dayuse_rateplan_obj.is_hourly_dayuse_rateplan=True
            hourly_dayuse_rateplan_obj.save()
    
def deactivate_hourly_dayuse_rateplan(room_id):
        hourly_dayuse_rateplan = RatePlan.objects.filter(roomtype_id =room_id,flag_bits_1__truth=RATE_PLAN_FLAG_DICT['is_hourly_dayuse_rateplan'])
        if hourly_dayuse_rateplan:
            hourly_dayuse_rateplan_obj=hourly_dayuse_rateplan[0]
            hourly_dayuse_rateplan_obj.is_hourly_dayuse_rateplan=False
            hourly_dayuse_rateplan_obj.save()         
            
            
def validate_room__meal_upgrade_fields(applicable_roomupgrade,roomupgrade_configs,applicable_mealupgrade,mealupgrade_configs,mealupgrade_room,allowed_template_id_dict={},is_dnd=False):
    # if both applicable_roomupgrade and applicable_mealupgrade is empty

    if not(applicable_roomupgrade or applicable_mealupgrade):
        if  (not applicable_roomupgrade and  roomupgrade_configs) or (not applicable_mealupgrade and  mealupgrade_configs):
            return False,"roomupgrade_configs/mealupgrade_configs cannot be given if applicable_roomupgrade and applicable_mealupgrade is empty"
        if not applicable_mealupgrade and mealupgrade_room:
            return False,"mealupgrade_room can not be given if applicable_roomupgrade and applicable_mealupgrade is empty"
        return True,"Valid value"
    
    # if any one of applicable_roomupgrade or applicable_mealupgrade is given 
    elif not applicable_roomupgrade or not applicable_mealupgrade:
        if (applicable_roomupgrade and applicable_roomupgrade not in [PLATINUM_ONLY,GOLD_AND_PLATINUM_BOTH]) or (applicable_mealupgrade and applicable_mealupgrade not in [PLATINUM_ONLY,GOLD_AND_PLATINUM_BOTH]):
            return False,"Invalid input for applicable_roomupgrade/applicable_mealupgrade it should have values in [1,2] ,1--> platinum ,2-->gold and platinum both"
        if not applicable_roomupgrade and roomupgrade_configs:
            return False,"roomupgrade_configs  can not be given if applicable_roomupgrade and applicable_mealupgrade is empty"
        if not mealupgrade_configs and mealupgrade_configs:
            return False,"mealupgrade_configs can not be given if applicable_roomupgrade and applicable_mealupgrade is empty"
        
        if applicable_roomupgrade and roomupgrade_configs:
            valid_room_config,mssg=validate_room_upgrade_config_field(applicable_roomupgrade,roomupgrade_configs)
            if not valid_room_config:
                return valid_room_config,mssg
            
        if applicable_mealupgrade and mealupgrade_configs :
            valid_meal_config,mssg=validate_meal_upgrade_config_field(applicable_mealupgrade,mealupgrade_configs,mealupgrade_room)
            if not valid_meal_config:
                return valid_meal_config,mssg
        
        if not applicable_mealupgrade and mealupgrade_room:
            return False,"mealupgrade_room can not be given if applicable_mealupgrade is empty"
        
    # both room and meal upgrade is given
    else:

        if applicable_roomupgrade and applicable_roomupgrade.lower() == "na" and applicable_mealupgrade and applicable_mealupgrade.lower() == "na":
            return True,"Valid value"
        
        if applicable_roomupgrade and applicable_roomupgrade.lower() != "na":
            if  applicable_roomupgrade not in ["1","2"]:
                return False,"Invalid input for applicable_roomupgrade "
            if not roomupgrade_configs:
                return  False,"roomupgrade_configs cannot be empty"
            if not allowed_template_id_dict.get(ROOM_UPGRADE_LEAF_CATEGORY_ID):
                return False,"Room upgrade leaf category is not allowed for this hotel"
                
            try:
                roomupgrade_config_list = roomupgrade_configs.split(HYPHEN_SEPARATOR)
                for room_configs in roomupgrade_config_list:
                    room_upgrades=room_configs.split(AT_SEAPARATOR)
                    # if len(room_upgrades)!=2:
                    #     return False,"Invalid roomupgrade_configs (valid R1:R2@5) "
                    
                    roomid_config=room_upgrades[0].split(COLON_SEPARATOR)
                    if len(roomid_config)!=2:
                        return False,"Invalid roomupgrade_configs"
                    if roomid_config[0]=='' or roomid_config[1]=='':
                        return False,"Invalid roomupgrade_configs roomcode missing "
                    if len(room_upgrades)>2:
                        return False,"Invalid roomupgrade_configs"
                    
                    # max_room_upgrade can be empty in that case we should not send the key of maxRoomUpgrade to update  service requestor weare sending then it wes hould send -1
                    if len(room_upgrades)==2:
                        max_room_upgrade=room_upgrades[1]
                        
                        if int(max_room_upgrade)<1:
                            return False,"max_room_upgrade cannot be less than 1"
                
                    
                    
            except  (ValueError, TypeError):
                return  False,"Invalid roomupgrade_configs"  
            
        if applicable_mealupgrade and applicable_mealupgrade.lower() != "na":
            if applicable_mealupgrade not in [PLATINUM_ONLY,GOLD_AND_PLATINUM_BOTH]:
                return False,"Invalid input for applicable_mealupgrade "
            if not mealupgrade_configs :
                return  False,"mealupgrade_config cannot be empty"
            if not allowed_template_id_dict.get(MEAL_UPGRADE_LEAF_CATEGORY_ID):
                return False,"Meal upgrade leaf category is not allowed for this hotel"
            mealupgrade_config_list = mealupgrade_configs.split(HYPHEN_SEPARATOR)
            
            # if len(mealupgrade_config_list)!=2:
            #     return False,"Invalid mealupgrade_configs (valid E1:E2-E3:E4#R1-R2)"
            # if mealupgrade_config_list[0]==''or mealupgrade_config_list[1]=='':
            #     return False,"Invalid mealupgrade_configs"
            for meal_upgrades in mealupgrade_config_list:
                mealid_config=meal_upgrades.split(COLON_SEPARATOR)
                if len(mealid_config)!=2:
                    return False,"Invalid mealupgrade_config"
                if mealid_config[0]=='' or mealid_config[1]=='':
                    return False,"Invalid mealupgrade_config"
                
            if mealupgrade_room.lower()=='na':
                return False,"Invalid mealupgrade_room"
            
            
        
        if roomupgrade_configs:
            if not applicable_roomupgrade or applicable_roomupgrade.lower() == "na":
                return False,"applicable_roomupgrade cannot be empty/na if roomupgrade_configs is given"
        if mealupgrade_configs:
            if not applicable_mealupgrade or applicable_mealupgrade.lower() == "na":
                return False,"applicable room/mealupgrade cannot be empty/na if mealupgrade_configs is given"
            
        # for dnd property it is mandatory to have applicable_roomupgrade and roomupgrade_configs some value it can't be left blank
        # if is_dnd :
        #     if applicable_roomupgrade=="" or applicable_roomupgrade==None:
        #         return False,"applicable_roomupgrade cannot be empty for dnd property"
        #     if room_configs=="" or room_configs==None:
        #         return False,"roomupgrade_configs cannot be empty for dnd property" 
    
    return True,"Valid value"

def validate_room_upgrade_config_field(applicable_roomupgrade,roomupgrade_configs):
    roomupgrade_config_list = roomupgrade_configs.split(HYPHEN_SEPARATOR)

    if roomupgrade_configs:
        if not applicable_roomupgrade or applicable_roomupgrade.lower() == "na":
            return False,"applicable_roomupgrade cannot be empty/na if roomupgrade_configs is given"
    for room_configs in roomupgrade_config_list:
        room_upgrades=room_configs.split(AT_SEAPARATOR)
        # if len(room_upgrades)!=2:
        #     return False,"Invalid roomupgrade_configs (valid R1:R2@5) "
        
        roomid_config=room_upgrades[0].split(COLON_SEPARATOR)
        if len(roomid_config)!=2:
            return False,"Invalid roomupgrade_configs (valid R1:R2@5)"
        if roomid_config[0]=='' or roomid_config[1]=='':
            return False,"Invalid roomupgrade_configs roomcode missing "
        if len(room_upgrades)>2:
            return False,"Invalid roomupgrade_configs"
        
        # max_room_upgrade can be empty in that case we should not send the key of maxRoomUpgrade to update  service requestor we are sending then it wes hould send -1
        if len(room_upgrades)==2:
            max_room_upgrade=room_upgrades[1]

            if int(max_room_upgrade)<1:
                return False,"max_room_upgrade cannot be less than 1"
        
    return True,"Valid value"

def validate_meal_upgrade_config_field(applicable_mealupgrade,mealupgrade_configs,mealupgrade_room):
    if mealupgrade_configs:
        if not applicable_mealupgrade or applicable_mealupgrade.lower() == "na":
            return False,"applicable room/mealupgrade cannot be empty/na if mealupgrade_configs is given"
    mealupgrade_config_list = mealupgrade_configs.split(HYPHEN_SEPARATOR)
    
    for meal_upgrades in mealupgrade_config_list:
        mealid_config=meal_upgrades.split(COLON_SEPARATOR)
        if len(mealid_config)!=2:
            return False,"Invalid mealupgrade_config (valid E1:E2-E3:E4)"
        if mealid_config[0]=='' or mealid_config[1]=='':
            return False,"Invalid mealupgrade_config"
        
    if mealupgrade_room.lower()=='na':
        return False,"Invalid mealupgrade_room"

    return True,"Valid value"
            

def validate_hotel_credit_fields(hotelcredit_gold, hotelcredit_platinum,hotel_credit_applicable,is_dnd=False):
    try:
        if hotelcredit_gold and hotelcredit_gold.lower()!="na":
            if int(hotelcredit_gold)<1:
                return False ,"Invalid hotelcredit_gold value"
            if hotelcredit_platinum.lower()=='na':
                return False,"Invalid value for hotelcredit_platinum,hotelcredit_platinum cannot be na if hotelcredit_gold has some value"
        
        if hotelcredit_platinum and  hotelcredit_platinum.lower()!="na":
            if int(hotelcredit_platinum)<1:
                return False ,"Invalid hotelcredit_platinum value" 
            
        if hotelcredit_gold and hotelcredit_platinum and hotelcredit_gold.lower()!="na" and hotelcredit_platinum.lower()!="na":
            if int(hotelcredit_platinum)<int(hotelcredit_gold):
                return False,"Platinum hotelcredit should be greater than gold"
            
        if (hotelcredit_gold and hotelcredit_gold.lower()!="na") or (hotelcredit_platinum and hotelcredit_platinum.lower()!="na") :
            if not hotel_credit_applicable:
                return False,"hotel_credit_applicable cannot be empty"
            if hotel_credit_applicable not in['pernight/room','perstay/room']:
                return False,"Invalid value for hotel_credit_applicable (valid pernight/room or perstay/room)"
            
        # for dnd property it is mandatory to have hotelcredit_gold and hotel_credit_applicable some value it can't be left blank
        # if is_dnd:
        #     if hotelcredit_platinum=="" or hotelcredit_platinum==None:
        #         return False,"hotelcredit_platinum cannot be empty for dnd property"
        #     if hotel_credit_applicable=="" or hotel_credit_applicable==None:
        #         return False,"hotel_credit_applicable cannot be empty for dnd property"
        # if hotel_credit_applicable :
        #     if not hotelcredit_platinum and hotelcredit_platinum.lower()!="na":
        #         return False,"hotel_credit cannot be empty"
            
    except  (ValueError, TypeError):
        return  False,"Enter valid hotelcredit value" 
    
    return True,"Valid value"


def validate_spa_fnb_discount_fields(spa_fnb_gold, spa_fnb_platinum,allowed_template_id_dict,is_dnd=False):
    try:
        if spa_fnb_gold and spa_fnb_gold.lower() != "na":
            if int(spa_fnb_gold) < 20 or int(spa_fnb_gold) > 100:
                return False, "spa_fnb_gold discount should be between 20 and 100"
            if spa_fnb_platinum.lower()=='na':
                return False,"Invalid value for spa_fnb_platinum,spa_fnb_platinum cannot be na if spa_fnb_gold has some value"
            # if not allowed_template_id_dict.get(SPA_BENEFIT_LEAF_CATEGORY_ID):
            #     return False,"Spa benefit leaf category is not allowed for this hotel"

            # if not allowed_template_id_dict.get(FNB_BENEFIT_LEAF_CATEGORY_ID):
            #     return False,"FNB benefit leaf category is not allowed for this hotel"
        
        if spa_fnb_platinum and spa_fnb_platinum.lower() != "na":
            if int(spa_fnb_platinum) < 20 or int(spa_fnb_platinum) > 100:
                return False, "spa_fnb_platinum discount should be between 20 and 100"
            # if not allowed_template_id_dict.get(SPA_BENEFIT_LEAF_CATEGORY_ID):
            #     return False,"Spa benefit leaf category is not allowed for this hotel"

            # if not allowed_template_id_dict.get(FNB_BENEFIT_LEAF_CATEGORY_ID):
            #     return False,"FNB benefit leaf category is not allowed for this hotel"
            
        if spa_fnb_gold and spa_fnb_platinum and spa_fnb_gold.lower() != "na" and spa_fnb_platinum.lower() != "na":
            if int(spa_fnb_platinum) < int(spa_fnb_gold):
                return False, "Platinum spa_fnb discount should be greater than gold"

        # removed spa fnb applicable on field from bulk uploader by default it will be perstay/room
        # if (spa_fnb_gold and spa_fnb_gold.lower() != "na") or (spa_fnb_platinum and spa_fnb_platinum.lower() != "na"):
            # removed spa fnb applicable on field from bulk uploader by default it will be perstay/room
            # if not spa_fnb_applicable:
            #     return False, "spa_fnb_applicable cannot be empty"
            # if spa_fnb_applicable not in ['pernight/room', 'perstay/room']:
            #     return False, "Invalid value for spa_fnb_applicable (valid pernight/room or perstay/room)"
            
        # for dnd property it is mandatory to have spa_fnb_platinum and spa_fnb_applicable some value it can't be left blank
        # if is_dnd :
        #     if spa_fnb_platinum=="" or spa_fnb_platinum==None:
        #         return False,"spa_fnb_platinum cannot be empty for dnd property"
        #     if spa_fnb_applicable=="" or spa_fnb_applicable==None:
        #         return False,"spa_fnb_applicable cannot be empty for dnd property"
        
        # if spa_fnb_applicable:
        #     if not spa_fnb_platinum or spa_fnb_platinum.lower() == "na":
        #         return False, "spa_fnb_discount cannot be empty/na if spa_fnb_applicable  is given"
            
            
    except  (ValueError, TypeError):
        return False, "Enter valid spa_fnb discount percentage"
    
    return True, "Valid value"


def validate_discount_fields(discount_gold, discount_platinum,is_dnd,black_enrolled_on='',no_of_benefits=1):
    
    if not(discount_gold or discount_platinum ): 
        if not is_dnd:
            return False, "Both discount_gold and discount_platinum should be present for non-dnd property"
        return True, "Both discount_gold and discount_platinum are None"
    
    try:
        if (discount_gold and discount_gold == 'na') and (discount_platinum and discount_platinum == 'na')  :
            if not is_dnd :
                # return False, "Both discount_gold and discount_platinum can't be removed for non-dnd property"
                # for non-dnd properties in bulk uploader add validation to not allow deactivation of cug before 90 days+black_enrolled_on
                # Parse the datetime string
                black_enrolled_on_date =datetime.datetime.strptime(black_enrolled_on, "%Y-%m-%d %H:%M:%S").date()
                today = datetime.datetime.now().date()
                # Calculate the date after 90 days
                after_90_days = black_enrolled_on_date + datetime.timedelta(days=90)
                # comment down below validation to allow force deactivation
                # if today < after_90_days:
                #     return False, "Black CUG cannot be deactivated before 90 days from black_enrolled_on date"
            return True, "Both discount_gold and discount_platinum are 'na'"
        
        if (discount_gold and not discount_platinum) or (discount_platinum and not discount_gold):
            return False, "Both discount_gold and discount_platinum should be present"
        if int(discount_gold) > int(discount_platinum):
            return False, "Discount_gold should be less than discount_platinum"
        
        if discount_gold and discount_platinum:
            if not (5 <= int(discount_gold)<= 100) or not (5 <= int(discount_platinum) <= 100):
                return False, "Discount values should be between 5 and 100"
        # comment down below validation when taking live to allow migration
        if not is_dnd and validate_min_black_diference(int(discount_gold), int(discount_platinum),no_of_benefits):
            return False, " if discount_platinum - discount_gold < 5  then no_of_benefits should be atleast 1 for non-dnd property"

    except (ValueError, TypeError):
        return False, "Enter valid values for discount fields"

    return True, "Valid discount values"


def validate_member_discount(discount_member,is_black_segment_to_be_migrated):
    # if discount member is not given in bulk uploader but in get cug response we are getting black segment offer then we should send validation error
    if is_black_segment_to_be_migrated:
        if not discount_member:
            return False, "Discount member should be given as black segment offer is present in db which needs to be migrated",discount_member
    
        if discount_member and discount_member.lower() == "na":
            return False,"Invalid value for discount_member ,it cannot be na",discount_member
    
        if not isinstance(discount_member, int) and not discount_member >= 0: #support 0 values also for member but if 0 is given then do not create member discount in update cug request 
            return False ,"Invalid value for discount_member ,it should be positive integer value",int(discount_member) # discount_member should be positive integer value
        if discount_member and discount_member !='na':
            discount_member=int(discount_member)
        return True ,"Valid discount_member value",discount_member
    else: #if no black segment offer is coming in get response then it is not mandatory to have discount_member ,if given then it will be ignored as already a  member disount exists with some value
        # discount_member=None #make it none so that it will be ignored in forming update cug request
        return True, "Valid discount_member value",discount_member
    
    
def validate_min_hotel_credits(min_hotel_credits):
    try:
        if min_hotel_credits and min_hotel_credits.lower() == "na":
            return True,"Valid value",0
        min_hotel_credits=int(min_hotel_credits)
        if min_hotel_credits and min_hotel_credits<=0:
            print("min_hotel_credit",min_hotel_credit)
            print("type",type(min_hotel_credit))
            return False, "Invalid min_hotel_credit value , it must be a interger greater than 0",min_hotel_credits
    
    except Exception as e:
        return False, "Invalid min_hotel_credit value , exception : {}".format(e),0
    return True,"Valid value",min_hotel_credits
    
# def validate_fields_for_mincount(black_dnd_mincount, *fields):
#     non_na_fields = [field for field in fields if field and field.lower() != 'na']

def validate_fields_for_mincount(black_dnd_mincount,discount_platinum,no_of_benefits):
    # no_of_benefits=count_no_of_benefits_given(applicable_roomupgrade, applicable_mealupgrade,hotelcredit_gold, hotelcredit_platinum, spa_fnb_gold,spa_fnb_platinum)
    if discount_platinum and discount_platinum.lower() != "na":
        no_of_benefits+=1

    # commenting below check for migrating dnd properties
    # if no_of_benefits < int(black_dnd_mincount):
    #     return False, "Validation failed: benefits count is less than black_dnd_mincount"

    return True, "Validation successful"


def validate_min_black_diference(discount_gold, discount_platinum,no_of_benefits):
    if discount_platinum-discount_gold < BLACK_MIN_DIFFERENCE_ALLOWED and no_of_benefits==0:
        return False,"Difference between discount_platinum and discount_gold should be atleast 5"

def count_no_of_benefits_given(applicable_roomupgrade, applicable_mealupgrade,hotelcredit_gold, hotelcredit_platinum, spa_fnb_gold,spa_fnb_platinum,service_benefit_map={}):
    benefit_count=0
    if (applicable_roomupgrade and applicable_roomupgrade.lower() != "na") or (service_benefit_map.get(ROOM_UPGRADE_LEAF_CATEGORY_ID,False)):
        benefit_count+=1
    if (applicable_mealupgrade and applicable_mealupgrade.lower() != "na") or (service_benefit_map.get(MEAL_UPGRADE_LEAF_CATEGORY_ID,False)):
        benefit_count+=1
    if (hotelcredit_platinum and hotelcredit_platinum.lower() != "na") or (hotelcredit_gold and hotelcredit_gold.lower() != "na") or (service_benefit_map.get(HOTEL_CREDIT_BENEFIT_LEAF_CATEGORY_ID,False)):
        benefit_count+=1

    if (spa_fnb_platinum and spa_fnb_platinum.lower() != "na") or (spa_fnb_gold and spa_fnb_gold.lower() != "na") or \
            (service_benefit_map.get(SPA_BENEFIT_LEAF_CATEGORY_ID,False) and service_benefit_map.get(FNB_BENEFIT_LEAF_CATEGORY_ID,False)): #since fnb and spa are counted  as 1 benefit
        benefit_count+=1

    return benefit_count

def validate_blackout_date(blackoutdate):
    
    if not blackoutdate:
        return True,"Valid input"
    
    if blackoutdate.lower() == "na":
        return True,"Valid input"

    date_ranges = blackoutdate.split(COMMA_SEPARATOR)
    for date_range in date_ranges:
        dates = date_range.split(COLON_SEPARATOR)
        if len(dates) != 2:
            return False,"Invalid BlackoutDates (valid 2023-12-21:2023-12-22,2023-12-22:2023-12-23)"
        try:
            start_date, end_date = dates
            _ = datetime.datetime.strptime(start_date, '%Y-%m-%d')
            _ = datetime.datetime.strptime(end_date, '%Y-%m-%d')
        except ValueError:
            return False,"Invalid BlackoutDates Format (valid 2023-12-21:2023-12-22,2023-12-22:2023-12-23)"

    return True,"Valid Input"
  
def validate_booking_and_stay_dates(bookingstartdate,bookingenddate,checkinstartdate,checkinenddate):

    
    if bookingstartdate:
        if not is_valid_date_format(bookingstartdate):
            return False,"Invalid date format for bookingstartdate , it should be YYYY-MM-DD format"
        if not bookingenddate:
            return False,"bookingenddate cannot be empty"
        if not checkinenddate:
            return False,"checkinenddate cannot be empty if booking_start_date is provided"
        if not checkinstartdate:
            return False,"checkinstartdate cannot be empty if booking_start_dateis provided"
        
    if bookingenddate:
        if not is_valid_date_format(bookingenddate):
            return False,"Invalid date format for bookingenddate , it should be YYYY-MM-DD format"
        if not bookingstartdate:
            return False,"bookingstartdate cannot be empty"
        
    if checkinstartdate:
        if not is_valid_date_format(checkinstartdate):
            return False,"Invalid date format for checkinstartdate , it should be YYYY-MM-DD format"
        if not checkinenddate:
            return False,"checkinenddate cannot be empty"   
        
    if checkinenddate:
        if not is_valid_date_format(checkinenddate):
            return False,"Invalid date format for checkinenddate , it should be YYYY-MM-DD format"
        if not checkinstartdate:
            return False,"checkinstartdate cannot be empty"
        
    return True,"Valid Input"

               

def validate_cpp_config_req(cpp_threshold_status,cpp_commission_threshold,cpp_tax_inclusive_status,cpp_comm_tax_inclusive_status):
    if cpp_threshold_status.lower() !="" and (cpp_threshold_status.lower() !='true' and cpp_threshold_status.lower() !='false'):
        return False,"Enter correct status value (True / False) for cpp_threshold_status"
     
    if cpp_threshold_status.lower()=='true':
        if not cpp_commission_threshold:
                    return False, "Enter threshold in  percentage(15-99)"
        else:
            try:
                cpp_commission_threshold = int(cpp_commission_threshold)
                if not (1 <= cpp_commission_threshold <= 99):
                    return False,"Enter valid percentage threshold (1-99)"
            except ValueError:
                return False, "Enter valid percentage threshold (1-99)"
        
    if cpp_tax_inclusive_status!="" and (cpp_tax_inclusive_status.lower() !='true' and cpp_tax_inclusive_status.lower() !='false'):
        return False,"Enter valid value (True / False) for cpp_tax_inclusive"
    
    if cpp_comm_tax_inclusive_status !="" and (cpp_comm_tax_inclusive_status.lower() !='true' and cpp_comm_tax_inclusive_status.lower() !='false'):
        return False,"Enter valid value (True / False) for cpp_comm_tax_inclusive"
    
    if cpp_threshold_status=="" and cpp_tax_inclusive_status=="" and cpp_comm_tax_inclusive_status=="":
        return False,"Invalid request"

    if cpp_commission_threshold and not cpp_threshold_status:
        return False,"cpp_threshold_status is required for cpp_commission_threshold update"

    return True,"Valid req"
        
def  validate_cpp_rate_res_req(start_date,end_date,contract_type_list,day_list,block_cpp_rates):
    from hotels.hotelchoice import RATES_AND_INV_CALENDER_SPAN_DAYS
    from common.constants import CONTRACTTYPE
    try:
        start_date_obj = datetime.datetime.strptime(start_date, '%Y-%m-%d').date()
        end_date_obj = datetime.datetime.strptime(end_date, '%Y-%m-%d').date()
    except:
        return False, "Incorrect data format, date should be YYYY-MM-DD"
    
    try:
        today = datetime.date.today()
        max_end_date = today + datetime.timedelta(days=RATES_AND_INV_CALENDER_SPAN_DAYS)
        
        if end_date_obj> max_end_date:
            return False, "End date can't be greater than 450 days"
        if start_date_obj<today:
            return False, "Start date must be greater than or equal to today."
        if end_date_obj < start_date_obj:
            return False, "End Date must be greater than Start date."
        
        if block_cpp_rates and (block_cpp_rates.lower()!="true" and  block_cpp_rates.lower()!="false"):
            return False, "Enter valid value (True / False) for block_cpp_rates"

        if not contract_type_list:
            return False,  "Contract type list can't be empty"
        
        contract_types_req = [contract_type.strip() for contract_type in contract_type_list.split(',')]
        valid_contract_types = [contract_type for contract_type, value in CONTRACTTYPE]
        invalid_contract_types = [contract_type_req for contract_type_req in contract_types_req if contract_type_req not in valid_contract_types]
            
        if invalid_contract_types:
            message = "Invalid contract_types: {}".format(", ".join(invalid_contract_types))
            return False, message
        
        if day_list: 
            days_req = [day.strip() for day in day_list.split(',')]
            valid_day_list= ['M','T','W','Th','F','Sa','Su']
            invalid_day_list = [day_req for day_req in days_req if day_req not in valid_day_list]   
            
            if invalid_day_list:
                return False, "Invalid day list: {} (Valid value M,T,W,Th,F,Sa,Su)".format(", ".join(invalid_day_list))
            
        return True, "Valid req"       
    except Exception as e:
        logger.critical(message='Error occur: %s' % str(e),
                        log_type='ingoibibo', bucket='bulk_uploader', stage='bulk_uploader.common_helper.validate_cpp_rate_res_req')
        return False, "Error occur while validating req"
            
def get_black_out_dates_in_2d_list(checkinblackoutdates=""):
    stay_blackout_dates=[]
    if checkinblackoutdates!="" and checkinblackoutdates!='na':
        date_ranges = checkinblackoutdates.split(COMMA_SEPARATOR)
        for date_range in date_ranges:
            dates = date_range.split(COLON_SEPARATOR)
            stay_blackout_dates.append([dates[0],dates[1]])
    
    return stay_blackout_dates

# convert "2024-02-01:2024-02-02,2024-02-04:2024-02-05" to ["2024-02-01,2024-02-02","2024-02-04,2024-02-05"]
def get_blackout_dates_1d_list(checkinblackoutdates=""):
    stay_blackout_dates=[]
    if checkinblackoutdates and checkinblackoutdates!="" and checkinblackoutdates!='na':
        # Split the string using comma as the delimiter
        checkinblackoutdates = checkinblackoutdates.replace(" ", "")
        date_ranges = checkinblackoutdates.split(',')
        for d in  date_ranges:
            # split_list=d.split(":")
            # d1=split_list[0]
            # d2=split_list[1]
            moadified_date=d.replace(":",",")
            print("modified_datae",moadified_date)
            stay_blackout_dates.append(moadified_date)

    return stay_blackout_dates



def get_currentdatetime_in_hotel_timezone_format(hotel_timezone):

    from datetime import datetime
    import pytz
    
    # Get the current time in ISO 8601 format with timezone
    current_time_with_timezone = datetime.now(pytz.timezone(hotel_timezone))
    
    formatted_date = current_time_with_timezone.strftime('%Y-%m-%d %H:%M:%S')
  
    return str(formatted_date)

def convert_time_in_iso_format(datetime_str, hotel_timezone):
    return str(datetime_str)
    # from datetime import datetime
    # import pytz

    # # Convert the datetime string to a datetime object
    # dt_object = datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')

    # # Localize the datetime object to the provided timezone
    # dt_object = pytz.timezone(hotel_timezone).localize(dt_object)

    # # Format the datetime object as a string in the desired format
    # formatted_datetime_str = dt_object.strftime('%Y-%m-%d %H:%M:%S%z')

    # # Insert the colon in the timezone offset
    # formatted_datetime_str = formatted_datetime_str[:-2] + ':' + formatted_datetime_str[-2:]

    # return formatted_datetime_str


        
def check_and_convert_stay_and_booking_dates_to_correct_format(bookingstartdate,bookingenddate,checkinstartdate,checkinenddate):
    s1,s2,s3,s4=True,True,True,True
    try:
        
        if bookingstartdate:
            s1,bookingstartdate=convert_date(bookingstartdate)
        if bookingenddate:
            s2,bookingenddate=convert_date(bookingenddate)
        if checkinstartdate:
            s3,checkinstartdate=convert_date(checkinstartdate)
        if checkinenddate:
            s4,checkinenddate=convert_date(checkinenddate)
        is_success=s1 and s2 and s3 and s4
        return is_success,bookingstartdate,bookingenddate,checkinstartdate,checkinenddate
    except Exception as e:
        print("exception occured in check_and_convert_stay_and_booking_dates_to_correct_format , e: ",e)
        return False,s1,s2,s3,s4

# it converts date string from 'dd/mm/yy' to 'yyyy-mm-dd' format
    
def convert_date(date_string):
    date_obj=None
    try:
        # Attempt to convert the date string to a datetime object
        date_obj = datetime.datetime.strptime(date_string, '%d/%m/%y')
    except ValueError:
        # If ValueError occurs (i.e., the date string is not in the expected format), check if it is in 'yyyy-mm-dd' format and return the same
        valid,date_obj=is_valid_date_format(date_string)
        if not valid:
            return False,date_string
        
        
    
    # Convert datetime object to string in desired format
    if not date_obj:
        return False ,""
    formatted_date_string = date_obj.strftime('%Y-%m-%d')
    return True,formatted_date_string

# check if the date string is in 'yyyy-mm-dd' format
def is_valid_date_format(date_string):
    try:
        # Attempt to parse the date string
        date_obj=datetime.datetime.strptime(date_string, '%Y-%m-%d')
        return True,date_obj  # If parsing succeeds, the format is correct
    except ValueError:
        return False,None  # If parsing fails, the format is incorrect

def format_payload_for_create_prebuy_config(data_dict, correlation_key):

    #formatting config_details
    #roomcode@rp1#rp2#rp3,roomcode2@rp1#rp2#rp3, roomcode3@*
    config_details = data_dict.get('config_details', '').strip()
    config_details_list = config_details.split(',')
    prebuy_entity = []
    for config in config_details_list:
        if config.strip() == '*':
            prebuy_entity.append({
                'ingoRoomId': '',
                'rateplanCodes': [],
                'level': 'hotel'
            })
        else:
            room_code, rateplans = config.split('@')
            rateplans_list = []
            level = 'room'
            if rateplans != '*':
                rateplans_list = rateplans.split('#')
                rateplans_list = [rateplan.strip() for rateplan in rateplans_list]
                level = "rateplan"
            room_code = room_code.strip()
            prebuy_entity.append(
                {
                    'ingoRoomId': room_code,
                    'rateplanCodes': rateplans_list,
                    'level': level
                }
            )
    config_id = data_dict.get('config_id', '0').strip()
    config_id = int(config_id) if config_id else 0

    payload = {
        'correlationKey': correlation_key,
        'hotelId':  data_dict.get('hotel_id', '').strip(),
        'actionSource': "bulkUploader",
        'prebuyConfigs': [
            {
                'prebuyEntities': prebuy_entity,
                'totalRoomNights': int(data_dict.get('room_nights', '0').strip()),
                'maxGuestOccupancy': int(data_dict.get('max_guest_occupancy', '0').strip()),
                'price': float(data_dict.get('price', '0').strip()),
                'orderValidityDays': int(data_dict.get('validity_days').strip()),
                'triggerOrder': data_dict.get('trigger_order', '0').strip().lower() in ['True', 'true', '1'],
                'reorderPoint': int(data_dict.get('reorder_point', '0').strip()),
                'dailySaleLimit': int(data_dict.get('daily_sale_limit', '0').strip()),
                'prebuyContractType': data_dict.get('prebuy_contract_type', '').strip(),
                'approvalType': int(data_dict.get('approval_type', '0').strip()),
                'approvalEmails': data_dict.get('approval_emails', '').strip().split(','),
                'isActive': data_dict.get('is_active', '0').strip().lower() in ['True', 'true', '1'],
                'configId': config_id,
                'approvedBy': data_dict.get('approved_by', '').strip(),
                'autoApprovalInMinutes': int(data_dict.get('auto_approval_in_minutes', '0').strip() or 0),
            }
        ]
    }

    if 'order_start_date' in data_dict or 'order_end_date' in data_dict:
        order_dates = {}
        if 'order_start_date' in data_dict and len(str(data_dict.get('order_start_date')).strip()) > 0:
            order_dates['orderStartDate'] = data_dict.get('order_start_date')

        if 'order_end_date' in data_dict and len(str(data_dict.get('order_end_date')).strip()) > 0:
            order_dates['orderEndDate'] = data_dict.get('order_end_date')

        if 'use_ceiling_end_time' in data_dict:
            order_dates['useCeilingEndTime'] = data_dict.get('use_ceiling_end_time', '0').strip().lower() in ['True', 'true', '1']

        if order_dates:  # Only add orderDates if it's not empty
            payload['prebuyConfigs'][0]['orderDates'] = order_dates

    return payload

def format_payload_for_create_prebuy_order(data_dict, correlation_key):
    order_numbers=[]
    order_number = data_dict.get('orderNumber', None).strip()
    order_numbers.append(order_number)
    action = data_dict.get('action', None)
    update_reason = data_dict.get('updateReason', None)
    hotel_id = data_dict.get('hotelId', None)
    approved_by = data_dict.get('approvedBy', None)
    payload = {
        'correlationKey': correlation_key,
        'orderNumbers': order_numbers,
        'action': action,
        'hotelId': hotel_id,
        'actionSource': "bulkUploader",
        'approvedBy': approved_by,
    }
    order_data = {}
    if 'orderValidityDate' in data_dict and len(str(data_dict.get('orderValidityDate')).strip()) > 0:
        order_data['orderValidityDate'] = data_dict.get('orderValidityDate')

    if len(str(update_reason).strip()) > 0:
        order_data['updateReason'] = update_reason

    if order_data:  # Only add orderData if it's not empty
        payload['orderData'] = order_data

    return payload

def format_payload_for_upsert_blackout_dates(data_dict, correlation_key):

    hotel_id = data_dict.get('mmt_hotelId')
    blackout_data = []
    blackout_data.append(
        {
        'productCode': "hotel",
        'productObjectCode': hotel_id,
        'blackoutDates':data_dict.get('blackout_dates')
    })

    payload = {
        'correlationKey': correlation_key,
        'hotelId': hotel_id,
        'blackoutData': blackout_data
    }

    return payload

def is_valid_time(time):
    regex = r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$"  # Regex for HH:MM format
    if not time:  # Check for empty string or None
        return False
    return bool(re.match(regex, time))

def validate_blackout_dates_format(blackout_dates_str):
    blackout_dates = blackout_dates_str.split(',')
    for date_range in blackout_dates:
        dates = date_range.split(':')
        if len(dates) != 2:
            return False, "Invalid blackout dates format. Expected format: '2024-02-25:2024-02-25,2024-02-26:2024-02-28'"
        for date in dates:
            try:
                datetime.datetime.strptime(date, '%Y-%m-%d')
            except ValueError:
                return False, "Invalid date format. Expected format: 'YYYY-MM-DD'"
    return True, None

def validate_blackout_policy(blackout_dates, blackout_policy_rule, blackout_status):

    if not blackout_dates and not blackout_policy_rule and not blackout_status:
        return True, None  # Skip validation if all fields are empty

    if blackout_status.lower() == 'true':  # Only validate dates and policy rule if status is active
        if not (blackout_dates and blackout_policy_rule):
            return False, "Both blackout dates and policy rules are required if blackout status is active"
    elif blackout_status == 'false':
        if not blackout_policy_rule:
            return False, "Blackout policy rules are required if blackout status is false"
        return True, None
    else:
        return False, "Invalid blackout status value"

    return True, None

def validate_template(template):
    if template and template not in CP_TEMPLATE_ID:
        return False, "Invalid template"
    return True, None

def validate_policy_type(policy_type):
    if policy_type and policy_type not in CP_POLICY_TYPE:
        return False, "Invalid policy type"
    return True, None

def validate_input_fields(template_id, policy_type, is_create_cp_req=False):
    if is_create_cp_req:
        if not policy_type:
            return False, "policy_type is required"
        if not template_id:
            return False, "template_id is required"

    is_valid, err_msg = validate_template(template_id)
    if not is_valid:
        return False, err_msg

    is_valid, err_msg = validate_policy_type(policy_type)
    if not is_valid:
        return False, err_msg

    return True, None

def validate_is_active(is_active_str):

    if not is_active_str:
        return None, None

    lower_str = is_active_str.lower()
    if lower_str == 'true':
        return True, None
    elif lower_str == 'false':
        return False, None
    else:
        return None, "Invalid is_active value. Must be true or false."

def validate_attach_all_rateplans_field(attach_all_rateplans):
    if attach_all_rateplans:
        lower_str = attach_all_rateplans.lower()
        if lower_str == 'true':
            return True, None
        elif lower_str == 'false':
            return False, None
        else:
            return None, "Invalid attach_all_rateplans value. Must be true or false."
    else:
        return False, None



def get_policy_template_from_policy_rule(policy_rule):
    return CP_POLICY_RULE_TO_TEMPLATE.get(policy_rule, CUSTOM_TEMPLATE)

def generate_policy_rules(policy_rule_code):
    rules = policy_rule_code.split('_')
    policy_rules = []

    rule_index = len(rules) - 1
    while rule_index >= 0:
        curr_rule = rules[rule_index]

        time, day, charge_value, charge_type = get_time_day_and_charges_information(curr_rule)
        if charge_type == "P":
            charge_type = "percent"
        elif charge_type == "N":
            charge_type = "night"

        policy_rule = {
            "chargeType": charge_type,
            "chargeValue": charge_value,
        }

        if rule_index == len(rules) - 1:
            policy_rule["day"] = -1
            policy_rule["time"] = time
        else:
            if day == "A":
                day_int = 365
            else:
                day_int = int(day)
            policy_rule["day"] = day_int
            policy_rule["time"] = time

        policy_rules.append(policy_rule)
        rule_index -= 1

    return policy_rules

def get_time_day_and_charges_information(rule):
    time_day_and_charge_info = rule.split('D')
    time_and_day = ""
    charge_value_and_charge_type = ""

    if len(time_day_and_charge_info) == 1:
        charge_value_and_charge_type = time_day_and_charge_info[0]
    else:
        time_and_day = time_day_and_charge_info[0]
        charge_value_and_charge_type = time_day_and_charge_info[1]

    if charge_value_and_charge_type.endswith("P"):
        charge_value = int(charge_value_and_charge_type[:-1])
        charge_type = "P"
    elif charge_value_and_charge_type.endswith("N"):
        charge_value = int(charge_value_and_charge_type[:-1])
        charge_type = "N"
    else:
        charge_value = 0
        charge_type = ""

    time_and_day_array = time_and_day.split('M')
    if len(time_and_day_array) == 1:
        day = time_and_day_array[0]
        time = ""
    else:
        time = time_and_day_array[0] + "M"
        day = time_and_day_array[1]

    return time, day, charge_value, charge_type

def generate_blackout_policies(data_dict):
    blackout_policies = []
    for i in range(1, 6):
        blackout_dates_str = data_dict.get('blackoutdates_{}'.format(i), "")
        blackout_policy_rule = data_dict.get('blackout_policy_rule_{}'.format(i), "")
        blackout_status_str = data_dict.get('blackout_status_{}'.format(i), "").lower()
        blackout_status = blackout_status_str == 'true'

        is_valid, err_msg = validate_blackout_policy(blackout_dates_str, blackout_policy_rule, blackout_status_str)
        if not is_valid:
            return None, err_msg

        if blackout_policy_rule:
            blackout_template_id = get_policy_template_from_policy_rule(blackout_policy_rule)
            blackout_policy = {
                'templateId': blackout_template_id,
                'isActive': blackout_status
            }
            #Incase blackout status is false, we don't need to send blackout dates
            if blackout_dates_str:
                is_valid, err_msg = validate_blackout_dates_format(blackout_dates_str)
                if not is_valid:
                    return None, err_msg
                blackout_policy['dates'] = blackout_dates_str
            if blackout_template_id == CUSTOM_TEMPLATE:
                try:
                    blackout_policy['policyRules'] = generate_policy_rules(blackout_policy_rule)
                except Exception as e:
                    return [], "Invalid policy rule: {}".format(str(e))
            blackout_policies.append(blackout_policy)
            
    return blackout_policies, None

def offline_booking_validator(data_dict, is_price_config_exist):
    from api.v1.common_resources.resources import validate_bool
    status = data_dict.get("status", "true")
    nett_markup = data_dict.get("nett_markup", "")
    nett_markup_type = data_dict.get("nett_markup_type", "")
    commission = data_dict.get("offline_commission", "")

    if not is_price_config_exist: #create new price der
        if not validate_bool(status):
            raise ValidationError("No Price Derivative Config found for this hotel.")
        if validate_bool(status):#if true - and create
            if nett_markup == "":
                raise ValidationError("nett_markup is required field.")
            if nett_markup_type == "":
                raise ValidationError("nett_markup_type is required field.")
            if commission == "":
                raise ValidationError("offline_commission is required field.")

def change_request_commission_validator(status, existing_commission, req_commission):
    '''
    Validate the commission fields for Price Derivative change request
    Args:
        status: True/False
        existing_commission: Fetched from price derivative table
        req_commission: New commission from BU

    '''
    if status.lower() == "true" and not existing_commission:
        if not req_commission:
            return False, "commission is mandatory for creating Price Derivative entry"

    if status.lower() == "false" and not existing_commission:
        return False, "No existing PriceDerivative entry found for hotelcode for deactivation"
    return True, ""

def change_request_validator(hotel_obj, content_type_id, request_type, object_id=None):
    from api.v1.change_requests.resources.common_helper import is_change_request_exist

    if not hotel_obj:
        raise ValidationError("Hotel not found, please enter valid hotel")

    if content_type_id == ContentTypeIdForHotel:
        object_id = hotel_obj.id

    cr_exists, cr_obj = is_change_request_exist(hotel_obj.id, object_id, content_type_id, request_type)
    if cr_exists:
        raise ValidationError("Change Request with id: {} already exist for this hotel: {}".format(cr_obj.ingo_request_id, hotel_obj.hotelcode))

    return True


def is_create_lmr_request(data_dict, correlation_key):
    from promotion_v2.grpc_promo_genie_client import PromoGenieClient
    hotel_code = data_dict.get('ingo_hotel_code', '').strip()
    user_id = data_dict.get('user_id', '').strip()

    formatted_request = {
        "correlationKey": correlation_key,
        "ingoHotelId": hotel_code,
    }
    promo_genie_client = PromoGenieClient()
    metadata = [
        ('userid', str(user_id)),
        ('platform', 'web'),
        ('source', 'ingo_admin'),
        ('language', 'eng'),
        ('country', 'in')
    ]
    api_response = promo_genie_client.get_lmr_config(formatted_request, metadata)
    if not api_response.get('success'):
        error_reason = api_response.get('error', [])
        if len(error_reason) > 0:
            if error_reason[0].get('code', '') == PROMO_GENIE_LMR_NOT_FOUND_ERROR_CODE:
                return True, ""
            else:
                return False, "Error occurred while fetching LMR config: {} hotelcode: {} correlation key: {}".format(error_reason, hotel_code, correlation_key)
    elif not api_response.get('data', {}).get('lmrConfig', {}).get('isActive', {}).get('value', False):
        return True, ""
    return False, ""


def get_update_lmr_request(data_dict):
    correlation_key = str(uuid.uuid4())
    hotel_code = data_dict.get('ingo_hotel_code', '').strip()
    is_applicable_to_all = data_dict.get('is_applicable_to_all', '').strip().upper()

    formatted_request = {
        "correlationKey": correlation_key,
        "ingoHotelId": hotel_code,
    }
    lmr_config = dict()
    is_active = data_dict.get('is_active', '').strip().upper()
    if is_active.strip() == 'FALSE':
        lmr_config['isActive'] = False
    else:
        charge_percentage = data_dict.get('charge_percentage', '').strip()
        start_time = data_dict.get('start_time', '').strip()
        stay_blackout_dates = data_dict.get('stay_blackout_dates', '').strip()
        if is_active != '':
            lmr_config['isActive'] = True if is_active == 'TRUE' else False
        if charge_percentage != '':
            lmr_config['chargeType'] = "percentage"
            lmr_config['chargeValue'] = int(charge_percentage)
        if start_time!= '':
            lmr_config['startTime'] = start_time
        if stay_blackout_dates != "":
            if str(stay_blackout_dates).lower().strip() == "na":
                lmr_config['stayDateBlackouts'] = ""
            else:
                lmr_config['stayDateBlackouts'] = stay_blackout_dates

        active_entities = list()
        if is_applicable_to_all == 'TRUE':
            active_entities.append({
                "level": LMR_LEVEL_HOTEL,
                "entityList": [hotel_code]
            })
        elif is_applicable_to_all == 'FALSE':
            room_codes = data_dict.get('ingo_room_codes', '').strip()
            rate_plan_codes = data_dict.get('ingo_rateplan_codes', '').strip()
            if room_codes != '':
                active_entities.append({
                    "level": LMR_LEVEL_ROOM,
                    "entityList": room_codes.split(",")
                })
            if rate_plan_codes != '':
                active_entities.append({
                    "level": LMR_LEVEL_RATEPLAN,
                    "entityList": rate_plan_codes.split(",")
                })
        formatted_request['activeEntities'] = active_entities
    formatted_request['lmrConfig'] = lmr_config

    return formatted_request


def fetch_all_rateplans(policy_type, hotel_id):
    from hotels.models import RatePlan, RoomDetail
    from hotels.models.hotel_flag_configuration import ROOM_DETAIL_FLAG_DICT
    from common.commonchoice import GROUP

    if policy_type == "NORMAL":
        room_ids = RoomDetail.objects.filter(hotel_id=hotel_id).exclude(flag_bits_1__truth=ROOM_DETAIL_FLAG_DICT['is_slot_room']).values_list('id', flat=True)
    elif policy_type == "HOURLY":
        room_ids = RoomDetail.objects.filter(hotel_id=hotel_id,flag_bits_1__truth=ROOM_DETAIL_FLAG_DICT['is_slot_room']).values_list('id', flat=True)
    else:
        # Incase of SCP/GRP
        return []

    if not room_ids:
        return []

    rateplans = RatePlan.objects.filter(roomtype__in=room_ids).exclude(contracttype=GROUP).values_list('rateplancode', flat=True)
    return list(rateplans)

def validate_vcc_uploader(vcc_payment_mode, vcc_currency, vcc_multi_currency, vcc_markup_percentage, vcc_validity_end):
    if vcc_payment_mode:
        if not vcc_currency:
            return False, "VCC Currency is required"
        if vcc_multi_currency and vcc_markup_percentage is None:
            return False, "Multi Currency cannot be enabled without vcc markup percentage."
        elif vcc_multi_currency and vcc_markup_percentage is not None and (
                vcc_markup_percentage < 0 or vcc_markup_percentage > 10):
            return False, "VCC Markup Percentage cannot be less than 0 or greater than 10."
        if vcc_validity_end is not None and vcc_validity_end < 10 or vcc_validity_end > 366:
            return False, "VCC Validity End should be between 10 and 365 days."
    else:
        if vcc_multi_currency:
            return False, "VCC Multi Currency cannot be enabled without VCC Payment Mode"
        if vcc_markup_percentage is not None:
            return False, "VCC Markup Percentage cannot be enabled without VCC Payment Mode"
        if vcc_validity_end:
            return False, "VCC Validity End cannot be set without VCC Payment Mode"

    return True, None



def format_payload_for_permission_create_update(data_dict, correlation_key):
    """
    Format payload for permission create/update to match UpsertAuthPermissionRequest proto

    Args:
        data_dict: Dictionary containing permission data
        correlation_key: Unique identifier for the request

    Returns:
        dict: Formatted payload for the permission service
    """
    # Prepare the request payload with required fields from proto
    payload = {
        "correlationKey": correlation_key,
        "name": data_dict.get('PermissionName', '')
    }
    # Add codeName field (field 3 in proto)
    if 'PermissionCode' in data_dict and data_dict.get('PermissionCode', ''):
        payload["codeName"] = data_dict.get('PermissionCode', '')

    # Add contentType field (field 4 in proto)
    if 'ContentType' in data_dict and data_dict.get('ContentType', ''):
        payload["contentType"] = data_dict.get('ContentType', '')

    # Set isActive field (field 5 in proto)
    action = data_dict.get('Action', '').lower()
    if action == 'add':
        payload["isActive"] = True
    elif action == 'remove':
        payload["isActive"] = False
    else:
        payload["isActive"] = True

    return payload

def format_payload_for_group_create_update(data_dict, correlation_key):
    """
    Format payload for group create/update based on the Action field and proto definitions

    Args:
        data_dict: Dictionary containing group data with mandatory fields GroupName and Action
        correlation_key: Unique identifier for the request

    Returns:
        dict: Formatted payload for the appropriate group service (Create or Update)
    """
    # Extract action to determine which payload format to use
    action = data_dict.get('Action', '').lower()

    # Common fields for both create and update
    payload = {
        "Correlation_key": correlation_key,
        "Name": data_dict.get('GroupName', ''),
        "IsActive": True  # Set default as True for both create and update
    }

    # Set IsActive based on action type
    if action == 'add':
        payload["IsActive"] = True
    elif action == 'remove':
        payload["IsActive"] = False
    # For other actions, only override IsActive if explicitly provided
    elif 'IsActive' in data_dict and data_dict.get('IsActive', ''):
        is_active_str = data_dict.get('IsActive', '').lower()
        payload["IsActive"] = is_active_str in ['true', '1', 'yes']

    return payload

def format_payload_for_user_group_assignment(data_dict, correlation_key):
    """
    Format payload for user-group upsert operation based on UpsertAuthUserGroupRequest proto.

    Args:
        data_dict: Dictionary containing user-group data with mandatory fields
                   UserID, GroupName. Optional field IsActive.
        correlation_key: Unique identifier for the request

    Returns:
        dict: Formatted payload for the auth service UpsertAuthUserGroup RPC call.
               Returns None if UserID is missing or invalid.
    """

    user_id_str = data_dict.get('UserID', '')
    try:
        user_id_int = int(user_id_str)
    except (ValueError, TypeError):
        return None

    # Create the payload matching the UpsertAuthUserGroupRequest proto fields
    payload = {
        "Correlation_key": correlation_key,         # Corrected key name
        "Name": data_dict.get('GroupName', ''),     # Corrected key name (maps to Group Name)
        "UserID": user_id_int,                      # Corrected key name and type
    }
    action = data_dict.get('Action', '').lower()

    if action == 'assign':
        payload["IsActive"] = True
    elif action == 'unassign':
        payload["IsActive"] = False
    # For other actions, only override IsActive if explicitly provided
    elif 'IsActive' in data_dict and data_dict.get('IsActive', ''):
        is_active_str = data_dict.get('IsActive', '').lower()
        payload["IsActive"] = is_active_str in ['true', '1', 'yes']

    return payload

def format_payload_for_permission_assignment(data_dict, correlation_key):
    """
    Format the payload for permission assignment operations

    Args:
        data_dict: Dictionary containing permission assignment data
        correlation_key: Unique identifier for tracking the request

    Returns:
        dict: Formatted payload for the gRPC call
    """
    content_type = data_dict.get('ContentType', '').upper()
    action = data_dict.get('Action', '').upper()

    # Format payload based on entity type
    if content_type == 'USER':
        # Convert comma-separated user IDs to list
        user_ids = [uid.strip() for uid in data_dict.get('ContentID', '').split(',')]
        payload = {
            "permissionId": int(data_dict.get('Permission')),
            "userIds": user_ids,
            "action": action,
            "correlationKey": correlation_key
        }
    elif content_type == 'GROUP':
        # Convert comma-separated permission IDs to list
        permission_ids = [int(pid.strip()) for pid in data_dict.get('Permission', '').split(',')]
        payload = {
            "groupId": int(data_dict.get('ContentID')),
            "permissionIds": permission_ids,
            "action": action,
            "correlationKey": correlation_key
        }

    return payload

def format_payload_for_screen_registry_upsert(data_dict, correlation_key):

    action = data_dict.get('Action', '').upper()
    is_active = False  # Default to False
    if action == 'ADD':
        is_active = True

    payload = {
        "screenName": data_dict.get('ScreenName', '').strip(),
        "client": data_dict.get('Client', '').strip(),
        "displayText": data_dict.get('DisplayText', '').strip(),
        "isActive": is_active
    }
    return payload

def format_payload_for_screen_assignment(data_dict, correlation_key):
    entity_type = data_dict.get('EntityType', '').upper()
    action = data_dict.get('Action', '').lower() # As per instruction: Set action value in lowercase as received in csvfile
    screen_id = data_dict.get('ScreenId')
    permission_type = data_dict.get('PermissionType').lower()
    entity_id = data_dict.get('EntityId')

    payload = {
        "screenId": int(screen_id),
        "permissionType": permission_type,
        "action": action,
    }

    if entity_type == 'USER':
        user_ids = [int(uid.strip()) for uid in str(entity_id).split(',')]
        payload["userIds"] = user_ids
    elif entity_type == 'GROUP':
        group_ids = [int(gid.strip()) for gid in str(entity_id).split(',')]
        payload["groupIds"] = group_ids
    
    return payload

def format_payload_for_api_registry_upsert(data_dict, correlation_key):
    action = data_dict.get('Action', '').lower()
    is_active = True if action == 'add' else False
    payload = {
        "name": data_dict.get('Name'),
        "uri": data_dict.get('URI'),
        "httpMethodSupported": data_dict.get('HttpMethod'),
        "permissionType": data_dict.get('PermissionType'),
        "isActive": is_active,
    }
    return payload

def format_payload_for_screen_api_assignment(data_dict, correlation_key):
    action = data_dict.get('Action', '').lower()
    is_active = True if action == 'assign' else False
    payload = {
        "screenId": int(data_dict.get('ScreenId')),
        "apiId": int(data_dict.get('APIId')),
        "isActive": is_active
    }
    return payload
def get_applicable_rateplans(attach_all_rateplans, list_of_rateplans, policy_type, hotel_id):
    """
    Determines the applicable rate plans based on the attach_all_rateplans flag and provided list_of_rateplans.
    """
    if attach_all_rateplans:
        # Fetch all rate plans for the given policy type and hotel ID
        rateplancodes = fetch_all_rateplans(policy_type, hotel_id)
        if rateplancodes:
            return [{'ingoRateplanId': rateplan} for rateplan in rateplancodes]
    elif list_of_rateplans:
        # Use the provided list of rate plans
        return [{'ingoRateplanId': rateplan} for rateplan in list_of_rateplans]
    return []  # Return an empty list if no rate plans are applicable

def format_payload_for_hotel_user_assignment(data_dict):
    """
    Format the payload for hotel user assignment operations

    Args:
        data_dict: Dictionary containing hotel user assignment data

    Returns:
        dict: Formatted payload for the gRPC call
    """
    user_id = int(data_dict.get('HotelCloudUserId'))
    user_email = data_dict.get('HotelCloudUserEmail', '')
    hotel_code = data_dict.get('HotelCloudHotelCode', '')
    action = data_dict.get('Action', '').lower()
    
    # Convert single hotel code to list (as per proto definition)
    hotel_codes = [hotel_code.strip()]
    
    payload = {
        "UserId": user_id,
        "UserEmail": user_email,
        "HotelCodes": hotel_codes,
        "Action": action
    }
    
    return payload

def format_payload_for_user_deactivation(data_dict):
    """
    Format the payload for user deactivation operations

    Args:
        data_dict: Dictionary containing user deactivation data

    Returns:
        dict: Formatted payload for the gRPC call
    """
    auth_id = data_dict.get('auth_id', '')
    
    payload = {
        "auth_id": auth_id
    }
    
    return payload

def format_payload_for_hotel_sync(data_dict, correlation_key):
    """
    Format the payload for hotel sync operations

    Args:
        data_dict: Dictionary containing hotel sync data with mandatory fields:
                  IngoHotelId, MMTHotelId
        correlation_key: Unique identifier for tracking the request

    Returns:
        dict: Formatted payload for the gRPC call matching SyncHotelRequest proto
    """
    payload = {
        "ingoHotelId": data_dict.get('IngoHotelId', ''),
        "mmtHotelId": data_dict.get('MMTHotelId', '')
    }
    
    return payload