import uuid
from utils.logger import Logger
from bulk_uploader.common_helpers import format_payload_for_screen_registry_upsert, format_payload_for_screen_assignment, format_payload_for_api_registry_upsert, format_payload_for_screen_api_assignment, format_payload_for_hotel_user_assignment, format_payload_for_user_deactivation, format_payload_for_hotel_sync
from hotel_cloud_service.grpc_hotel_cloud_heimdall_client import HotelCloudHeimdallClient
from hotel_cloud_service.grpc_hotel_cloud_content_client import HotelCloudContentClient

api_logger = Logger(logger='inventoryAPILogger')

def save_screen_registry_upsert(data_dict):
    try:
        correlation_key = str(uuid.uuid4())
        action = data_dict.get('Action', '').upper()

        api_logger.info(
            message="Bulk uploader triggered for screen registry upsert with action: %s for client: %s, screen_name: %s, correlation_key: %s" % (
                action.lower(), 
                data_dict.get('Client', 'N/A'), 
                data_dict.get('ScreenName', 'N/A'), 
                correlation_key
            ),
            log_type='ingoibibo',
            bucket='bulk_uploader',
            stage='bulk_uploader.external_functions3.save_screen_registry_upsert'
        )

        payload = format_payload_for_screen_registry_upsert(data_dict, correlation_key)
        api_logger.info(
            message="Formatted payload for screen registry upsert: %s, correlation_key: %s" % (
                payload, correlation_key
            ),
            log_type='ingoibibo',
            bucket='bulk_uploader',
            stage='bulk_uploader.external_functions3.save_screen_registry_upsert'
        )
        
        htlcld_service = HotelCloudHeimdallClient()
        # This method name 'upsert_screen_registry' is an assumption based on the proto RPC name.
        # It will be defined in Step 8.
        response = htlcld_service.upsert_screen_registry(payload, data_dict) # Passing data_dict for potential metadata like user_id

        # Error handling based on UpsertScreenRegistryResponse proto, assuming response is a dict
        error_obj = response.get('error')
        if error_obj and error_obj.get('errorCode'):
            api_logger.error(
                message="Screen registry upsert failed for client: %s, screen_name: %s. ErrorCode: %s, ErrorMessage: %s, correlation_key: %s" % (
                    data_dict.get('Client', 'N/A'),
                    data_dict.get('ScreenName', 'N/A'),
                    error_obj.get('errorCode'),
                    error_obj.get('errorMessage'),
                    correlation_key
                ),
                log_type='ingoibibo',
                bucket='bulk_uploader',
                stage='bulk_uploader.external_functions3.save_screen_registry_upsert'
            )
            return [], "Failed, errorCode: %s, message: %s" % (error_obj.get('errorCode'), error_obj.get('errorMessage'))
        elif not response.get('success'): # Check success field from dict
            error_message = "Unknown error during screen registry upsert."
            if error_obj and error_obj.get('errorMessage'): # Check error message even if no specific code
                error_message = error_obj.get('errorMessage')
            
            api_logger.error(
                message="Screen registry upsert failed for client: %s, screen_name: %s. Success flag false or missing. Message: %s, correlation_key: %s" % (
                    data_dict.get('Client', 'N/A'),
                    data_dict.get('ScreenName', 'N/A'),
                    error_message,
                    correlation_key
                ),
                log_type='ingoibibo',
                bucket='bulk_uploader',
                stage='bulk_uploader.external_functions3.save_screen_registry_upsert'
            )
            return [], "Failed: %s" % error_message

        api_logger.info(
            message="Screen registry upsert successful for client: %s, screen_name: %s, correlation_key: %s" % (
                data_dict.get('Client', 'N/A'),
                data_dict.get('ScreenName', 'N/A'),
                correlation_key
            ),
            log_type='ingoibibo',
            bucket='bulk_uploader',
            stage='bulk_uploader.external_functions3.save_screen_registry_upsert'
        )
        return [], "Success"

    except Exception as e:
        message = 'Exception occurred while performing %s for screen registry (Client: %s, ScreenName: %s): %s' % (
            data_dict.get("Action", "unknown action").lower(), 
            data_dict.get('Client', 'N/A'), 
            data_dict.get('ScreenName', 'N/A'), 
            str(e)
        )
        api_logger.critical(
            message=message,
            log_type='ingoibibo',
            bucket='bulk_uploader',
            stage='bulk_uploader.external_functions3.save_screen_registry_upsert'
        )
        return [], "Error occurred: %s" % str(e)

def save_screen_assignment(data_dict):
    try:
        correlation_key = str(uuid.uuid4())
        action = data_dict.get('Action', '').upper()
        entity_type = data_dict.get('EntityType', '').upper()
        entity_id = data_dict.get('EntityId', '')

        if action not in ['ASSIGN', 'UNASSIGN']:
            return [], "Invalid action: %s. Must be 'ASSIGN' or 'UNASSIGN'." % action

        if entity_type not in ['USER', 'GROUP']:
            return [], "Invalid entity type: %s. Must be 'USER' or 'GROUP'." % entity_type

        api_logger.info(
            message="Bulk uploader triggered for screen assignment (%s) to %s %s with correlation_key: %s" % (
                action.lower(), entity_type.lower(), entity_id, correlation_key
            ),
            log_type='ingoibibo',
            bucket='bulk_uploader',
            stage='bulk_uploader.external_functions3.save_screen_assignment'
        )

        payload = format_payload_for_screen_assignment(data_dict, correlation_key)
        api_logger.info(
            message="Formatted payload for screen assignment: %s, correlation_key: %s" % (
                payload, correlation_key
            ),
            log_type='ingoibibo',
            bucket='bulk_uploader',
            stage='bulk_uploader.external_functions3.save_screen_assignment'
        )
        
        htlcld_service = HotelCloudHeimdallClient()
        response = htlcld_service.manage_screen_assignment(payload, data_dict, entity_type) 

        error_obj = response.get('error')
        if error_obj:
            error_obj_code = error_obj.get('code', '') or error_obj.get('errorCode', '')
            if error_obj_code:
                return [], "Failed, errorCode: %s, message: %s" % (error_obj_code, error_obj.get('message', '') or error_obj.get('errorMessage', ''))
        return [], "Success"

    except Exception as e:
        message = 'Exception occurred while %sing screen assignment: %s' % (
            data_dict.get("Action", "").lower(), str(e)
        )
        api_logger.critical(
            message=message,
            log_type='ingoibibo',
            bucket='bulk_uploader',
            stage='bulk_uploader.external_functions3.save_screen_assignment'
        )
        return [], "Error occurred: %s" % str(e)

def save_api_registry_upsert(data_dict):
    try:
        correlation_key = str(uuid.uuid4())
        action = data_dict.get('Action', '').lower()

        if action not in ['add', 'remove']:
            return [], "Invalid action: %s. Must be 'add' or 'remove'." % action

        api_logger.info(
            message="Bulk uploader triggered for api registry upsert with action: %s, correlation_key: %s" % (
                action, correlation_key
            ),
            log_type='ingoibibo',
            bucket='bulk_uploader',
            stage='bulk_uploader.external_functions3.save_api_registry_upsert'
        )

        payload = format_payload_for_api_registry_upsert(data_dict, correlation_key)
        api_logger.info(
            message="Formatted payload for api registry upsert: %s, correlation_key: %s" % (
                payload, correlation_key
            ),
            log_type='ingoibibo',
            bucket='bulk_uploader',
            stage='bulk_uploader.external_functions3.save_api_registry_upsert'
        )
        htlcld_service = HotelCloudHeimdallClient()
        response = htlcld_service.upsert_api_registry(payload, data_dict)

        error_obj = response.get('error')
        if error_obj:
            error_obj_code = error_obj.get('code', '') or error_obj.get('errorCode', '')
            if error_obj_code:
                return [], "Failed, errorCode: %s, message: %s" % (error_obj_code, error_obj.get('message', '') or error_obj.get('errorMessage', ''))
        return [], "Success"

    except Exception as e:
        message = 'Exception occurred while %sing api registry: %s' % (
            data_dict.get("Action", "").lower(), str(e)
        )
        api_logger.critical(
            message=message,
            log_type='ingoibibo',
            bucket='bulk_uploader',
            stage='bulk_uploader.external_functions3.save_api_registry_upsert'
        )
        return [], "Error occurred: %s" % str(e)

def save_screen_api_assignment(data_dict):
    try:
        correlation_key = str(uuid.uuid4())
        action = data_dict.get('Action', '').lower()

        if action not in ['assign', 'unassign']:
            return [], "Invalid action: %s. Must be 'assign' or 'unassign'." % action

        api_logger.info(
            message="Bulk uploader triggered for screen API assignment with action: %s, correlation_key: %s" % (
                action, correlation_key
            ),
            log_type='ingoibibo',
            bucket='bulk_uploader',
            stage='bulk_uploader.external_functions3.save_screen_api_assignment'
        )

        payload = format_payload_for_screen_api_assignment(data_dict, correlation_key)
        api_logger.info(
            message="Formatted payload for screen API assignment: %s, correlation_key: %s" % (
                payload, correlation_key
            ),
            log_type='ingoibibo',
            bucket='bulk_uploader',
            stage='bulk_uploader.external_functions3.save_screen_api_assignment'
        )
        htlcld_service = HotelCloudHeimdallClient()
        response = htlcld_service.manage_screen_api_assignment(payload, data_dict)

        error_obj = response.get('error')
        if error_obj:
            error_obj_code = error_obj.get('code', '') or error_obj.get('errorCode', '')
            if error_obj_code:
                return [], "Failed, errorCode: %s, message: %s" % (error_obj_code, error_obj.get('message', '') or error_obj.get('errorMessage', ''))
        return [], "Success"

    except Exception as e:
        message = 'Exception occurred while %sing screen API assignment: %s' % (
            data_dict.get("Action", "").lower(), str(e)
        )
        api_logger.critical(
            message=message,
            log_type='ingoibibo',
            bucket='bulk_uploader',
            stage='bulk_uploader.external_functions3.save_screen_api_assignment'
        )
        return [], "Error occurred: %s" % str(e)

def save_hotel_user_assignment(data_dict):
    """
    Save hotel user assignment data by calling the hotel user assignment RPC

    Args:
        data_dict: Dictionary containing hotel user assignment data with mandatory fields:
                  HotelCloudUserId, HotelCloudUserEmail, HotelCloudHotelCode, Action

    Returns:
        tuple: (list of affected IDs, message)
    """
    try:
        user_id = data_dict.get('HotelCloudUserId', '')
        user_email = data_dict.get('HotelCloudUserEmail', '')
        hotel_code = data_dict.get('HotelCloudHotelCode', '')
        action = data_dict.get('Action', '').lower()

        # Validate action
        if action not in ['assign', 'unassign']:
            return [], "Invalid action: %s. Must be 'assign' or 'unassign'." % action

        api_logger.info(
            message="Bulk uploader triggered for hotel user %s with user_id: %s, hotel_code: %s" % (
                action, user_id, hotel_code
            ),
            log_type='ingoibibo',
            bucket='bulk_uploader',
            stage='bulk_uploader.external_functions3.save_hotel_user_assignment'
        )

        # Prepare payload
        payload = format_payload_for_hotel_user_assignment(data_dict)

        # Call gRPC service
        htlcld_service = HotelCloudContentClient()
        response = htlcld_service.assign_hotels_to_users(payload, data_dict)

        error_obj = response.get('Errors')
        if error_obj and error_obj.get('errorCode'):
            error_code = error_obj.get('errorCode', '')
            error_message = error_obj.get('errorMessage', '')
            return [], "Failed, errorCode: %s, message: %s" % (error_code, error_message)
        
        success = response.get('Success', False)
        message = response.get('Message', 'Unknown response')
        
        if success:
            return [], "Success: %s" % message
        else:
            return [], "Failed: %s" % message

    except Exception as e:
        message = 'Exception occurred while %sing hotel user assignment: %s' % (
            data_dict.get("Action", "").lower(), str(e)
        )
        api_logger.critical(
            message=message,
            log_type='ingoibibo',
            bucket='bulk_uploader',
            stage='bulk_uploader.external_functions3.save_hotel_user_assignment'
        )
        return [], "Error occurred: %s" % str(e)

def save_user_deactivation(data_dict):
    """
    Save user deactivation data by calling the user deactivation RPC

    Args:
        data_dict: Dictionary containing user deactivation data with mandatory field: auth_id

    Returns:
        tuple: (list of affected IDs, message)
    """
    try:
        api_logger = Logger(logger='inventoryAPILogger')
        
        auth_id = data_dict.get('auth_id', '')
        
        if not auth_id:
            return [], "auth_id is required"

        api_logger.info(
            message="Bulk uploader triggered for user deactivation with auth_id: %s" % auth_id,
            log_type='ingoibibo',
            bucket='bulk_uploader',
            stage='bulk_uploader.external_functions3.save_user_deactivation'
        )

        payload = format_payload_for_user_deactivation(data_dict)
        htlcld_service = HotelCloudHeimdallClient()
        response = htlcld_service.deactivate_user(payload, data_dict)

        error_obj = response.get('error')
        if error_obj:
            error_code = error_obj.get('errorCode', '')
            error_message = error_obj.get('errorMessage', '')
            if error_code:
                return [], "Failed, errorCode: %s, message: %s" % (error_code, error_message)
        
        success = response.get('success', False)
        if success:
            return [auth_id], "User deactivation successful"
        else:
            return [], "User deactivation failed"

    except Exception as e:
        message = 'Exception occurred while deactivating user: %s' % str(e)
        api_logger.critical(
            message=message,
            log_type='ingoibibo',
            bucket='bulk_uploader',
            stage='bulk_uploader.external_functions3.save_user_deactivation'
        )
        return [], "Error occurred: %s" % str(e)

def save_hotel_sync(data_dict):
    """
    Save hotel sync data by calling the hotel sync RPC

    Args:
        data_dict: Dictionary containing hotel sync data with mandatory fields:
                  IngoHotelId, MMTHotelId

    Returns:
        tuple: (list of affected IDs, message)
    """
    try:
        correlation_key = str(uuid.uuid4())
        ingo_hotel_id = data_dict.get('IngoHotelId', '')
        mmt_hotel_id = data_dict.get('MMTHotelId', '')

        api_logger.info(
            message="Bulk uploader triggered for hotel sync with IngoHotelId: %s, MMTHotelId: %s, correlation_key: %s" % (
                ingo_hotel_id, mmt_hotel_id, correlation_key
            ),
            log_type='ingoibibo',
            bucket='bulk_uploader',
            stage='bulk_uploader.external_functions3.save_hotel_sync'
        )

        payload = format_payload_for_hotel_sync(data_dict, correlation_key)
        api_logger.info(
            message="Formatted payload for hotel sync: %s, correlation_key: %s" % (
                payload, correlation_key
            ),
            log_type='ingoibibo',
            bucket='bulk_uploader',
            stage='bulk_uploader.external_functions3.save_hotel_sync'
        )
        
        htlcld_service = HotelCloudContentClient()
        response = htlcld_service.manage_hotel_sync(payload, data_dict)

        error_obj = response.get('error')
        if error_obj:
            error_obj_code = error_obj.get('code', '')
            if error_obj_code:
                return [], "Failed, errorCode: %s, message: %s" % (error_obj_code, error_obj.get('message', ''))
        
        # Check for successful response with hotel cloud hotel ID
        data_obj = response.get('data')
        if data_obj and data_obj.get('hotelCloudHotelId'):
            hotel_cloud_id = data_obj.get('hotelCloudHotelId')
            api_logger.info(
                message="Hotel sync successful. HotelCloudHotelId: %s, correlation_key: %s" % (
                    hotel_cloud_id, correlation_key
                ),
                log_type='ingoibibo',
                bucket='bulk_uploader',
                stage='bulk_uploader.external_functions3.save_hotel_sync'
            )
            return [hotel_cloud_id], "Success: Hotel synced with HotelCloudHotelId: %s" % hotel_cloud_id
        
        return [], "Success"

    except Exception as e:
        message = 'Exception occurred while syncing hotel (IngoHotelId: %s, MMTHotelId: %s): %s' % (
            data_dict.get("IngoHotelId", "N/A"), 
            data_dict.get("MMTHotelId", "N/A"), 
            str(e)
        )
        api_logger.critical(
            message=message,
            log_type='ingoibibo',
            bucket='bulk_uploader',
            stage='bulk_uploader.external_functions3.save_hotel_sync'
        )
        return [], "Error occurred: %s" % str(e) 