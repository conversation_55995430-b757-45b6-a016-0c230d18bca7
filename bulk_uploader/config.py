from ingouser.models import User

from api.v1.bank_details.bulk_uploader_functions.bulk_add_bacc_handler import bulk_update_finance_details, \
    validate_bulk_addition_of_bank_detail, validate_bulk_addition_allowed_or_not, bulk_upload_bank_status
from api.v1.bank_details.bulk_uploader_functions.bulk_push_inactive_vendor import validate_bulk_push_inactive_vendor, \
    bulk_push_inactive_vendors
from bulk_uploader.bulk_uploaders import Type1BulkUploader, BaseBulkUploader
from bulk_uploader.external_functions import (update_inventory, update_inventory_validator, create_agreement_mapping, \
                                              update_amendment_policy, room_bulk_creation, rateplan_bulk_creation_new,
                                              cancellation_policy_bulk_creation, \
                                              convert_cancellation_policy_csv, update_booking_model,
                                              restrictions_bulk_uploader_validator, \
                                              restrictions_bulk_uploader, upload_prewarm_config,
                                              price_derivative_bulk_uploader_validator, \
                                              price_derivative_bulk_uploader, ingo_express_bulk_uploader,
                                              cancel_booking_with_cancellation_time_validator, \
                                              cancel_booking_with_cancellation_time, add_new_affiliate,
                                              add_new_affiliate_contract_validator, \
                                              add_new_affiliate_contract, group_booking_bulk_uploader,
                                              remove_group_booking_hotels_bulk_uploader, \
                                              default_data_bulk_uploader_validator, default_data_bulk_uploader,
                                              reject_pending_account, first_booking_promotion_validator, \
                                              day_based_promotion_validator, day_based_promotion_save, \
                                              long_stay_promotion_validator, long_stay_promotion_save, \
                                              basic_promotion_validator, basic_promotion_save, \
                                              early_bird_promotion_validator, early_bird_promotion_save, \
                                              last_minute_promotion_validator, last_minute_promotion_save, \
                                              free_nights_promotion_save, free_nights_promotion_validator, \
                                              fnd_kitchen_space_update_save, create_kitchen_space_fnd_validator,
                                              rateplan_commission_update, \
                                              convert_rateplan_commission_update_csv, \
                                              custom_promotion_bulk_uploader_validator,
                                              custom_promotion_bulk_uploader_save, map_old_vendor_and_sync, \
                                              fnd_kitchen_space_validator, create_kitchen_space_fnd_save,
                                              deactivate_gstn, \
                                              bulk_invoice_ocr_verified_to_approved, reseller_updation_validator,
                                              reseller_flag_updation, validate_policy_configs,
                                              update_hoteltravel_porting_flag, update_ingo_route_attributes,
                                              update_hourly_settings_for_hotel,
                                              hourly_ari_bulk_uploader, registration_document_bulk_uploader,
                                              mib_flag_bulk_uploader,
                                              update_reseller_status_for_hotel, reseller_update_validator,
                                              update_fullday_hourly_dayuse_rooms, validate_linkage_enabled,
                                              new_linkage_enable_uploader, locus_priority_update,
                                              locus_priority_validator, create_update_frn_uploader,
                                              rack_rates_create_update, pet_policy_update,
                                              validate_rack_rates, cpp_config_uploader, cpp_rates_res_uploader,
                                              validate_hotels_short_links, save_hotels_short_links, cm_repush_booking, \
                                              validate_rateplan_meal_and_contract_type_update, \
                                              meal_and_contract_type_update, checkin_checkout_time_bulk_uploader, \
                                              deboard_reseller, create_prebuy_config_reseller, set_hcp_deviation, \
                                              modify_hotel_travel_platform_flag_for_rate_plan_according_to_pre_buy_order, \
                                              validate_abso_reason_payload, save_abso_reason,
                                              validate_occasions_package_uploader, occasions_package_upload,
                                              validate_single_inventory_flag_payload, save_single_inventory_flag,
                                              upsert_prebuy_order, upsert_blackout_dates,save_permission_create_update,save_group_create_update,save_user_group_association,save_permission_assignment,
                                              directly_enable_is_chat_access_and_is_chat_enable_flags_for_hotel,
                                              validate_is_chat_enable_flag_update_payload,
                                              validate_is_chat_access_flag_update_payload,
                                              update_is_chat_enabled_flag_for_hotel,
                                              validate_save_host_details_uploader, save_host_details,
                                              validate_tag_host_to_hotel_payload, tag_host_to_hotel_uploader,
                                              update_is_chat_access_flag_for_hotel, validate_mmt_black_control_payload,
                                              mmt_black_control_uploader, validate_mmt_black_uploader_new_payload,
                                              mmt_black_uploader_new, validate_inclusion_grammar_payload,
                                              update_grammar_in_services, grammar_refresh_in_async,
                                              dl_unlink_bulk_uploader,
                                              save_host_chain_update_data, validate_host_chain_update_data,
                                              save_caretaker_details, validate_caretaker_update,
                                              validate_gstn_assurance_payload, update_penalty_in_gstn_assurance,
                                              fcnr_basic_promotion_save, fcnr_custom_promotion_bulk_uploader_save,
                                              fbp_promotion_save, validate_pet_policy,
                                              validate_mmt_spot_light, mmt_spot_light_update,
                                              validate_mmt_spot_light_enable_hotel, mmt_spot_light_enable_hotel,
                                              validate_mmt_spotlight_disenroll, mmt_spot_light_disenroll,
                                              validate_create_update_lmr_uploader, create_update_lmr_uploader,
                                              save_hotel_partner_details, validate_hotel_partner_details,
                                              validate_gcc_contracting_payload, set_gcc_contracting_flag,
                                              update_non_staff_user_status, validate_user_status_update,
                                              validate_user_contact_details_update, update_user_contact_details,
                                              validate_gcc_contracting_payload, set_gcc_contracting_flag,
                                              validate_logout_request, logout_users,
                                              validate_derby_pull_flag_payload, set_derby_pull_flag,
                                              validate_hotel_chain_mapping_payload, save_hotel_chain_mapping_data,
                                              validate_gstn_rejection_bulk_uploader, validate_hotel_policy, save_hotel_policy,
                                              validate_derby_ratePlan_inclusion_suppression_payload, set_derby_ratePlan_desc_inclusion_suppression_flag)


from bulk_uploader.external_functions2 import (hotel_detail_flag_bit_modifier, update_user_roles,mmt_black_uploader,
                                               create_hotel_cloud_booking,upsert_new_cancellation_policy,
                                               vcc_bulk_uploader_handler)
from bulk_uploader.external_functions3 import (save_screen_registry_upsert, save_screen_assignment, save_screen_api_assignment, save_api_registry_upsert, save_hotel_user_assignment, save_user_deactivation, save_hotel_sync)
from common.campaign_manager.bulk_uploader_custom_functions import validate_campaign_info, save_campaign_related_info, \
    validate_campaign_entity_mapping, save_campaign_entity_mapping, validate_disenroll_bulk_uploader, \
    terminate_campaign_entity_mapping, validate_campaign_hotel_mapping, save_campaign_hotel_mapping_v2, \
    save_campaign_v2, update_campaign_v2, validate_campaign_master, validate_update_existing_campaign, \
    participate_in_campaign_v2, validate_modification_of_campaign_hotel_mapping, terminate_campaign_entity_mapping_v2, map_hotel_id_to_mmt_id
from common.models import CampaignRelatedInfo, CampaignEntityMapping, CampaignMaster, Caretaker, ProgramEntityMapping
from corporate_gstn.resources import approve_ocr_verified_gstn_invoices

from hotels.models import PerformanceLinkBonus, VendorMapping, HotelDetail, HotelOfferCondition, \
    HotelAgreementMapping, RatePlan, CancellationRules, RoomDetail, CorporateBookingInfo, ChangeRequest, GSTDetail, \
    LinkedRateRule, HotelAdminUser
from hotels.models.ingomap_models import HotelRoute
from hotels.models.user_management import HostProfile, UserProfile
from hotels.models import HotelUserLink
from hotels.models.inventory_bulk_uploader import InventoryBulkUploader
from hotels.models.vendor_detail import validate_vendor_mapping, BankAccountDetail, VendorDetail
from hotels.models.performance_link_bonus import (validate_plb, validate_update_plb, validate_vdi,
                                                  validate_update_vdi, validate_hotel_detail_vdi,
                                                  validate_account_rejection)
from hotels.models.hoteldetail import validate_hotel_detail, SpaceDetail, RegistrationDocument, HostChain
from constants import RESTRICTIONS_BULK_UPLOADER_VENDORS, FND_KITCHEN_FIELDS
from hotels.models.prewarm_cache_config_manager import PreWarmCacheConfig
from hotels.models.offline_bookings import PriceDerivative
from hotels.models.default_data import DefaultData
from hotels.models.sales_related_models import HotelCancellation
from hotels.models.affiliate_contract import Affiliates, AffiliateContracts
from hotels.models.group_bookings import GroupBookingHotelConfig, GroupBookingPriorityConfig

from bulk_uploader.constants import COMMON_PROMOTION_FIELDS, FREE_NIGHTS_FIELDS, PROMOTION_BULK_LINE_LIMIT, \
    PROMOTION_BULK_RESTRICTED_TIME, CUSTOM_PROMOTION_FIELDS, NEW_FND_COLUMNS, FC_NR_FIELDS, FBP_FIELDS
from hotels import hotelchoice
from hotels.models.basic_models import Services
from hotels.services.helpers.bulk_uploader.methods import create_services_for_hotel, validate_services_data
from hotels.models.policy_rules_models import PolicyConfigV2, HotelPolicyMappingV2
from hotel_cloud_service.bulk_uploader_validator.upsert_prebuy_config_validator import validate_upsert_prebuy_config_payload
from hotel_cloud_service.bulk_uploader_validator.create_hotelcloud_booking_validator import validate_create_hotelcloud_booking_payload
from hotel_cloud_service.bulk_uploader_validator.validate_upsert_prebuy_order import validate_upsert_prebuy_order
from hotel_cloud_service.bulk_uploader_validator.validate_upsert_blackout_dates import validate_upsert_blackout_dates
from hotel_cloud_service.bulk_uploader_validator.validate_permission_create_update import validate_permission_create_update
from hotel_cloud_service.bulk_uploader_validator.validate_group_create_update import validate_group_create_update
from hotel_cloud_service.bulk_uploader_validator.validate_user_group_assignment import validate_user_group_assignment
from hotel_cloud_service.bulk_uploader_validator.validate_permission_assignment import validate_permission_assignment
from hotel_cloud_service.bulk_uploader_validator.validate_screen_registry_upsert import validate_screen_registry_upsert
from hotel_cloud_service.bulk_uploader_validator.validate_screen_assignment import validate_screen_assignment
from hotel_cloud_service.bulk_uploader_validator.validate_screen_api_assignment import validate_screen_api_assignment
from hotel_cloud_service.bulk_uploader_validator.validate_api_registry_upsert import validate_api_registry_upsert
from hotel_cloud_service.bulk_uploader_validator.validate_hotel_user_assignment import validate_hotel_user_assignment
from hotel_cloud_service.bulk_uploader_validator.validate_user_deactivation import validate_user_deactivation
from hotel_cloud_service.bulk_uploader_validator.validate_hotel_sync import validate_hotel_sync


BULK_UPLOADER_CONFIG = [
    {'id': 1, 'name': 'PLB Create', 'handle': 'plb_create', 'auth_group': ['Admin Booking Group'],
     'auth_group2': ['ADMIN_BOOKING_GROUP'],
     'sample_file': 'https://gos3.ibcdn.com/PLBUploader_sample.csv53c44c51b05411e884a70c4de9d4efa6.csv',
     'operation': 'add', 'model': PerformanceLinkBonus, 'validators': [validate_plb], 'request_users': ['user'],
     'upload_restricted_time': 0, 'bulk_uploader_class': Type1BulkUploader},

    {'id': 2, 'name': 'PLB Update', 'handle': 'plb_update', 'auth_group': ['Admin Booking Group'],
     'auth_group2': ['ADMIN_BOOKING_GROUP'],
     'sample_file': 'https://gos3.ibcdn.com/e27d0f35fd4011e8afa70c4de9d4efa6.csv',
     'primary_key': ['plbcode'], 'operation': 'change', 'model': PerformanceLinkBonus,
     'validators': [validate_update_plb],
     'fields': ['hotel', 'plbcode', 'isactive', 'ruleenddate', 'checkin_end_date', 'plb_adjusment_type', 'plb_info'],
     'upload_restricted_time': 0, 'request_users': ['user']},

    {'id': 3, 'name': 'VDI Create', 'handle': 'vdi_create', 'auth_group': ['ROLE_SUPPLY_IND_CATEGORY'],
     'auth_group2': ['ROLE_SUPPLY_IND_CATEGORY'],
     'sample_file': 'https://gos3.ibcdn.com/2f3ed37e2ba211eeb55b0242ac140005.csv',
     'operation': 'add', 'model': PerformanceLinkBonus, 'validators': [validate_vdi], 'request_users': ['user'],
     'upload_restricted_time': 0, 'bulk_uploader_class': Type1BulkUploader},

    {'id': 4, 'name': 'VDI Update', 'handle': 'vdi_update', 'auth_group': ['ROLE_SUPPLY_IND_CATEGORY'],
     'auth_group2': ['ROLE_SUPPLY_IND_CATEGORY'],
     'sample_file': 'https://gos3.ibcdn.com/2c5c5ba82ba311eeb55b0242ac140005.csv',
     'primary_key': ['plbcode'], 'operation': 'change', 'model': PerformanceLinkBonus,
     'validators': [validate_update_vdi],
     'fields': ['hotel', 'plbcode', 'state', 'isactive', 'checkinblackoutdates', 'plb_rule_description'],
     'upload_restricted_time': 0, 'request_users': ['user']},

    {'id': 5, 'name': 'VDI at Hotel Detail Update', 'handle': 'vdi_hotel_detail_update',
     'auth_group': ['ROLE_SUPPLY_IND_CATEGORY'], 'auth_group2': ['ROLE_SUPPLY_IND_CATEGORY'],
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/0-2019-03-14_2019-03-14T18:54:27_7e124307-465c-11e9-9463-a0999b1713c7.csv',
     'primary_key': ['hotelcode'], 'operation': 'change', 'model': HotelDetail,
     'validators': [validate_hotel_detail_vdi],
     'fields': ['hotelcode', 'plb_model'], 'upload_restricted_time': 0,
     'request_users': ['user']},

    {'id': 6, 'name': 'Mapping Update', 'handle': 'mapping_update', 'auth_group': ['Admin Booking Group'],
     'auth_group2': ['ADMIN_BOOKING_GROUP'],
     'sample_file': 'https://gos3.ibcdn.com/PLBUploader_sample.csv53c44c51b05411e884a70c4de9d4efa6.csv',
     'primary_key': ['hotel', 'vendor'], 'operation': 'change', 'model': VendorMapping,
     'validators': [validate_vendor_mapping],
     'fields': ['hotel', 'vendor', 'isactive'],
     'upload_restricted_time': 0, 'request_users': ['user']},

    {'id': 7, 'name': 'Hotel Detail Update', 'handle': 'hotel_detail_update', 'auth_group': ['Admin Booking Group'],
     'auth_group2': ['ADMIN_BOOKING_GROUP'],
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/'
                    '2018-11-21_2018-11-21T13:13:30.221778_zcr_23c2b2b0-ed61-11e8-9b75-0242ac130002.csv',
     'primary_key': ['hotelcode'], 'operation': 'change', 'model': HotelDetail, 'validators': [validate_hotel_detail],
     'fields': ['zcr_threshold_percent', 'hotelcode', hotelchoice.IH_GST_SAVINGS_FLAG, 'checkintime', 'checkouttime'],
     'upload_restricted_time': 0, 'request_users': ['user']},

    {'id': 8, 'name': 'Offer Condition Status', 'handle': 'offer_cndtn_st', 'auth_group': ['Admin Booking Group'],
     'auth_group2': ['ADMIN_BOOKING_GROUP'],
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader'
                    '/0-2018-12-05_2018-12-05T20:31:57_b602a1e2-f89e-11e8-aa74-0242ac1e0002.csv',
     'primary_key': ['offercode'], 'operation': 'change', 'model': HotelOfferCondition, 'validators': [],
     'fields': ['offercode', 'isactive'], 'upload_restricted_time': 15, 'max_line_count': 1000},

    {'id': 9, 'name': 'Invoice Guaranteed', 'handle': 'invoice_hotel_detail_update',
     'auth_group': ['Admin Booking Group'], 'auth_group2': ['ADMIN_BOOKING_GROUP'],
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/invoice/invoice-guaranteed.csv',
     'primary_key': ['hotelcode'], 'operation': 'change', 'model': HotelDetail, 'validators': [],
     'fields': ['hotelcode', 'invoice_guaranteed'], 'upload_restricted_time': 0},

    {'id': 10, 'name': 'Inventory Restriction Update', 'handle': 'inv_res_update',
     'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT'],
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2024-05-06_2024-05-06T14:56:58.353165_Inventory_c97343a8-0b8a-11ef-899f-0242ac140104.csv',
     'request_users': ['user'], 'operation': 'add', 'model': InventoryBulkUploader,
     'validators': [update_inventory_validator],
     'fields': ['hotel_code', 'room_type_code', 'start_date', 'end_date', 'block', 'inventory'], 'upload_restricted_time': 15,
     'save': update_inventory, 'max_line_count': 5000},

    {'id': 11, 'name': 'Agreement Uploader', 'handle': 'hotels_agreement', 'auth_group': ['Admin Booking Group'],
     'auth_group2': ['ADMIN_BOOKING_GROUP'],
     'sample_file': 'https://gos3.ibcdn.com/039de52a9f4b11ea93550242ac110002.csv',
     'request_users': ['user'], 'operation': 'add', 'model': HotelAgreementMapping, 'validators': [],
     'fields': ['hotelcode', 'agreement_id'], 'upload_restricted_time': 0, 'save': create_agreement_mapping,
     'max_line_count': 1000},

    {'id': 12, 'name': 'Rateplan Update', 'handle': 'rateplan_update', 'auth_group': ['Admin Booking Group'],
     'auth_group2': ['ADMIN_BOOKING_GROUP'],
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2020-06-23_2020-06-23T23:22:58.372790_rateplan-update_5fb08008-b57a-11ea-9363-0242ac130002.csv',
     'request_users': ['user'], 'primary_key': ['rateplancode'], 'operation': 'change', 'model': RatePlan,
     'validators': [],
     'fields': ['rateplancode', 'isactive'], 'upload_restricted_time': 0,
     'max_line_count': 1000, 'generate_audit_log': True},

    {'id': 13, 'name': 'Amendment Policies Update', 'handle': 'amendment_policy', 'auth_group': ['Admin Booking Group'],
     'auth_group2': ['ADMIN_BOOKING_GROUP'],
     'sample_file': 'https://gos3.ibcdn.com/7a758c8873ec11eeb3650242ac140101.csv',
     'request_users': ['user'], 'operation': 'add', 'model': HotelDetail, 'validators': [],
     'fields': ['hotelcode', 'namechange', 'paxchange', 'mealplanchange', 'roomcategorychange',
                'datechange', 'hours', 'days'], 'upload_restricted_time': 0, 'save': update_amendment_policy,
     'max_line_count': 1000},

    {'id': 14, 'name': 'RatePlan Creation/Updation Uploader', 'handle': 'rateplan_creation',
     'auth_group': ['Admin Booking Group'], 'auth_group2': ['ADMIN_BOOKING_GROUP'],
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2025-01-30_rateplan_creation_updation_uploader_d52b5114-ded8-11ef-b16c-0242ac140105.csv',
     'request_users': ['user'], 'operation': 'add', 'model': RatePlan, 'validators': [],
     'fields': ['ingo_hotel_code', 'vendor_source_code', 'vendor_room_code', 'vendor_rateplan_code', 'rateplan_name',
                'meal_plan', 'description', 'promotion', 'delete_description', 'non_refundable',
                'tax_included', 'pay_at_hotel', 'is_active', 'is_staycation', 'is_only_rateplan_cug',
                'is_only_rateplan_hcp', 'is_package', 'contract_type', 'min_los', 'max_los', 'cut_off_days',
                'commission',
                'single_occ_rates', 'double_occ_rates', 'rates_start_date', 'rates_end_date', 'access_code',
                'only_rateplan_offers_flag', 'rp_booking_model', 'mybiz_emailids', 'mypartner_emailids', 'is_net_rate_model', 'cancellation_policy_code'],
     'upload_restricted_time': 0, 'save': rateplan_bulk_creation_new, 'max_line_count': 1000},

    {'id': 15, 'name': 'Cancellation Policy Creation Uploader', 'handle': 'cancel_policy_create',
     'auth_group': ['Admin Booking Group'], 'auth_group2': ['ADMIN_BOOKING_GROUP'],
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2021-05-31_2021-05-31T04:36:01.967876_cancellation_sample_csv_9a77718c-c19b-11eb-82bf-0242ac14005f.csv',
     'request_users': ['user'], 'operation': 'add', 'model': CancellationRules, 'validators': [],
     'manipulate_file': convert_cancellation_policy_csv,
     'fields': ['ingo_hotel_code', 'vendor_source_code', 'vendor_room_code', 'related_to', 'vendor_rateplan_code',
                'policy_start_date', 'policy_end_date', 'no_show_charges_in_percent',
                'offset_day', 'charge_type', 'charge_value'], 'upload_restricted_time': 0,
     'save': cancellation_policy_bulk_creation, 'max_line_count': 1000},

    {'id': 16, 'name': 'Room Creation/Updation Uploader', 'handle': 'room_creation',
     'auth_group': ['Admin Booking Group'], 'auth_group2': ['ADMIN_BOOKING_GROUP'],
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/room_update_create.csv',
     'request_users': ['user'], 'operation': 'add', 'model': RoomDetail, 'validators': [],
     'fields': ['ingo_hotel_code', 'vendor_source_code', 'vendor_room_code', 'name', 'is_active', 'ingo_room_type',
                'size', 'no_of_rooms',
                'vendor_room_view_code', 'vendor_bed_type_code', 'description',
                'base_adult_occupancy', 'base_child_occupancy', 'max_adult_occupancy', 'max_child_occupancy',
                'max_guest_occupancy',
                'is_bathroom_shared', 'is_bathroom_attached', 'is_smoking_allowed', 'extra_bed_type_child1',
                'extra_bed_type_child2', 'extra_bed_type_child3', 'extra_bed_type_adult', 'max_extra_beds_allowed',
                'is_extra_bed_enabled_synxis',
                'extra_bed_price_synxis'],
     'upload_restricted_time': 10, 'save': room_bulk_creation, 'max_line_count': 1000},

    {'id': 17, 'name': 'Booking Model Uploader', 'handle': 'booking_model_update', 'auth_group': [
        'ROLE_INGOMMT_PROD_SUPPORT', 'Role_Moderation_Agents'], 'auth_group2': [
        'ROLE_INGOMMT_PROD_SUPPORT', 'ROLE_MODERATION_AGENTS'],
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/booking_model_sample_data',
     'request_users': ['user'], 'operation': 'add', 'model': HotelDetail, 'validators': [],
     'fields': ['hotelcode', 'booking_model', 'rtb_request_validity', 'itb_request_validity',
                'preapproved_rtb_segments'], 'upload_restricted_time': 0, 'save': update_booking_model,
     'max_line_count': 1000},

    {'id': 18, 'name': 'Room Detail Update Uploader', 'handle': 'room_detail_update',
     'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT'],
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/0-2021-08-20_2021-08-20T13:43:12_76af708c-018e-11ec-b399-0242c0a80002.csv',
     'primary_key': ['roomtypecode'], 'operation': 'change', 'model': RoomDetail, 'validators': [],
     'fields': ['roomtypecode', 'roomtypename'], 'upload_restricted_time': 0
     },

    {'id': 19, 'name': 'Host Profile Update Uploader', 'handle': 'host_profile_update',
     'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT'],
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/0-2021-09-09_2021-09-09T16:24:06_40c8a51e-115c-11ec-bcec-0242ac140002.csv',
     'primary_key': ['user_id'], 'operation': 'change', 'model': HostProfile, 'validators': [],
     'fields': ['user_id', 'gender', 'about', 'is_custom_about_host'], 'upload_restricted_time': 0
     },

    {'id': 20, 'name': 'Restrictions Bulk Uploader', 'handle': 'restriction_uploader',
     'auth_group': ['ROLE_SUPPLY_IND_AUDIT','DAY_USE_ADMIN','ROLE_SUPPLY_IND_CATEGORY'],
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2021-12-06_sample_d5cd5b56-566b-11ec-a891-0242ac140004.csv',
     'request_users': ['user'], 'operation': 'add', 'model': InventoryBulkUploader,
     'validators': [restrictions_bulk_uploader_validator], 'fields': ['start_date', 'end_date', 'hotel_code',
                                                                      'room_type_code', 'rate_plan_code', 'block',
                                                                      'min_los', 'max_los', 'min_cutoff',
                                                                      'fixed_cutoff', 'max_cutoff', 'close_for_arrival',
                                                                      'close_for_departure', 'min_los_checkin',
                                                                      'max_los_checkin', 'contract_type'],
     'upload_restricted_time': 10, 'save': restrictions_bulk_uploader, 'max_line_count': 5000,
     'custom_fields': {"select_options": [{"label": "Choose Vendor", "choices": RESTRICTIONS_BULK_UPLOADER_VENDORS,
                                           "key": "vendor"}, {"label": "Contract Type", "choices": ["b2c"],
                                                              "key": "contract_type"}]}},

    {'id': 21, 'name': 'Pre warm cache uploader', 'handle': 'prewarm_conf_upload',
     'auth_group': [],
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2023-08-23_prewarmcache_1160d5b4-41a5-11ee-b0d8-0242ac140104.csv',
     'operation': 'add', 'model': PreWarmCacheConfig, 'validators': [],
     'fields': ['hotel_id', 'pax_list', 'los_list', 'advance_window'], 'upload_restricted_time': 0,
     'save': upload_prewarm_config
     },

    {'id': 22, 'name': 'Offline Booking Configuration Bulk Uploader', 'handle': 'bulk_direct_booking',
     'auth_group': ['ROLE_SUPPLY_IND_AUDIT', 'ROLE_INGOMMT_PROD_SUPPORT', 'ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_SUPPLY_IND_ALTACCO_ZM'],
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2025-02-24_offline_new_efe4cf56-f2ad-11ef-b836-0242ac140100.csv',
     'request_users': ['user'], 'operation': 'add', 'model': PriceDerivative,
     'validators': [price_derivative_bulk_uploader_validator], 'fields': ['hotel_code', 'offline_commission',
                                                                          'nett_markup', 'nett_markup_type', 'status', 'approver'],
     'upload_restricted_time': 10, 'save': price_derivative_bulk_uploader, 'max_line_count': 1000, 'batch_count': 100},

    {'id': 23, 'name': 'Corporate GSTN Invoice Bulk Rejection', 'handle': 'update_gstn_inv',
     'auth_group': ['ROLE_SUPPLY_IND_AUDIT'], 'auth_group2': ['ROLE_CORPORATE_MYBIZ_CHANNEL'],
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/0-2022-01-20_2022-01-20T16:54:09_7c626480-79e3-11ec-a2d5-0242ac140005.csv',
     'request_users': ['user'], 'operation': 'change', 'model': CorporateBookingInfo,
     'validators': [validate_gstn_rejection_bulk_uploader], 'primary_key': ['confirm_booking_id'],
     'fields': ['confirm_booking_id', 'qc_reject_reason', 'gst_invoice_status'], 'upload_restricted_time': 0,
     'max_line_count': 1000},

    {'id': 24, 'name': 'Ingo Express Migration Bulk Uploader', 'handle': 'express_migrate',
     'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT'],
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2022-02-02_sample_0b4ad33a-83a3-11ec-ac68-0242ac140003.csv',
     'request_users': ['user'], 'operation': 'add', 'model': InventoryBulkUploader,
     'validators': [], 'fields': ['hotelcode', 'rateplancodes', 'cancellation_policy'],
     'upload_restricted_time': 5, 'save': ingo_express_bulk_uploader, 'max_line_count': 1000, 'batch_count': 100},

    {'id': 25, 'name': 'Cancel Booking With Time', 'handle': 'cancel_booking',
     'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT'],
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2022-02-18_cancel_booking_with_time_3382e4d8-907c-11ec-a7e8-0242ac140003.csv',
     'request_users': ['user'], 'operation': 'add', 'model': HotelCancellation,
     'validators': [cancel_booking_with_cancellation_time_validator],
     'fields': ['vendor_booking_id', 'cancellation_time'],
     'upload_restricted_time': 5, 'save': cancel_booking_with_cancellation_time, 'max_line_count': 100,
     'batch_count': 10},

    {'id': 26, 'name': 'Add New Affiliate', 'handle': 'new_affiliate', 'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT'],
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2022-03-21_Affiliates_97ac8c9c-a90f-11ec-841a-0242ac140007.csv',
     'request_users': ['user'], 'operation': 'add', 'model': Affiliates,
     'validators': [], 'fields': ['name', 'segment'],
     'upload_restricted_time': 5, 'save': add_new_affiliate, 'max_line_count': 100, 'batch_count': 10},

    {'id': 27, 'name': 'Add New Affiliate Contract', 'handle': 'add_contract',
     'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT'],
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2022-03-21_Affiliates_contract_f482e672-a910-11ec-8e48-0242ac140007.csv',
     'request_users': ['user'], 'operation': 'add', 'model': AffiliateContracts,
     'validators': [add_new_affiliate_contract_validator],
     'fields': ['affiliate_segment', 'cug_segment', 'offer_proportion', 'inclusion_included'],
     'upload_restricted_time': 5, 'save': add_new_affiliate_contract, 'max_line_count': 100, 'batch_count': 10},

    {'id': 28, 'name': 'Group Booking Bulk Uploader', 'handle': 'group_booking_hotel',
     'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT', 'ROLE_SUPPLY_IND_CATEGORY',
                    'ROLE_SUPPLY_CHAINS_INTL_EXCLUSIVE_SUPPORT', 'ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_SUPPLY_IND_ALTACCO_ZM'],
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2025-02-24_group_new_fe1f5d3e-f2ad-11ef-b836-0242ac140100.csv',
     'request_users': ['user'], 'operation': 'add', 'model': GroupBookingHotelConfig,
     'validators': [], 'fields': ['hotelcode', 'group_booking_eligible', 'commission', 'ask_for_rate', 'approver', 'status'],
     'upload_restricted_time': 5, 'save': group_booking_bulk_uploader, 'max_line_count': 100, 'batch_count': 10},

    {'id': 29, 'name': 'Default data Bulk Uploader', 'handle': 'default_data', 'auth_group': ['ROLE_SUPPLY_IND_CATEGORY', 'ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_SUPPLY_IND_ZM',
                                                                                              'ROLE_SUPPLY_IND_AUDIT', "ROLE_INGOMMT_DEV_LEVEL_2"],
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/0-2022-05-18_2022-05-18T18:36:27_53ff04a6-d6ab-11ec-abf8-0242ac120002.csv',
     'request_users': ['user'], 'operation': 'add', 'model': DefaultData,
     'validators': [default_data_bulk_uploader_validator],
     'fields': ['chain_code', 'property_code', 'source_rateplancode', 'content_type_id', 'isactive', 'applicable_to',
                'meal_plan',
                'contracttype', 'long_description', 'commission_pah', 'commission_pas', 'commission_group_booking',
                'is_staycation', 'is_package', 'is_suppress_desc', 'partner_gross_to_net', 'is_net_rate_model',
                'is_commission_on_post_tax', 'is_only_rateplan_cug', 'is_only_rateplan_hcp',
                'only_rateplan_offers_flag',
                'access_code', 'approver'],
       'upload_restricted_time': 0,
      'save': default_data_bulk_uploader, 'batch_count': 100},

    {'id': 30, 'name': 'Bulk Account Rejection', 'handle': 'account_rejection',
     'auth_group': ['ROLE_SUPPLY_IND_AUDIT'],
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2022-06-29_2022-06-29T17:47:46.111882_account_rejection_7bfd5b90-f7a5-11ec-8c21-0242ac140003.csv',
     'request_users': ['user'],
     'operation': 'add', 'model': BankAccountDetail,
     'validators': [validate_account_rejection],
     'fields': ['id', 'reject_reason'], 'upload_restricted_time': 0, 'save': reject_pending_account,
     'max_line_count': 10000},

    {'id': 31, 'name': ' Remove Group Booking Hotels Bulk Uploader', 'handle': 'remove_grpbooking',
     'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT', 'ROLE_SUPPLY_IND_CATEGORY',
                    'ROLE_SUPPLY_CHAINS_INTL_EXCLUSIVE_SUPPORT'],
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2022-07-25_abc_e2a2d3d8-0be2-11ed-8de7-0242ac140007.csv',
     'request_users': ['user'], 'operation': 'add', 'model': GroupBookingPriorityConfig,
     'validators': [], 'fields': ['hotel_id'],
     'upload_restricted_time': 0, 'save': remove_group_booking_hotels_bulk_uploader, 'max_line_count': 1000},

    {'id': 32, 'name': 'Update Campaign Info', 'handle': 'campaign_info_update',
     'auth_group': ['ROLE_SUPPLY_IND_AUDIT', 'ROLE_SUPPLY_IND_CENTRAL', 'ROLE_SUPPLY_IND_CATEGORY'],
     'sample_file': 'https://gos3.ibcdn.com/99e21d40722911eda1c80a58a9feac02.csv',
     'request_users': ['user'],
     'operation': 'add', 'model': CampaignRelatedInfo,
     'validators': [validate_campaign_info],
     'fields': ['campaign_id', 'city_id', 'ap_window', 'max_slots'], 'upload_restricted_time': 10,
     'save': save_campaign_related_info,
     'max_line_count': 10000},

    {'id': 33, 'name': 'Create Campaign Entity Mapping', 'handle': 'cmp_ent_mapping',
     'auth_group': ['ROLE_SUPPLY_IND_AUDIT', 'ROLE_SUPPLY_IND_CENTRAL', 'ROLE_SUPPLY_IND_CATEGORY'],
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/0-2022-09-07_2022-09-07T12:27:49_62775b38-2e7a-11ed-b5ef-0242ac140005.csv',
     'request_users': ['user'],
     'operation': 'add', 'model': CampaignEntityMapping,
     'validators': [validate_campaign_entity_mapping],
     'fields': ['campaign_id', 'entity_type', 'entity_id', 'rpd_target_value'], 'upload_restricted_time': 10,
     'save': save_campaign_hotel_mapping_v2, 'max_line_count': 10000},

    {'id': 34, 'name': 'Basic Promotion Bulk Uploader', 'handle': 'basic_promo',
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2022-09-01_Bulk_Uploader_Basic_76b8aeca-29e4-11ed-9ab3-0242ac140006.csv',
     'user_manual': 'https://gos3.ibcdn.com/d33560a4673211ed91cc0a58a9feac02.pdf',
     'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT'],
     'request_users': ['user'],
     'operation': 'add', 'model': HotelOfferCondition,
     'validators': [basic_promotion_validator],
     'fields': COMMON_PROMOTION_FIELDS, 'upload_restricted_time': PROMOTION_BULK_RESTRICTED_TIME,
     'save': basic_promotion_save, 'bulk_uploader_class': Type1BulkUploader,
     'max_line_count': PROMOTION_BULK_LINE_LIMIT},

    {'id': 35, 'name': 'Day Based Promotion Bulk Uploader', 'handle': 'day_based_promo',
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2022-09-01_Bulk_Uploader_Day_Based_673d8a7e-29e4-11ed-9ab3-0242ac140006.csv',
     'user_manual': 'https://gos3.ibcdn.com/e2dd477e673211edb1f50a58a9feac02.pdf',
     'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT'],
     'request_users': ['user'],
     'operation': 'add', 'model': HotelOfferCondition,
     'validators': [day_based_promotion_validator],
     'fields': COMMON_PROMOTION_FIELDS + ['days_to_apply_promotion'],
     'upload_restricted_time': PROMOTION_BULK_RESTRICTED_TIME, 'save': day_based_promotion_save,
     'bulk_uploader_class': Type1BulkUploader, 'max_line_count': PROMOTION_BULK_LINE_LIMIT},

    {'id': 36, 'name': 'Long Stay Promotion Bulk Uploader', 'handle': 'long_stay_promo',
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2022-09-01_Bulk_Uploader_Long_Stay_71366dfc-29e4-11ed-9ab3-0242ac140006.csv',
     'user_manual': 'https://gos3.ibcdn.com/04ca9f58673311ed93450a58a9feac02.pdf',
     'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT'],
     'request_users': ['user'],
     'operation': 'add', 'model': HotelOfferCondition,
     'validators': [long_stay_promotion_validator],
     'fields': COMMON_PROMOTION_FIELDS + ['min_stay_duration'],
     'upload_restricted_time': PROMOTION_BULK_RESTRICTED_TIME, 'save': long_stay_promotion_save,
     'bulk_uploader_class': Type1BulkUploader, 'max_line_count': PROMOTION_BULK_LINE_LIMIT},

    {'id': 37, 'name': 'Early Bird Promotion Bulk Uploader', 'handle': 'early_bird_promo',
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2022-09-01_Bulk_Uploader_Early_Bird_53b52f48-29e4-11ed-9ab3-0242ac140006.csv',
     'user_manual': 'https://gos3.ibcdn.com/1393bb0a673311ed99420a58a9feac02.pdf',
     'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT'],
     'request_users': ['user'],
     'operation': 'add', 'model': HotelOfferCondition,
     'validators': [early_bird_promotion_validator],
     'fields': COMMON_PROMOTION_FIELDS + ['days_to_book_in_advance'],
     'upload_restricted_time': PROMOTION_BULK_RESTRICTED_TIME, 'save': early_bird_promotion_save,
     'bulk_uploader_class': Type1BulkUploader, 'max_line_count': PROMOTION_BULK_LINE_LIMIT},

    {'id': 38, 'name': 'Last Minute Promotion Bulk Uploader', 'handle': 'last_minute_promo',
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2022-09-01_Bulk_Uploader_Last_Minute_40baaaf8-29e4-11ed-9ab3-0242ac140006.csv',
     'user_manual': 'https://gos3.ibcdn.com/26387c1e673311ed83500a58a9feac02.pdf',
     'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT'],
     'request_users': ['user'],
     'operation': 'add', 'model': HotelOfferCondition,
     'validators': [last_minute_promotion_validator],
     'fields': COMMON_PROMOTION_FIELDS + ['last_minute_booking_days_before'],
     'upload_restricted_time': PROMOTION_BULK_RESTRICTED_TIME, 'save': last_minute_promotion_save,
     'bulk_uploader_class': Type1BulkUploader, 'max_line_count': PROMOTION_BULK_LINE_LIMIT},

    {'id': 39, 'name': 'Free Nights Promotion Bulk Uploader', 'handle': ' free_nights_promo',
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2022-09-01_Bulk_Uploader_Free_Nights_4bc0a6f0-29e4-11ed-9ab3-0242ac140006.csv',
     'user_manual': 'https://gos3.ibcdn.com/30752ca4673311ed950d0a58a9feac02.pdf',
     'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT'],
     'request_users': ['user'],
     'operation': 'add', 'model': HotelOfferCondition,
     'validators': [free_nights_promotion_validator],
     'fields': FREE_NIGHTS_FIELDS, 'upload_restricted_time': PROMOTION_BULK_RESTRICTED_TIME,
     'save': free_nights_promotion_save, 'bulk_uploader_class': Type1BulkUploader,
     'max_line_count': PROMOTION_BULK_LINE_LIMIT},

    # only for campaign types 1, 2, 3 NOT for 4
    {'id': 40, 'name': 'Campaign Entity Disenroll', 'handle': 'cmp_entity_change',
     'auth_group': ['ROLE_SUPPLY_IND_AUDIT', 'ROLE_SUPPLY_IND_CENTRAL', 'ROLE_SUPPLY_IND_CATEGORY', 'ROLE_INGOMMT_DEV_LEVEL_2'],
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/0-2022-09-20_2022-09-20T16:42:08_110b12de-38d5-11ed-9ff4-0242ac140005.csv',
     'request_users': ['user'],
     'operation': 'add', 'model': CampaignEntityMapping,
     'validators': [validate_disenroll_bulk_uploader],
     'fields': ['campaign_id', 'entity_id', 'entity_type'], 'upload_restricted_time': 10,
     'save': terminate_campaign_entity_mapping_v2, 'max_line_count': 10000},

    {'id': 41, 'name': 'Custom Promotion Bulk Uploader', 'handle': ' custom_promo',
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2022-11-15_custom_promotion_bulk_uploader_sample_22182b5a-64ab-11ed-a38c-0242ac140006.csv',
     'user_manual': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2023-01-03_custom_promotion_bulk_uploader_7d66ebce-8b32-11ed-9c58-0242ac140007.pdf',
     'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT'],
     'request_users': ['user'],
     'operation': 'add', 'model': HotelOfferCondition,
     'validators': [custom_promotion_bulk_uploader_validator],
     'fields': COMMON_PROMOTION_FIELDS + CUSTOM_PROMOTION_FIELDS,
     'upload_restricted_time': PROMOTION_BULK_RESTRICTED_TIME, 'save': custom_promotion_bulk_uploader_save,
     'bulk_uploader_class': Type1BulkUploader, 'max_line_count': 300},

    {'id': 42, 'name': 'Sync MMT and GI Vendors', 'handle': 'sync_mmt_gi_vendors',
     'sample_file': 'https://gos3.ibcdn.com/67a02f0481e011edb6ec0a58a9feac02.csv',
     'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT'],
     'request_users': ['user'],
     'operation': 'add', 'model': VendorDetail,
     'validators': [], 'fields': ['hotel_code'], 'upload_restricted_time': 10,
     'save': map_old_vendor_and_sync, 'max_line_count': 1000},
    {'id': 43, 'name': 'Kitchen Space Update to FND flow Bulk Uploader', 'handle': 'update_kitchen',
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2022-11-15_custom_promotion_bulk_uploader_sample_22182b5a-64ab-11ed-a38c-0242ac140006.csv',
     'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT'],
     'request_users': ['user'],
     'operation': 'add', 'model': SpaceDetail,
     'validators': [fnd_kitchen_space_validator],
     'fields': ['hotelcode'], 'upload_restricted_time': 10,
     'save': fnd_kitchen_space_update_save, 'bulk_uploader_class': Type1BulkUploader, 'max_line_count': 1000},
    {'id': 44, 'name': 'Food And Dining Main Uploader', 'handle': 'create_kitchen',
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2022-11-15_custom_promotion_bulk_uploader_sample_22182b5a-64ab-11ed-a38c-0242ac140006.csv',
     'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT', 'ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_SUPPLY_CHAINS_INTL_EXCLUSIVE_SUPPORT', 'ROLE_SUPPLY_IND_ALTACCO_ZM', 'ROLE_SUPPLY_CHAINS_INTL_CONTENT'],
     'request_users': ['user'],
     'operation': 'add', 'model': SpaceDetail,
     'validators': [],
     'fields': NEW_FND_COLUMNS, 'upload_restricted_time': 5,
     'batch_count': 20,
     'save': create_kitchen_space_fnd_save, 'bulk_uploader_class': Type1BulkUploader, 'max_line_count': 5000},
    {'id': 45, 'name': 'FAB - Bulk Add Bank Account', 'handle': 'bulk_add_bacc',
     'auth_group': ['ROLE_SUPPLY_CHAINS_INTL_BDM'],
     'sample_file': 'https://gos3.ibcdn.com/8631bb2671fb11ed8faa0a58a9feac02.csv',
     'request_users': ['user'],
     'operation': 'add', 'model': BankAccountDetail,
     'validators': [validate_bulk_addition_allowed_or_not, validate_bulk_addition_of_bank_detail],
     'fields': ['hotel_code', 'acc_no', 'acc_name', 'ifsc_code', 'bank_name', 'acc_type',
                'relationship_document'],
     'upload_restricted_time': 0,
     'save': bulk_update_finance_details, 'max_line_count': 10000},
    {'id': 46, 'name': 'FAB - Bulk Bank Approval', 'handle': 'update_bank_status',
     'auth_group': ['ROLE_QC_BANKAPPROVAL_FINANCE'],
     'sample_file': 'https://gos3.ibcdn.com/fccd3c9ea77311eda57e0242ac140005.csv',
     'request_users': ['user'],
     'operation': 'add', 'model': BankAccountDetail,
     'validators': [validate_bulk_addition_allowed_or_not],
     'fields': ['hotel_code', 'status', 'reject_reasons'], 'upload_restricted_time': 0,
     'save': bulk_upload_bank_status, 'max_line_count': 10000},

    {'id': 47, 'name': 'Rateplan Commission Update', 'handle': 'update_rp_commission',
     'auth_group': ['SELL_COMMISSION_USERNAME', 'ROLE_QC_BANKAPPROVAL_FINANCE', 'ROLE_HBC', 'ROLE_INGOMMT_DEV_LEVEL_2',
                    'ROLE_CORPORATE_MYBIZ_CHANNEL', 'ROLE_SUPPLY_IND_AUDIT_BANKDETAIL', 'TRAINERS',
                    'ROLE_SUPPLY_IND_ALTACCO_ZM', 'ROLE_SUPPLY_CHAINS_INTL_EXCLUSIVE_SUPPORT',
                    'ROLE_SUPPLY_CHAINS_INTL_RD', 'ROLE_SUPPLY_CHAINS_INTL_SUPPORT', 'ROLE_SUPPLY_CHAINS_INTL_TRAINER',
                    'ROLE_SUPPLY_CHAINS_INTL_ZM_OYO', 'ROLE_SUPPLY_IND_CENTRAL', 'ROLE_SUPPLY_IND_ANALYTICS',
                    'ROLE_SUPPLY_IND_AUDIT', 'ROLE_SUPPLY_IND_CONTRACTING', 'ROLE_SUPPLY_IND_CONTRACTING_SUPERVISOR',
                    'ROLE_SUPPLY_IND_MIB', 'ROLE_SUPPLY_IND_QC', 'ROLE_SUPPLY_IND_QC_BANKDETAIL',
                    'ROLE_SUPPLY_IND_QC_SUPERVISOR', 'ROLE_SUPPLY_IND_TRAINER',
                    'ROLE_SUPPLY_IND_TRAINER_FUNCTIONAL_TRAINERS', 'ROLE_OPERATIONS_HOLIDAY_SUPPLY_SUPERVISOR',
                    'ROLE_OPERATIONS_HOLIDAY_SUPPLY', 'ROLE_SUPPLY_CHAINS_INTL_BDM', 'ROLE_SUPPLY_CHAINS_INTL_ZM',
                    'ROLE_SUPPLY_CHAINS_INTL_EXCLUSIVE_TRAINER', 'ROLE_SUPPLY_IND_BDM'],
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2023-05-13_2023-05-13T09:49:57.148336_Sample_6b64361a-f145-11ed-9c48-0242ac140005.csv',
     'request_users': ['user'], 'operation': 'add', 'model': ChangeRequest, 'validators': [],
     'manipulate_file': convert_rateplan_commission_update_csv,
     'fields': ['hotel_code', 'rateplan_code', 'new_commission', 'approver'], 'upload_restricted_time': 0,
     'save': rateplan_commission_update, 'max_line_count': 1000},

    {'id': 48, 'name': 'Deactivate GSTN', 'handle': 'bulk_deactivate_gstn',
     'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT'],
     'sample_file': 'https://gos3.ibcdn.com/b7d09da0df4211edb52d0242ac140004.csv',
     'request_users': ['user'],
     'operation': 'add', 'model': GSTDetail,
     'validators': [],
     'fields': ['hotel_code', 'gstn'], 'upload_restricted_time': 0,
     'save': deactivate_gstn, 'max_line_count': 10000},

    {'id': 49, 'name': 'Cron Trigger - Corporate GSTN Verified to Approved', 'handle': 'cbi_ver_to_apr',
     'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2'],
     'sample_file': 'https://gos3.ibcdn.com/e39e0ff2f08211ed873e0242ac140005.csv',
     'request_users': ['user'],
     'operation': 'add', 'model': CorporateBookingInfo,
     'validators': [],
     'fields': ['date'], 'upload_restricted_time': 5,
     'save': bulk_invoice_ocr_verified_to_approved, 'max_line_count': 1000
     },
    {
        'id': 50,
        'name': 'Add Hotel Inclusions',
        'handle': 'create_hotel_inclusions',
        'auth_group': ['Admin Booking Group'],
        'auth_group2': ['ADMIN_BOOKING_GROUP'],
        'sample_file': 'https://gos3.ibcdn.com/ac2f572afb3f11ed91050242ac140007.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': Services,
        'validators': [validate_services_data],
        'fields': ['hotelcode', 'segment_id', 'inclusions'],
        'upload_restricted_time': 5,
        'save': create_services_for_hotel,
        'max_line_count': 500
    },
    {'id': 51, 'name': 'Push Inactive Vendor', 'handle': 'upd_inactive_vndr',
     'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT'],
     'sample_file': 'https://gos3.ibcdn.com/b42dca2ada8711edb8670a58a9feac02.csv',
     'request_users': ['user'],
     'operation': 'add', 'model': VendorDetail,
     'validators': [validate_bulk_push_inactive_vendor],
     'fields': ['vendor_code'], 'upload_restricted_time': 5,
     'save': bulk_push_inactive_vendors, 'max_line_count': 1000
     },
    {'id': 52,
     'name': 'Reseller Updation - 0:default, 1:reseller+gommt',
     'handle': 'user_profile_update',
     'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT'],
     'sample_file': 'https://gos3.ibcdn.com/203b744ef56b11ed905e0242ac140003.csv',
     'operation': 'add',
     'model': UserProfile,
     'validators': [reseller_updation_validator],
     'save': reseller_flag_updation,
     'fields': ['hotelcode', 'user_id', 'user_legal_entity_type'],
     'upload_restricted_time': 5,
     'max_line_count': 10000
     },
    {
        'id': 53,
        'name': 'Create Policy Template Config mapping',
        'handle': 'create_policy_config',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2'],
        'sample_file': 'https://gos3.ibcdn.com/013f14e00b6111eeb6590242ac140008.csv',
        'operation': 'add',
        'model': PolicyConfigV2,
        'validators': [validate_policy_configs],
        'fields': ['template_id', 'is_active', 'bitwise_config'],
        'upload_restricted_time': 0,
        'max_line_count': 1000
    },
    {
        'id': 54,
        'name': 'Update Hotel Travel Porting',
        'handle': 'update_hotel_porting',
        'auth_group': ['Admin Booking Group'],
        'auth_group2': ['ADMIN_BOOKING_GROUP'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2023-07-10_abc_e85781a8-1eff-11ee-8c93-0242ac140007.csv',
        'operation': 'add',
        'model': HotelDetail,
        'validators': [],
        'fields': ['hotelcode', 'is_hoteltravel_porting_on'],
        'upload_restricted_time': 5,
        'save': update_hoteltravel_porting_flag,
        'max_line_count': 100
    },
    {
        'id': 55, 'name': 'Hourly Settings Bulk Uploader', 'handle': 'upd_hrly_settings',
        'auth_group': ['DAY_USE_ADMIN'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2023-08-02_abc_60317b08-3129-11ee-81e0-0242ac140107.csv',
        'request_users': ['user'], 'operation': 'add', 'model': HotelDetail, 'validators': [],
        'fields': ['hotelcode', 'is_hourly_eligible', 'isactive_3hr_rp', 'isactive_6hr_rp', 'isactive_9hr_rp',
                   'change_linkage_model', 'percentage_3hr_rp', 'percentage_6hr_rp', 'percentage_9hr_rp'],
        'upload_restricted_time': 0, 'save': update_hourly_settings_for_hotel, 'max_line_count': 500
    },
    {
        'id': 56, 'name': 'Hourly ARI Bulk Uploader', 'handle': 'update_hourly_ari',
        'auth_group': ['DAY_USE_ADMIN'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2023-08-02_abc_fd4fd056-3129-11ee-bc2c-0242ac140107.csv',
        'request_users': ['user'], 'operation': 'add', 'model': InventoryBulkUploader, 'validators': [],
        'fields': ['action', 'room/rateplan_code', 'start_date', 'end_date', 'inventory', 'rates'],
        'upload_restricted_time': 0, 'save': hourly_ari_bulk_uploader, 'max_line_count': 500
    },
    {
        'id': 57,
        'name': 'Convert Chain Hotel and User to Reseller',
        # reseller chain property agreement mapping, admin user mark reseller, legal entity is set to reseller
        'handle': 'reseller_user_update',
        'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT'],
        'sample_file': 'https://gos3.ibcdn.com/283a235040a911ee8fed0242ac140101.csv',
        'operation': 'add',
        'model': HotelDetail,
        'request_users': ['user'],
        'validators': [reseller_update_validator],
        'save': update_reseller_status_for_hotel,
        'fields': ['hotelcode', 'user_legal_entity_type', 'reason', 'agreement_id', 'agreement_accepted_by_user_id'],
        'upload_restricted_time': 0,
        'max_line_count': 1000
    },
    {
        'id': 58,
        'name': 'MIB Flag uploader',
        'handle': 'mib_flag_uploader',
        'auth_group': ['ROLE_SUPPLY_IND_AUDIT', 'ROLE_INGOMMT_PROD_SUPPORT', 'ROLE_INGOMMT_DEV_LEVEL_2', 'INGO_ADMIN_STAFF', 'ROLE_SUPPLY_IND_ALTACCO_ZM'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2025-02-24_mib_new_06bda8ce-f2ae-11ef-b836-0242ac140100.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelDetail,
        'validators': [],
        'fields': ['hotelcode', 'status', 'percentage_commission', 'approver'],
        'upload_restricted_time': 0,
        'save': mib_flag_bulk_uploader,
        'max_line_count': 500
    },
    # This change was included as part of INGO-25305. (Allowing updates on additional_fields)
    {
        'id': 59,
        'name': 'Update Ingo Map Routes',
        'handle': 'update_route_details',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/0-2023-08-28_2023-08-28T16:13:53_c7dc04ce-458f-11ee-88d2-0242ac140100.csv',
        'operation': 'add',
        'model': HotelDetail,
        'validators': [],
        'fields': ['hotelcode', 'additional_details'],
        'upload_restricted_time': 5,
        'save': update_ingo_route_attributes,
        'max_line_count': 100
    },
    {
        'id': 60,
        'name': 'FullDay/Hourly Dayuse Bulk Uploader', 'handle': 'full_hrly_dayuse',
        'auth_group': ['DAY_USE_ADMIN'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2023-09-11_abc_48a7caf2-50c6-11ee-8f50-0242ac140104.csv',
        'operation': 'add', 'model': HotelDetail,
        'validators': [],
        'fields': ['hotelcode', 'roomtypecode','is_dayuse_room','hourly_enabled_dayuse_room','hourly_dayuse_rateplancode', 'check_in_time', 'check_out_time','available_slots'],
        'upload_restricted_time': 0, 'save': update_fullday_hourly_dayuse_rooms,
        'max_line_count': 500
    },
    {
        'id': 61,
        'name': 'ARI Linkage Bulk Uploader',
        'handle': 'enable_new_linkage',
        'auth_group': ['ROLE_SUPPLY_IND_CENTRAL'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2023-08-07_is_new_linkage_enabled_uploader_cfac2e36-34f0-11ee-b954-0242ac140102.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelDetail,
        'validators': [validate_linkage_enabled],
        'fields': ['hotelcode', 'is_new_linkage_enabled'],
        'upload_restricted_time': 20,
        'max_line_count': 2000,
        'save': new_linkage_enable_uploader
    },
    {
        'id': 62,
        'name': 'Registration document bulk uploader',
        'handle': 'reg_doc_uploader',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2'],
        'request_users': ['user'],
        'operation': 'add',
        'model': RegistrationDocument,
        'validators': [],
        'fields': ['hotel_code', 'expiry_date', 'registration_number'],
        'upload_restricted_time': 0,
        'save': registration_document_bulk_uploader,
        'max_line_count': 500,
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/0-2023-10-04_2023-10-04T21:40:53_97c10182-62d0-11ee-9e11-0242ac140106.csv',
    },
    {
        'id': 63,
        'name': 'Locus priority uploader',
        'handle': 'locus_priority',
        'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT'],
        'sample_file': 'https://gos3.ibcdn.com/86fb54ec5b9111eea60a0242ac140105.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelDetail,
        'validators': [locus_priority_validator],
        'fields': ['locus_code', 'leaf_category_id', 'priority', 'action', 'update_inclusions'],
        'upload_restricted_time': 0,
        'save': locus_priority_update,
        'max_line_count': 500
    },
    {
        'id': 64, 'name': 'Free Room Night Create/Update Bulk Uploader', 'handle': 'frn_create_update',
        'auth_group': ['ROLE_SUPPLY_IND_CATEGORY', 'ROLE_INGOMMT_PROD_SUPPORT'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2023-10-17_abc_aac4288e-6cb9-11ee-b254-0242ac140104.csv',
        'request_users': ['user'], 
        'operation': 'add', 
        'model': HotelDetail, 
        'validators': [],
        'fields': ['mmt_hotelId','mmt_roomId','frncode','start_date','end_date','blackout_dates','max_per_day',
                   'max_total','frn_type','is_active','rateplancodes','vendor'],
        'upload_restricted_time': 0, 'save': create_update_frn_uploader, 'max_line_count': 500
    },
    {
        'id': 65,
        'name': 'Hotel detail flag bit modifier',
        'handle': 'hotel_flag_bit',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2025-01-21_hotel_detail_flag_bit_modifier_c7382066-d7e2-11ef-acde-0242ac140101.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelDetail,
        'validators': [],
        'fields': ['hotelcode', 'is_override_rates_from_ratecheck_enabled', 'is_competitive_pricing_enabled', 'is_einvoice_enabled', 'is_onboarded_to_new_cp'],
        'upload_restricted_time': 0,
        'save': hotel_detail_flag_bit_modifier,
        'max_line_count': 1000
    },
    {
        'id': 66,
        'name': 'Rack rates create and update',
        'handle': 'rack_rates_uploader',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT', 'ROLE_SUPPLY_IND_CATEGORY'],
        'sample_file': 'https://gos3.ibcdn.com/729ce0aa8eaa11eea4840242ac140106.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelPolicyMappingV2,
        'validators': [validate_rack_rates],
        'batch_count': 1000,
        'fields': ['mmt_id', 'template_id', 'price_value', 'is_active'],
        'upload_restricted_time': 0,
        'save': rack_rates_create_update,
        'max_line_count': 10000
    },
    {
        'id': 67,
        'name': 'CPP Config uploader',
        'handle': 'cpp_config_uploader',
        'auth_group': ['ROLE_SUPPLY_IND_CATEGORY','ROLE_INGOMMT_PROD_SUPPORT', 'ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_SUPPLY_IND_ALTACCO_ZM'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2025-02-24_cpp_new_f5494d82-f2ad-11ef-b836-0242ac140100.csv',
        'request_users': ['user'],'operation': 'add','model': HotelDetail, 'validators': [],
        'fields': ['hotelcode', 'cpp_threshold_status', 'cpp_commission_threshold','cpp_tax_inclusive_status','cpp_comm_tax_inclusive_status', 'approver'],
        'upload_restricted_time': 0,'save': cpp_config_uploader,'max_line_count': 1000
    },
    {
        'id': 68,
        'name': 'CPP Rates/Restriction uploader',
        'handle': 'cpp_rates_uploader',
        'auth_group': ['ROLE_SUPPLY_IND_CATEGORY','ROLE_INGOMMT_PROD_SUPPORT'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2023-11-13_abc_ddedbf7a-81fb-11ee-ab36-0242ac140104.csv',
        'request_users': ['user'],'operation': 'add','model': HotelDetail, 'validators': [],
        'fields': ['hotelcode','rateplancode','start_date','end_date','cpp_nett_price_one','cpp_nett_price_two','cpp_nett_price_three','cpp_nett_price_four','block_cpp_rates','contract_type_list','day_list'],
        'upload_restricted_time': 0,'save': cpp_rates_res_uploader,'max_line_count': 500
    },
    {
        'id': 69,
        'name': 'User Roles Modifier',
        'handle': 'group_id',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/0-2023-12-13_2023-12-13T03:43:39_b4031258-993b-11ee-a5d9-0242ac140104.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': User,
        'validators': [],
        'fields': ['id'],
        'upload_restricted_time': 0,
        'save': update_user_roles,
        'bulk_uploader_class': Type1BulkUploader,
        'max_line_count': 500,
    },
    {
        'id': 70,
        'name': 'Create Campaign Hotel Mapping',
        'handle': 'cmp_htl_mapping',
        'auth_group': ['ROLE_SUPPLY_IND_AUDIT', 'ROLE_SUPPLY_IND_CENTRAL', 'ROLE_SUPPLY_IND_CATEGORY', 'ROLE_INGOMMT_DEV_LEVEL_2'],
        'sample_file': 'https://gos3.ibcdn.com/5f6ac9f6a57511ef93700242ac140103.csv',
        'request_users': ['user'],
        'operation': 'add', 'model': CampaignEntityMapping,
        'validators': [validate_campaign_hotel_mapping],
        'fields': ['campaign_id', 'hotel_id', 'rpd_target_value', 'offer_category', 'send_mail'], 'upload_restricted_time': 10,
        'save': save_campaign_hotel_mapping_v2, 'max_line_count': 10000
    },
    {
        'id': 71,
        'name': 'Create New Campaign',
        'handle': 'cmp_master',
        'auth_group': ['ROLE_SUPPLY_IND_AUDIT', 'ROLE_SUPPLY_IND_CENTRAL', 'ROLE_SUPPLY_IND_CATEGORY', 'ROLE_INGOMMT_DEV_LEVEL_2'],
        'sample_file': 'https://gos3.ibcdn.com/57aa5462ecfb11eeb4a40242ac140102.csv',
        'request_users': ['user'],
        'operation': 'add', 'model': CampaignMaster,
        'validators': [validate_campaign_master],
        'fields': ['campaign_type', 'campaign_name', 'campaign_description', 'campaign_long_description',
                   'discount_value', 'campaign_start_time', 'campaign_end_time', 'stay_start_date', 'stay_end_date',
                   'booking_start_time', 'booking_end_time', 'video_url', 'campaign_logo', 'campaign_short_inlet',
                   'campaign_mid_inlet', 'campaign_long_inlet', 'campaign_detailed_participation', 'campaign_short_inlet_app',
                   'campaign_mid_inlet_app', 'campaign_long_inlet_app', 'campaign_detailed_participation_app', 'charge_type',
                   'campaign_status', 'max_discount', 'min_discount', 'num_slot_warn', 'days_to_elapse', 'max_enroll_days',
                   'd_enroll_min_days', 'commission_value', 'inventory_threshold_value', 'commission_flag', 'agreement_id',
                   'max_black_out_days', 'campaign_level'],
        'upload_restricted_time': 10,
        'save': save_campaign_v2, 'max_line_count': 500
    },
    {
        'id': 72,
        'name': 'Update Existing Campaign',
        'handle': 'update_cmp_master',
        'auth_group': ['ROLE_SUPPLY_IND_AUDIT', 'ROLE_SUPPLY_IND_CENTRAL', 'ROLE_SUPPLY_IND_CATEGORY', 'ROLE_INGOMMT_DEV_LEVEL_2'],
        'sample_file': 'https://gos3.ibcdn.com/ba5f30e6dad111eeb0f70242ac140106.csv',
        'request_users': ['user'],
        'operation': 'add', 'model': CampaignMaster,
        'validators': [validate_update_existing_campaign],
        'fields': ['campaign_id', 'campaign_name', 'campaign_description', 'campaign_long_description', 'video_url',
                   'campaign_logo', 'campaign_status', 'discount_value', 'campaign_short_inlet',
                   'campaign_mid_inlet', 'campaign_long_inlet', 'campaign_detailed_participation',
                   'campaign_short_inlet_app', 'campaign_mid_inlet_app', 'campaign_long_inlet_app',
                   'campaign_detailed_participation_app', 'charge_type', 'campaign_start_time', 'campaign_end_time'],
        'upload_restricted_time': 10,
        'save': update_campaign_v2, 'max_line_count': 500
    },
    {'id': 73,
     'name': 'Hotel Short Link Create/Update Uploader',
     'handle': 'hotels_short_links',
     'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT', 'ROLE_INGOMMT_DEV_LEVEL_2'],
     'sample_file': 'https://gos3.ibcdn.com/98d51c74aaf211eea1760242ac140102.csv',
     'request_users': ['user'],
     'operation': 'add',
     'model': HotelDetail,
     'validators': [validate_hotels_short_links],
     'fields': ['mmt_hotel_id', 'short_link_url', 'type', 'status'],
     'upload_restricted_time': 30,
     'save': save_hotels_short_links,
     'max_line_count': 10000
     },
     {
        'id': 74, 'name': 'Repush Booking to CM', 'handle': 'cm_repush_booking',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/0-2023-12-22_2023-12-22T12:09:35_df11f6c0-a094-11ee-bf9b-0242ac140104.csv',
        'request_users': ['user'], 'operation': 'add', 'model': InventoryBulkUploader, 'validators': [],
        'fields': ['push_type(booking/cancellation/all)', 'start_time (YYYY-MM-DD HH:MM)', 'end_time (YYYY-MM-DD HH:MM)', 'cm_name'],
        'upload_restricted_time': 0, 'save': cm_repush_booking, 'max_line_count': 10
    },
    {
        "id":75,
        'name': 'SALE Campaign Participation',
        'handle': 'cmp_participation',
        'auth_group': ['ROLE_SUPPLY_IND_AUDIT', 'ROLE_SUPPLY_IND_CENTRAL', 'ROLE_SUPPLY_IND_CATEGORY',
                       'ROLE_INGOMMT_DEV_LEVEL_2'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2024-11-27_abc_8b29e958-accf-11ef-8c49-0242ac140103.csv',
        'request_users': ['user'],
        'operation': 'add', 'model': CampaignEntityMapping,
        'validators': [validate_modification_of_campaign_hotel_mapping],
        'fields': ['campaign_id', 'hotel_id', 'blackout_dates(start_date:end_date;start_date:end_date)',
                   'final_discount_coupon', 'final_discount_mobile',
                   'final_discount_mmt_black_1', 'final_discount_mmt_black_2', 'final_discount_ipos', 'final_discount_gcc'], 'upload_restricted_time': 10,
        'save': participate_in_campaign_v2, 'max_line_count': 2000
    },
    {'id': 76,
     'name': 'Hotel Checkin-checkout details uploader',
     'handle': 'checkin_range_update',
     'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2'],
     'request_users': ['user'],
     'operation': 'add',
     'model': HotelDetail,
     'validators': [],
     'fields': ['ingo_hotel_id', 'checkin_time', 'checkin_end_time','checkout_time'],
     'upload_restricted_time': 0,
     'save': checkin_checkout_time_bulk_uploader,
     'max_line_count': 500,
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/0-2023-10-04_2023-10-04T21:40:53_97c10182-62d0-11ee-9e11-0242ac140106.csv'
    },
    {
        'id': 77,
        'name': 'MMT Black Uploader',
        'handle': 'mmt_black_uploader',
        'auth_group': [],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2024-05-20_abc_7c43c66e-1686-11ef-8c5a-0242ac140103.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelDetail,
        'validators': [],
        'fields': ['hotel_id',	'opt_black_program','black_dnd_flag_status','black_dnd_mincount','exclude_room_meal_upgrade','discount_gold','discount_platinum','discount_member',
                   'applicable_roomupgrade','roomupgrade_configs','applicable_mealupgrade','mealupgrade_configs','mealupgrade_room','hotelcredit_gold','hotelcredit_platinum','hotel_credit_applicable','min_hotel_credits',
                   'spa_fnb_gold','spa_fnb_platinum','checkinblackoutdates','checkinstartdate','checkinenddate','bookingstartdate','bookingenddate'],
        'upload_restricted_time': 0,
        'save': mmt_black_uploader,
        'max_line_count': 2000,
        'batch_count' : 1000,
     },
    {
        'id': 78,
        'name': 'Derby/Synxis- Contract Type and Meal Code Update',
        'handle': 'ct_mc_update',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT'],
        'sample_file': 'https://gos3.ibcdn.com/4cff07cad49011ee8b5d0242ac140102.csv',
        'request_users': ['user'],
        'operation': 'add', 'model': RatePlan,
        'validators': [validate_rateplan_meal_and_contract_type_update],
        'fields': ['ingo_hotel_code', 'source_rateplancode', 'contract_type', 'meal_plan'], 'upload_restricted_time': 10,
        'save': meal_and_contract_type_update, 'max_line_count': 100
    },

    {
        'id': 79,
        'name': 'Deboard Reseller',
        'handle': 'deboard_reseller',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/0-2024-03-26_2024-03-26T10:13:24_61995f4e-eb2b-11ee-8ef1-0242ac140106.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelDetail,
        'validators': [],
        'fields': ['hotelcode', 'user_id'],
        'upload_restricted_time': 0,
        'save': deboard_reseller,
        'max_line_count': 500
    },
    {
        'id': 80, 'name': 'Save MMT ID on the basis of hotel id', 'handle': 'htl_id_mmt_id_map',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2'],
        'sample_file': 'https://gos3.ibcdn.com/f9307a30f0e011ee9ffe0242ac140102.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelDetail,
        'validators': [],
        'fields': ['model_name(case sensitive)', 'destination_column_name(case sensitive)',
                   'column_of_hotel_id(case sensitive)', 'hotel_id', 'content_type_id'],
        'upload_restricted_time': 5,
        'save': map_hotel_id_to_mmt_id,
        'max_line_count': 10000
    },
    {
        'id': 81,
        'name': 'Create PreBuy Config',
        'handle': 'create_prebuy_config',
        'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT'],
        'sample_file': 's3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2025-05-29_abc_b882dde8-3c7a-11f0-beb1-0242ac140101.csv',
        'request_users': ['user'],
        'model': RatePlan,
        'validators':[validate_upsert_prebuy_config_payload],
        'operation': 'add',
        'fields': [ 'hotel_id', 'config_details',  # This is a repeated field of type PrebuyEntity
        'room_nights',
        'max_guest_occupancy',
        'price',
        'validity_days',
        'trigger_order',
        'reorder_point',
        'daily_sale_limit',
        'prebuy_contract_type',
        'approval_type',
        'approval_emails',  # This is a repeated field of type string
        'is_active',
        'config_id',
        'approved_by',
        'order_start_date',
        'order_end_date',
        'use_ceiling_end_time',
        'auto_approval_in_minutes',
        'request_type'],
        'upload_restricted_time': 5,
        'save': create_prebuy_config_reseller, 'max_line_count': 500
    },
    {
        'id':82,
        'name': 'Set Dynamic HCP Variation',
        'handle': 'set_hcp_deviation',
        'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2024-06-03_hcp_deviation_ceef3822-21bb-11ef-b0a3-0242ac140104.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelDetail,
        'validators': [],
        'fields': ['ingo_hotel_code','hcp_deviation','is_dynamic_hcp_enabled'],
        'upload_restricted_time': 0,
        'save': set_hcp_deviation,
        'max_line_count': 2000,
        'batch_count' : 1000,
    },
    {
        'id': 83,
        'name': 'Create Hotel Cloud Booking',
        'handle': 'ct_htlcld_cfg',
        'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2024-12-13_abc_a8e6998e-b90c-11ef-8bda-0242ac140102.csv',
        'request_users': ['user'],
        'model': RatePlan,
        'validators':[validate_create_hotelcloud_booking_payload],
        'operation': 'add',
        'fields': [ 'vendorBookingId', 'prebuyOrderId','oldPrebuyOrderId', 'auditReason'],
        'upload_restricted_time': 5,
        'save': create_hotel_cloud_booking,
        'max_line_count': 4000
    },
    {'id': 84, 'name': 'Update Hotel Travel Flag for RatePlans for Reseller Onboarded hotels',
     'handle': 'update_rps_hotel', 'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2'],
     'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2022-07-25_abc_e2a2d3d8-0be2-11ed-8de7-0242ac140007.csv',
     'request_users': ['user'], 'operation': 'add', 'model': RatePlan, 'validators': [], 'fields': ['hotel_id'],
     'upload_restricted_time': 0, 'save': modify_hotel_travel_platform_flag_for_rate_plan_according_to_pre_buy_order, 'max_line_count': 1000
     },
    {
        'id': 85,
        'name': 'Hotel ABSO Flag Uploader',
        'handle': 'abso_lsp',
        'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT', 'ROLE_SUPPLY_IND_CATEGORY'],
        'sample_file': 'https://gos3.ibcdn.com/b368990049a111efb2700242ac140101.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelDetail,
        'validators': [validate_abso_reason_payload],
        'fields': ['ingoHotelCode', 'isAbso', 'absoReason', 'startingSellingPrice'],
        'upload_restricted_time': 5,
        'save': save_abso_reason,
        'max_line_count': 1000
    },
    {
        'id': 86,
        'name': 'Occasion Packages Uploader',
        'handle': 'occ_pkg',
        'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT', 'ROLE_INGOMMT_DEV_LEVEL_2'],
        'sample_file': '',
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelDetail,
        'validators': [validate_occasions_package_uploader],
        'fields': ['Id', 'Occasion', 'Alias', 'CountryID', 'StateID', 'CityID', 'CompulsoryInclusions', 'MandatoryInclusions', 'ExcludedInclusions', 'StayStartDate', 'StayEndDate', 'StarRating-MinInclusionCount', 'ImageURL', 'OccasionType', 'Action'],
        'save': occasions_package_upload,
        'max_line_count': 1000
    },
    {
        'id': 87,
        'name': 'Single Inventory Flag Uploader',
        'handle': 'sif_up',
        'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT', 'ROLE_INGOMMT_DEV_LEVEL_2'],
        'sample_file': 'https://gos3.ibcdn.com/5c53f4d086e011efa8ae0242ac140102.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelDetail,
        'validators': [validate_single_inventory_flag_payload],
        'fields': ['hotelCode', 'isSingleInventory'],
        'upload_restricted_time': 5,
        'save': save_single_inventory_flag,
        'max_line_count': 1000
    },
    {
        'id': 88,
        'name': 'Upsert Prebuy Order',
        'handle': 'Upsert Prebuy Order',
        'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2025-02-06_abc_b62b9f7c-e472-11ef-821d-0242ac140103.csv',
        'request_users': ['user'],
        'model': RatePlan,
        'validators': [validate_upsert_prebuy_order],
        'operation': 'add',
        'fields': ['orderNumber', 'action', 'orderValidityDate','updateReason','hotelId','approvedBy', 'requesttype'],
        'upload_restricted_time': 5,
        'save': upsert_prebuy_order,
        'max_line_count': 500
    },
    {
        'id': 89,
        'name': 'Update Is Chat Enabled Flag for Hotels',
        'handle': 'update_enable_flag',
        'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT', 'ROLE_INGOMMT_DEV_LEVEL_2'],
        'sample_file': 'https://gos3.ibcdn.com/857d0d50798a11efaadc0242ac140102.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelDetail,
        'validators': [validate_is_chat_enable_flag_update_payload],
        'fields': ['hotel_id', 'is_chat_enabled'],
        'upload_restricted_time': 5,
        'save': update_is_chat_enabled_flag_for_hotel,
        'max_line_count': 5000
    },
    {
        'id': 90,
        'name': 'Update Is Chat Access Flag for Hotels',
        'handle': 'update_access_flag',
        'auth_group':['ROLE_INGOMMT_PROD_SUPPORT', 'ROLE_INGOMMT_DEV_LEVEL_2'],
        'sample_file': 'https://gos3.ibcdn.com/c4d9a544798a11efaadc0242ac140102.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelDetail,
        'validators': [validate_is_chat_access_flag_update_payload],
        'fields': ['hotel_id', 'is_chat_access'],
        'upload_restricted_time': 5,
        'save': update_is_chat_access_flag_for_hotel,
        'max_line_count': 5000
    },
    {
        'id': 91,
        'name': 'Directly enable the isChatEnabled and isChatAccess for hotels',
        'handle': 'enable_chat_flags',
        'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT', 'ROLE_INGOMMT_DEV_LEVEL_2'],
        'sample_file': 'https://gos3.ibcdn.com/e3ddb142798a11efaadc0242ac140102.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelDetail,
        'validators': [],
        'fields': ['hotel_id'],
        'upload_restricted_time': 5,
        'save': directly_enable_is_chat_access_and_is_chat_enable_flags_for_hotel,
        'max_line_count': 2000
    },
    {
        'id': 92,
        'name': 'MMT Black Control Uploader',
        'handle': 'black_ctrl_uploader',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2','ROLE_SUPPLY_IND_CATEGORY','ROLE_INGOMMT_PROD_SUPPORT'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2024-09-03_2024-09-03T14:29:03.207849_blackControlUploader_caf0bc46-69d2-11ef-afe1-0242ac140105.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelDetail,
        'validators': [validate_mmt_black_control_payload],
        'fields': ['hotel_id', 'black_dnd_flag_status', 'black_dnd_mincount_benefits', 'exclude_room_meal_upgrade', 'min_hotel_credits'],
        'upload_restricted_time': 0,
        'save': mmt_black_control_uploader,
        'max_line_count': 2000,
        'batch_count': 1000,
     },
    {
        'id': 93,
        'name': 'MMT Black Uploader 2.0',
        'handle': 'black_uploader_new',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_SUPPLY_IND_CATEGORY', 'ROLE_INGOMMT_PROD_SUPPORT'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2024-11-06_abc_7c98f484-9c0c-11ef-a82f-0242ac140105.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelDetail,
        'validators': [validate_mmt_black_uploader_new_payload],
        'fields': ['hotel_id', 'opt_black_program', 'discount_gold', 'discount_platinum', 'applicable_roomupgrade',
                   'roomupgrade_configs', 'applicable_mealupgrade', 'mealupgrade_configs', 'mealupgrade_room',
                   'hotelcredit_gold', 'hotelcredit_platinum', 'hotel_credit_applicable', 'fnb_gold', 'fnb_platinum',
                   'spa_gold', 'spa_platinum', 'early_checkin_gold', 'early_checkin_platinum', 'late_checkout_gold',
                   'late_checkout_platinum', 'checkinblackoutdates', 'checkinstartdate', 'checkinenddate',
                   'bookingstartdate', 'bookingenddate'],
        'upload_restricted_time': 0,
        'save': mmt_black_uploader_new,
        'max_line_count': 2000,
        'batch_count': 1000,
    },
    {
        'id': 94,
        'name': 'Enable MOF generated voucher for hotels',
        'handle': 'enable_mof_voucher',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2024-09-30_mof_generated_voucher_sample_e583b346-7f09-11ef-bc49-0242ac140104.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelDetail,
        'validators': [],
        'fields': ['hotelcode', 'mof_generated_voucher'],
        'upload_restricted_time': 0,
        'save': hotel_detail_flag_bit_modifier,
        'max_line_count': 500
    },
    {
        'id': 95,
        'name': 'Grammar Refresh',
        'handle': 'inclusion_grammar',
        'auth_group': ['ADMIN_BOOKING_GROUP'],
        'sample_file': 'https://gos3.ibcdn.com/2e405c94806b11ef883a0242ac140102.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelDetail,
        'validators': [validate_inclusion_grammar_payload],
        'fields': ['service_id'],
        'upload_restricted_time': 15,
        'save': grammar_refresh_in_async,
        'max_line_count': 10000,
        'batch_count': 1000,
    },
    {
        'id': 96,
        'name': 'DL Properties Unlink bulk uploader',
        'handle': 'dl_unlink_uploader',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2','ROLE_INGOMMT_PROD_SUPPORT'],
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelDetail,
        'validators': [],
        'fields': ['parent_hotel_code','child_hotel_code'],
        'upload_restricted_time': 0,
        'save': dl_unlink_bulk_uploader,
        'max_line_count': 100,
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/0-2023-10-04_2023-10-04T21:40:53_97c10182-62d0-11ee-9e11-0242ac140106.csv',
    },
    {
        'id': 97,
        'name': 'Update Host Detail',
        'handle': 'update_host',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT', 'ROLE_SUPPLY_CHAINS_INTL_EXCLUSIVE_SUPPORT', 'ROLE_SUPPLY_IND_ALTACCO_ZM'],
        'request_users': ['user'],
        'operation': 'add',
        'model': HostProfile,
        'validators': [validate_save_host_details_uploader],
        'fields': ['HostId', 'UserId', 'HostType', 'HostingSince', 'Gender', 'Languages', 'DisplayName', 'HostDescription', 'NoOfProperties', 'ChainId'],
        'save': save_host_details,
        'upload_restricted_time': 5,
        'max_line_count': 500,
        'sample_file': 'https://gos3.ibcdn.com/0793a1e492c911ef8c290242ac140101.csv',
    },
    {
        'id': 98,
        'name': 'Tag Host To Hotel',
        'handle': 'tag_host_htl',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT', 'ROLE_SUPPLY_CHAINS_INTL_EXCLUSIVE_SUPPORT', 'ROLE_SUPPLY_IND_ALTACCO_ZM'],
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelUserLink,
        'validators': [validate_tag_host_to_hotel_payload],
        'fields': ['UserId', 'HotelId'],
        'save': tag_host_to_hotel_uploader,
        'upload_restricted_time': 2,
        'max_line_count': 500,
        'sample_file': 'https://gos3.ibcdn.com/ee56d8be912811ef87350242ac140105.csv',
    },
    {
        'id': 99,
        'name': 'Update Chain Host Details',
        'handle': 'chainhost_update',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT', 'ROLE_SUPPLY_CHAINS_INTL_EXCLUSIVE_SUPPORT', 'ROLE_SUPPLY_IND_ALTACCO_ZM'],
        'request_users': ['user'],
        'operation': 'add',
        'model': HostChain,
        'validators': [validate_host_chain_update_data],
        'fields': ['HotelChainId', 'HostChainDescription', 'HostChainImageUrl', 'Status', 'HostingSince', 'Languages', 'NoOfProperties'],
        'save': save_host_chain_update_data,
        'upload_restricted_time': 2,
        'max_line_count': 500,
        'sample_file': 'https://gos3.ibcdn.com/6d63fbe692b411ef99bb0242ac140101.csv',
    },
    {
        'id': 100,
        'name': 'Caretaker update',
        'handle': 'caretaker_Update',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT', 'ROLE_SUPPLY_CHAINS_INTL_EXCLUSIVE_SUPPORT', 'ROLE_SUPPLY_IND_ALTACCO_ZM', 'ROLE_SUPPLY_CHAINS_INTL_CONTENT'],
        'request_users': ['user'],
        'operation': 'add',
        'model': Caretaker,
        'validators': [validate_caretaker_update],
        'fields': ['hotelcode', 'caretakerId', 'caretakerAmenityStatus', 'housekeepingAmenityStatus', 'name', 'mobile', 'languages', 'is_vaccinated', 'is_fulltime', 'start_time', 'end_time','is_communicable', 'responsibilities', 'action'],
        'save': save_caretaker_details,
        'max_line_count': 5000,
        'batch_count': 50,
        'upload_restricted_time': 0,
        'sample_file': '',
    },
    {
        'id': 101,
        'name': 'GSTN Assurance updater',
        'handle': 'gstn_assurance',
        'auth_group': ['ADMIN_BOOKING_GROUP'],
        'sample_file': 'https://gos3.ibcdn.com/f8810d82b3ec11ef90a40242ac140102.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelDetail,
        'validators': [validate_gstn_assurance_payload],
        'fields': ['hotelcode', 'lob', 'gst_with_penalty', 'gst_without_penalty'],
        'upload_restricted_time': 5,
        'save': update_penalty_in_gstn_assurance,
        'max_line_count': 500
    },
    {
        'id': 102,
        'name': 'Hotelcloud (Reseller): Create/Update  Blackout date (hotel level)',
        'handle': 'htlcld_blackout',
        'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2024-12-03_abc_f606841c-b15b-11ef-a7bd-0242ac140103.csv',
        'request_users': ['user'],
        'model': RatePlan,
        'validators': [validate_upsert_blackout_dates],
        'operation': 'add',
        'fields': ['mmt_hotelId', 'blackout_dates'],
        'upload_restricted_time': 5,
        'save': upsert_blackout_dates,
        'max_line_count': 500
    },
    {
        'id': 103, 'name': 'FC-NR Basic Promotion Offer Bulk Uploader', 'handle': 'fcnr_basic_promo',
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2024-12-11_fcnr_basic_bulk_uploader_1ce67196-b7d3-11ef-bc66-0242ac140106.csv',
        'user_manual': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2024-12-11_output_0881ac6c-b7c8-11ef-80c9-0242ac140103.pdf',
        'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT'],
        'request_users': ['user'],
        'operation': 'add', 'model': HotelOfferCondition,
        'validators': [basic_promotion_validator],
        'fields': COMMON_PROMOTION_FIELDS + FC_NR_FIELDS, 'upload_restricted_time': PROMOTION_BULK_RESTRICTED_TIME,
        'save': fcnr_basic_promotion_save, 'bulk_uploader_class': Type1BulkUploader,
        'max_line_count': PROMOTION_BULK_LINE_LIMIT
    },
    {
        'id': 104, 'name': 'FC-NR Custom Promotion Bulk Uploader', 'handle': 'fcnr_custom_promo',
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2024-12-11_fcnr_custom_bulk_uploader_44d53a20-b7d3-11ef-bc66-0242ac140106.csv',
        'user_manual': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2024-12-11_output2_d26aa3da-b7c8-11ef-80c9-0242ac140103.pdf',
        'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT'],
        'request_users': ['user'],
        'operation': 'add', 'model': HotelOfferCondition,
        'validators': [custom_promotion_bulk_uploader_validator],
        'fields': COMMON_PROMOTION_FIELDS + FC_NR_FIELDS + CUSTOM_PROMOTION_FIELDS,
        'upload_restricted_time': PROMOTION_BULK_RESTRICTED_TIME, 'save': fcnr_custom_promotion_bulk_uploader_save,
        'bulk_uploader_class': Type1BulkUploader, 'max_line_count': 300
    },
    {
        'id': 105,
        'name': 'Pet policy update',
        'handle': 'pet_policy_uploader',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT', 'ROLE_SUPPLY_IND_CATEGORY'],
        'sample_file': 'https://gos3.ibcdn.com/b411ab18b7aa11efb7800242ac140102.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelPolicyMappingV2,
        'validators': [validate_pet_policy],
        'batch_count': 1000,
        'fields': ['hotelcode', 'template_id', 'selected_value'],
        'upload_restricted_time': 5,
        'save': pet_policy_update,
        'max_line_count': 5000
    },
    {
        'id': 106,
        'name': 'Spotlight Commission configuration and Over-ride eligibility check',
        'handle': 'spotlight_uploader',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT', 'ROLE_SUPPLY_IND_CATEGORY'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-prod/bulk-uploader/2025-01-31_2025-01-31T10:35:05.128916_e3397fc4c9c511efbfc40242ac140101_ef189330-df90-11ef-b01b-0a58a9feac02.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': ProgramEntityMapping,
        'validators': [validate_mmt_spot_light],
        'fields': ['hotelId', 'high', 'low', 'overrideEligibility'],
        'upload_restricted_time': 5,
        'save': mmt_spot_light_update,
        'max_line_count': 1000
    },
    {
        'id': 107,
        'name': 'Activate/ Deactivate Spotlight tab',
        'handle': 'sptl_eligibility',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT', 'ROLE_SUPPLY_IND_CATEGORY'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-prod/bulk-uploader/0-2025-01-31_2025-01-31T10:41:15_cc046120-df91-11ef-b59f-0a58a9feac02.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': ProgramEntityMapping,
        'validators': [validate_mmt_spot_light_enable_hotel],
        'fields': ['hotelId', 'enable'],
        'upload_restricted_time': 5,
        'save': mmt_spot_light_enable_hotel,
        'max_line_count': 1000
    },
    {
        'id': 108,
        'name': 'Spotlight Exceptional Program withdrawal (for enrolled properties)',
        'handle': 'sptl_withdraw',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT', 'ROLE_SUPPLY_IND_CATEGORY'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2025-02-05_2025-02-05T21:56:23.276920_TEST_f081e0a4-e3dd-11ef-a141-0242ac140100.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': ProgramEntityMapping,
        'validators': [validate_mmt_spotlight_disenroll],
        'fields': ['hotelId'],
        'upload_restricted_time': 5,
        'save': mmt_spot_light_disenroll,
        'max_line_count': 1000
    },
    {
        'id': 109,
        'name': 'New Cancellation Policy Bulk Uploader',
        'handle': 'new_cp',
        'auth_group': ['ROLE_SUPPLY_IND_CATEGORY','ROLE_INGOMMT_DEV_LEVEL_2','ADMIN_BOOKING_GROUP'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2025-05-15_abc_7b8c8cca-3155-11f0-b1d0-0242ac140105.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelDetail,
        'validators': [],
        'fields': ['hotelcode', 'cancellation_policy_code','policy_type','template','custom_policy_rule','custom_policy_name','is_active','attach_all_rateplans','list_of_rateplans',
                   'blackoutdates_1','blackout_policy_rule_1','blackout_status_1','blackoutdates_2','blackout_policy_rule_2','blackout_status_2',
                    'blackoutdates_3','blackout_policy_rule_3','blackout_status_3','blackoutdates_4','blackout_policy_rule_4','blackout_status_4',
                    'blackoutdates_5','blackout_policy_rule_5','blackout_status_5'],
        'upload_restricted_time': 5,
        'max_line_count': 3000,
        'save': upsert_new_cancellation_policy,
    },
    {
        'id': 110,
        'name': 'GCC Contracting Flag',
        'handle': 'gcc_contracted_flag',
        'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT','ADMIN_BOOKING_GROUP'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/0-2025-02-10_2025-02-10T16:21:15_f32ae6e4-e79c-11ef-bb64-0242ac140105.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelDetail,
        'validators': [validate_gcc_contracting_payload],
        'fields': ['hotelcode','is_gcc_cont'],
        'upload_restricted_time': 15,
        'save': set_gcc_contracting_flag,
        'max_line_count': 1000,
        'batch_count': 100,
    },
    {
        'id': 111,
        'name': 'LMR Create/Update Bulk Uploader',
        'handle': 'lmr_create_update',
        'auth_group': ['ROLE_SUPPLY_IND_CATEGORY','INGO_ADMIN_STAFF','ROLE_INGOMMT_PROD_SUPPORT'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2025-02-24_abc_c14ec730-f282-11ef-9b65-0242ac140107.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelDetail,
        'validators': [validate_create_update_lmr_uploader],
        'batch_count': 1000,
        'fields': ['ingo_hotel_code', 'is_applicable_to_all', 'ingo_room_codes', 'ingo_rateplan_codes',
                   'charge_percentage','start_time', 'is_active', 'stay_blackout_dates'],
        'upload_restricted_time': 5,
        'save': create_update_lmr_uploader,
        'max_line_count': 1000
    },
    {
        'id': 112,
        'name': 'VCC Bulk Uploader',
        'handle': 'vcc_bulk_uploader',
        'auth_group': ['ROLE_SUPPLY_IND_CATEGORY', 'ROLE_SUPPLY_CHAINS_INTL_ZM', 'ROLE_INGOMMT_PROD_SUPPORT','ROLE_INGOMMT_DEV_LEVEL_2'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/0-2025-02-27_2025-02-27T13:44:46_e8508154-f4e2-11ef-8585-0242ac140106.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelDetail,
        'validators': [],
        'fields': ['hotelcode', 'vcc_payment_mode', 'vcc_currency', 'vcc_multi_currency', 'vcc_multi_currency_markup', 'vcc_validity_end'],
        'upload_restricted_time': 5,
        'max_line_count': 3000,
        'save': vcc_bulk_uploader_handler,
    },
    {
        'id': 113,
        'name': 'Hotel Partner Commission uploader',
        'handle': 'htlpartner_uploader',
        'auth_group': ["ROLE_SUPPLY_IND_BDM", "ROLE_SUPPLY_IND_ZM", "ROLE_INGOMMT_DEV_LEVEL_2"],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/0-2025-02-24_2025-02-24T18:14:59_286b8e60-f2ad-11ef-8d38-0242ac140101.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelDetail,
        'validators': [validate_hotel_partner_details],
        'fields': ["ingoHotelCode", "partnerCommission", "bookingModel", "sourceConfigId", "pricingModel", "vendor", "approver"],
        'upload_restricted_time': 1,
        'max_line_count': 3000,
        'save': save_hotel_partner_details,
    },
    {
        'id': 114,
        'name': 'Bulk Logout User',
        'handle': 'logout_user',
        'auth_group': ['INGO_ADMIN_STAFF','ROLE_INGOMMT_PROD_SUPPORT','ROLE_INGOMMT_DEV_LEVEL_2'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/1-2025-03-03_2025-03-03T18:59:23_85704372-f833-11ef-9b5b-0242ac140101.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': User,
        'validators': [validate_logout_request],
        'fields': ['hotelcode','userId'],
        'upload_restricted_time': 5,
        'save': logout_users,
        'max_line_count': 1000
    },
    {
        'id': 115,
        'name': 'User status Activate/Deactivate Uploader',
        'handle': 'user_status_update',
        'auth_group': ['INGO_ADMIN_STAFF','ROLE_INGOMMT_PROD_SUPPORT','ROLE_INGOMMT_DEV_LEVEL_2'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/0-2025-03-05_2025-03-05T23:02:40_d677dffe-f9e7-11ef-987a-0242ac140101.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelAdminUser,
        'validators': [validate_user_status_update],
        'fields': ['hotelcode','userId', 'status'],
        'upload_restricted_time': 5,
        'save': update_non_staff_user_status,
        'max_line_count': 500,
        'generate_audit_log': True
    },
    {
        'id': 116,
        'name': 'User contact details update uploader',
        'handle': 'user_contact_update',
        'auth_group': ['INGO_ADMIN_STAFF','ROLE_INGOMMT_PROD_SUPPORT','ROLE_INGOMMT_DEV_LEVEL_2'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2025-03-06_2025-03-06T16:09:34.384244_updateuser_4b74026c-fa77-11ef-b2d9-0242ac140101.csv',
        'request_users': ['user'],
        'operation': 'add',
        'model': User,
        'validators': [validate_user_contact_details_update],
        'fields': ['userId', 'email', 'mobile'],
        'upload_restricted_time': 5,
        'save': update_user_contact_details,
        'max_line_count': 500,
        'generate_audit_log': True
    },
    {
        'id': 117,
        'name': 'First Booking Promotion Bulk Uploader',
        'handle': 'fbp_promo',
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2025-03-11_first_booking_promotion_f4dbeb3a-fea0-11ef-9339-0242ac140105.csv',
        'auth_group': ['ROLE_INGOMMT_PROD_SUPPORT'],
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelOfferCondition,
        'validators': [first_booking_promotion_validator],
        'fields': FBP_FIELDS, 'upload_restricted_time': PROMOTION_BULK_RESTRICTED_TIME,
        'save': fbp_promotion_save, 'bulk_uploader_class': Type1BulkUploader,
        'max_line_count': PROMOTION_BULK_LINE_LIMIT
    },
    {
        'id': 118,
        'name': 'Derby Pull Flag',
        'handle': 'derby_pull_flag',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT', 'ROLE_SUPPLY_CHAINS_INTL_EXCLUSIVE_SUPPORT', 'ROLE_SUPPLY_IND_ALTACCO_ZM'],
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelDetail,
        'validators': [validate_derby_pull_flag_payload],
        'fields': ['hotelcode', 'is_derby_pull'],
        'save': set_derby_pull_flag,
        'upload_restricted_time': 2,
        'max_line_count': 10000,
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2025-03-21_2025-03-21T12:19:18.687538_derby_pull_uploader_9cd67ee6-0620-11f0-bc1f-0242ac140103.csv',
    },
    {
        'id': 119,
        'name': 'Hotel Chain Mapping',
        'handle': 'htl_chain_map',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT', 'ROLE_SUPPLY_CHAINS_INTL_EXCLUSIVE_SUPPORT', 'ROLE_SUPPLY_IND_ALTACCO_ZM'],
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelDetail,
        'validators': [validate_hotel_chain_mapping_payload],
        'fields': ['mmt_id', 'chain_id'],
        'save': save_hotel_chain_mapping_data,
        'upload_restricted_time': 2,
        'max_line_count': 5000,
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2025-03-21_2025-03-21T12:15:17.640621_hotel_chain_uploader_0d2babb8-0620-11f0-827a-0242ac140103.csv',
    },
    {
        'id': 120,
        'name': 'Hotel Policy Uploader',
        'handle': 'htl_policy',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT', 'ROLE_SUPPLY_CHAINS_INTL_EXCLUSIVE_SUPPORT', 'ROLE_SUPPLY_IND_ALTACCO_ZM'],
        'request_users': ['user'],
        'operation': 'add',
        'model': HotelPolicyMappingV2,
        'validators': [validate_hotel_policy],
        'fields': ['ingo_hotel_code', 'category_id', 'template_id', 'submitted_values'],
        'save': save_hotel_policy,
        'upload_restricted_time': 0,
        'max_line_count': 5000,
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/0-2025-05-14_2025-05-14T14:53:39_1f045dbc-30a5-11f0-8fd3-0242ac140105.csv',
    },
    {
    'id': 121,  # Next available ID
    'name': '(Hotel-Cloud) Permission Create/Update',
    'handle': 'perm_create_update',
    'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT'],
    'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/0-2025-05-14_2025-05-14T16:47:58_1744947e-30b5-11f0-9e7e-0242ac140104.csv',
    'operation': 'add',
    'model': RatePlan,  # You'll need to define this model
    'validators': [validate_permission_create_update],
    'fields': [
        'PermissionName',
        'PermissionCode',
        'ContentType',
        'Action'
    ],
    'upload_restricted_time': 0,
    'save': save_permission_create_update,
    'max_line_count': 1000
},
{
    'id': 122,  # Next available ID
    'name': '(Hotel-Cloud) Group Create/Update',
    'handle': 'group_create_update',
    'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT'],
    'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/0-2025-05-14_2025-05-14T16:25:04_e40af36c-30b1-11f0-9e7e-0242ac140104.csv',
    'operation': 'add',
    'model': RatePlan,  # You'll need to define this model
    'validators': [validate_group_create_update],
    'fields': [
        'GroupName',
        'Action'
    ],
    'upload_restricted_time': 0,
    'save': save_group_create_update,
    'max_line_count': 1000
    },
    {
    'id': 123,  # Next available ID
    'name': '(Hotel-Cloud) User Group Assignment',
    'handle': 'user_group_asnmt',
    'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT'],
    'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/0-2025-05-14_2025-05-14T16:27:02_2abb0176-30b2-11f0-af26-0242ac140104.csv',
    'operation': 'add',
    'model': RatePlan,  # You'll need to define this model
    'validators': [validate_user_group_assignment],
    'fields': [
        'GroupName',
        'UserID',
        'Action'  # This will handle ASSIGN/UNASSIGN operations
    ],
    'upload_restricted_time': 0,
    'save': save_user_group_association,
    'max_line_count': 1000
    },
    {
        'id': 124,  # Next available ID
        'name': '(Hotel-Cloud)Assign Permission To User Or Group',
        'handle': 'permission_asnmt',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/0-2025-05-14_2025-05-14T16:29:26_8078c878-30b2-11f0-af26-0242ac140104.csv',
        'operation': 'add',
        'model': RatePlan,
        'validators': [validate_permission_assignment],
        'fields': [
            'ContentType',  # USER or GROUP
            'ContentID',    # User ID or Group ID
            'Permission',  # PermissionId
            'Action'       # ADD or REMOVE
        ],
        'upload_restricted_time': 0,
        'save': save_permission_assignment,
        'max_line_count': 1000
    },
    {
        'id': 125,
        'name': 'Derby RatePlan inclusion suppression',
        'handle': 'derby_rp_suppress',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT', 'ROLE_SUPPLY_CHAINS_INTL_EXCLUSIVE_SUPPORT', 'ROLE_SUPPLY_IND_ALTACCO_ZM'],
        'request_users': ['user'],
        'operation': 'add',
        'model': RatePlan,
        'validators': [validate_derby_ratePlan_inclusion_suppression_payload],
        'fields': ['hotelcode', 'is_rateplan_desc_inclusion_suppressed'],
        'save': set_derby_ratePlan_desc_inclusion_suppression_flag,
        'upload_restricted_time': 5,
        'max_line_count': 10000,
        'generate_audit_log': True,
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2025-05-14_2025-05-14T18:07:52.773869_uploadS_42e9ab86-30c0-11f0-a297-0242ac140102.csv',
    },
    {
        'id': 126,
        'name': 'Screen Registry Upsert',
        'handle': 'scr_reg_upsert',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2025-05-22_2025-05-22T00:18:33.222892_ScreenRegistry_321bed8c-3674-11f0-8815-0242ac140104.csv',
        'operation': 'add',
        'model': RatePlan,
        'validators': [validate_screen_registry_upsert],
        'fields': [
            'ScreenName',
            'Client',
            'DisplayText',
            'Action'
        ],
        'upload_restricted_time': 0,
        'save': save_screen_registry_upsert,
        'max_line_count': 1000
    },
    {
        'id': 127,
        'name': 'Screen Assignment to User or Group',
        'handle': 'scr_asn_usrgrp',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2025-05-22_2025-05-22T13:16:10.997682_scrasnusrgrp_d44f6e5a-36e0-11f0-8a9f-0242ac140104.csv',
        'operation': 'add',
        'model': RatePlan,
        'validators': [validate_screen_assignment],
        'fields': [
            'ScreenId',
            'EntityType',      # USER or GROUP
            'EntityId',        # User ID or Group ID
            'PermissionType',
            'Action'           # assign or unassign
        ],
        'upload_restricted_time': 0,
        'save': save_screen_assignment,
        'max_line_count': 1000
    },
    {
        'id': 128,
        'name': 'Api Registry Upsert',
        'handle': 'api_reg_upsert',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2025-05-22_2025-05-22T00:21:47.877123_ApiRegistry_a6220a68-3674-11f0-b1e5-0242ac140104.csv',
        'operation': 'add',
        'model': RatePlan,
        'validators': [validate_api_registry_upsert],
        'fields': [
            'Name',
            'URI',
            'HttpMethod',
            'PermissionType',
            'Action'
        ],
        'upload_restricted_time': 0,
        'save': save_api_registry_upsert,
        'max_line_count': 1000
    },
    {
        'id': 129,
        'name': 'Screen API Assignment',
        'handle': 'scr_api_assign',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/2025-05-22_2025-05-22T00:34:23.514201_ScreenApi_688753be-3676-11f0-8815-0242ac140104.csv',
        'operation': 'add',
        'model': RatePlan,
        'validators': [validate_screen_api_assignment],
        'fields': [
            'ScreenId',
            'APIId',
            'Action'
        ],
        'upload_restricted_time': 0,
        'save': save_screen_api_assignment,
        'max_line_count': 1000
    },
    {
        'id': 130,
        'name': '(Hotel Cloud)Hotel User Assignment',
        'handle': 'hotel_user_asnmt',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/hotel-user-assignment-sample.csv',
        'operation': 'add',
        'model': RatePlan,
        'validators': [validate_hotel_user_assignment],
        'fields': [
            'HotelCloudUserId',
            'HotelCloudUserEmail',
            'HotelCloudHotelCode',
            'Action'
        ],
        'upload_restricted_time': 0,
        'save': save_hotel_user_assignment,
        'max_line_count': 1000
    },
    {
        'id': 131,
        'name': '(Hotel Cloud)User Deactivation',
        'handle': 'user_deactivate',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/user-deactivation-sample.csv',
        'operation': 'add',
        'model': RatePlan,
        'validators': [validate_user_deactivation],
        'fields': [
            'auth_id'
        ],
        'upload_restricted_time': 0,
        'save': save_user_deactivation,
        'max_line_count': 1000
    },
    {
        'id': 132,
        'name': '(Hotel Cloud) Hotel Sync',
        'handle': 'hotel_sync',
        'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT'],
        'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/hotel-sync-sample.csv',
        'operation': 'add',
        'model': RatePlan,
        'validators': [validate_hotel_sync],
        'fields': [
            'IngoHotelId',
            'MMTHotelId'
        ],
        'upload_restricted_time': 0,
        'save': save_hotel_sync,
        'max_line_count': 1000
    },
]
