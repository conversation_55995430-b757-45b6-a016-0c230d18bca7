import sys
import os
import django

os.environ['DJANGO_SETTINGS_MODULE'] = 'goibibo_inventory.settings'

pa = os.path.abspath(os.path.dirname(__file__) + "../")
pa1 = '/usr/local/apache2/htdocs/goibibo_inventory/'
pa1 = '/home/<USER>/go20Jul2015/goibibo_inventory/'
sys.path.append(pa)
sys.path.append(pa1)

django.setup()

module_name = sys.argv[1]
function_name = None
if len(sys.argv) == 4:
    function_name = ' ' + sys.argv[2] + '("' + sys.argv[3] + '")'
elif len(sys.argv) == 3:
    function_name = ' '.join(sys.argv[2:]) + '()'

if function_name:
    exec ('import %s' % module_name)
    exec ('%s.%s' % (module_name, function_name))