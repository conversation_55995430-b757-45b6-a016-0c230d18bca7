from django.contrib.auth.decorators import login_required
from django.db.models import Q
from django.db.models import F
from django.conf import settings
from django.http import HttpResponse
from django.core.cache import cache
from django.core.paginator import Paginator
from django.conf import settings
from django.db import close_old_connections
import mysql.connector
import csv

import datetime
import json
import traceback
import logging
import calendar
import string

from dateutil.relativedelta import relativedelta
from django.template.loader import render_to_string
from lib.custom_decorators import token_required, custom_required

from hotels import hotelchoice
from hotels.models import *
from hotels.models import ChangeHistory, configuration, helper
from hotels.admin.helper import HotelAdminViews
from hotels.admin.hoteldetail import AdminHotelDetail
from hotels.models import configuration as HotelConf
from hotels.models import helper as HotelHelper
from hotels.methods import HotelMethods
from reports.models import AnalyticsModel
from reports import analytics_data
from rates_service.grpc_rates_client import ChangeHistoryClient
from rates_service.rates_formatter.grpc_rates_formatter import get_formatted_request_for_change_history
from rates_service.rates_formatter.grpc_rates_formatter import get_formatted_request_for_getting_rates_and_restrictions
from rates_service.grpc_rates_client import GetRatesClient
from utils.logger import Logger
from inventory_service.inventory_formatter import grpc_inventory_formatter
from inventory_service.grpc_inventory_client import InventoryClient
from hotels.models.roomdetail import RoomDetail

enLogger = logging.getLogger('extranetLogger')
inventory_logger = Logger(logger="inventoryLogger")

admin_views = HotelAdminViews()
hm = HotelMethods()
grpc_change_history_client = ChangeHistoryClient()
get_rates_client = GetRatesClient()
grpc_inventory_client = InventoryClient()

BOOKINGDATE_FORMAT = '%Y-%m-%d'
LOGDATE_FORMAT = '%Y-%m-%d %H:%M:%S'
SELL_IDX = [0,1,2]



@custom_required(token_required, login_required)
def getlogs(request):
    response = {'success': False}
    logList = []
    try:
        hotelcode = request.GET.get('hid', '')
        action = request.GET.get('action', 'new')
        page_id = int(request.GET.get('page_id', 1))
        today = datetime.date.today()
        resp = hm.checkHotelAccessibility(request.user, hotelcode=hotelcode)
        pagination_context = {'is_show_next': 'none', 'next': None, 'is_show_prev': 'none', 'prev': None}
        if resp['success']:
            if hotelcode:
                hotel_object = HotelDetail.objects.get(hotelcode=hotelcode)
                if settings.ENABLE_PHOENIX_CHANGE_HISTORY and hotel_object.enable_phoenix_call:
                    grpc_response = {'success': False}
                    try:
                        url = request.build_absolute_uri()
                        formatted_request = get_formatted_request_for_change_history(request.GET, hotel_object.id, url)
                        grpc_response = grpc_change_history_client.get_change_history_logs(formatted_request)
                        grpc_response['logList'] = grpc_response.pop('logsList', [])
                        return HttpResponse(json.dumps(grpc_response))
                    except Exception as exx:
                        enLogger.critical(message='Error Occured while making grpc call : %s\t%s\t' %
                                                    (str(exx), repr(traceback.format_exc())),
                                            log_type='ingoibibo', bucket='ReportsAPI',
                                            stage='reports.views.change_history.ChangeHistoryViewSet')
                        return HttpResponse(json.dumps(grpc_response))
                else:
                    if action == 'logFilters':
                        fromdate = datetime.datetime.strptime(
                            request.GET.get('fromDate', today), '%m/%d/%Y')
                        todate = datetime.datetime.strptime(
                            request.GET.get('toDate', today), '%m/%d/%Y')

                        if (todate - fromdate).days > 31:
                            response['success'] = False
                            response['message'] = "Maximum window allowed for this " \
                                                  "report: 31 days"

                            return HttpResponse(json.dumps(response))

                        logList = get_form_filtered_log_data(request)
                    logList = getLogDataFromObjList(logList)
                    if logList:
                        if page_id > 1:
                            pagination_context['is_show_next'] = 'block'
                            pagination_context['is_show_prev'] = 'block'
                            pagination_context['prev_page'] = page_id-1
                            pagination_context['next_page'] = page_id+1
                        else:
                            pagination_context['is_show_next'] = 'block'
                            pagination_context['next_page'] = 2
                    else:
                        if page_id > 1:
                            pagination_context['is_show_prev'] = 'block'
                            pagination_context['prev_page'] = page_id-1

                    response['pagination_context'] = pagination_context
                    response['logList'] = logList
                    response['action'] = action
                    response['success'] = True
                    response['logstype'] = request.GET['logstype']
        else:
            context = resp['context'] if 'context' in resp else {}
            response['renderHtml'] = render_to_string('extranet/page403.html', context)
    except Exception, e:
        enLogger.critical(
            '%s\t%s\t%s\t%s\t%s\t%s' % ('extranet', 'views', 'getlogs', '', str(e), repr(traceback.format_exc())))
    return HttpResponse(json.dumps(response))


def get_form_filtered_log_data(request):
    logList = []
    try:
        hotelcode = request.GET.get('hid', '')
        hotel_id = HotelHelper.get_id_from_code(hotelcode, HotelConf.HotelCodeLength, HotelConf.HotelCodePrefix)
        today = datetime.datetime.strftime(datetime.datetime.today(), '%m/%d/%Y')
        fromdate = datetime.datetime.strptime(request.GET.get('fromDate', today), '%m/%d/%Y')
        todate = datetime.datetime.strptime(request.GET.get('toDate', today), '%m/%d/%Y')
        oneday = datetime.timedelta(days=1)
        roomtype = request.GET['roomtypecode']
        logstype = request.GET['logstype']
        logFilter = request.GET['filtertype']
        page = int(request.GET.get('page_id', 1))
        if logFilter == 'modification':
            if logstype == 'inventory':
                logList = ChangeHistory.objects.filter(hotel=hotel_id, modifiedon__gte=fromdate,
                                                       modifiedon__lte=(todate + oneday),
                                                       rateplancode__isnull=True).order_by('-modifiedon')
            elif logstype == 'rates':
                logList = ChangeHistory.objects.filter(hotel=hotel_id, modifiedon__gte=fromdate,
                                                       modifiedon__lte=(todate + oneday),
                                                       rateplancode__isnull=False).order_by('-modifiedon')
            else:
                logList = ChangeHistory.objects.filter(hotel=hotel_id, modifiedon__gte=fromdate,
                                                       modifiedon__lte=(todate + oneday),
                                                       ).order_by('-modifiedon')
        elif logFilter == 'stay':
            if logstype == 'inventory':
                logList = ChangeHistory.objects.filter(hotel=hotel_id, fromdate__lte=todate,
                                                       todate__gte=fromdate, rateplancode__isnull=True).\
                                                       order_by('-modifiedon')
            elif logstype == 'rates':
                logList = ChangeHistory.objects.filter(hotel=hotel_id, fromdate__lte=todate,
                                                       todate__gte=fromdate, rateplancode__isnull=False).\
                                                       order_by('-modifiedon')
            else:
                logList = ChangeHistory.objects.filter(hotel=hotel_id, fromdate__lte=todate,
                                                       todate__gte=fromdate,
                                                       ).order_by('-modifiedon')
        if roomtype:
            logList = logList.filter(roomtypecode=roomtype)
        # if fromdate == todate:
        #     # In case fromdate = todate it picks up all ch_objs whose fromdate is less than todate
        #     # and todate less than todate. This leads to a large set of data and gateway timeout.
        #     logList = logList.filter(fromdate=F('todate'))
        logList = Paginator(logList, settings.CHANGE_HISTORY_EXTRANET_PAGE_SIZE).page(page)
    except Exception, e:
        enLogger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
        'extranet', 'views', 'get_form_filtered_log_data', '', str(e), repr(traceback.format_exc())))
        logList = []
    return logList


def getLogDataFromObjList(logObjList):
    logList = []
    for l in logObjList:
        changetype = 'inventory'
        change = ''
        if l.rateplancode:
            changetype = 'rate'
            change = AdminHotelDetail.get_changeRateString(str(l.note))
        else:
            change = AdminHotelDetail.get_changeInventoryString(str(l.note))
        daylist = admin_views.get_daysofweek(str(l.daylist))
        logDict = {}
        logDict['action_time'] = (l.modifiedon).strftime(LOGDATE_FORMAT)
        logDict['username'] = l.username
        logDict['success'] = l.is_success
        logDict['fromdate'] = (l.fromdate).strftime(BOOKINGDATE_FORMAT)
        logDict['todate'] = (l.todate).strftime(BOOKINGDATE_FORMAT)
        logDict['daylist'] = daylist
        logDict['room_name'] = l.room_name
        logDict['rateplan_name'] = l.rateplan_name
        logDict['change_message'] = change
        logList.append(logDict)
    return logList


def getDisplayDates(dateslist):
    daylist = ''
    datedict = {}
    keys = set()
    for day in dateslist:
        keys.add(day[:-2])
        if day[:-2] not in datedict:
            datedict[day[:-2]] = []
        datedict[day[:-2]].append(day[-2:])
    for key in datedict:
        monthlist = ''
        for day in datedict[key]:
            monthlist += str(day) + ', '
        daylist += calendar.month_name[int(key[-2:])] + '-' + key[:-2] + '(' + monthlist[:-2] + '), '
    return daylist[:-2]


def getDisplayText(subkey, availtype):
    text_dict = {
        'blockdates': 'Blocked Dates',
        'cutoffdates': 'Dates with CutOff > 1',
        'zeroinventorydates': 'ZERO Inventory Dates',
        'unallocateddates': 'Dates with NO ' + availtype + ' Allocation',
        'availabilitypercentage': availtype + ' Health',
        'mlos': 'Dates with MLOS > 3' if availtype == 'Inventory' else 'MLOS'

    }

    return text_dict[subkey]


def updateCrReportDict(inventory, i, reportdict):
    reportdict['days'] += 1
    if 'p' in inventory and int(inventory['p']):
        reportdict['pl'] += int(inventory['p'])
    if 'v' in inventory and int(inventory['v']):
        reportdict['v'] += int(inventory['v'])


def getConversionRateDict(hvid, startdate, enddate):
    reportDict = {'pl': 0, 'v': 0, 'days': 0}
    today = startdate
    durationindays = (enddate - today).days + 1
    currentmonth = today.month
    enddatemonth = enddate.month
    startyear = today.year
    enddateyear = enddate.year

    monthyearlist = []
    tmpdate = startdate.replace(day=1)
    duration = 0
    while tmpdate <= enddate:
        x, monthduration = calendar.monthrange(tmpdate.year, tmpdate.month)
        duration += monthduration
        if tmpdate.month == enddate.month and tmpdate.year == enddate.year:
            delta = monthduration - enddate.day
            duration -= delta
        if tmpdate.month == today.month and tmpdate.year == today.year:
            duration -= (today.day - 1)
        if tmpdate.month % 2 == 0 or (
                        tmpdate.month % 2 == 1 and tmpdate.month == enddate.month and tmpdate.year == enddate.year):
            monthyeardict = {}
            monthyeardict['col'] = hotelchoice.month2[tmpdate.month]
            monthyeardict['year'] = tmpdate.year
            monthyeardict['duration'] = duration
            monthyearlist.append(monthyeardict)
            duration = 0
        tmpdate += relativedelta(months=1)

    start = currentmonth * 100 + today.day
    end = enddatemonth * 100 + enddate.day
    if startyear == enddateyear:
        atList = AnalyticsModel.objects.filter(hotelvid=hvid, year=startyear)
    else:
        atList = AnalyticsModel.objects.filter(hotelvid=hvid).filter(Q(year=startyear) | Q(year=enddateyear))

    for atObj in atList:
        for monthyear in monthyearlist:
            inventmonth = atObj.__getattribute__(monthyear['col'])
            if inventmonth and monthyear['year'] == atObj.year:
                jsonmonth = eval(inventmonth)
                datelist = sorted(jsonmonth.keys(), key=lambda n: int(n))
                for i in jsonmonth:
                    if startyear == enddateyear:
                        if int(i) >= start and int(i) <= end:
                            updateCrReportDict(jsonmonth[str(i)], i, reportDict)
                    elif atObj.year == startyear and int(i) >= start:
                        updateCrReportDict(jsonmonth[str(i)], i, reportDict)
                    elif atObj.year == enddateyear and int(i) <= end:
                        updateCrReportDict(jsonmonth[str(i)], i, reportDict)

    return reportDict


def get_view_data_from_redis(report_name, hotel_code):
    result = {'data': '', 'success': False}
    today = datetime.date.today().strftime('%Y-%m-%d')
    key = '%s_%s_%s' % (report_name, hotel_code, today)
    value = cache.get(key)
    if value:
        result['data'] = value
        result['success'] = True
    return result


def get_view_data_from_redis_or_generate(report_name, hotel_code, start_date, end_date):
    """

    :param report_name:
    :param hotel_voyager_id:
    :param start_date:
    :param end_date:
    :return: if in DEBUG mode, return random image, else returns value if found in cache, else generates the value and
    returns status as false.
    """
    result = {'success': False, 'data': ''}
    value = get_view_data_from_redis(report_name, hotel_code)

    if value['success']:
        result['data'] = value['data']
        result['success'] = True
    else:
        result = generate_analytics_report(report_name, hotel_code, start_date, end_date)
    return result


def generate_analytics_report(report_name, hotel_code, start_date, end_date):
    result = {'success': False, 'data': ''}
    cache_daily = 60 * 60 * 24
    func_to_call = getattr(analytics_data, report_name)
    image_url = func_to_call(hotel_code, start_date, end_date)
    today = datetime.date.today().strftime('%Y-%m-%d')
    key = '%s_%s_%s' % (report_name, hotel_code, today)
    if image_url['success']:
        cache.set(key, image_url['url'], cache_daily)
        result['success'] = True
        result['data'] = image_url['url']
    return result


def updateAnalyticsData(atObj, vid, data_dict, monthyearlist, startyear, enddateyear, start, end):
    if vid not in data_dict:
        data_dict[vid] = {'pl': 0, 'v': 0, 'days': 0}
    for monthyear in monthyearlist:
        inventmonth = atObj.__getattribute__(monthyear['col'])
        if inventmonth and monthyear['year'] == atObj.year:
            jsonmonth = eval(inventmonth)
            datelist = sorted(jsonmonth.keys(), key=lambda n: int(n))
            for i in jsonmonth:
                if startyear == enddateyear:
                    if int(i) >= start and int(i) <= end:
                        updateCrReportDict(jsonmonth[str(i)], i, data_dict[vid])
                elif atObj.year == startyear and int(i) >= start:
                    updateCrReportDict(jsonmonth[str(i)], i, data_dict[vid])
                elif atObj.year == enddateyear and int(i) <= end:
                    updateCrReportDict(jsonmonth[str(i)], i, data_dict[vid])


def getConversionRateDictForAllHotels(startdate, enddate, city_data_flag=False):
    reportDict = {}
    # sampleDict = {'pl':0, 'v':0, 'days':0}
    today = startdate
    durationindays = (enddate - today).days + 1
    currentmonth = today.month
    enddatemonth = enddate.month
    startyear = today.year
    enddateyear = enddate.year
    monthyearlist = []
    tmpdate = startdate.replace(day=1)
    duration = 0
    while tmpdate <= enddate:
        x, monthduration = calendar.monthrange(tmpdate.year, tmpdate.month)
        duration += monthduration
        if tmpdate.month == enddate.month and tmpdate.year == enddate.year:
            delta = monthduration - enddate.day
            duration -= delta
        if tmpdate.month == today.month and tmpdate.year == today.year:
            duration -= (today.day - 1)
        if tmpdate.month % 2 == 0 or (
                        tmpdate.month % 2 == 1 and tmpdate.month == enddate.month and tmpdate.year == enddate.year):
            monthyeardict = {}
            monthyeardict['col'] = hotelchoice.month2[tmpdate.month]
            monthyeardict['year'] = tmpdate.year
            monthyeardict['duration'] = duration
            monthyearlist.append(monthyeardict)
            duration = 0
        tmpdate += relativedelta(months=1)

    start = currentmonth * 100 + today.day
    end = enddatemonth * 100 + enddate.day
    if startyear == enddateyear:
        atList = AnalyticsModel.objects.filter(hotelvid__isnull=False, year=startyear)
    else:
        atList = AnalyticsModel.objects.filter(hotelvid__isnull=False).filter(Q(year=startyear) | Q(year=enddateyear))

    for atObj in atList:
        hvid = str(atObj.hotelvid)
        updateAnalyticsData(atObj, hvid, reportDict, monthyearlist, startyear, enddateyear, start, end)

    if not city_data_flag:
        return reportDict

    city_dict = {}
    if startyear == enddateyear:
        atList = AnalyticsModel.objects.filter(
            hotelvid__isnull=False, hotelvid=F('cityvid'), year=startyear)
    else:
        atList = AnalyticsModel.objects.filter(
            hotelvid__isnull=False, hotelvid=F('cityvid')).filter(Q(year=startyear) | Q(year=enddateyear))

    for atObj in atList:
        cvid = str(atObj.cityvid)
        updateAnalyticsData(atObj, cvid, city_dict, monthyearlist, startyear, enddateyear, start, end)

    return reportDict, city_dict

def get_inventory_report(start_date, end_date, all_room_types, full_inventory=False):
    all_room_codes = set(all_room_types.values_list('roomtypecode', flat=True))

    keys = ['availabilitypercentage', 'blockdates', 'cutoffdates', 'zeroinventorydates', 'unallocateddates', 'mlos']
    report_dict = {room: {key: [] for key in keys} for room in all_room_codes}

    today = start_date  # datetime.date.today()
    duration_in_days = (end_date - today).days + 1

    date_itr = today
    if full_inventory:
        while date_itr <= end_date:
            for key in report_dict:
                if 'inventory' not in report_dict[key]:
                    report_dict[key]['inventory'] = {}
                if 'cutoff' not in report_dict[key]:
                    report_dict[key]['cutoff'] = {}
                report_dict[key]['inventory'][date_itr.strftime('%Y%m%d')] = 0
                report_dict[key]['cutoff'][date_itr.strftime('%Y%m%d')] = -13
            date_itr += datetime.timedelta(days=1)

    close_old_connections()

    room_codes = [str(room_type_code) for room_type_code in all_room_codes]
    room_code_for_hotel = room_codes[0]
    room_obj = RoomDetail.objects.select_related("hotel").get(roomtypecode=room_code_for_hotel)

    try:
        hotel_code = room_obj.hotel.hotelcode
        request_data = grpc_inventory_formatter.format_grpc_request_get_inventory('room', room_codes, start_date.strftime('%Y-%m-%d'),
                                                                        end_date.strftime('%Y-%m-%d'), hotel_code, 'get_inventory_report')
        response_data = grpc_inventory_client.get_inventory(request_data)
        update_inventory_report_dict_from_inventory_response(report_dict, response_data, full_inventory=full_inventory)
    except Exception, e:
        inventory_logger.critical(
            message='error: %s traceback: %s' % (str(e), repr(traceback.format_exc())),
            log_type='ingoibibo',
            bucket='report',
            stage='get_inventory_report')

    for key in report_dict:
        unavailable_dates = []
        unavailable_dates.extend(report_dict[key].get('unallocateddates', []))
        unavailable_dates.extend(report_dict[key].get('blockdates', []))
        unavailable_dates.extend(report_dict[key].get('zeroinventorydates', []))
        not_available_duration = len(set(unavailable_dates))
        report_dict[key]['availabilitypercentage'].append((int(duration_in_days) - not_available_duration) * 100 /
                                                          int(duration_in_days))

    return report_dict


def getInventoryCutoff(cutoffvalue):
    if ord(str(cutoffvalue)[-1]) < 64:
        invencutoff = int(cutoffvalue) * 24
    elif cutoffvalue[-1] == 'h':
        invencutoff = int(cutoffvalue[:-1])
    else:
        invencutoff = int(cutoffvalue[:-1]) * 24
    return invencutoff


def updateReportDict(inventory, i, reportdict, inventoryobj, fullInventory=False):
    dateKey = str(inventoryobj.year * 10000 + int(i))
    if dateKey in reportdict[inventoryobj.roomtypeid]['unallocateddates']:
        if 'al' in inventory:
            reportdict[inventoryobj.roomtypeid]['unallocateddates'].remove(dateKey)
    if 'bl' in inventory and inventory['bl']:
        reportdict[inventoryobj.roomtypeid]['blockdates'].append(dateKey)
    if 'a' in inventory and inventory['a'] == 0:
        reportdict[inventoryobj.roomtypeid]['zeroinventorydates'].append(dateKey)
    if 'c' in inventory and getInventoryCutoff(inventory['c']) > 24:
        reportdict[inventoryobj.roomtypeid]['cutoffdates'].append(dateKey)
    if 'n' in inventory and inventory['n'] > 3:
        reportdict[inventoryobj.roomtypeid]['mlos'].append(dateKey)
    if fullInventory:
        if 'a' in inventory and (not inventory.get('bl')):
            reportdict[inventoryobj.roomtypeid]['inventory'][dateKey] = inventory['a']
        if 'c' in inventory:
            reportdict[inventoryobj.roomtypeid]['cutoff'][dateKey] = getInventoryCutoff(inventory['c'])


def update_inventory_report_dict(report_dict, inventory_obj, full_inventory=False):
    date = inventory_obj.idate
    date_key = date.strftime('%Y%m%d')
    room_id = inventory_obj.room_code
    if inventory_obj.available == 0:
        report_dict[room_id]['zeroinventorydates'].append(date_key)
    if inventory_obj.cutoff > 24:
        report_dict[room_id]['cutoffdates'].append(date_key)
    if inventory_obj.min_los > 3:
        report_dict[room_id]['mlos'].append(date_key)
    if inventory_obj.blocked:
        report_dict[room_id]['blockdates'].append(date_key)
    if not inventory_obj.available and not inventory_obj.booked:
        report_dict[room_id]['unallocateddates'].append(date_key)
    if full_inventory:
        if inventory_obj.available is not None and not inventory_obj.blocked:
            report_dict[room_id]['inventory'][date_key] = inventory_obj.available
        if inventory_obj.cutoff is not None:
            report_dict[room_id]['cutoff'][date_key] = inventory_obj.cutoff

def update_inventory_report_dict_from_grpc_response(report_dict, inventory_grpc_response, full_inventory=False):
    for data_item in inventory_grpc_response.get('response_data', [{}])[0].get('data', [{}]):
        room_code = data_item['product_code']
        for inventory_item in data_item.get('product_data', []):
            date_str = inventory_item['idate']
            date_obj = datetime.datetime.strptime(date_str, '%Y-%m-%d').date()
            date_key = date_obj.strftime('%Y%m%d')
            available = inventory_item['inventory'].get('available', {}).get('value', 0)
            booked = inventory_item['inventory'].get('booked', {}).get('value', 0)
            blocked = inventory_item['restrictions'].get('block', {}).get('value', False)
            if available == 0:
                report_dict[room_code]['zeroinventorydates'].append(date_key)
            if blocked:
                report_dict[room_code]['blockdates'].append(date_key)
            if not available and not booked:
                report_dict[room_code]['unallocateddates'].append(date_key)
            if full_inventory:
                if available is not None and not blocked:
                    report_dict[room_code]['inventory'][date_key] = available

def update_inventory_report_dict_from_inventory_response(report_dict, inventory_response, full_inventory=False):
    for data_item in inventory_response.get('response_data', [{}])[0].get('data', [{}]):
        product_data = data_item.get('product_data', [])
        for inventory_item in product_data:            
            date = datetime.datetime.strptime(inventory_item['idate'], '%Y-%m-%d').date()
            date_key = date.strftime('%Y%m%d')
            room_id = data_item['product_code']

            available = 0
            booked = 0
            blocked = False
            if 'inventory' in inventory_item:
                inventory_data = inventory_item['inventory']
                available = inventory_data.get('available', None)
                if available is not None:
                    available = available.get('value', 0)
                booked = inventory_data.get('booked', None)
                if booked is not None:
                    booked = booked.get('value', 0)
            if 'restrictions' in inventory_item:
                blocked = inventory_item['restrictions'].get('block', {}).get('value', False)
    
            if available == 0:
                report_dict[room_id]['zeroinventorydates'].append(date_key)
            if blocked:
                report_dict[room_id]['blockdates'].append(date_key)
            if not available and not booked:
                report_dict[room_id]['unallocateddates'].append(date_key)
            if full_inventory:
                if available is not None and not blocked:
                    report_dict[room_id]['inventory'][date_key] = available


# will work fine for less than equal to 365 days
# def getRateReport(startdate, enddate, allrateplans, full_rates=False):
#     from api.v1.reports.views import is_price_valid
#     rateplan_val_list = allrateplans.values_list('id', 'rateplancode')
#     rateplanid_list = []
#     rateplancode_list = []
#     for rateplan_val in rateplan_val_list:
#         rateplanid_list.append(rateplan_val[0])
#         rateplancode_list.append(rateplan_val[1])

#     keys = ['availabilitypercentage', 'blockdates', 'unallocateddates', 'mlos']
#     report_dict = {rateplan: {key: [] for key in keys} for rateplan in rateplancode_list}

#     duration_in_days = (enddate - startdate).days + 1
#     dateitr = startdate
#     date_list = []
#     while dateitr <= enddate:
#         for key in report_dict:
#             report_dict[key]['unallocateddates'].append(dateitr.strftime('%Y%m%d'))
#             if full_rates:
#                 if 'rates' not in report_dict[key]:
#                     report_dict[key]['rates'] = {}
#                 report_dict[key]['rates'][dateitr.strftime('%Y%m%d')] = 0
#         date_list.append(dateitr)
#         dateitr += datetime.timedelta(days=1)

#     # This is not getting used
#     get_rates_request = get_formatted_request_for_getting_rates_and_restrictions(
#         'rate_plan', rateplancode_list, ['all'], startdate.strftime('%Y-%m-%d'), enddate.strftime('%Y-%m-%d'),
#         '', 'RatePlanRatesReportViewSet', True, False)
#     get_rates_response = get_rates_client.get_rates(get_rates_request)
#     response_data = get_rates_response.get('response_data', [])
#     data = response_data[0].get('data', [])
#     for product_data in data:
#         rates_data_list = product_data.get('product_data', [])
#         product_code = product_data.get('product_code', '')
#         for rates_data in rates_data_list:
#             date_string = rates_data['idate']
#             date_key = date_string.replace('-', '')
#             price_available = False
#             price_val = 0
#             sell_price = rates_data.get('rates', {}).get('sell_price', [])
#             for price in sell_price:
#                 if is_price_valid(price):
#                     price_available = True
#                     price_val = float(price)
#                     break
#             if price_available and date_key in report_dict[product_code]['unallocateddates']:
#                 report_dict[product_code]['unallocateddates'].remove(date_key)
#                 if full_rates:
#                     report_dict[product_code]['rates'][date_key] = price_val

#     for key in report_dict:
#         mlos = 1
#         if allrateplans.filter(rateplancode=key):
#             mlos = allrateplans.filter(rateplancode=key)[0].minnumofnights
#         report_dict[key]['mlos'].append(mlos)
#         report_dict[key]['availabilitypercentage'].append(
#             (int(duration_in_days) - len(report_dict[key]['unallocateddates'])) * 100 / int(duration_in_days))

#     return report_dict


# will work fine for less than equal to 365 days
def getOffer(hotelIds, startdate, enddate, allroomtypes, allrateplans):
    #    hotelid = HotelHelper.get_id_from_code(hotelcode, HotelConf.HotelCodeLength, HotelConf.HotelCodePrefix)
    allRoomTypeSet = set(list(allroomtypes.values_list('id', flat=True)))
    allRatePlanSet = set(list(allrateplans.values_list('id', flat=True)))

    hotelkeys = ['roomcode', 'roomname', 'rpcode', 'rpname']
    roomdict = {str(room.id): {key: '' for key in hotelkeys} for room in allroomtypes}
    rateplandict = {str(rateplan.id): {key: '' for key in hotelkeys} for rateplan in allrateplans}
    for room in allroomtypes:
        roomdict[str(room.id)]['roomcode'] = room.roomtypecode
        roomdict[str(room.id)]['roomname'] = room.roomtypename
        roomdict[str(room.id)]['hotelid'] = room.hotel_id
    for rateplan in allrateplans:
        rateplandict[str(rateplan.id)]['roomcode'] = rateplan.roomtype.roomtypecode
        rateplandict[str(rateplan.id)]['roomname'] = rateplan.roomtype.roomtypename
        rateplandict[str(rateplan.id)]['rpcode'] = rateplan.rateplancode
        rateplandict[str(rateplan.id)]['rpname'] = rateplan.rateplanname
        rateplandict[str(rateplan.id)]['hotelid'] = rateplan.roomtype.hotel_id

    durationindays = (enddate - startdate).days + 1
    today = datetime.datetime.combine(startdate, datetime.time(0))
    enddate = datetime.datetime.combine(enddate, datetime.time(0))

    # fix this one as well
    hoteloffers = HotelOfferCondition.objects.filter( \
        Q(checkoutdateend__gte=today) | Q(bookingdateend__gte=today), \
        content_type__model='hoteldetail', object_id__in=hotelIds, \
        isactive=True)
    # hoteltopoffers = hoteloffers.filter(showtop=True)
    roomoffers = HotelOfferCondition.objects.filter( \
        Q(checkoutdateend__gte=today) | Q(bookingdateend__gte=today), \
        content_type__model='roomdetail', object_id__in=allRoomTypeSet, \
        isactive=True)
    rateplanoffers = HotelOfferCondition.objects.filter( \
        Q(checkoutdateend__gte=today) | Q(bookingdateend__gte=today), \
        content_type__model='rateplan', object_id__in=allRatePlanSet, \
        isactive=True)
    mainkeys = ['active', 'expiring']
    keys = ['hoteloffers', 'roomoffers', 'rateplanoffers']
    reportdict = {mainkey: {key: [] for key in keys} for mainkey in mainkeys}

    if not (hoteloffers or roomoffers or rateplanoffers):
        reportdict['offers'] = 'N'
    else:
        reportdict['offers'] = 'Y'

    getOfferStatus(reportdict, today, enddate, hoteloffers, roomoffers, rateplanoffers, roomdict, rateplandict)

    #    return_dict
    return reportdict


def checkOffer(reportdict, offer, offerdict, offertype, today, enddate):
    offerdict['offerdetails'] = offer.description
    offerdict['checkindateend'] = offer.checkoutdateend
    offerdict['bookingdateend'] = offer.bookingdateend
    offerdict['checkindatestart'] = offer.checkindatestart
    offerdict['bookingdatestart'] = offer.bookingdatestart
    offerdict['category'] = offer.offercategory
    if (offerdict['bookingdateend'] and offerdict['bookingdateend'] <= enddate) or \
            (offerdict['checkindateend'] and offerdict['checkindateend'] <= enddate.date()):
        reportdict['expiring'][offertype].append(offerdict)
    if offerdict['bookingdatestart'] and offerdict['checkindatestart']:
        if offerdict['bookingdatestart'] <= enddate and offerdict['checkindatestart'] <= enddate.date():
            if not offerdict['bookingdateend'] or offerdict['bookingdateend'] >= today:
                if not offerdict['checkindateend'] or offerdict['checkindateend'] >= today.date():
                    reportdict['active'][offertype].append(offerdict)
    elif (offerdict['bookingdatestart'] and offerdict['bookingdatestart'] <= enddate and offerdict[
        'bookingdateend'] >= today) or \
            (offerdict['checkindatestart'] and offerdict['checkindatestart'] <= enddate.date()):
        reportdict['active'][offertype].append(offerdict)


def getOfferStatus(reportdict, today, enddate, hoteloffers, roomoffers, rateplanoffers, rooms, rateplans):
    subkeys = ['hotelid', 'roomtypecode', 'roomname', 'rateplancode', 'rateplanname',
               'offerdetails', 'checkindateend', 'bookingdateend']
    if hoteloffers:
        for offer in hoteloffers:
            offerdict = {subkey: '' for subkey in subkeys}
            offerdict['hotelid'] = str(offer.object_id)
            checkOffer(reportdict, offer, offerdict, 'hoteloffers', today, enddate)
    if roomoffers:
        for offer in roomoffers:
            offerdict = {subkey: '' for subkey in subkeys}
            offerdict['hotelid'] = rooms[str(offer.object_id)]['hotelid']
            offerdict['roomtypecode'] = rooms[str(offer.object_id)]['roomcode']
            offerdict['roomname'] = rooms[str(offer.object_id)]['roomname']
            checkOffer(reportdict, offer, offerdict, 'roomoffers', today, enddate)
    if rateplanoffers:
        for offer in rateplanoffers:
            offerdict = {subkey: '' for subkey in subkeys}
            offerdict['hotelid'] = rateplans[str(offer.object_id)]['hotelid']
            offerdict['roomtypecode'] = rateplans[str(offer.object_id)]['roomcode']
            offerdict['roomname'] = rateplans[str(offer.object_id)]['roomname']
            offerdict['rateplancode'] = rateplans[str(offer.object_id)]['rpcode']
            offerdict['rateplanname'] = rateplans[str(offer.object_id)]['rpname']
            checkOffer(reportdict, offer, offerdict, 'rateplanoffers', today, enddate)



#will work fine for less than equal to 365 days
def getCancellationPolicyReport(hotelcode, startdate, enddate, allroomtypes, allrateplans):
    hotelid = HotelHelper.get_id_from_code(hotelcode, HotelConf.HotelCodeLength, HotelConf.HotelCodePrefix)
    #    allroomtypeIDs = list(RoomDetail.objects.filter(hotel=hotelid, isactive=True).values_list('id', flat=True))
    allRoomTypeSet = set(allroomtypes.values_list('id', flat=True))
    #    allrateplanIDs = RatePlan.objects.filter(roomtype_id__in=allRoomTypeSet, isactive=True)
    allRatePlanSet = set(list(allrateplans.values_list('id', flat=True)))

    subkeys = ['unallocateddates']
    reportdict = {HotelHelper.update_code(rateplan, HotelConf.RatePlanCodeLength, HotelConf.RatePlanCodePrefix): \
                      {subkey: [] for subkey in subkeys} for rateplan in allRatePlanSet}
    reportdict[hotelcode] = {'unallocateddates': []}
    for rateplan in allrateplans:
        key = HotelHelper.update_code(rateplan.id, HotelConf.RatePlanCodeLength, HotelConf.RatePlanCodePrefix)
        reportdict[key]['roomname'] = rateplan.roomtype.roomtypename
        reportdict[key]['rateplanname'] = rateplan.rateplanname

    durationindays = (enddate - startdate).days + 1
    today = startdate

    validcprules = HotelCancellationRule.objects.filter( \
        Q(content_type__model='rateplan', object_id__in=allRatePlanSet) \
        | Q(content_type__model='hoteldetail', object_id=hotelid)) \
        .filter(isactive=True) \
        .filter(Q(staystart__lte=enddate, stayend__gte=today, bookingdatestart__lte=enddate, bookingdateend__gte=today) \
                | Q(staystart__lte=enddate, stayend__gte=today))

    hotelcprules = validcprules.filter(content_type__model='hoteldetail')
    rateplancprules = validcprules.filter(content_type__model='rateplan')

    dateitr = today
    while dateitr <= enddate:
        for key in reportdict:
            reportdict[key]['unallocateddates'].append(dateitr.strftime('%Y%m%d'))
        dateitr += datetime.timedelta(days=1)

    if (hotelcprules):
        for rule in hotelcprules:
            dateitr = rule.staystart
            while dateitr <= rule.stayend:
                if dateitr.strftime('%Y%m%d') in reportdict[hotelcode]['unallocateddates']:
                    reportdict[hotelcode]['unallocateddates'].remove(dateitr.strftime('%Y%m%d'))
                dateitr += datetime.timedelta(days=1)
    if (rateplancprules):
        for rule in rateplancprules:
            rateplancode = HotelHelper.update_code(rule.object_id, HotelConf.RatePlanCodeLength,
                                                   HotelConf.RatePlanCodePrefix)
            dateitr = rule.staystart
            while dateitr <= rule.stayend:
                if dateitr.strftime('%Y%m%d') in reportdict[rateplancode]['unallocateddates']:
                    reportdict[rateplancode]['unallocateddates'].remove(dateitr.strftime('%Y%m%d'))
                dateitr += datetime.timedelta(days=1)

    for key in reportdict:
        keyid = 0
        if key == hotelcode:
            ruledict = hm.getObjectDictWithType(hotelcprules, 'cprule')
            keyid = hotelid
        else:
            ruledict = hm.getObjectDictWithType(rateplancprules, 'cprule')
            keyid = allrateplans.filter(rateplancode=key)[0].id
        ruleList = ruledict.pop(keyid, [])
        reportdict[key]['status'] = getIncompleteCancellationPolicy(ruleList)
        reportdict[key]['cppercentage'] = (int(durationindays) - len(reportdict[key]['unallocateddates'])) * 100 / int(
            durationindays)

    #    return_dict
    return reportdict


def getIncompleteCancellationPolicy(cancellationRulesList):
    status = 'Complete'
    if cancellationRulesList:
        startdayList = map(lambda rule: rule.startday, cancellationRulesList)
        enddayList = map(lambda rule: rule.endday, cancellationRulesList)

        maxdayfromlist = max(enddayList)
        today = datetime.date.today()
        stayendList = map(lambda rule: rule.stayend, cancellationRulesList)
        maxstayend = max(stayendList)
        maxdays = (maxstayend - today).days
        if maxdayfromlist <= maxdays:
            print maxdayfromlist, maxdays
        if maxdayfromlist <= maxdays or 0 not in startdayList:
            status = 'Incomplete'
    else:
        status = 'Empty'
    return status



@custom_required(token_required, login_required)
def getConversionRateReport(request):
    response = {'success': False}
    crList = []
    try:
        hotelcode = request.GET.get('hid', '')
        action = request.GET.get('action', 'new')
        today = datetime.date.today()
        resp = hm.checkHotelAccessibility(request.user, hotelcode=hotelcode)
        if resp['success']:
            if hotelcode:
                if action == 'crFilters':
                    crList = getFormFilteredCrData(request);
                response['crList'] = crList
                response['success'] = True
        else:
            context = resp['context'] if 'context' in resp else {}
            response['renderHtml'] = render_to_string('extranet/page403.html', context)
    except Exception, e:
        enLogger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
        'extranet', 'views', 'getConversionRateReport', '', str(e), repr(traceback.format_exc())))
    return HttpResponse(json.dumps(response))


def getFormFilteredCrData(request):
    crList = []
    try:
        hotelcode = request.GET.get('hid', '')
        hotel_id = HotelHelper.get_id_from_code(hotelcode, HotelConf.HotelCodeLength, HotelConf.HotelCodePrefix)
        today = datetime.datetime.strftime(datetime.datetime.today(), '%m/%d/%Y')
        fromdate = datetime.datetime.strptime(request.GET.get('fromDate', today), '%m/%d/%Y')
        todate = datetime.datetime.strptime(request.GET.get('toDate', today), '%m/%d/%Y')

        reportdict = {}
    except Exception, e:
        enLogger.critical('%s\t%s\t%s\t%s\t%s\t%s' % (
        'extranet', 'views', 'getFormFilteredCrData', '', str(e), repr(traceback.format_exc())))

    #    return_dict
    return crList

def get_advance_payment_summer(vendor_code, start_date, advancetype, vendor):
    sub_doc = get_subdoctype(advancetype)
    posting_group = ''
    if vendor == 'MakeMyTrip':
        posting_group = "and vle.VendorPostingGroup in %s" % hotelchoice.PAYMENT_VENDOR_POSTING_GROUP
    query = "select max(dvle.amount), sum(dvle.amount), vle.Description, vle.ChequeNo, vle.EntryNo from htl_VendorLedgerEntryFull as vle inner join htl_DetailedVendorLedgerEntry as dvle on dvle.VendorLedgerEntryNo=vle.EntryNo where vle.VendorNo = '{vendor_id}' and vle.PostingDate > '{start_date}' " \
            "and vle.SubDocumentType in ({sub_doc}) {posting_group} GROUP BY vle.EntryNo, DATE(vle.PostingDate);".format(vendor_id=vendor_code, start_date=start_date, sub_doc=sub_doc, posting_group=posting_group)
    result = get_data_summer(vendor=vendor, query=query, needbankref=True)
    csvfile = open('/tmp/advance_vendor_%s.csv' % (vendor_code), 'w')
    writer = csv.DictWriter(csvfile, fieldnames=['Amount Accrued', 'Amount Utilised (Till Date)', 'Amount Remaining', 'Description', 'Bank Reference'])
    writer.writeheader()
    if result:
        writer.writerows(result)
    csvfile.close()
    csvfile = open('/tmp/advance_vendor_%s.csv' % (vendor_code), 'r')
    return csvfile


def get_advance_payment_detail(vendor_code, start_date, advancetype, vendor):
    from hotels.navision_payment import get_mmt_fulldbdata, get_fulldbdata
    sub_doc = get_subdoctype(advancetype)
    posting_group = ''
    if vendor == 'MakeMyTrip':
        posting_group = "and vle.VendorPostingGroup in %s" % hotelchoice.PAYMENT_VENDOR_POSTING_GROUP
    query = "select max(dvle.amount), sum(dvle.amount), vle.Description, vle.ChequeNo, vle.EntryNo from htl_VendorLedgerEntryFull as vle inner join htl_DetailedVendorLedgerEntry as dvle on dvle.VendorLedgerEntryNo=vle.EntryNo where vle.VendorNo = '{vendor_id}' and vle.PostingDate > '{start_date}' " \
            "and vle.SubDocumentType in ({sub_doc}) {posting_group} GROUP BY vle.EntryNo, DATE(vle.PostingDate);".format(
        vendor_id=vendor_code, start_date=start_date, sub_doc=sub_doc, posting_group=posting_group)
    rows = get_data_summer(vendor=vendor, query=query, needentryno=True)
    booking_ids = []
    for row in rows:
        booking_ids.extend(get_booking_list(entry_no=row.get('Entry No'), start_date=start_date, sub_doc=sub_doc, vendor=vendor))
    from hotels.navision_payment import create_master_dict2
    detail_report = []
    for booking_id in booking_ids:
        rows = []
        if vendor == 'MakeMyTrip':
            rows = get_mmt_fulldbdata(start=0, end=1, arg_vendorbookingid=booking_id[0], vendor_code = vendor_code)
        else:
            rows = get_fulldbdata(start=0, end=1, arg_vendorbookingid=booking_id[0], vendor_code = vendor_code)
        if not rows:
            inventory_logger.info(
                message='missing booking object: : %s' % booking_id,
                log_type='ingoibibo',
                bucket='NavisionPayment',
                stage='get_advance_payment_detail')
        booking_detail = get_booking_summary(rows=rows, vendor=vendor, vendor_code=vendor_code)
        if booking_detail.get(booking_id[0]):
            detail_report.append(booking_detail[booking_id[0]])
    csvfile = open('/tmp/advance_payment_detail_%s.csv' % (vendor_code), 'w')
    writer = csv.DictWriter(csvfile, fieldnames=['Booking ID', 'PNR No', 'CheckIn', '#Nights', 'Guest Name', 'Vendor Code', 'Total Amount Paid', 'Payment Date', 'Payment Ref.', 'Total Amount Adjusted', 'Adjustment Ref.'])

    writer.writeheader()
    writer.writerows(detail_report)
    csvfile.flush()
    csvfile.close()

    csvfile = open('/tmp/advance_payment_detail_%s.csv' % (vendor_code), 'r')
    return csvfile

def get_subdoctype(type):
    subdoc_mapping = {
        'Advance Payment': 25,
        'plb': 19,
    }
    return subdoc_mapping.get(type, None)


def get_data_summer(vendor, query, needentryno=False, needbankref=False):
    try:
        config = {
            'user': 'payment',
            'password': settings.NAVISION_MYSQL_DATA_PASSWORD,
            'host': settings.DATABASES["ingo-analytics-slave"]["HOST"],
            'database': 'back_payment'
        }
        if vendor == 'MakeMyTrip':
            config = {
                'user': 'payment',
                'password': settings.NAVISION_MYSQL_DATA_PASSWORD,
                'host': settings.DATABASES["ingo-analytics-slave"]["HOST"],
                'database': 'htl_reports'
            }
        cnx = mysql.connector.connect(**config)
        cur = cnx.cursor(buffered=True)
        cur.execute(query)
        result = []
        for (total, remaining, description, ChequeNo, EntryNo) in cur:
            temp = {
                'Amount Accrued': float(total),
                'Amount Utilised (Till Date)': float(total - remaining),
                'Description': description,
                'Amount Remaining': float(remaining),
            }
            if needbankref:
                temp.update({'Bank Reference': ChequeNo})
            if needentryno:
                temp.update({'Entry No': EntryNo})
            result.append(temp)
        return result
    except Exception, e:
        inventory_logger.info(
            message='extranet:report:get_data_summer %s' % e,
            log_type='ingoibibo',
            bucket='NavisionPayment',
            stage='get_data_summer')
        return []

def get_booking_list(entry_no, start_date, sub_doc, vendor='MakeMyTrip'):
    config = {
        'user': 'payment',
        'password': settings.NAVISION_MYSQL_DATA_PASSWORD,
        'host': settings.DATABASES["ingo-analytics-slave"]["HOST"],
        'database': 'back_payment'
    }
    if vendor == 'MakeMyTrip':
        config = {
            'user': 'payment',
            'password': settings.NAVISION_MYSQL_DATA_PASSWORD,
            'host': settings.DATABASES["ingo-analytics-slave"]["HOST"],
            'database': 'htl_reports'
        }
    cnx = mysql.connector.connect(**config)
    cur = cnx.cursor(buffered=True)
    query = "select distinct(dvle.DocumentNo) from htl_VendorLedgerEntryFull as vle inner join htl_DetailedVendorLedgerEntry as dvle on vle.EntryNo = dvle.VendorLedgerEntryNo where vle.EntryNo={entry_no} and vle.PostingDate > '{start_date}' and vle.SubDocumentType={sub_doc};".format(entry_no=entry_no, start_date=start_date, sub_doc=sub_doc)
    cur.execute(query)
    ## sql_injection id: 11 999-1009 R
    document_list = []
    for doc_no in cur:
        document_list.append(doc_no)
    document_list_values = [data[0] for data in document_list]
    entry_no_values = entry_no if isinstance(entry_no, list) else [entry_no]
    doc_placeholders = ','.join(['%s'] * len(document_list_values))
    entry_placeholders = ','.join(['%s'] * len(entry_no_values))
    query = (
        "select vle.BookingNo from htl_DetailedVendorLedgerEntry as dvle "
        "inner join htl_VendorLedgerEntryFull as vle on dvle.VendorLedgerEntryNo=vle.EntryNo "
        "where dvle.DocumentNo in ({doc_placeholders}) and dvle.VendorLedgerEntryNo not in ({entry_placeholders});"
    ).format(doc_placeholders=doc_placeholders, entry_placeholders=entry_placeholders)
    params = document_list_values + entry_no_values
    result = []
    cur.execute(query, params)
    ## sql_injection id: 10 1006-1016 R
    for (booking_id) in cur:
        result.append(booking_id)
    return result

def get_booking_summary(rows = [], vendor='Goibibo', vendor_code=None):
    result = {}

    def key_getter(row, key):
        try:
            str(row.get(key))
            return row.get(key)
        except Exception:
            return ''

    for row in rows:
        try:
            booking_id = key_getter(row, 'BookingNo')
            adj_booking_id = key_getter(row, 'adj_pnr')
            vendorbookingid = key_getter(row, 'VendorBookingId')
            d_documenttype = key_getter(row, 'dDocumentType')
            v_documenttype = key_getter(row, 'vDocumentType')
            d_entrytype = key_getter(row, 'dEntryType')
            abookingid = key_getter(row, 'aBookingID')
            row_vendor_booking = key_getter(row, 'VendorBookingId')
            row_hotelcode = key_getter(row, 'HotelCode')
            row_vendorNo = key_getter(row, 'VendorNo')
            description = key_getter(row, 'Description')
            new_pnr = key_getter(row, 'newBookingNo')
            new_adjustment_pnr = key_getter(row, 'new_adj_pnr')
            discription =  key_getter(row, 'Description')
            payment_date = row.get('Date')
            bank_ref = key_getter(row, 'UTR')
            createdon = row.get('BookingDate'),
            checkin = row.get('checkin')
            checkout = row.get('checkout')
            customer_name = key_getter(row, 'customer_name')
            number_of_night = None
            if checkin and checkout:
                number_of_night = (checkout - checkin).days
            try:
                amount = float(row.get('Amount', 0))
            except:
                amount = float(0)
            txn_type = ''
            if d_entrytype != 2:
                continue
            if v_documenttype == 2 and d_documenttype == 1:
                txn_type = 'payment'
            elif v_documenttype == 2 and d_documenttype in (3, 0):
                txn_type = 'adjustment'
            elif v_documenttype in (3, 0) and d_documenttype in (3, 0):
                txn_type = 'recovery'
            if txn_type in ('adjustment', 'recovery') and abookingid == row.get('VendorBookingId'):
                continue
            if not txn_type:
                continue
            if vendorbookingid not in result:
                result.update({vendorbookingid: {
                    'Booking ID': vendorbookingid, 'CheckIn': checkin,
                    '#Nights': number_of_night, 'Guest Name': customer_name, 'Total Amount Paid': 0,
                    'Payment Ref.': '', 'Total Amount Adjusted': 0, 'Adjustment Ref.': '', 'Vendor Code': vendor_code, 'Payment Date': payment_date, 'PNR No': new_pnr
                }})
            if vendorbookingid in result:
                if txn_type == 'payment':
                    result[vendorbookingid]['Total Amount Paid'] += amount
                    result[vendorbookingid]['Payment Ref.'] += '%s(%s-%s)' % (amount, bank_ref, discription)
                if txn_type in ('adjustment', 'recovery'):
                    result[vendorbookingid]['Total Amount Adjusted'] += abs(amount)
                    result[vendorbookingid]['Adjustment Ref.'] += '%s(%s-%s)' % (amount, abookingid, discription)

        except Exception, e:
            inventory_logger.info(
                message='error: ' % e,
                log_type='ingoibibo',
                bucket='NavisionPayment',
                stage='get_booking_summary')
    return result
