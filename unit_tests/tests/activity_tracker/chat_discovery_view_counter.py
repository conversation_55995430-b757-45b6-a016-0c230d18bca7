import mock

from api.v1.activity_tracker.constants import COUNTER_TTL
from api.v1.activity_tracker.counter.chat_discovery_view_counter import ChatDiscoveryViewCounter
from api.v2.users.tests.UserManagementTests import UnitTestClass


class ChatDiscoveryCounterSuite(UnitTestClass):

    @mock.patch('redis.Redis')
    def test_init_value(self, redis_mock):
        redis_mock.setnx.return_value = True

        counter = ChatDiscoveryViewCounter(10001, 100000001)
        counter.redis_server._client = redis_mock
        counter.init_value()

        redis_value = 1
        redis_key = 'ingo_ibibo:1:activity_tracker:chat_discovery_view:user:10001:hotel:100000001'
        redis_mock.setnx.assert_called_with(redis_key, redis_value)
        redis_mock.expire.assert_called_with(redis_key, COUNTER_TTL)

    @mock.patch('redis.Redis')
    def test_increment_counter(self, redis_mock):
        redis_mock.exists.return_value = True
        redis_mock.incr.return_value = True

        counter = ChatDiscoveryViewCounter(10001, 100000001)
        counter.redis_server._client = redis_mock
        counter.increment_counter()

        redis_key = 'ingo_ibibo:1:activity_tracker:chat_discovery_view:user:10001:hotel:100000001'
        redis_mock.incr.assert_called_with(redis_key, 1)

    @mock.patch('redis.Redis')
    def test_get_value(self, redis_mock):
        redis_mock.get.return_value = 1

        counter = ChatDiscoveryViewCounter(10001, 100000001)
        counter.redis_server._client = redis_mock
        counter.get_value()

        redis_key = 'ingo_ibibo:1:activity_tracker:chat_discovery_view:user:10001:hotel:100000001'
        redis_mock.get.assert_called_with(redis_key)
