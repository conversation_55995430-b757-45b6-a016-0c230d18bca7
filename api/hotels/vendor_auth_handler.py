from django.http import HttpResponse

__author__ = 'keshav'

from hotels.methods import HotelMethods
from utils.libs import goibibo_smartjson as json


class VendorAuthHandler():
    """
    Class for custom vendor authentications
    Accepts 'Authorization' in header with value in format: <username>:<password>

    :returns False if invalid vendor with message: 'Authentication for <PERSON><PERSON><PERSON> failed!' along
      with status 401
             True if vendor OK and sets django request.user object as vendor object
             for APIs to use the same validated vendor.
    """
    def __init__(self, modify_user=True):
        self.hotel_methods = HotelMethods()
        self.modify_user = modify_user

    def authenticate_user(self, request):
        """
        This method just validates normal user NOT vendor

        :param request: request object
        :return: returns user object if found
        """
        user_response = {
            'success': False
        }

        if not request:
            return user_response

        if request.user.is_anonymous():
            if not request.META.get('HTTP_AUTHORIZATION', None):
                return user_response
            vendor_username, vendor_password = request.META['HTTP_AUTHORIZATION'].split(':')

            user_response = self.hotel_methods.validateUser({
                'username': vendor_username,
                'password': vendor_password
            })
        else:
            user_response.update({
                'success': True,
                'user': request.user
            })

        return user_response

    def is_authenticated(self, request):
        """
        This method is required by piston to check the validity. Returns False or True depending
        upon vendor authenticity

        :param request:
        :return:
        """
        user_response = self.authenticate_user(request)

        if user_response.get('success', None):
            vendor_response = self.hotel_methods.validateVendor(user=user_response['user'])

            if vendor_response.get('success', None) and self.modify_user:
                request.user = vendor_response['vendor']
                return True
            elif vendor_response.get('success', None):
                return True
        return False

    def challenge(self):
        return HttpResponse(status=401, content=json.dumps({"error": "Authentication for Vendor failed!"}),
                            content_type='application/json')
