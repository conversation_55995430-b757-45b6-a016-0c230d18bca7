from django.contrib.admin.utils import unquote
from django.shortcuts import get_object_or_404
from rest_framework import status
from rest_framework.decorators import detail_route
from rest_framework.exceptions import ValidationError
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.viewsets import GenericViewSet

from api.v2.contact.resources import send_otp_for_contact, verify_otp_for_contact
from api.v2.hotels.permissions import CaretakerPermissionCheck
from api.v2.hotels.resources.update_hotel_resources import upload_caretaker_image
from api.v2.users.exceptions.custom_exceptions import IllegalOperation
from api.v2.users.resources.constants import ProfileImage
from api.v3.hotels.serializers.caretaker import HotelCaretakerSerializer, HotelCaretakerListSerializer, \
    HotelCaretakerContactSerializer
from common.models import Caretaker
from hotels.models import HotelDetail, GenericContactDetail
from utils.logger import Logger

api_logger = Logger(logger='inventoryAPILogger')


def get_caretaker(hotel, pk):
    return hotel.caretakers.get(pk=unquote(pk), is_active=True)


class HotelCaretakerViewSet(GenericViewSet):
    lookup_field = 'pk'
    permission_classes = (IsAuthenticated, CaretakerPermissionCheck)

    def get(self, request, hotelcode=None, *args, **kwargs):
        """
            This API is to list all caretaker details associated to a hotel.

            URL : {{base_url}}/api/v2/hotel/{hotelcode}/caretakers/
            REQUEST TYPE: GET
        """
        response = {
            'success': False,
            'message': '',
            'data': {
            }
        }
        log_data = request.log_data
        hotel = get_object_or_404(HotelDetail, hotelcode=hotelcode)
        try:
            log_data['api_specific_identifiers']['ingo_hotel_id'] = hotelcode
            response['success'] = True
            response['message'] = 'Caretaker List'
            response['data'] = HotelCaretakerListSerializer(
                hotel.caretakers.filter(is_active=True).prefetch_related('generic_contacts', 'generic_contacts__country'), many=True
            ).data
            response_status = status.HTTP_200_OK

        except ValidationError as e:
            response['message'] = str(e)
            response_status = status.HTTP_400_BAD_REQUEST

        except Exception as e:
            log_data['error']['message'] = str(e)
            response['message'] = "Something went wrong. Please try again later."
            response_status = status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical(
                message='Error Occured', log_type='ingoibibo',
                bucket='HotelCaretakerAPIv2', stage='hotels.views.caretaker.HotelCaretakerViewSet.get',
                identifier='{}'.format(log_data)
            )

        return Response(response, status=response_status)

    def create(self, request, hotelcode=None, *args, **kwargs):
        """
            This API is to create caretaker associated to a hotel.

            URL : {{base_url}}/api/v2/hotel/{hotelcode}/caretakers/
            REQUEST TYPE: POST
        """
        response = {
            'success': False,
            'message': '',
            'data': {
            }
        }
        log_data = request.log_data
        hotel = get_object_or_404(HotelDetail, hotelcode=hotelcode)
        log_data['api_specific_identifiers']['ingo_hotel_id'] = hotelcode
        try:
            serialized_data = HotelCaretakerSerializer(data=request.data)
            serialized_data.is_valid(raise_exception=True)
            caretaker = serialized_data.save(hotel=hotel)

            response['success'] = True
            response['data'] = HotelCaretakerSerializer(caretaker).data
            response['message'] = '{0} added as caretaker successfully'.format(caretaker.name)
            response_status = status.HTTP_200_OK

        except ValidationError as e:
            response['message'] = e.msg
            response_status = status.HTTP_400_BAD_REQUEST

        except Exception as e:
            log_data['error']['message'] = str(e)
            response['message'] = "Something went wrong. Please try again later."
            response_status = status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical(
                message='Error', log_type='ingoibibo',
                bucket='HotelCaretakerAPIv2', stage='hotels.views.caretaker.HotelCaretakerViewSet.create',
                identifier='{}'.format(log_data)
            )

        return Response(response, status=response_status)

    def retrieve(self, request, hotelcode=None, pk=None, *args, **kwargs):
        """
            This API is to retrieve a particular caretaker details associated to a hotel.

            URL : {{base_url}}/api/v2/hotel/{hotelcode}/caretakers/{pk}/
            REQUEST TYPE: GET
        """
        response = {
            'success': False,
            'message': '',
            'data': {
            }
        }
        log_data = request.log_data
        hotel = get_object_or_404(HotelDetail, hotelcode=hotelcode)
        log_data['api_specific_identifiers']['ingo_hotel_id'] = hotelcode
        try:
            caretaker = get_caretaker(hotel, pk)
            response['success'] = True
            response['data'] = HotelCaretakerSerializer(caretaker).data
            response['message'] = 'Caretaker Found'
            response_status = status.HTTP_200_OK

        except Caretaker.DoesNotExist:
            response['message'] = "Hotel Caretaker does not exist"
            response_status = status.HTTP_404_NOT_FOUND

        except ValidationError as e:
            response['message'] = str(e)
            response_status = status.HTTP_400_BAD_REQUEST

        except Exception as e:
            log_data['error']['message'] = str(e)
            response['message'] = "Something went wrong. Please try again later."
            response_status = status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical(
                message='Error', log_type='ingoibibo',
                bucket='HotelCaretakerAPIv2', stage='hotels.views.caretaker.HotelCaretakerViewSet.retrieve',
                identifier='{}'.format(log_data)
            )

        return Response(response, status=response_status)

    def update(self, request, hotelcode=None, pk=None, *args, **kwargs):
        """
            This API is to update a particular caretaker details associated to a hotel.

            URL : {{base_url}}/api/v2/hotel/{hotelcode}/caretakers/
            REQUEST TYPE: PUT
        """
        response = {
            'success': False,
            'message': '',
            'data': {
            }
        }
        log_data = request.log_data
        hotel = get_object_or_404(HotelDetail, hotelcode=hotelcode)
        log_data['api_specific_identifiers']['ingo_hotel_id'] = hotelcode
        try:
            caretaker = get_caretaker(hotel, pk)
            log_data['api_specific_identifiers']['caretaker_id'] = caretaker.id

            serialized_data = HotelCaretakerSerializer(caretaker, data=request.data, partial=True)
            serialized_data.is_valid(raise_exception=True)
            caretaker = serialized_data.save()

            response['success'] = True
            response['data'] = HotelCaretakerSerializer(caretaker).data
            response['message'] = 'Caretaker Updated Successfully'
            response_status = status.HTTP_200_OK

        except Caretaker.DoesNotExist:
            response['message'] = "Hotel Caretaker does not exist"
            response_status = status.HTTP_404_NOT_FOUND

        except ValidationError as e:
            response['message'] = e.msg
            response_status = status.HTTP_400_BAD_REQUEST

        except Exception as e:
            log_data['error']['message'] = str(e)
            response['message'] = "Something went wrong. Please try again later."
            response_status = status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical(
                message='Error', log_type='ingoibibo',
                bucket='HotelCaretakerAPIv2', stage='hotels.views.caretaker.HotelCaretakerViewSet.update',
                identifier='{}'.format(log_data)
            )

        return Response(response, status=response_status)

    def destroy(self, request, hotelcode=None, pk=None, *args, **kwargs):
        """
            This API is to delete a particular caretaker associated to a hotel.

            URL : {{base_url}}/api/v2/hotel/{hotelcode}/caretakers/{pk}/
            REQUEST TYPE: DELETE
        """
        response = {
            'success': False,
            'message': '',
            'data': {
            }
        }
        log_data = request.log_data
        hotel = get_object_or_404(HotelDetail, hotelcode=hotelcode)
        log_data['api_specific_identifiers']['ingo_hotel_id'] = hotelcode
        try:
            caretaker = get_caretaker(hotel, pk)
            log_data['api_specific_identifiers']['caretaker_id'] = caretaker.id

            caretaker.generic_contacts.using("default").filter(is_active=True).update(is_active=False)
            caretaker.is_active = False
            caretaker.save()

            response['success'] = True
            response['message'] = "Caretaker Deleted Successfully"
            response_status = status.HTTP_200_OK

        except Caretaker.DoesNotExist:
            response['message'] = "Hotel Caretaker does not exist"
            response_status = status.HTTP_404_NOT_FOUND

        except ValidationError as e:
            response['message'] = str(e)
            response_status = status.HTTP_400_BAD_REQUEST

        except Exception as e:
            log_data['error']['message'] = str(e)
            response['message'] = "Something went wrong. Please try again later."
            response_status = status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical(
                message='Error', log_type='ingoibibo',
                bucket='HotelCaretakerAPIv2', stage='hotels.views.caretaker.HotelCaretakerViewSet.delete',
                identifier='{}'.format(log_data)
            )

        return Response(response, status=response_status)

    parser_classes = (MultiPartParser, FormParser, JSONParser)

    @detail_route(methods=['put', 'delete'], url_path='image')
    def caretaker_profile_image(self, request, hotelcode=None, pk=None, *args, **kwargs):
        """
            This API is to upload profile image of a caretaker.

            URL : {{base_url}}/api/v2/hotel/{hotelcode}/caretakers/{pk}/image/
            REQUEST TYPE: POST
        """
        response = {
            'success': False,
            'message': '',
            'data': {
            }
        }
        log_data = request.log_data
        hotel = get_object_or_404(HotelDetail, hotelcode=hotelcode)
        log_data['api_specific_identifiers']['ingo_hotel_id'] = hotelcode
        try:
            caretaker = get_caretaker(hotel, pk)

            if request.method == 'PUT':
                response['data'] = upload_caretaker_image(request, caretaker)
                response['message'] = ProfileImage.UPLOAD_IMAGE_SUCCESS

            elif request.method == 'DELETE':
                caretaker.image.using("default").filter(isactive=True).update(isactive=False)
                response['message'] = ProfileImage.DELETE_IMAGE_SUCCESS

            response['success'] = True
            response_status = status.HTTP_200_OK

        except Caretaker.DoesNotExist:
            response['message'] = "Hotel Caretaker does not exist"
            response_status = status.HTTP_400_BAD_REQUEST

        except (ValidationError, IllegalOperation) as e:
            response['message'] = str(e)
            response_status = status.HTTP_400_BAD_REQUEST

        except Exception as e:
            log_data['error']['message'] = str(e)
            response['message'] = "Something went wrong. Please try again later."
            response_status = status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical(
                message='Error: %s' % str(e), log_type='ingoibibo',
                bucket='HotelCaretakerAPIv2', stage='hotels.views.caretaker.HotelCaretakerViewSet.caretaker_profile_image',
                identifier='{}'.format(log_data)
            )

        return Response(response, status=response_status)


class HotelCaretakerContactViewSet(GenericViewSet):
    permission_classes = (IsAuthenticated, CaretakerPermissionCheck)

    def get_contact(self, caretaker, pk):
        return caretaker.generic_contacts.get(pk=unquote(pk), is_active=True)

    def get(self, request, hotelcode=None, c_id=None, *args, **kwargs):
        """
            This API is to list all contacts associated to a caretaker.

            URL : {{base_url}}/api/v2/hotels/{hotelcode}/caretakers/{c_id}/contacts/
            REQUEST TYPE: GET
        """
        response = {
            'success': False,
            'message': '',
            'data': {
            }
        }
        log_data = request.log_data
        hotel = get_object_or_404(HotelDetail, hotelcode=hotelcode)
        log_data['api_specific_identifiers']['ingo_hotel_id'] = hotelcode
        try:
            caretaker = get_caretaker(hotel, c_id)
            log_data['api_specific_identifiers']['caretaker_id'] = caretaker.id

            response['success'] = True
            response['message'] = 'Contacts List'
            response['data'] = HotelCaretakerContactSerializer(
                caretaker.generic_contacts.filter(is_active=True).prefetch_related('country'), many=True).data
            response_status = status.HTTP_200_OK

        except Caretaker.DoesNotExist:
            response['message'] = "Hotel Caretaker does not exist"
            response_status = status.HTTP_404_NOT_FOUND

        except ValidationError as e:
            response['message'] = str(e)
            response_status = status.HTTP_400_BAD_REQUEST

        except Exception as e:
            response['message'] = "Something went wrong. Please try again later."
            response_status = status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical(
                message='Error: %s' % str(e), log_type='ingoibibo',
                bucket='HotelCaretakerContactViewSetAPIv2',
                stage='hotels.views.caretaker.HotelCaretakerContactViewSet.get',
                identifier='{}'.format(log_data)
            )

        return Response(response, status=response_status)

    def create(self, request, hotelcode=None, c_id=None, *args, **kwargs):
        """
            This API is to create contact associated to a caretaker.

            URL : {{base_url}}/api/v2/hotels/{hotelcode}/caretakers/{c_id}/contacts/
            REQUEST TYPE: POST
        """
        response = {
            'success': False,
            'message': '',
            'data': {
            }
        }
        log_data = request.log_data
        hotel = get_object_or_404(HotelDetail, hotelcode=hotelcode)
        log_data['api_specific_identifiers']['ingo_hotel_id'] = hotelcode
        try:
            serialized_data = HotelCaretakerContactSerializer(data=request.data)
            serialized_data.is_valid(raise_exception=True)

            caretaker = get_caretaker(hotel, c_id)
            log_data['api_specific_identifiers']['caretaker_id'] = caretaker.id
            contact = serialized_data.save(caretaker=caretaker)

            response['success'] = True
            response['data'] = HotelCaretakerContactSerializer(contact).data
            response['message'] = 'Successfully Added a new Contact'
            response_status = status.HTTP_200_OK

        except Caretaker.DoesNotExist:
            response['message'] = "Hotel Caretaker does not exist"
            response_status = status.HTTP_404_NOT_FOUND

        except ValidationError as e:
            response['message'] = e.msg
            response_status = status.HTTP_400_BAD_REQUEST

        except Exception as e:
            response['message'] = "Something went wrong. Please try again later."
            response_status = status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical(
                message='Error: %s' % str(e), log_type='ingoibibo',
                bucket='HotelCaretakerContactViewSetAPIv2',
                stage='hotels.views.hotel.HotelCaretakerContactViewSet.create',
                identifier='{}'.format(log_data)
            )

        return Response(response, status=response_status)

    def retrieve(self, request, hotelcode=None, c_id=None, pk=None, *args, **kwargs):
        """
            This API is to retrieve a particular contact detail associated to a caretaker.

            URL : {{base_url}}/api/v2/hotels/{hotelcode}/caretakers/{c_id}/contacts/{id}/
            REQUEST TYPE: GET
        """
        response = {
            'success': False,
            'message': '',
            'data': {
            }
        }
        log_data = request.log_data
        hotel = get_object_or_404(HotelDetail, hotelcode=hotelcode)
        log_data['api_specific_identifiers']['ingo_hotel_id'] = hotelcode
        try:
            caretaker = get_caretaker(hotel, c_id)
            contact = self.get_contact(caretaker, pk)
            log_data['api_specific_identifiers']['caretaker_id'] = caretaker.id
            log_data['api_specific_identifiers']['contact_id'] = contact.id

            response['success'] = True
            response['data'] = HotelCaretakerContactSerializer(contact).data
            response['message'] = 'Retrieved Contact Successfully'
            response_status = status.HTTP_200_OK

        except Caretaker.DoesNotExist:
            response['message'] = "Hotel Caretaker does not exist"
            response_status = status.HTTP_404_NOT_FOUND

        except GenericContactDetail.DoesNotExist:
            response['message'] = "Contact does not exist"
            response_status = status.HTTP_404_NOT_FOUND

        except ValidationError as e:
            response['message'] = str(e)
            response_status = status.HTTP_400_BAD_REQUEST

        except Exception as e:
            response['message'] = "Something went wrong. Please try again later."
            response_status = status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical(
                message='Error: %s' % str(e), log_type='ingoibibo',
                bucket='HotelCaretakerContactViewSetAPIv2',
                stage='hotels.views.caretaker.HotelCaretakerContactViewSet.retrieve',
                identifier='{}'.format(log_data)
            )

        return Response(response, status=response_status)

    def update(self, request, hotelcode=None, c_id=None, pk=None, *args, **kwargs):
        """
            This API is to update a particular contact detail associated to a caretaker.

            URL : {{base_url}}/api/v2/hotels/{hotelcode}/caretakers/{c_id}/contacts/{id}/
            REQUEST TYPE: PUT
        """
        response = {
            'success': False,
            'message': '',
            'data': {
            }
        }
        log_data = request.log_data
        hotel = get_object_or_404(HotelDetail, hotelcode=hotelcode)
        log_data['api_specific_identifiers']['ingo_hotel_id'] = hotelcode
        try:
            caretaker = get_caretaker(hotel, c_id)
            contact = self.get_contact(caretaker, pk)
            log_data['api_specific_identifiers']['caretaker_id'] = caretaker.id
            log_data['api_specific_identifiers']['contact_id'] = contact.id

            # Because if you update contact, contact will get inactive
            # and there wont be a single contact inside contact detail
            if GenericContactDetail.get_count_of_contacts_for_content_object(caretaker) == 1:
                raise ValidationError('Atleast one contact is necessary')

            serialized_data = HotelCaretakerContactSerializer(contact, data=request.data, partial=True)
            serialized_data.is_valid(raise_exception=True)
            contact = serialized_data.save()

            response['success'] = True
            response['data'] = HotelCaretakerContactSerializer(contact).data
            response['message'] = 'Updated Contact Successfully'
            response_status = status.HTTP_200_OK

        except Caretaker.DoesNotExist:
            response['message'] = "Hotel Caretaker does not exist"
            response_status = status.HTTP_404_NOT_FOUND

        except GenericContactDetail.DoesNotExist:
            response['message'] = "Contact does not exist"
            response_status = status.HTTP_404_NOT_FOUND

        except ValidationError as e:
            response['message'] = e.msg
            response_status = status.HTTP_400_BAD_REQUEST

        except Exception as e:
            response['message'] = "Something went wrong. Please try again later."
            response_status = status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical(
                message='Error: %s' % str(e), log_type='ingoibibo',
                bucket='HotelCaretakerContactViewSetAPIv2',
                stage='hotels.views.caretaker.HotelCaretakerContactViewSet.update',
                identifier='{}'.format(log_data)
            )

        return Response(response, status=response_status)

    def destroy(self, request, hotelcode=None, c_id=None, pk=None, *args, **kwargs):
        """
            This API is to delete a particular contact detail associated to a caretaker.

            URL : {{base_url}}/api/v2/hotels/{hotelcode}/caretakers/{c_id}/contacts/{id}/
            REQUEST TYPE: DELETE
        """
        response = {
            'success': False,
            'message': '',
            'data': {
            }
        }
        log_data = request.log_data
        hotel = get_object_or_404(HotelDetail, hotelcode=hotelcode)
        log_data['api_specific_identifiers']['ingo_hotel_id'] = hotelcode
        try:
            caretaker = get_caretaker(hotel, c_id)
            contact = self.get_contact(caretaker, pk)
            log_data['api_specific_identifiers']['caretaker_id'] = caretaker.id
            log_data['api_specific_identifiers']['contact_id'] = contact.id

            if GenericContactDetail.get_count_of_contacts_for_content_object(caretaker) == 1:
                raise ValidationError('Atleast one contact is necessary')

            contact.is_active = False
            contact.save()

            response['success'] = True
            response['message'] = "Deleted Contact Successfully"
            response_status = status.HTTP_200_OK

        except Caretaker.DoesNotExist:
            response['message'] = "Hotel Caretaker does not exist"
            response_status = status.HTTP_404_NOT_FOUND

        except GenericContactDetail.DoesNotExist:
            response['message'] = "Contact does not exist"
            response_status = status.HTTP_404_NOT_FOUND

        except ValidationError as e:
            response['message'] = str(e)
            response_status = status.HTTP_400_BAD_REQUEST

        except Exception as e:
            response['message'] = "Something went wrong. Please try again later."
            response_status = status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical(
                message='Error: %s' % str(e), log_type='ingoibibo',
                bucket='HotelCaretakerContactViewSetAPIv2',
                stage='hotels.views.caretaker.HotelCaretakerContactViewSet.delete',
                identifier='{}'.format(log_data)
            )

        return Response(response, status=response_status)

    @detail_route(methods=['post'], url_path='send-otp')
    def send_otp(self, request, hotelcode=None, c_id=None, pk=None, *args, **kwargs):
        """
            This API is to send otp for a particular contact associated to a caretaker.

            URL : {{base_url}}/api/v2/hotels/{hotelcode}/caretakers/{c_id}/contacts/{id}/send-otp/
            REQUEST TYPE: POST
        """
        response = {
            'success': False,
            'message': '',
            'data': {
            }
        }
        log_data = request.log_data
        hotel = get_object_or_404(HotelDetail, hotelcode=hotelcode)
        log_data['api_specific_identifiers']['ingo_hotel_id'] = hotelcode
        try:
            caretaker = get_caretaker(hotel, c_id)
            contact = self.get_contact(caretaker, pk)
            log_data['api_specific_identifiers']['caretaker_id'] = caretaker.id
            log_data['api_specific_identifiers']['contact_id'] = contact.id

            send_otp_for_contact(request.user, contact, source=request.META.get('HTTP_META_DATA_SOURCE', ''))

            response['success'] = True
            response['message'] = "OTP Sent Successfully"
            response_status = status.HTTP_200_OK

        except Caretaker.DoesNotExist:
            response['message'] = "Hotel Caretaker does not exist"
            response_status = status.HTTP_404_NOT_FOUND

        except GenericContactDetail.DoesNotExist:
            response['message'] = "Contact does not exist"
            response_status = status.HTTP_404_NOT_FOUND

        except (ValidationError, IOError) as e:
            response['message'] = str(e)
            response_status = status.HTTP_400_BAD_REQUEST

        except Exception as e:
            response['message'] = "Something went wrong. Please try again later."
            response_status = status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical(
                message='Error: %s' % str(e), log_type='ingoibibo',
                bucket='HotelCaretakerContactViewSetAPIv2',
                stage='hotels.views.caretaker.HotelCaretakerContactViewSet.send_otp',
                identifier='{}'.format(log_data)
            )

        return Response(response, status=response_status)

    @detail_route(methods=['post'], url_path='verify-otp')
    def verify_otp(self, request, hotelcode=None, c_id=None, pk=None, *args, **kwargs):
        """
            This API is to verify otp for a particular contact associated to a caretaker.

            URL : {{base_url}}/api/v2/hotels/{hotelcode}/caretakers/{c_id}/contacts/{id}/verify-otp/
            REQUEST TYPE: POST
        """
        response = {
            'success': False,
            'message': '',
            'data': {
            }
        }
        log_data = request.log_data
        hotel = get_object_or_404(HotelDetail, hotelcode=hotelcode)
        log_data['api_specific_identifiers']['ingo_hotel_id'] = hotelcode
        try:
            if 'otp' not in request.data:
                raise ValidationError("OTP is mandatory field")

            caretaker = get_caretaker(hotel, c_id)
            contact = self.get_contact(caretaker, pk)
            log_data['api_specific_identifiers']['caretaker_id'] = caretaker.id
            log_data['api_specific_identifiers']['contact_id'] = contact.id

            result, key = verify_otp_for_contact(request.user, request.data['otp'], contact)

            if result:
                response['message'] = "OTP Verified Successfully"
            else:
                response['message'] = "Invalid OTP"

            response['success'] = True
            response_status = status.HTTP_200_OK

        except Caretaker.DoesNotExist:
            response['message'] = "Hotel Caretaker does not exist"
            response_status = status.HTTP_404_NOT_FOUND

        except GenericContactDetail.DoesNotExist:
            response['message'] = "Contact does not exist"
            response_status = status.HTTP_404_NOT_FOUND

        except (ValidationError, IOError) as e:
            response['message'] = str(e)
            response_status = status.HTTP_400_BAD_REQUEST

        except Exception as e:
            response['message'] = "Something went wrong. Please try again later."
            response_status = status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical(
                message='Error: %s' % str(e), log_type='ingoibibo',
                bucket='HotelCaretakerContactViewSetAPIv2',
                stage='hotels.views.caretaker.HotelCaretakerContactViewSet.verify_otp',
                identifier='{}'.format(log_data)
            )

        return Response(response, status=response_status)
