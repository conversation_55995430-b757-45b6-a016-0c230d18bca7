from rest_framework import permissions
from utils.logger import Logger

from common.object_guardian import check_permission
from hotels.models import HotelBooking, HotelDetail

api_logger = Logger(logger="inventoryAPILogger")


class GuardianPermissionCheck(permissions.BasePermission):
    def has_permission(self, request, view):

        # For reviews we have to manually pass voyagerid from client as a get param.
        voyager_id = request.GET.get('voyagerid') \
                     or request.POST.get('voyagerid') or view.kwargs.get('voyagerid')
        if voyager_id:
            try:
                request.hotel = None
                request.hotel = HotelDetail.objects.get(voyagerid=voyager_id)
                return check_permission(request.user, request.hotel, ['view_hoteldetail'])
            except HotelDetail.DoesNotExist:
                return False
            except Exception, e:
                api_logger.critical(message="Permission denied: %s  %s  %s  %s" % (e, view.kwargs,
                                        request.GET, request.POST), log_type='ingoibibo',
                                        bucket='ReviewsAPI', stage='Reviews.has_permission')
                return False
        if hasattr(request, 'hotel'):
            return True
        return False


class BookingPermissionCheck(permissions.BasePermission):
    def has_permission(self, request, view):
        permitted = False
        generic_ids = request.query_params.get('ids')
        if generic_ids:
            generic_ids = generic_ids.split(',')
        else:
            return permitted
        request.genericIds = generic_ids
        booking_ids = generic_ids
        bookings = HotelBooking.objects.select_related('hotel').\
            only('hotel__hotelcode', 'hotel__id', 'id').\
            filter(vendorbookingid__in=booking_ids)
        permitted = True if not bookings else False
        if request.method == 'GET':
            for booking in bookings:
                permitted = check_permission(request.user, booking.hotel, ['view_hotelbooking'],False)
                if not permitted:
                    return permitted
        return permitted
