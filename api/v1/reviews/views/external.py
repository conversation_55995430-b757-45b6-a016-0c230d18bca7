import json
import traceback

from rest_framework import status
from rest_framework.response import Response
from rest_framework.authentication import BasicAuthentication, TokenAuthentication
from rest_framework.decorators import list_route
from rest_framework import viewsets, filters
from rest_framework.permissions import IsAuthenticated

from api.v1.reviews.permissions import *
from django.db import connection
from hotels.methods import HotelMethods
from utils.logger import Logger

hotel_methods = HotelMethods()
log_usage = hotel_methods.extranet_web_log_format
api_logger = Logger(logger="inventoryAPILogger")


USER_QUERY = 'SELECT id, email from auth_user where EMAIL=%s'
HOTEL_QUERY = 'SELECT hotels_hoteldetail.id, voyagerid from hotels_hoteldetail ' \
              'INNER JOIN hotel_hoteluserlink ON (hotels_hoteldetail.id =' \
              ' hotel_hoteluserlink.hoteldetail_id) where (hotel_hoteluserlink.user_id in ' \
              '({user_ids}) AND hotels_hoteldetail.cityfk_id = {cityvoyid})'


class ExternalAPI(viewsets.GenericViewSet):
    authentication_classes = (BasicAuthentication, TokenAuthentication)
    permission_classes = (IsAuthenticated, )
    filter_backends = (filters.DjangoFilterBackend, )
    http_method_names = ['get', 'post']

    @list_route(methods=['get'], url_path='getvoyagerid', permission_classes=(IsAuthenticated,))
    def get_voyagerid(self, request, *args, **kwargs):
        data = json.loads(request.query_params.get('data', '[]'))
        response, stat = {}, status.HTTP_200_OK
        for answer in data:
            hotels = []
            try:
                cityvoyid, email = answer.get('cityVoyId', ''), answer.get('email', '')
                with connection.cursor() as cursor:
                    ## sql_injection id: 14 43-53 R
                    cursor.execute(USER_QUERY, [email])
                    users = cursor.fetchall()
                    user_ids = []
                    for user in users:
                        user_ids.append(str(user[0]))
                    if user_ids:
                        ## sql_injection id: 13 49-59 R
                        placeholders = ','.join(['%s'] * len(user_ids))
                        hotel_query = (
                            'SELECT hotels_hoteldetail.id, voyagerid from hotels_hoteldetail '
                            'INNER JOIN hotel_hoteluserlink ON (hotels_hoteldetail.id = hotel_hoteluserlink.hoteldetail_id) '
                            'where (hotel_hoteluserlink.user_id in ({user_ids}) AND hotels_hoteldetail.cityfk_id = %s)'
                        ).format(user_ids=placeholders)
                        params = user_ids + [cityvoyid]
                        cursor.execute(hotel_query, params)
                        hotels = cursor.fetchall()
                    res_dict = {'cityVoyId': cityvoyid,
                                'hotelVoyagerId': None,
                                'email': email
                                }
                    response[answer['answerId']] = res_dict
                    if hotels:
                        hvid = hotels[0][1]
                        response[answer['answerId']].update({'hotelVoyagerId': hvid})
            except Exception as e:
                message = 'Could not find voyagerid %s %s %s %s %s' % \
                          (e, answer, traceback.format_exc(), hotels, data)
                api_logger.critical(message=message,
                                    log_type='ingoibibo', bucket='ExternalAPI',
                                    stage='ExternalAPI.list')
                response, stat = {}, status.HTTP_500_INTERNAL_SERVER_ERROR
        return Response(response, stat)
