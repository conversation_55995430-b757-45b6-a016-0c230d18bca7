import random
import traceback

from rest_framework import viewsets, status
from rest_framework.authentication import TokenAuthentication, SessionAuthentication, BasicAuthentication
from rest_framework.decorators import list_route
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from api.v1.inventory.resources import bulk_update_hotel_inventory_and_restrictions
from api.v1.rates.resources import bulk_update_hotel_rates_and_restrictions
from api.v1.inventory.permissions.permissions import UpdateHotelInventoryAccess
from api.v1.rates.permissions.permissions import UpdateHotelRatesAccess
from api.v1.validators import validate_request_with_contract
from api.v1.rates_and_inventory.contracts.contracts import update_hotel_restrictions_contract
from hotels.methods import HotelMethods
from utils.logger import Logger

api_logger = Logger(logger="inventoryAPILogger")

hotel_methods = HotelMethods()


class RatesAndInventoryViewSet(viewsets.GenericViewSet):

    authentication_classes = (TokenAuthentication, SessionAuthentication, BasicAuthentication)
    permission_classes = IsAuthenticated
    http_method_names = ["get", "post", "put"]

    @list_route(methods=['put'], url_path="restrictions", permission_classes=[UpdateHotelInventoryAccess, UpdateHotelRatesAccess])
    def stay_restrictions(self, request):
        response = {'success': False, 'message': '', 'data': {}}
        random_number = random.random()
        try:
            request_data = request.data
            request_data['user'] = request.user
            request_data['device_type'] = hotel_methods.get_device_type(request)
            request_data['hotel_object'] = request.hotel_object
            request_data['random_number'] = random_number

            validation_error = validate_request_with_contract(update_hotel_restrictions_contract, request_data)
            if validation_error:
                response['message'] = validation_error
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            if 'inventory' in request_data:
                inventory_restrictions_update_response = bulk_update_hotel_inventory_and_restrictions(request_data,
                                                                                                      False, True)
                response['data']['inventory'] = dict()
                response['data']['inventory'] = inventory_restrictions_update_response['data']
                response['data']['inventory']['message'] = inventory_restrictions_update_response['message']
                response['data']['inventory']['success'] = inventory_restrictions_update_response['success']
            if 'rates' in request_data:
                rates_restrictions_update_response = bulk_update_hotel_rates_and_restrictions(request_data, False, True)
                response['data']['rates'] = dict()
                response['data']['rates'] = rates_restrictions_update_response['data']
                response['data']['rates']['message'] = rates_restrictions_update_response['message']
                response['data']['rates']['success'] = rates_restrictions_update_response['success']
            response['message'] = 'updated successfully'
            response['success'] = True
        except Exception, e:
            api_logger.critical(
                message='Error %s occurred while updating inventory for request %s. random_number %s Traceback : %s' %
                        (str(e), repr(request.data), random_number, repr(traceback.format_exc())), log_type='ingoibibo',
                bucket='views.RatesAndInventoryViewSet', stage='update_stay_restrictions')
            response['message'] = "Exception occurred " + str(e)
            return Response(response, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        return Response(response, status=status.HTTP_200_OK)

