import traceback
import uuid

from ingouser.models import User
from django.core.exceptions import ValidationError
from rest_framework import status, serializers
from rest_framework.authentication import TokenAuthentication, SessionAuthentication, BasicAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.viewsets import GenericViewSet

from api.v1.external.resource import ExternalHotelUpdateRequestFieldValidator, IsHotelAccessibility
from api.v1.users.authentication import JWTAuthentication
from common.services import SpoorsHistoryService
from hotels.services.constant import QC_SPOC_ID_KEY
from utils.logger import Logger

api_logger = Logger(logger='inventoryAPILogger')


class ExternalHotelUpdateViewSet(GenericViewSet):
    hotel_used_fields = ('hotelcode', 'id')
    permission_classes = (IsAuthenticated, IsHotelAccessibility)
    authentication_classes = (TokenAuthentication, SessionAuthentication, BasicAuthentication, JWTAuthentication)
    http_method_names = ['post']

    def create(self, request, *args, **kwargs):
        """
            This API is to update hotel entity related details.
            This will be used by external participants such as Spoors.

            URL : {{base_url}}/api/v1/external/update-hotel/
            METHOD: POST
            Request: {
                "type": 1, This defines the type of request. For e.g. type 1 is spoors qc update request
                ...Update data...
            }
            Response: {
                "request_id": "955f673d-6b3a-482b-a583-405c9c0a08d9"
                "message": "Hotel Data Updated Successfully",
                "details": null,
                "success": true
            }
        """
        request_id = str(uuid.uuid4())
        response = {
            'request_id': request_id,
            'success': False,
            'message': 'Data sent is invalid',
        }
        try:

            api_logger.info("Request data for uuid: {} - {}".format(request_id, request.data), log_type='ingoibibo',
                            bucket='external.ExternalHotelUpdateViewSet', stage='post')
            valid, details = ExternalHotelUpdateRequestFieldValidator.validate_fields(request.data)
            if valid:
                ticket_id = details.pop('ticket_id', None)
                qc_spoc_email = details.get('qc_spoc_email', None)
                # CHECKUSERCHANGE - DONE
                qc_user = User.objects.filter(email=qc_spoc_email).first()
                if not qc_user:
                    raise ValidationError("QC User not found")
                spoors_metadata = {QC_SPOC_ID_KEY: qc_user.id}
                SpoorsHistoryService.update_spoors_metadata(ticket_id, request.hotel, spoors_metadata, request_id)
                message = 'Data updated Successfully'
                response['message'] = message
                response['success'] = True
                response_status = status.HTTP_200_OK
            else:
                api_logger.info(
                    message='ValidationError details: {} uuid: {}'.format(details, request_id),
                    log_type='ingoibibo',
                    bucket='external.ExternalHotelUpdateViewSet', stage='post'
                )
                response['detail'] = details
                response_status = status.HTTP_400_BAD_REQUEST

        except (serializers.ValidationError, ValidationError) as e:
            api_logger.critical(
                message='ValidationError errors: {} uuid: {}'.format(repr(traceback.format_exc()), request_id),
                log_type='ingoibibo',
                bucket='external.ExternalHotelUpdateViewSet', stage='post'
            )
            response['detail'] = repr(e)
            response_status = status.HTTP_400_BAD_REQUEST

        except Exception as e:
            response['message'] = "Something went wrong. Please try again later"
            response_status = status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical(
                message='Error: {} uuid: {}'.format(repr(traceback.format_exc()), request_id), log_type='ingoibibo',
                bucket='external.ExternalHotelUpdateViewSet', stage='post'
            )
        api_logger.info("Response data for uuid {} - {}".format(request_id, response), log_type='ingoibibo',
                        bucket='fake_details.ExternalHotelUpdateViewSet', stage='post')
        return Response(response, status=response_status)
