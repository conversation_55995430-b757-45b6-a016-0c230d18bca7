import json
import random
from datetime import timed<PERSON>ta
from timeit import default_timer

from django.conf import settings
from django.contrib.admin.models import LogEntry, CHANGE
from ingouser.models import User
from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import ValidationError
from django.core.cache import cache
from django.utils.encoding import force_unicode

from api.v2.users.resources.constants import UserDeletionReasonsConstants
from scripts.user_deletion.helpers import UserDeletion
from utils.logger import Logger
from hotels.models import configuration as hotel_conf
from hotels.models import helper as hotel_helper
from hotels.models.hoteldetail import HotelDetail
from api.v1.users.serializers.hotel_user_serializer import AccountDeletionReasonSerializer
from common.object_guardian import assign_permissions, remove_permissions
from hotels.tasks import assign_permission_task, remove_permission_task
from django.db import connection
from hotels.constants.constants import ALLOWED_TA_USER_GROUP_IDS

api_logger = Logger(logger="inventoryAPILogger")


class UserMgmtConstants:
    INVALID_USER_META_DATA = "Invalid user metadata found resulting in conflicting user details."
    USER_DOES_NOT_EXIST = "The user does not exist."
    STAFF_USER = "This is a Staff User!"
    USER_DELETION_TASK_INITIATED = "User will be successfully deleted."
    LEARN_MORE_CTA_NODE = {
        'url' : "https://{host}/api/content/delete-user-learn-more".format(host=settings.HOST),
        'text' : 'Learn More'
    }
    PERMISSION_DENIED_TO_UNASSIGN_ALL_HOTELS = "Make another user as host of this property first to unassign it from this user."

def start_permission_task(user, hotel_ids, task):
    """
    Takes care of delegating the tasks to celery if needed.

    :param user: `User` instance
    :param hotel_ids: hotel object ids
    :param task: either `remove_permission_task` or `assign_permission_task`
    :return: None if all good
    """
    if settings.DEBUG:
        task(user, hotel_ids)
        return

    start = 0
    stop = len(hotel_ids)
    step = settings.MAX_HOTEL_PER_CELERY
    for _ in xrange(start, stop, step):
        task_identifier = random.random()
        api_logger.info(
            message="%s\t%s\t%s\t%s" % (task.__name__, user.username, hotel_ids[_],
                                        "Permission Task Initation"),
            log_type="ingoibibo", bucket="UserAPI", stage="start_permission_task")
        task.apply_async(args=(user, hotel_ids[_:_ + step],),
                         kwargs={"task_identifier": task_identifier})


def get_hotels_to_be_added_or_removed(user, final_hotel_codes):
    # Getting extra column beforehand to avoid multiple queries
    # which gets fired because of model attributes irrespective of
    # whether we use those columns or not
    current_hotels = user.hotels.all().only("hotelcode", "id",
                                                     "flag_bits_1", "city",
                                                     "hotelname")

    current_hotels = map(lambda _: _.id, current_hotels)
    final_hotel_codes = map(
        lambda x: int(hotel_helper.get_id_from_code(x, hotel_conf.HotelCodeLength, hotel_conf.HotelCodePrefix)),
        final_hotel_codes
    )
    to_be_added = list(set(final_hotel_codes) - set(current_hotels))
    to_be_removed = list(set(current_hotels) - set(final_hotel_codes))

    if to_be_removed:
        ignore_hotel_ids = HotelDetail.filter_rtb_properties_with_user_as_only_host(to_be_removed, user.id)
        to_be_removed = list(set(to_be_removed) - set(ignore_hotel_ids))

    return to_be_added, to_be_removed


def filter_hotels_by_user_group(user, hotels_to_add, log_data=None):
    """
    Filter a list of hotel IDs to only include those in the same group as the user.

    :param user: User instance
    :param hotels_to_add: List of hotel IDs to be added
    :param log_data: Dictionary for logging
    :return: List of hotel IDs that are in the same group as the user
    """
    from hotels.models import HotelDetail

    if not hotels_to_add:
        return []

    api_logger.info(
        message='Filtering hotels by group - original to_be_added: {}'.format(hotels_to_add),
        log_type='ingoibibo',
        bucket='UserAPI',
        stage='user.resources.filter_hotels_by_user_group',
        identifier='{}'.format(log_data or {})
    )

    # Get the user's assigned groups
    user_groups = list(user.groups.all().values_list('id', flat=True))

    api_logger.info(
        message='User groups: {}'.format(user_groups),
        log_type='ingoibibo',
        bucket='UserAPI',
        stage='user.resources.filter_hotels_by_user_group',
        identifier='{}'.format(log_data or {})
    )

    # Initialize list to store hotels that can be added
    hotels_to_be_finally_added = []

    # Fetch all hotels at once to avoid multiple queries
    hotels = HotelDetail.objects.filter(id__in=hotels_to_add)

    # For each hotel, check if it belongs to any of the user groups
    for hotel in hotels:
        # Get hotel group from hotel's related data
        hotel_groups = hotel.hotel_related_json_data.get('userGroup', [])

        api_logger.info(
            message='Checking hotel group - hotel_id: {}, hotel_groups: {}, user_groups: {}'.format(
                hotel.id, hotel_groups, user_groups),
            log_type='ingoibibo',
            bucket='UserAPI',
            stage='user.resources.filter_hotels_by_user_group',
            identifier=log_data or {}
        )

        # Handle None case
        if not hotel_groups:
            # Option: Allow hotels with no groups to be assigned without restriction
            hotels_to_be_finally_added.append(hotel.id)
            continue  # Skip the rest of the loop for this hotel


        if isinstance(hotel_groups,int):
            hotel_groups = [hotel_groups]

        filtered_user_groups = set(user_groups) & ALLOWED_TA_USER_GROUP_IDS
        filtered_hotel_groups = set(hotel_groups) & ALLOWED_TA_USER_GROUP_IDS

        if filtered_user_groups and filtered_hotel_groups and filtered_user_groups & filtered_hotel_groups:
            hotels_to_be_finally_added.append(hotel.id)

    # Hotels that weren't added due to group mismatch
    hotels_not_in_group = list(set(hotels_to_add) - set(hotels_to_be_finally_added))

    api_logger.info(
        message='Hotels filtered - allowed: {}, not in same group: {}'.format(
            hotels_to_be_finally_added, hotels_not_in_group),
        log_type='ingoibibo',
        bucket='UserAPI',
        stage='user.resources.filter_hotels_by_user_group',
        identifier='{}'.format(log_data or {})
    )

    return hotels_to_be_finally_added

def host_eligible_to_be_removed(user_id, to_be_removed):
    from hotels.models import HotelUserLink
    user_hosting_in_removing_hotels = HotelUserLink.objects.filter(user_id=user_id, hoteldetail_id__in=to_be_removed, is_host=1).exists()
    if user_hosting_in_removing_hotels:
        return False
    return True

def update_hotel_assignment(user, to_be_added, to_be_removed, request_user=None, log_data={'api_specific_identifiers': {}, 'error': {}, 'request_id':''}):
    """

    :param final_hotel_codes: final list of hotels to remain assigned to user
    :param user: user to which the list has to be assigned
    :return: None if all good
    """
    from api.v2.users.tasks import clear_hotel_ids_cache

    user_id = None
    if request_user:
        user_id = request_user.id
    log_data['api_specific_identifiers']['change_message'] = "Update hotel assignment, added: {0}, removed: {1}".format(to_be_added, to_be_removed)
    log_data['api_specific_identifiers']['changed_user'] = user.id
    log_data['api_specific_identifiers']['requesting_user'] = user_id

    api_logger.info(
        message='update_hotel_assignment, user: {0}, to_be_added: {1}, to_be_removed: {2}, request_user: {3}'.
            format(user.id, to_be_added, to_be_removed, user_id),
        log_type='ingoibibo', bucket='api.v1.users.resources.resources', stage='update_hotel_assignment',
        identifier='{}'.format(log_data)
    )

    if request_user:
        LogEntry.objects.log_action(
        user_id=request_user.pk,
        content_type_id=ContentType.objects.get_for_model(User).pk,
        object_id=user.pk,
        object_repr=force_unicode(user),
        action_flag=CHANGE,
        change_message="Update hotel assignment, added: {0}, removed: {1} ".format(to_be_added, to_be_removed)
    )

    user.hotels.add(*to_be_added)
    user.hotels.remove(*to_be_removed)

    # Reset the Hotel User Mapping cache
    if to_be_added or to_be_removed:
        cacheKey = "all_child_hotels_list_%s" % user.username
        codeCacheKey = "all_hotelcodelist_%s" % user.username
        cache.set(cacheKey, [])
        cache.set(codeCacheKey, [])

    if to_be_removed:
        if len(to_be_removed) > settings.ENABLE_CELERY_QUEUE_LIMIT:
            start_permission_task(user, to_be_removed, remove_permission_task)
        elif not remove_permissions(user, to_be_removed):
            raise ValidationError("Unable to delete permissions")

    if to_be_added:
        if len(to_be_added) > settings.ENABLE_CELERY_QUEUE_LIMIT:
            start_permission_task(user, to_be_added, assign_permission_task)
        elif not assign_permissions(user, to_be_added):
            raise ValidationError("Unable to add permissions")

    if not user.is_staff:
        # Invalidating cache keys for hard blocker middleware.
        hotel_ids = to_be_added + to_be_removed
        if settings.DEBUG:
            clear_hotel_ids_cache(hotel_ids)
        else:
            api_logger.info(
            message="clear_hotel_ids_cache for \t%s\t%s" % (user.username,
                                        "HardBlock Hotel Cache Task Initiation"),
            log_type="ingoibibo", bucket="update_hotel_assignment", stage="hard_block_hotel_cache_clear",
            identifier='{}'.format(log_data))

            clear_hotel_ids_cache.apply_async(args=(hotel_ids,))

    # Intentionally triggering the user save to refresh cache and other tasks.
    user.save()

def hotel_assignment(user, hotel_id, log_data={'api_specific_identifiers': {}, 'error': {}, 'request_id':''}):
    """

    :param user: user to which the hotel has to be assigned
    :param hotel_id:
    :return: None if all good
    """

    if hotel_id:
        user.hotels.add(hotel_id)
        cacheKey = "all_child_hotels_list_%s" % user.username
        codeCacheKey = "all_hotelcodelist_%s" % user.username
        cache.set(cacheKey, [])
        cache.set(codeCacheKey, [])
        if not assign_permissions(user, hotel_id, log_data=log_data):
            return False, "User doesn't have permissions"
        user.save()
    return True, "Successfully assigned permissions"


def validate_user_deletion(request, log_data={'api_specific_identifiers': {}, 'error': {}, 'request_id': ''}):
    from api.v2.users.exceptions.custom_exceptions import IllegalOperation

    request_user = request.user

    if not request_user:
        raise IllegalOperation(UserMgmtConstants.USER_DOES_NOT_EXIST)
    else:
        data = dict()
        data['deleteUser'] = True
        data['blockReason'] = []

        org = request.META.get('HTTP_ORG')
        uuid = get_uuid_from_request_header(request.META)
        api_logger.info(
            message="validate_user_deletion for \t%s\t%s" % (uuid,
                                                             org),
            log_type="ingoibibo", bucket="userAPI", stage="validate_user_deletion",
            identifier='{}'.format(log_data))
        validation_user = None
        if uuid and org:
            try:
                start_time = default_timer()
                ingo_user_id = get_ingo_user_from_sso_mapping(uuid, org)
                end_time = default_timer()
                total = timedelta(seconds=end_time - start_time).total_seconds()
                log_data['api_specific_identifiers']['ingo_user_id'] = ingo_user_id
                api_logger.info(
                    message="time taken for sso mapping based user : %s, ingo user id - %d" % (total, ingo_user_id),
                    log_type="ingoibibo", bucket="userAPI", stage="validate_user_deletion",
                    identifier='{}'.format(log_data))
                validation_user = User.objects.get(id=ingo_user_id)
            except Exception:
                validation_user = None
        else:
            validation_user = request_user
        if not validation_user:
            api_logger.info(
                message="No valid user found, exiting | uuid: {}, org: {}, request_user:{}".format(uuid, org, request_user),
                log_type="ingoibibo", bucket="userAPI", stage="validate_user_deletion", identifier='{}'.format(log_data))
            return data

        if validation_user.is_staff:
            raise ValidationError(UserMgmtConstants.STAFF_USER)

        for func in UserDeletion.validate_functions:
            try:
                start_time = default_timer()
                response, status = func(validation_user)
                end_time = default_timer()
                total = timedelta(seconds=end_time - start_time).total_seconds()
                api_logger.info(
                    message="time taken for %s validation method : %s" % (func.__name__, total),
                    log_type="ingoibibo", bucket="userAPI", stage="validate_user_deletion",
                    identifier='{}'.format(log_data))
                if 'success' not in response or response['success']:
                    continue
                api_logger.info(
                    message="%s validation didn't pass. Adding block reason." % (func.__name__),
                    log_type="ingoibibo", bucket="userAPI", stage="validate_user_deletion",
                    identifier='{}'.format(log_data))
                if 'data' in response:
                    for data_item in response['data']:
                        data_item['cta'] = UserMgmtConstants.LEARN_MORE_CTA_NODE
                    data['deleteUser'] = False
                    data['blockReason'].extend(response['data'])
                elif 'message' in response:
                    raise Exception(response['message'])
            except Exception as e:
                log_data['error']['message'] = e.message
                api_logger.critical(
                    message="Exception in validate_user_deletion : \t%s" % (e.message),
                    log_type="ingoibibo", bucket="userAPI", stage="validate_user_deletion",
                    identifier='{}'.format(log_data))
                raise e
        return data


def check_for_otp_verified(email, log_data):
    # check if request.email is present in same redis_key
    # if not, then we can assume that the otp is not valid
    # Check for the same code in HotelUserOTPSet -> api/v2/users/views/views.py
    try:
        redis_key = email + "_otp_allowed_del_access"
        if not cache.get(redis_key):
            api_logger.info(
                message="No valid otp found for user %s, exiting" % (email),
                log_type="ingoibibo", bucket="userAPI", stage="validate_user_deletion",
                identifier='{}'.format(log_data))
            raise ValidationError("No valid OTP found for user.")
    except Exception as e:
        raise ValidationError("No valid OTP found for user.")

def save_delete_user_reason(user, source=None, deletionReason=None, customDeletionReason=None):
    try:

        user_deletion_reason_data = {
            'userId': user.id,
            'source': source,
            'deletionReason': deletionReason,
            'customDeletionReason': customDeletionReason,
        }

        account_deletion_reason_serializer = AccountDeletionReasonSerializer(data=user_deletion_reason_data)
        if account_deletion_reason_serializer.is_valid():
            account_deletion_reason_serializer.save()
        else:
            error_message = account_deletion_reason_serializer.errors
            raise ValueError("Invalid data provided for user deletion reason: {}".format(error_message))

    except ValueError as ve:
        raise ve
    except Exception as e:
        error_message = str(e)
        raise ValueError("Error saving user deletion reason: {}".format(error_message))


def delete_user(user):
    from scripts.user_deletion.run import run
    run.apply_async(args=([user.id],), kwargs={'started_by': user.id})


def get_uuid_from_request_header(request_meta):
    res = {}

    if not request_meta:
        return None

    user_json = request_meta.get('HTTP_USER_IDENTIFIER')
    if not user_json:
        return None

    user_identifier = json.loads(user_json)
    if not user_identifier:
        return None
    if user_identifier['type'] and user_identifier['type'].lower() == 'uuid' and user_identifier['value']:
        return user_identifier['value']
    return None


def get_ingo_user_from_sso_mapping(user_id, org):
    query = "SELECT ingo_uuid FROM sso_auth_mapping WHERE {}_uuid = '{}' and source_of_management = '{}'"\
        .format(org.lower(), user_id, org)

    cursor = connection.cursor()
    cursor.execute(query)
    row = cursor.fetchone()
    if row and len(row) == 1:
        return row[0]

    return None
