__author__ = 'prerna_arya'

from rest_framework import serializers
from ingouser.models import User
from hotels.models import HotelAdminUser


class UserSerializer(serializers.ModelSerializer):

    class Meta:
        model = User

    def to_representation(self, instance):
        return {
            'username': instance.username,
            'email': instance.email,
            'first_name': instance.first_name,
            'last_name': instance.first_name
        }


class UserProfileSerializer(serializers.ModelSerializer):
    username = serializers.ReadOnlyField(source='hoteluser.username')
    first_name = serializers.ReadOnlyField(source='hoteluser.first_name')
    last_name = serializers.ReadOnlyField(source='hoteluser.last_name')
    email = serializers.ReadOnlyField(source='hoteluser.email')

    class Meta:
        model = HotelAdminUser
        fields = ('first_name', 'last_name', 'username', 'email', 'mobile', 'usertype')


class UserHotelAssignmentSerializer(serializers.Serializer):
    hotels = serializers.ListField(child=serializers.Char<PERSON><PERSON>())

    # class Meta:
    #     fields = ('hotels')

class PasswordResetSerializer(serializers.Serializer):
    password = serializers.CharField(required=True)
