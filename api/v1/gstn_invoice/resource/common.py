import datetime
import json
import traceback
from django.core.exceptions import ObjectDoesNotExist

from api.v1.gstn_invoice.validator_helper.ocr_booking_validator import OCRBookingValidator
from hotels.hotelchoice import GST_INVOICE_QC_REJECT_REASON
from hotels.models import GSTDetail, HotelBooking, HotelDetail
from hotels.pi_data import get_customer_gst
from utils.logger import Logger
api_logger = Logger(logger='inventoryAPILogger')

ALLOWED_INVOICE_FORMATS = ['pdf', 'png', 'jpeg', 'jpg']


def update_resp_list(err, each_booking, booking_guest_name_dict, booking_invoices, vendorbookingid, is_dcb):
    from api.v1.gstn_invoice.resource.utilities import GstnInvoiceOCRUpload
    booking_response = [''] * 16
    proceed_further = False
    try:
        if err == 'Booking doesnot exist':
            booking_response[2] = 'Error!'
            booking_response[3] = 'Attachment not named as booking ID'
            email_received_date = datetime.date.today().strftime("%Y-%m-%d")
            booking_response[12] = email_received_date
        else:
            booking_id = vendorbookingid if is_dcb else each_booking
            guest_detail_dict = booking_guest_name_dict[booking_id]
            booking_response[0] = guest_detail_dict['vendor_booking_id']
            booking_response[1] = ','.join(guest_detail_dict['bookings'])
            booking_response[4] = guest_detail_dict['checkout'].strftime("%Y-%m-%d")
            booking_response[5] = guest_detail_dict['days_since_checkout']
            booking_response[6] = guest_detail_dict['guest_name']
            booking_response[7] = guest_detail_dict['hotelname']
            booking_response[8] = guest_detail_dict['hotelcode']
            booking_response[9] = guest_detail_dict['city']
            booking_response[10] = guest_detail_dict['bdm']
            booking_response[11] = guest_detail_dict['bdm_email_id']
            booking_response[12] = guest_detail_dict['date_on_which_mail_received']

            booking = vendorbookingid or each_booking
            booking_exist = booking_invoices.get(booking, '')
            if booking_exist and booking_invoices[booking]['file_ext']:
                guest_detail_dict['file_ext'] = booking_invoices[booking]['file_ext']

            gstn_invoice_email_upload = GstnInvoiceOCRUpload()
            is_validate = gstn_invoice_email_upload.validate_deed(guest_detail_dict, is_dcb)
            if is_validate:
                proceed_further = True
            else:
                booking_response[2] = 'Validation error'
                err = gstn_invoice_email_upload.errors
                booking_response[3] = err
        gstNUploadsTableId=getGstInvoiceUploadsTableId(each_booking, booking_invoices, vendorbookingid)
        booking_response[15]=gstNUploadsTableId
    except Exception as e:
        api_logger.critical(message='Exception occurred {} traceback {}'.format(repr(e),repr(traceback.format_exc())),
                            log_type='ingoibibo',
                            bucket='gstn_invoice', stage='update_resp_list')

    return booking_response, proceed_further, err


def get_booking_snapshot(input_dict):
    """
    It have mapping between key from DCB booking api and booking snapshot field, clean the input_dict
    return booking_snap_dict = {
                        "guest_name": '',
                        "company_name": '',
                        "invoice_amount": '',
                        "company_gstn": '',
                        "company_address": '',
                        "hotel_gstn": '',
    }
    """
    booking_snap_dict = dict()
    booking_snap_dict['guest_name'] = input_dict.get('guest_name', '')
    booking_snap_dict['company_name'] = input_dict.get('company_name', '')
    booking_snap_dict['company_address'] = input_dict.get('company_address', '')
    booking_snap_dict['company_gstn'] = input_dict.get('customer_gst', '')
    booking_snap_dict['hotel_gstn'] = input_dict.get('hotel_gstn', '')
    return booking_snap_dict


def get_booking_snapshot_dict(confirm_booking_id):
    try:
        bobj = HotelBooking.objects.get(confirmbookingid=confirm_booking_id)
        guest_name = bobj.bookingname.encode('utf-8')
        misc = json.loads(bobj.misc) if bobj.misc else {}

        # Get the hotel-gstn from GSTDetail Table as gstn at time of booking can be different than actual hotelgstn available during invoice generation
        hotel_gst_detail = GSTDetail.objects.only('gstn').filter(hotel=bobj.hotel, isactive=True).last()
        hotel_gstn = hotel_gst_detail.gstn if hotel_gst_detail else ''

        company_info = get_customer_gst(bobj.misc, bobj.booking_vendor_name)

        company_gstn = company_info.get('gstn', '').encode('utf-8') if company_info else ''
        company_name = company_info.get('gst_cn', '').encode('utf-8') if company_info else ''
        company_address = company_info.get('gst_ca', '').encode('utf-8') if company_info else ''

        # Fetch the e-invoice flag from HotelDetail
        try:
            hotel_detail = HotelDetail.objects.only('hotelcode', 'flag_bits_4').get(id=bobj.hotel_id)
            is_einvoice_enabled = hotel_detail.is_einvoice_enabled
        except:
            is_einvoice_enabled = False


        snapshot_dict = {
            'guest_name': guest_name,
            'company_name': company_name,
            'invoice_amount': bobj.bookingamount,
            'company_gstn': company_gstn,
            'company_address': company_address,
            'hotel_gstn': hotel_gstn,
            'is_einvoice_enabled': is_einvoice_enabled,
            'is_hotelcloud_booking': bobj.is_hotelcloud_booking,
            'sales_channel': str(misc.get('sales_channel_info', {}).get('sales_channel', '')),
            'sales_category': str(misc.get('sales_channel_info', {}).get('sales_category', '')),
        }

        return snapshot_dict

    except ObjectDoesNotExist as error:
        error_msg = "Object doesn't exist for confirm booking id {confirmbookingid} with error {error}".format(
            confirmbookingid=confirm_booking_id,
            error=error
        )
        api_logger.error(message=error_msg,
                               log_type='ingoibibo',
                               bucket='GstnInvoiceOCRUpload',
                               stage='run_validation')
        raise ObjectDoesNotExist(error_msg)



def matching_invoice_params_to_ingo(ocr_dict, booking_id, booking_snapshot, is_dcb=False):
    """
    ocr_dict:  {
                "guest_name": "MR IMAMUL HASNAIN ANSARI",
                "hotel_gstn": "09AAAFI1556K2Z9",
                "company_name": "GROUP PRIVATE LIMITED",
                "ocr_id": "5583a0d6-8d95-11ec-aeef-caa8add44dc4",
                "company_address": "3RD FLOOR, SEC 32,"
                # Ensure 'irn', 'qr_code', 'signature', etc. are present for Hotel Cloud
            }
    booking_id : *********
    """
    try:
        resp = {'is_matched': True, 'reject_reason': [], 'data': {'nn_data': {}, 'booking_data': {}}}
        ocr_validator = OCRBookingValidator()
        ocr_validator.is_dcb = is_dcb

        # # Ensure necessary fields for Hotel Cloud are in ocr_dict
        # required_fields = ['irn', 'qr_code', 'signature']
        # for field in required_fields:
        #     if field not in ocr_dict:
        #         ocr_dict[field] = ''

        if booking_id:
            resp = ocr_validator.run_validation(ocr_input=ocr_dict, confirm_booking_id=booking_id,
                                                booking_snapshot=booking_snapshot)
        else:
            resp = ocr_validator.run_validation(ocr_input=ocr_dict, confirm_booking_id=None,
                                                booking_snapshot=booking_snapshot)

        message = 'Response received from match logic: {}'.format(str(resp))
        api_logger.info(identifier=message,
                        log_type='ingoibibo',
                        bucket='gstn_invoice', stage='matching_invoice_params_to_ingo')

    except Exception as e:
        message = '{} traceback {}'.format(repr(e), repr(traceback.format_exc()))
        api_logger.critical(identifier=message,
                            log_type='ingoibibo',
                            bucket='gstn_invoice', stage='matching_invoice_params_to_ingo')
        raise e

    return resp


def get_reject_reasons_stringlist(enum_list):
    reject_reason_list = []
    try:
        reject_reason_list = [dict(GST_INVOICE_QC_REJECT_REASON).get(int(value)) for value in enum_list]
    except Exception as e:
        raise e
    return reject_reason_list

def getGstInvoiceUploadsTableId(bookingid, booking_invoices, vendorbookingid):
    if bookingid not in booking_invoices:
        booking_id = vendorbookingid
    else:
        booking_id = bookingid

    return booking_invoices[booking_id]['gstInvoiceUploadsTableId']
