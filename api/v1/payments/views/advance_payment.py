from django.http.response import HttpResponse
from rest_framework import viewsets, status
from rest_framework.decorators import list_route, detail_route
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.authentication import SessionAuthentication, TokenAuthentication, BasicAuthentication
from api.v1.payments.permissions import (IsHotelAccessibility,
                                         GuardianPermissionCheck, GuardianMailPermissionCheck)
from api.v1.payments.resources import download_mmt_advance_payment_excel, fetch_mmt_advance_payment
from api.v1.users.authentication.token_authentication import CustomTokenNewAuthentication
from hotels.models import HotelDetail, VendorMapping
import json

class AdvancePaymentViewset(viewsets.GenericViewSet):
    authentication_classes = (BasicAuthentication, SessionAuthentication, CustomTokenNewAuthentication)
    permission_classes = (IsAuthenticated, IsHotelAccessibility)

    @list_route(methods=["get"], url_path="download-advance-payment", permission_classes=(IsAuthenticated,),
                  authentication_classes=(SessionAuthentication, CustomTokenNewAuthentication))
    ## sql_injection id: 10, 11 NR
    def download_advance_payment_excel(self, request):
        query_params = request.query_params
        infotype=query_params.get('infotype', '')
        advancetype = query_params.get('advancetype', '')
        hotelcode = query_params.get('hotelcode', '')
        entrynumber=query_params.get('entrynumber', '')
        vendor = query_params.get('vendor', '')
        if not infotype or not advancetype or not hotelcode or not entrynumber and vendor:
            return Response({"message": "URL parameters are incorrect!"},
                        status=status.HTTP_400_BAD_REQUEST)
        hotel_obj = HotelDetail.objects.filter(hotelcode=hotelcode)
        vendor_code = None
        if vendor.lower() == 'goibibo':
            vendor = 'Goibibo'
            vendor_mapping = VendorMapping.objects.filter(hotel=hotel_obj, vendor__vendor__code=vendor,
                                                          isactive=True).last()
        if vendor.lower() == 'makemytrip':
            vendor = 'MakeMyTrip'
            vendor_mapping = VendorMapping.objects.filter(hotel=hotel_obj, vendor__vendor__code=vendor,
                                                          isactive=True).last()
        if vendor_mapping:
            vendor_code = vendor_mapping.vendor.vendor_code
        if not vendor_code:
            return Response({"message": "Vendor code not found!"}, status=status.HTTP_400_BAD_REQUEST)
        response = download_mmt_advance_payment_excel(vendor=vendor, vendor_code=vendor_code, advancetype=advancetype, infotype=infotype)
        if not response.get('success'):
            return Response({"message": response.get('message')},
                            status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        response = HttpResponse(response.get('data'), content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename=%s_%s_report.csv' % (advancetype, infotype)
        return response

    @list_route(methods=["get"], url_path="fetch-advance-payment", permission_classes=(IsAuthenticated,),
                authentication_classes=(SessionAuthentication, CustomTokenNewAuthentication))
    def fetch_advance_payment(self, request):
        response = {'message': '', 'success': False, 'data': None}
        query_params = request.query_params
        hotelcode = query_params.get('hotelcode', '')
        if not hotelcode:
            response['message'] = "HotelCode is missing!"
            return Response(response,
                            status=status.HTTP_400_BAD_REQUEST)
        mmt_hotel_obj = HotelDetail.objects.filter(hotelcode=hotelcode).only('mmt_id', 'flag_bits_1',
                                                                             'flag_bits_2', 'flag_bits_3')
        if not mmt_hotel_obj or not mmt_hotel_obj[0].mmt_id:
            response['message'] = "Hotel not found!"
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        payment_response = fetch_mmt_advance_payment(mmt_hotel_obj[0].mmt_id)
        return Response(payment_response, status=status.HTTP_200_OK)
