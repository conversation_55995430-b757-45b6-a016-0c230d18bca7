import csv
import json
import os
import traceback
from operator import itemgetter
from datetime import datetime

from django.core.exceptions import ObjectDoesNotExist, PermissionDenied
from django.conf import settings
from django.http.response import HttpResponse
from django.shortcuts import render_to_response
from django.template import RequestContext

from rest_framework import viewsets
from rest_framework.decorators import detail_route, list_route
from rest_framework.permissions import IsAuthenticated
from rest_framework.authentication import SessionAuthentication, BasicAuthentication
from rest_framework.response import Response
from rest_framework.exceptions import ValidationError
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST, HTTP_500_INTERNAL_SERVER_ERROR, HTTP_403_FORBIDDEN

from api.v1.taxes.permissions.permissions import IsHotelAccessibility
from api.v1.hotels.permissions.permissions import HotelViewAccessibilityCheck
from api.v1.pagination import StandardResultsSetPagination
from api.v1.users.authentication.token_authentication import CustomTokenNewAuthentication
from common.commonhelper import is_user_assigned_to_hotel
from hotels.methods import get_hotel_id_from_code
from hotels.models import HotelBooking, MultiRoomBookingInfo, HotelDetail, VendorMapping, VendorDetail, \
    PerformanceLinkBonus, PerformanceLinkRecords, AdjustmentEntry, FreeRoomNightDetails
from hotels.hotelchoice import VENDOR_HOTEL_CODE_MAPPING, VENDOR_CODE_MAPPING, VENDOR_TO_COMPANY_CODE
from utils.logger import Logger
from mo_finance.MO_payment_management import MOPayment, MOHotelPayment, MOAdjustmentHandler, PENDING
from api.v1.payments.serializers import MOHotelPaymentSummarySerializers, MOHotelPaymentDetailSerializers \
    , MOPLBRequestValidator, MOPLBRuleSerializer, MOAdjustmentSerializer, MOFreeRoomNightSerializer, \
    MOMIBDetailRequestValidator, MOMultiReportRequestValidator, MOCombinedReportRequestValidator
from hotels.tasks import send_plb_vdi_multi_reports, send_plb_combined_report, send_adt_combine_zip_report
from corporate_gstn.resources import THRESHOLD_DATE
from hotels.helper import upload_voucher_or_mo_report_to_s3

api_logger = Logger(logger='inventoryAPILogger')
mo_interface = MOPayment()
mo_hotel_interface = MOHotelPayment()


class ResponseInfo(object):
    def __init__(self):
        self.response_message = {
            'responseMessage': '',
            'responseData': {
            },
            'responseStatus': True,
            'responseError': []
        }


class MOPaymentViewset(viewsets.GenericViewSet):
    authentication_classes = (BasicAuthentication, SessionAuthentication, CustomTokenNewAuthentication)
    permission_classes = (IsAuthenticated, IsHotelAccessibility)
    lookup_field = 'bookingid'

    @detail_route(methods=["get"], url_path="detail")
    def get_booking_payment_detail(self, request, bookingid):
        response = {
            "data": {},
            "success": False,
            "message": ""
        }
        try:
            response_type = request.query_params.get("type", "json")
            is_corporate_booking = request.query_params.get("corporate_booking", False)
            if bookingid.startswith('MB'):
                bookingid = MultiRoomBookingInfo.objects.filter(parent_booking_id=bookingid).first().confirm_booking_id
            bobj = HotelBooking.objects.filter(id=bookingid).first()

            if bobj and bobj.hotel:
                hotelcode = bobj.hotel.hotelcode
                if not is_user_assigned_to_hotel(hotelcode, request.user.id):
                    api_logger.info(
                        identifier='Hotel not assigned to this user Hotelcode: {}, user_id: {}'.format(
                            hotelcode, request.user.id),
                        log_type='ingoibibo', bucket='mo_integration',
                        stage='get_booking_payment_detail')
                    response["message"] = "User does not have access to hotel"
                    return Response(response, status=HTTP_403_FORBIDDEN)

            # Check if it's a FRN booking
            if bobj.is_frn_booking:
                response["message"] = "This booking is adjusted against Free Room Night campaign"
            else:
                response = mo_interface.get_booking_payment_detail(booking_obj=bobj)

                if is_corporate_booking and response.get('data', {}).get("payment_status", '') == PENDING:
                    response["message"] = "Payment for corporate booking will be initiated once the valid invoice is uploaded as per the Agreement."

            if response_type.lower() == "html":
                template = 'admin/hotels/hotelbooking/payment_detail.html'
                context = {
                    'mo_response': response,
                }
                return render_to_response(template, context, context_instance=RequestContext(request))
        except Exception, e:
            api_logger.critical(message='Failed get_payment_detail_popup(booking_id=%s) due to %s' % (
                bookingid, repr(traceback.format_exc())), log_type='ingoibibo', bucket='MOPaymentViewset',
                                stage='mo-api')
        return HttpResponse(json.dumps(response, default=str))

    @detail_route(methods=["get"], url_path="detail-dc")
    def get_dc_booking_payment_detail(self, request, bookingid):
        """
        This Api returns details for direct-connect bookings
        """
        response = {
            "data": {},
            "success": False,
            "message": ""
        }
        try:
            response = mo_interface.get_dc_booking_payment_detail(vendor_booking_id=bookingid)
        except Exception, e:
            api_logger.critical(message='Failed get_payment_detail_popup(booking_id=%s) due to %s' % (
                bookingid, repr(traceback.format_exc())), log_type='ingoibibo', bucket='MOPaymentViewset',
                                stage='mo-api')
        return HttpResponse(json.dumps(response, default=str))

    @list_route(methods=["get"], url_path="get_bookings_payment_details")
    def get_bookings_payment_details(self, request):
        '''
            API to fetch bookings payment details from MO
            Query params consist of bookingIDs like bookingIDs=1,2,3,4,5
            Response is the data coming from MO response which includes pnr invoice details, payment details, etc
        '''
        response = {
            "data": {},
            "success": False,
            "message": ""
        }
        try:
            api_logger.info(message='Booking Payment Detail request: {}'.format(request),
                            log_type='ingoibibo', bucket='MOPaymentViewset',
                            stage='get_bookings_payment_details')

            booking_ids = request.query_params.get("bookingIDs", "").split(",")
            booking_ids_list = list()

            if not booking_ids or (len(booking_ids) == 1 and not booking_ids[0]):
                response["message"] = "Booking ID/s missing"
                return Response(response, status=HTTP_400_BAD_REQUEST)

            # Preparing the required booking ID list
            for booking_id in booking_ids:
                if booking_id.startswith('MB'):
                    booking_id_mb = MultiRoomBookingInfo.objects.filter(parent_booking_id=booking_id).first()
                    if booking_id_mb:
                        booking_ids_list.append(booking_id_mb.confirm_booking_id)
                else:
                    booking_ids_list.append(int(booking_id))
            # Validating booking IDs passed from client from DB and further using the DB response data to call MO API
            bookings_obj = HotelBooking.objects.filter(id__in=booking_ids_list)

            if not bookings_obj:
                response["message"] = "Invalid Booking ID/s"
                return Response(response, status=HTTP_400_BAD_REQUEST)

            api_logger.info(
                message='Booking Payment Detail api_response for booking ID list: {} and response booking objects are: {}'.format(
                    booking_ids_list, bookings_obj),
                log_type='ingoibibo', bucket='MOPaymentViewset',
                stage='get_bookings_payment_details')

            # Calling method to triggered MO API if required details present
            response = mo_interface.get_bookings_payment_details(bookings_obj=bookings_obj)

        except Exception, e:
            api_logger.critical(message='Failed get_payment_detail_popup(request: {}) due to: {}'.format(
                request, repr(traceback.format_exc())), log_type='ingoibibo', bucket='MOPaymentViewset',
                stage='mo-api')

        return HttpResponse(json.dumps(response, default=str))


class MOHotelPaymentViewset(viewsets.GenericViewSet):
    authentication_classes = (SessionAuthentication, CustomTokenNewAuthentication)
    permission_classes = (HotelViewAccessibilityCheck,)
    lookup_field = "hotelcode"

    @detail_route(methods=["get"], url_path="allow-mo-payment")
    def allow_new_payment(self, request, hotelcode):
        """
        This api will define payment detail screen.
        :param request: HTTP request
        :param hotelcode: INGO hotel code
        :return: HTTP response
        URL:
            /api/v1/payment/mo-hotel-payment/**********/allow-mo-payment
        Response:
            {
                {
                    "can_allow": False
                },
                "success": True,
                "message": ""
            }
        """
        response = {
            "data": {
                "can_allow": False
            },
            "success": True,
            "message": ""
        }
        try:
            log_data = request.log_data
            from common.commonhelper import is_user_assigned_to_hotel
            if not is_user_assigned_to_hotel(hotelcode, request.user.id):
                log_data['error']['message'] = "User not assigned to this hotel"
                api_logger.info(message='Send Invoice Report Requested for Hotelcode: {}, user_id: {}'.format(hotelcode,
                                                                                                              request.user.id),
                                log_type='ingoibibo', bucket='MOHotelPaymentViewset',
                                stage='allow_new_payment',
                                identifier='{}'.format(log_data))
                return Response(response, status=HTTP_403_FORBIDDEN)
            hotel_obj = HotelDetail.objects.filter(hotelcode=hotelcode).last()
            can_toggle = True
            api_logger.info(message="allow_new_payment: hotel code %s" % (hotelcode), log_type="ingoibibo",
                            bucket="MOHotelPaymentViewset", stage="allow_new_payment")

            if hotel_obj.vcc_payment:
                return Response(response, status=HTTP_200_OK)
            # vendor_mappings = VendorMapping.objects.filter(hotel=hotel_obj, isactive=True)
            # for vendor_mapping in vendor_mappings:
            #     if vendor_mapping and vendor_mapping.vendor and vendor_mapping.vendor.payment_to_vendor and \
            #             vendor_mapping.vendor.id != vendor_mapping.vendor.payment_to_vendor.id:
            #         can_toggle = False
            if can_toggle:
                response["data"]["can_allow"] = True
        except Exception, e:
            api_logger.critical(message="failed api hotelcode: %s %s" % (hotelcode, repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="MOHotelPaymentViewset", stage="allow_new_payment")
        return Response(response, status=HTTP_200_OK)

    @detail_route(methods=["get"], url_path="summary")
    def get_hotel_payment_summary(self, request, hotelcode):
        """
        will return hotel payment summary
        :param request: HTTP request
        :param hotelcode: INGO hotel code
        :return: HTTP response

        URL:
            /api/v1/payment/mo-hotel-payment/**********/summary/

        Query parms:
            from_date=YYYY-MM-DD
            to_date=YYYY-MM-DD
            vendor=possible values both, makemytrip, goibibo. Default value both
            bank_reference = payment reference

        response:
            {
                "message": "",
                "data": [
                    {
                        "payment_date": "2019-02-18",
                        "amount_adjusted": 19153,
                        "company": "MekeMyTrip",
                        "ifsccode": "",
                        "account_number": "",
                        "payment_mode": "WIRE",
                        "payment_reference": "",
                        "amount_paid": 0,
                        "total_booking": 1
                    },
                    {
                        "payment_date": "2019-02-18",
                        "amount_adjusted": 19153,
                        "company": "MakeMyTrip",
                        "ifsccode": "",
                        "account_number": "",
                        "payment_mode": "WIRE",
                        "payment_reference": "",
                        "amount_paid": 0,
                        "total_booking": 1
                    },
                    {
                        "payment_date": "2019-02-21",
                        "amount_adjusted": 9704,
                        "company": "MekeMyTrip",
                        "ifsccode": "",
                        "account_number": "",
                        "payment_mode": "WIRE",
                        "payment_reference": "",
                        "amount_paid": 0,
                        "total_booking": 1
                    },
                    {
                        "payment_date": "2019-02-21",
                        "amount_adjusted": 9704,
                        "company": "MekeMyTrip",
                        "ifsccode": "",
                        "account_number": "",
                        "payment_mode": "WIRE",
                        "payment_reference": "",
                        "amount_paid": 0,
                        "total_booking": 1
                    },
                    {
                        "payment_date": "2019-02-22",
                        "amount_adjusted": 1,
                        "company": "MekeMyTrip",
                        "ifsccode": "",
                        "account_number": "",
                        "payment_mode": "WIRE",
                        "payment_reference": "",
                        "amount_paid": 0,
                        "total_booking": 1
                    },
                    {
                        "payment_date": "2019-02-22",
                        "amount_adjusted": 1,
                        "company": "MekeMyTrip",
                        "ifsccode": "",
                        "account_number": "",
                        "payment_mode": "WIRE",
                        "payment_reference": "",
                        "amount_paid": 0,
                        "total_booking": 1
                    }
                ],
                "success": true
            }

        """
        response = {
            "data": [],
            "success": False,
            "message": ""
        }
        try:
            log_data = request.log_data
            from common.commonhelper import is_user_assigned_to_hotel
            if not is_user_assigned_to_hotel(hotelcode, request.user.id):
                log_data['error']['message'] = "User not assigned to this hotel"
                api_logger.info(message='Send Invoice Report Requested for Hotelcode: {}, user_id: {}'.format(hotelcode,
                                                                                                              request.user.id),
                                log_type='ingoibibo', bucket='MOHotelPaymentViewset',
                                stage='get_hotel_payment_summary',
                                identifier='{}'.format(log_data))
                return Response(response, status=HTTP_403_FORBIDDEN)
            api_logger.info(message="allow_new_payment: hotel code %s and query params %s" % (hotelcode,
                                                                                              request.query_params),
                            log_type="ingoibibo", bucket="MOHotelPaymentViewset", stage="allow_new_payment")
            hotel_obj = HotelDetail.objects.get(hotelcode=hotelcode)
            MOHotelPaymentSummarySerializers(data=request.query_params).is_valid(raise_exception=True)
            payment_records = []
            from_date = request.query_params.get('from_date', '')
            to_date = request.query_params.get('to_date', '')
            bankreference = request.query_params.get('bank_reference', '')

            if request.query_params.get('vendor', 'both') == 'both':
                mo_hotelcode = hotel_obj.mmt_id
                company_code = VENDOR_TO_COMPANY_CODE.get(VENDOR_CODE_MAPPING.get('makemytrip', ''))

                payment_records.extend(
                    self.get_hotel_payment_summary_companywise(mo_hotelcode, from_date, to_date, company_code,
                                                               bankreference))

                mo_hotelcode = hotel_obj.voyagerid
                company_code = VENDOR_TO_COMPANY_CODE.get(VENDOR_CODE_MAPPING.get('goibibo', ''))
                payment_records.extend(
                    self.get_hotel_payment_summary_companywise(mo_hotelcode, from_date, to_date, company_code,
                                                               bankreference))

            else:
                # vendor : makemytrip, goibibo(specific)
                # interate on the basis of vendor, hotel_obj.values_list(VENDOR_HOTEL_CODE_MAPPING[vendor])
                vendor = request.query_params.get('vendor')
                mo_hotelcode = hotel_obj.__getattr__(VENDOR_HOTEL_CODE_MAPPING[vendor])
                company_code = VENDOR_TO_COMPANY_CODE.get(VENDOR_CODE_MAPPING.get(vendor, ''))
                # mo_hotelcodes = [item for sublist in mo_hotelcodes for item in sublist]
                # mo_hotelcodes = filter(None, mo_hotelcodes)
                payment_records.extend(
                    self.get_hotel_payment_summary_companywise(mo_hotelcode, from_date, to_date, company_code,
                                                               bankreference))

            payment_records.sort(key=itemgetter('payment_date'), reverse=True)
            response["data"] = payment_records
            response["success"] = True
        except ValidationError, e:
            response["message"] = "{error_type}".format(error_type=e)
            log_msg = "Error in MOHotelPaymentViewset error %s" % repr(traceback.format_exc())
            api_logger.critical(
                log_msg, log_type="ingoibibo", bucket="MOHotelPaymentViewset",
                stage="get_hotel_payment_summary")
            return Response(response, status=HTTP_400_BAD_REQUEST)
        except ObjectDoesNotExist as e:
            api_logger.critical(
                message="invalid hotelcode : {}, exception {}".format(hotelcode, repr(traceback.format_exc())),
                log_type="ingoibibo", bucket="MOHotelPaymentViewset", stage="get_hotel_payment_summary")
            response["message"] = "{error_type}".format(error_type=e)
            return Response(response, status=HTTP_400_BAD_REQUEST)
        except Exception, e:
            api_logger.critical(
                message="failed api for hotelcode: {} exception {}".format(hotelcode, repr(traceback.format_exc())),
                log_type="ingoibibo", bucket="MOHotelPaymentViewset", stage="get_hotel_payment_summary")
            response["message"] = "{error_type}".format(error_type=e)
            return Response(response, status=HTTP_500_INTERNAL_SERVER_ERROR)
        return Response(response, status=HTTP_200_OK)

    def get_hotel_payment_summary_companywise(self, mo_hotelcode, fromdate, todate, company_code, bank_reference):
        payment_records = []
        try:
            if mo_hotelcode and company_code:
                payment_records = mo_hotel_interface.get_hotel_payment_summary(mo_hotelcode=mo_hotelcode,
                                                                               from_date=fromdate,
                                                                               to_date=todate,
                                                                               company_code=company_code,
                                                                               bankreference=bank_reference
                                                                               )
            else:
                api_logger.info(
                    message="Either company code {} or mo_hotel_code {} is missing".format(company_code, mo_hotelcode),
                    log_type="ingoibibo", bucket="MOHotelPaymentViewset", stage="get_hotel_payment_summary_companywise")
        except Exception as e:
            api_logger.error(
                message="Exception {} while fetching payment summary for mo hotel code{}".format(str(e), mo_hotelcode),
                log_type="ingoibibo", bucket="MOHotelPaymentViewset", stage="get_hotel_payment_summary_companywise")
            raise e

        return payment_records

    @detail_route(methods=["get"], url_path="detail")
    def get_hotel_payment_detail(self, request, hotelcode):
        """
        Hotel payment detail

        :param request: HTTP request
        :param hotelcode: INGO hotelcode
        :return: HTTP response

        URL:
            /api/v1/payment/mo-hotel-payment/**********/detail/

        Query parms:

            from_date: yyyy-mm-dd
            to_date: yyyy-mm-dd
            vendor: possible values makemytrip, goibibo, both. Default value would be both

        response:
        {
            "message": "",
            "data": [
                {
                    "bank_name": "",
                    "booking_date": "2019-04-30",
                    "description": "Payment of Invoice JV/18/**********",
                    "booking_status": "Cancelled",
                    "vendor_booking_id": "NH722469883564",
                    "net_payable": 728.9,
                    "amount_paid": 728.9,
                    "beneficiary_name": "",
                    "ifsccode": "SIBL0000325",
                    "ingo_booking_id": "",
                    "translation_date": "2019-05-09",
                    "checkin": "2019-06-18",
                    "account_number": "****************",
                    "payment_mode": "NEFT",
                    "payment_reference": "N129190821997599",
                    "hotel_name": "ELIXIR HILLS",
                    "checkout": "2019-06-18",
                    "company": "MakeMyTrip",
                    "currency_code": "INR",
                    "payment_type": "payment"
                },
                {
                    "booking_date": "2019-02-11",
                    "description": "ELIXIR HILLS",
                    "booking_status": "confirmed",
                    "vendor_booking_id": "****************",
                    "adjustment_amount": 1,
                    "company": "MakeMyTrip",
                    "hotel_name": "ELIXIR HILLS",
                    "adjustment_id": "**********",
                    "translation_date": "2019-05-01",
                    "checkin": "2019-05-01",
                    "ingo_booking_id": "**********",
                    "net_payable": 0,
                    "checkout": "2019-05-02",
                    "adjustment_ref": "NH7511110291934",
                    "currency_code": "INR",
                    "payment_type": "adjustment"
                },
                {
                    "booking_date": "2019-02-11",
                    "description": "ELIXIR HILLS",
                    "booking_status": "confirmed",
                    "vendor_booking_id": "NH73202106616072",
                    "adjustment_amount": 728,
                    "company": "MakeMyTrip",
                    "hotel_name": "ELIXIR HILLS",
                    "adjustment_id": "0047828996",
                    "translation_date": "2019-05-02",
                    "checkin": "2019-05-02",
                    "ingo_booking_id": "0046105001",
                    "net_payable": 0,
                    "checkout": "2019-05-03",
                    "adjustment_ref": "NH722469883564",
                    "currency_code": "INR",
                    "payment_type": "adjustment"
                },
                {
                    "booking_date": "2019-04-11",
                    "description": "ELIXIR HILLS",
                    "booking_status": "confirmed",
                    "vendor_booking_id": "NH2503911947726",
                    "adjustment_amount": 6254,
                    "company": "MakeMyTrip",
                    "hotel_name": "ELIXIR HILLS",
                    "adjustment_id": "0049255098",
                    "translation_date": "2019-05-10",
                    "checkin": "2019-05-10",
                    "ingo_booking_id": "0048698134",
                    "net_payable": 0,
                    "checkout": "2019-05-11",
                    "adjustment_ref": "NH7418313291142",
                    "currency_code": "INR",
                    "payment_type": "adjustment"
                },
                {
                    "booking_date": "2019-04-26",
                    "description": "ELIXIR HILLS",
                    "booking_status": "confirmed",
                    "vendor_booking_id": "NH7313613767100",
                    "adjustment_amount": 14471,
                    "company": "MakeMyTrip",
                    "hotel_name": "ELIXIR HILLS",
                    "adjustment_id": "0049453075",
                    "translation_date": "2019-05-04",
                    "checkin": "2019-06-01",
                    "ingo_booking_id": "0049453075",
                    "net_payable": 0,
                    "checkout": "2019-06-02",
                    "adjustment_ref": "NH7313613767100",
                    "currency_code": "INR",
                    "payment_type": "adjustment"
                },
                {
                    "booking_date": "2019-05-01",
                    "description": "ELIXIR HILLS",
                    "booking_status": "confirmed",
                    "vendor_booking_id": "NH7416614455726",
                    "adjustment_amount": 26291,
                    "company": "MakeMyTrip",
                    "hotel_name": "ELIXIR HILLS",
                    "adjustment_id": "0049741060,0049741059",
                    "translation_date": "2019-05-04",
                    "checkin": "2019-06-25",
                    "ingo_booking_id": "0049741060,0049741059",
                    "net_payable": 0,
                    "checkout": "2019-06-26",
                    "adjustment_ref": "NH7416614455726",
                    "currency_code": "INR",
                    "payment_type": "adjustment"
                },
                {
                    "booking_date": "2019-04-09",
                    "description": "ELIXIR HILLS",
                    "booking_status": "confirmed",
                    "vendor_booking_id": "NL2106411821608",
                    "adjustment_amount": 10726,
                    "company": "MakeMyTrip",
                    "hotel_name": "ELIXIR HILLS",
                    "adjustment_id": "0049323772",
                    "translation_date": "2019-05-10",
                    "checkin": "2019-05-12",
                    "ingo_booking_id": "0048565855",
                    "net_payable": 0,
                    "checkout": "2019-05-13",
                    "adjustment_ref": "NH7505413461718",
                    "currency_code": "INR",
                    "payment_type": "adjustment"
                },
                {
                    "booking_date": "2019-04-09",
                    "description": "ELIXIR HILLS",
                    "booking_status": "confirmed",
                    "vendor_booking_id": "NL2106411821608",
                    "adjustment_amount": 8301,
                    "company": "MakeMyTrip",
                    "hotel_name": "ELIXIR HILLS",
                    "adjustment_id": "0049255098",
                    "translation_date": "2019-05-10",
                    "checkin": "2019-05-12",
                    "ingo_booking_id": "0048565855",
                    "net_payable": 0,
                    "checkout": "2019-05-13",
                    "adjustment_ref": "NH7418313291142",
                    "currency_code": "INR",
                    "payment_type": "adjustment"
                }
            ],
            "success": true
        }

        """
        response = {
            "data": [],
            "success": False,
            "message": ""
        }
        try:
            log_data = request.log_data
            from common.commonhelper import is_user_assigned_to_hotel
            if not is_user_assigned_to_hotel(hotelcode, request.user.id):
                log_data['error']['message'] = "User not assigned to this hotel"
                api_logger.info(message='Send Invoice Report Requested for Hotelcode: {}, user_id: {}'.format(hotelcode,
                                                                                                              request.user.id),
                                log_type='ingoibibo', bucket='MOHotelPaymentViewset',
                                stage='get_hotel_payment_detail',
                                identifier='{}'.format(log_data))
                return Response(response, status=HTTP_403_FORBIDDEN)
            MOHotelPaymentDetailSerializers(data=request.query_params).is_valid(raise_exception=True)
            hotel_obj = HotelDetail.objects.get(hotelcode=hotelcode)
            payment_ref = request.query_params.get('payment_ref', '')
            from_date = request.query_params.get('from_date', '')
            to_date = request.query_params.get('to_date', '')
            vendor = request.query_params.get('vendor')
            if not (payment_ref or vendor):
                message = "payment_ref and vendor are mandatory for payment detail"
                raise ValidationError(message)

            mo_hotelcode = hotel_obj.__getattr__(VENDOR_HOTEL_CODE_MAPPING[vendor])
            company_code = VENDOR_TO_COMPANY_CODE.get(VENDOR_CODE_MAPPING.get(vendor, ''))
            payment_records = []
            payment_records.extend(mo_hotel_interface.get_hotel_payment_detail(mo_hotelcode=mo_hotelcode,
                                                                               from_date=from_date,
                                                                               to_date=to_date,
                                                                               payment_ref=payment_ref,
                                                                               company_code=company_code))

            temp_payment_records = []
            temp_booking_ids = []
            for payment_record in payment_records:
                if payment_record["payment_type"] == 'payment' and payment_record["vendor_booking_id"]:
                    temp_booking_ids.append(payment_record["vendor_booking_id"])
                    temp_payment_records.append(payment_record)
                elif payment_record["payment_type"] == "adjustment" and payment_record["vendor_booking_id"] not in \
                        temp_booking_ids:
                    temp_booking_ids.append(payment_record["vendor_booking_id"])
                    temp_payment_records.append(payment_record)
            payment_records = temp_payment_records
            payment_records.sort(key=itemgetter('translation_date'), reverse=True)
            response["data"] = payment_records
            response["success"] = True
        except ValidationError as e:
            response["message"] = '{error_type}'.format(error_type=e.detail)
            log_msg = "Error in MOHotelPaymentViewset error %s" % repr(traceback.format_exc())
            api_logger.critical(
                log_msg, log_type="ingoibibo", bucket="MOHotelPaymentViewset",
                stage="get_hotel_payment_detail")
            return Response(response, status=HTTP_400_BAD_REQUEST)
        except Exception as e:
            api_logger.critical(message="failed api hotelcode: %s %s" % (hotelcode, repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="MOHotelPaymentViewset", stage="get_hotel_payment_detail")
        return Response(response, status=HTTP_200_OK)

    @detail_route(methods=["get"], url_path="download-payment-summary")
    def download_summary_payment_report(self, request, hotelcode):
        """
        Download payment summary report
        :param request: HTTP request
        :param hotelcode: INGO hotelcode
        :return: HTTP response
        URL:
            /api/v1/payment/mo-hotel-payment/**********/summary/

        Query Parms:
            from_date=YYYY-MM-DD
            to_date=YYYY-MM-DD
            vendor=possible values both, makemytrip, goibibo. Default value both
            bank_reference = payment reference
        response:
            Downloadable csv file

        """
        try:
            response = {
                "success": True,
                "message": ""
            }
            log_data = request.log_data
            from common.commonhelper import is_user_assigned_to_hotel
            if not is_user_assigned_to_hotel(hotelcode, request.user.id):
                log_data['error']['message'] = "User not assigned to this hotel"
                api_logger.info(message='Send Invoice Report Requested for Hotelcode: {}, user_id: {}'.format(hotelcode,
                                                                                                              request.user.id),
                                log_type='ingoibibo', bucket='MOHotelPaymentViewset',
                                stage='download_summary_payment_report',
                                identifier='{}'.format(log_data))
                return Response(response, status=HTTP_403_FORBIDDEN)
            MOHotelPaymentSummarySerializers(data=request.query_params).is_valid(raise_exception=True)
            from_date = datetime.strptime(request.GET.get("from_date"), "%Y-%m-%d")
            to_date = datetime.strptime(request.GET.get("to_date"), "%Y-%m-%d")
            if (to_date - from_date).days > 61:
                return Response({"detail": "Sorry, you can download maximum 61 days booking report."},
                                status=HTTP_400_BAD_REQUEST)
            response = HttpResponse(content_type='text/csv')
            response[
                "Content-Disposition"] = "attachment; filename=Transactions_summary_report_from_%s_to_%s_for_%s.csv" \
                                         % (request.GET.get("from_date"), request.GET.get("to_date"), hotelcode)
            if not hotelcode:
                return Response({"detail": "Hotelcode is missing"}, status=HTTP_400_BAD_REQUEST)
            hotel_obj = HotelDetail.objects.get(hotelcode=hotelcode)

            from_date = request.query_params.get('from_date')
            to_date = request.query_params.get('to_date')
            bankreference = request.query_params.get('bank_reference')

            writer = csv.writer(response, dialect='excel', quoting=csv.QUOTE_ALL)
            writer.writerow(["Transaction Date", "Brand", "Account Number", "IFSC Code", "Total Amount Paid (Rs.)",
                             "Payment Ref. No.", "Total Amount Adjusted (Rs.)"])
            payment_summary_rows = []

            if request.query_params.get('vendor', 'both') == 'both':
                mo_hotelcode = hotel_obj.mmt_id
                company_code = VENDOR_TO_COMPANY_CODE.get(VENDOR_CODE_MAPPING.get('makemytrip', ''))

                payment_summary_rows.extend(
                    self.get_hotel_payment_summary_companywise(mo_hotelcode, from_date, to_date, company_code,
                                                               bankreference))

                mo_hotelcode = hotel_obj.voyagerid
                company_code = VENDOR_TO_COMPANY_CODE.get(VENDOR_CODE_MAPPING.get('goibibo', ''))
                payment_summary_rows.extend(
                    self.get_hotel_payment_summary_companywise(mo_hotelcode, from_date, to_date, company_code,
                                                               bankreference))

            else:
                # vendor : makemytrip, goibibo(specific)
                vendor = request.query_params.get('vendor')
                mo_hotelcode = hotel_obj.__getattr__(VENDOR_HOTEL_CODE_MAPPING[vendor])
                company_code = VENDOR_TO_COMPANY_CODE.get(VENDOR_CODE_MAPPING.get(vendor, ''))
                payment_summary_rows.extend(
                    self.get_hotel_payment_summary_companywise(mo_hotelcode, from_date, to_date, company_code,
                                                               bankreference))

            for payment_summary_row in payment_summary_rows:
                writer.writerow([payment_summary_row["payment_date"], payment_summary_row["company"],
                                 payment_summary_row["account_number"], payment_summary_row["ifsccode"],
                                 payment_summary_row["amount_paid"], payment_summary_row["payment_reference"],
                                 payment_summary_row["amount_adjusted"]])
            return response
        except ValidationError, e:
            response = dict()
            response["message"] = "{error_type}".format(error_type=e)
            log_msg = "Error in MOHotelPaymentViewset error %s" % repr(traceback.format_exc())
            api_logger.critical(
                log_msg, log_type="ingoibibo", bucket="MOHotelPaymentViewset",
                stage="download_summary_payment_report")
            return Response({"detail": "Invalid request params"}, status=HTTP_400_BAD_REQUEST)
        except Exception, e:
            api_logger.critical(message="failed api hotelcode: %s %s" % (hotelcode, repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="MOHotelPaymentViewset",
                                stage="download_summary_payment_report")
            return Response({"detail": "something went wrong, Please <NAME_EMAIL>"},
                            status=HTTP_500_INTERNAL_SERVER_ERROR)

    @detail_route(methods=["get"], url_path="download-payment-detail")
    def download_detail_payment_report(self, request, hotelcode):
        """
        Download payment detail
        :param request: HTTP request
        :param hotelcode: INGO hotel code
        :return: HTTP response

        URL: /api/v1/payment/mo-hotel-payment/**********/download-payment-detail/

        Query parms
            from_date=YYYY-MM-DD
            to_date=YYYY-MM-DD
            vendor=possible values both, makemytrip, goibibo. Default value both
            Bank_reference: bank reference of payment

        response:
            Downloadable csv file

        """
        tmp_csv_file_path = "/tmp/Transactions_report_from_%s to_%s_for_%s.csv" % (request.GET.get("from_date"),
                                                                                   request.GET.get("to_date"),
                                                                                   hotelcode)
        response = {
            "success": False,
            "message": ""
        }
        log_data = request.log_data
        try:
            from common.commonhelper import is_user_assigned_to_hotel
            if not is_user_assigned_to_hotel(hotelcode, request.user.id):
                log_data['error']['message'] = "User not assigned to this hotel"
                api_logger.info(message='Send Invoice Report Requested for Hotelcode: {}, user_id: {}'.format(hotelcode,
                                                                                                              request.user.id),
                                log_type='ingoibibo', bucket='MOHotelPaymentViewset',
                                stage='download_detail_payment_report',
                                identifier='{}'.format(log_data))
                return Response(response, status=HTTP_403_FORBIDDEN)
            hotel_obj = HotelDetail.objects.get(hotelcode=hotelcode)
            MOHotelPaymentDetailSerializers(data=request.query_params).is_valid(raise_exception=True)
            from_date = datetime.strptime(request.GET.get("from_date"), "%Y-%m-%d")
            to_date = datetime.strptime(request.GET.get("to_date"), "%Y-%m-%d")
            download = self.request.GET.get('download', 'false') in ['true', 'True', 1, ['True']]
            if (to_date - from_date).days > 31:
                return Response({"detail": "Sorry, you can download maximum 31 days booking report."},
                                status=HTTP_400_BAD_REQUEST)

            payment_ref = request.query_params.get('payment_ref', '')
            if not payment_ref:
                message = "payment_ref is mandatory for payment detail"
                raise ValidationError(message)

            payment_detail = []
            payment_summary = []

            from_date = request.query_params.get('from_date')
            to_date = request.query_params.get('to_date')
            bankreference = request.query_params.get('bank_reference')

            # for payment detail vendor code mandatory so both condition is not in use
            if request.query_params.get('vendor', '') == 'both':
                mo_hotelcode = hotel_obj.mmt_id
                company_code = VENDOR_TO_COMPANY_CODE.get(VENDOR_CODE_MAPPING.get('makemytrip', ''))

                payment_summary.extend(
                    self.get_hotel_payment_summary_companywise(mo_hotelcode, from_date, to_date, company_code,
                                                               bankreference))

                mo_hotelcode = hotel_obj.voyagerid
                company_code = VENDOR_TO_COMPANY_CODE.get(VENDOR_CODE_MAPPING.get('goibibo', ''))
                payment_summary.extend(
                    self.get_hotel_payment_summary_companywise(mo_hotelcode, from_date, to_date, company_code,
                                                               bankreference))

            else:
                # vendor : makemytrip, goibibo
                vendor = request.query_params.get('vendor')
                mo_hotelcode = hotel_obj.__getattr__(VENDOR_HOTEL_CODE_MAPPING[vendor])
                company_code = VENDOR_TO_COMPANY_CODE.get(VENDOR_CODE_MAPPING.get(vendor, ''))
                payment_summary.extend(
                    self.get_hotel_payment_summary_companywise(mo_hotelcode, from_date, to_date, company_code,
                                                               bankreference))

            # payment detail api require companycode as of now so fetching payment detail separate
            payment_detail.extend(mo_hotel_interface.get_hotel_payment_detail(mo_hotelcode=mo_hotelcode,
                                                                              from_date=from_date,
                                                                              to_date=to_date,
                                                                              payment_ref=payment_ref,
                                                                              company_code=company_code))
            payment_detail_node = {}
            total_amount_paid = 0
            total_amount_adjusted = 0
            self.create_payment_detail_node(payment_detail=payment_detail, payment_detail_node=payment_detail_node,
                                            total_amount_adjusted=total_amount_adjusted,
                                            total_amount_paid=total_amount_paid)
            if download:
                log_msg = "Downloading MO Payment Report File Path: %s" % tmp_csv_file_path
                api_logger.info(log_msg, log_type="ingoibibo", bucket="MOHotelPaymentViewset",
                                stage="download_detail_payment_report")
                try:
                    with open(tmp_csv_file_path, "w+") as csv_file:
                        self.create_payment_csv_file(csv_file, payment_summary, payment_detail_node)
                    presigned_url = upload_voucher_or_mo_report_to_s3(tmp_csv_file_path)
                    response = {"download_url": presigned_url}
                    response = Response({"message": response, "success": True}, status=HTTP_200_OK)
                except Exception as e:
                    log_msg = "Error Occurred while generating MO report and uploading to S3. File Path: %s, " \
                              "Error: %s, traceback: %s" % (tmp_csv_file_path, str(e), repr(traceback.format_exc()))
                    api_logger.critical(
                        log_msg, log_type="ingoibibo", bucket="MOHotelPaymentViewset",
                        stage="download_detail_payment_report")
                    response = {"message": "", "success": False}
                    response = Response(response, status=HTTP_500_INTERNAL_SERVER_ERROR)
                return response

            else:
                response = HttpResponse(content_type='text/csv')
                response["Content-Disposition"] = "attachment; filename=%s" % tmp_csv_file_path
                self.create_payment_csv_file(response, payment_summary, payment_detail_node)
                response["success"] = True
                return response
        except ValidationError as e:
            message = "%s" % e
            log_msg = "Error in MOHotelPaymentViewset error %s" % repr(traceback.format_exc())
            api_logger.critical(
                log_msg, log_type="ingoibibo", bucket="MOHotelPaymentViewset",
                stage="download_detail_payment_report")
            return Response(response, status=HTTP_400_BAD_REQUEST)
        except Exception as e:
            response["message"] = "something went wrong, Please <NAME_EMAIL>"
            api_logger.critical(message="failed api hotelcode: %s %s" % (hotelcode, repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="MOHotelPaymentViewset",
                                stage="download_detail_payment_report")
            return Response(response, status=HTTP_500_INTERNAL_SERVER_ERROR)
        finally:
            if os.path.exists(tmp_csv_file_path):
                os.remove(tmp_csv_file_path)

    @detail_route(methods=["get"], url_path="download-payment-detail-v2")
    def download_detail_payment_report_v2(self, request, hotelcode):
        """
        Download payment detail
        :param request: HTTP request
        :param hotelcode: INGO hotel code
        :return: HTTP response

        URL: /api/v1/payment/mo-hotel-payment/**********/download-payment-detail-v2/

        Query parms
            vendor=possible values both, makemytrip, goibibo. Default value both
            Bank_reference: bank reference of payment

        response:
            Downloadable csv file

        """
        tmp_csv_file_path = "/tmp/Transactions_report_from_{} to_{}_for_{}.csv".format(request.GET.get("from_date"),
                                                                                       request.GET.get("to_date"),
                                                                                       hotelcode)
        response = {
            "success": False,
            "message": ""
        }
        log_data = request.log_data
        try:
            from common.commonhelper import is_user_assigned_to_hotel
            if not is_user_assigned_to_hotel(hotelcode, request.user.id):
                log_data['error']['message'] = "User not assigned to this hotel"
                api_logger.info(message='Send Invoice Report Requested for Hotelcode: {}, user_id: {}'.format(hotelcode,
                                                                                                              request.user.id),
                                log_type='ingoibibo', bucket='MOHotelPaymentViewset',
                                stage='download_detail_payment_report_v2',
                                identifier='{}'.format(log_data))
                return Response(response, status=HTTP_403_FORBIDDEN)
            MOHotelPaymentDetailSerializers(data=request.query_params).is_valid(raise_exception=True)
            download = self.request.GET.get('download', 'false') in ['true', 'True', 1, ['True']]
            payment_ref = request.query_params.get('payment_ref', '')
            if not payment_ref:
                message = "payment_ref is mandatory for payment detail"
                raise ValidationError(message)

            payment_detail = []
            # after gi mmt merger for 'both' we are passing 'MMTINDIA' as company code to MO
            vendor = request.query_params.get('vendor', 'both')
            if vendor == 'both':
                company_code = VENDOR_TO_COMPANY_CODE.get(VENDOR_CODE_MAPPING.get('makemytrip', ''))
            else:
                company_code = VENDOR_TO_COMPANY_CODE.get(VENDOR_CODE_MAPPING.get(vendor))
            payment_detail.extend(
                mo_hotel_interface.get_hotel_payment_detail_v2(
                    bankref=payment_ref, companycode=company_code
                )
            )
            vendorIds = [payment_node['vendor_booking_id'] for payment_node in payment_detail if payment_node.get('vendor_booking_id')]
            different_hotel_found = HotelBooking.objects.filter(vendorbookingid__in=vendorIds).exclude(hotel__hotelcode=hotelcode)
            if different_hotel_found.exists():
                response["message"] = "Some related bookings belong to different hotels"
                return Response(response, status=HTTP_403_FORBIDDEN)
            payment_detail_node = {}
            payment_summary_node = {}

            total_amount_paid, total_amount_adjusted = self.create_payment_detail_node(
                payment_detail=payment_detail, payment_detail_node=payment_detail_node,
                company_code=company_code
            )
            self.create_payment_summary_node(
                payment_detail=payment_detail, payment_summary_node=payment_summary_node,
                total_amount_adjusted=total_amount_adjusted, total_amount_paid=total_amount_paid
            )

            if download:
                log_msg = "Downloading MO Payment Report File Path: {}".format(tmp_csv_file_path)
                api_logger.info(log_msg, log_type="ingoibibo", bucket="MOHotelPaymentViewset",
                                stage="download_detail_payment_report")
                try:
                    with open(tmp_csv_file_path, "w+") as csv_file:
                        self.create_payment_csv_file(
                            csv_file, [payment_summary_node], payment_detail_node
                        )
                    presigned_url = upload_voucher_or_mo_report_to_s3(tmp_csv_file_path)
                    response = {"download_url": presigned_url}
                    response = Response(
                        {"message": response, "success": True}, status=HTTP_200_OK
                    )
                except Exception as e:
                    log_msg = "Error Occurred while generating MO report and uploading to S3. File Path: {}, " \
                              "Error: {}, traceback: {}".format(tmp_csv_file_path, str(e), repr(traceback.format_exc()))
                    api_logger.critical(
                        log_msg, log_type="ingoibibo", bucket="MOHotelPaymentViewset",
                        stage="download_detail_payment_report")
                    response = {"message": "", "success": False}
                    response = Response(response, status=HTTP_500_INTERNAL_SERVER_ERROR)
                return response

            else:
                response = HttpResponse(content_type='text/csv', status=HTTP_200_OK)
                response["Content-Disposition"] = "attachment; filename={}".format(tmp_csv_file_path)
                self.create_payment_csv_file(
                    response, [payment_summary_node], payment_detail_node
                )
                response["success"] = True
                return response
        except ValidationError as e:
            log_msg = "ValidationError in MOHotelPaymentViewset error {}, traceback {}".format(e, repr(
                traceback.format_exc()))
            api_logger.critical(
                message=log_msg, log_type="ingoibibo", bucket="MOHotelPaymentViewset",
                stage="download_detail_payment_report"
            )
            return Response(response, status=HTTP_400_BAD_REQUEST)
        except Exception as e:
            response["message"] = "something went wrong, Please <NAME_EMAIL>"
            log_msg = "failed api hotelcode: {} {}".format(hotelcode, repr(traceback.format_exc()))
            api_logger.critical(
                message=log_msg, log_type="ingoibibo", bucket="MOHotelPaymentViewset",
                stage="download_detail_payment_report"
            )
            return Response(response, status=HTTP_500_INTERNAL_SERVER_ERROR)
        finally:
            if os.path.exists(tmp_csv_file_path):
                os.remove(tmp_csv_file_path)

    @staticmethod
    def create_payment_csv_file(csv_file, payment_summary, payment_detail_node):
        total_amount_paid = 0
        total_amount_adjusted = 0
        writer = csv.writer(csv_file, dialect="excel", quoting=csv.QUOTE_ALL)
        writer.writerow(
            [
                "Transaction Date", "Account Number", "IFSC Code", "Amount Paid (Rs.)", "Payment Ref. No.",
                "Amount Adjusted "
            ]
        )
        for payment_row in payment_summary:
            writer.writerow(
                [
                    payment_row.get("transaction_date", ""),
                    payment_row.get("account_number", ""),
                    payment_row.get("ifsccode", ""),
                    payment_row.get("amount_paid", 0.0),
                    payment_row.get("payment_reference", ""),
                    payment_row.get("amount_adjusted", 0.0)
                ]
            )
        writer.writerow([])
        writer.writerow(["Payment/Adjustment Details"])
        writer.writerow(
            [
                "Vendor Booking ID", "Ingo PNR", "Booking Vendor", "Booking Status", "Hotel Name", "Booking date",
                "Checkin", "CheckOut", "Payable", "Amount Paid", "Payment Ref.", "Amount Adjusted", "Adjustment Ref."
            ]
        )
        for vendor_booking_id in payment_detail_node:
            writer.writerow(
                [
                    payment_detail_node[vendor_booking_id]["vendor_booking_id"],
                    payment_detail_node[vendor_booking_id]["ingo_booking_id"],
                    payment_detail_node[vendor_booking_id]["company"],
                    payment_detail_node[vendor_booking_id]["booking_status"],
                    payment_detail_node[vendor_booking_id]["hotel_name"],
                    payment_detail_node[vendor_booking_id]["booking_date"],
                    payment_detail_node[vendor_booking_id]["checkin"],
                    payment_detail_node[vendor_booking_id]["checkout"],
                    payment_detail_node[vendor_booking_id]["net_payable"],
                    payment_detail_node[vendor_booking_id]["amount_paid"],
                    payment_detail_node[vendor_booking_id]["payment_reference"],
                    payment_detail_node[vendor_booking_id]["amount_adjusted"],
                    payment_detail_node[vendor_booking_id]["adjustment_refernence"]
                ]
            )
            total_amount_paid += float(payment_detail_node[vendor_booking_id]["amount_paid"])
            total_amount_adjusted += float(payment_detail_node[vendor_booking_id]["amount_adjusted"])
        writer.writerow(["", "", "", "", "", "", "", "", "Total amount paid", total_amount_paid,
                         "Total amount adjusted", total_amount_adjusted])

    def create_payment_summary_node(self, payment_detail, payment_summary_node, total_amount_adjusted,
                                    total_amount_paid):
        payment_summary_node['amount_paid'] = total_amount_paid
        payment_summary_node['amount_adjusted'] = total_amount_adjusted
        if len(payment_detail) > 0:
            payment_summary_node['payment_reference'] = payment_detail[0].get("payment_reference", "")
            payment_summary_node['ifsccode'] = payment_detail[0].get("ifsccode", "")
            payment_summary_node['account_number'] = payment_detail[0].get("account_number", "")
            payment_summary_node['transaction_date'] = payment_detail[0].get("translation_date")

    def create_payment_detail_node(self, payment_detail, payment_detail_node, total_amount_adjusted=0.0,
                                   total_amount_paid=0.0,
                                   company_code="MMTINDIA"):
        try:
            for payment_row in payment_detail:
                if VENDOR_TO_COMPANY_CODE.get(payment_row.get("company", "")) or company_code == "MMTINDIA":
                    vendor_booking_id = payment_row.get("vendor_booking_id")
                    total_amount_adjusted += payment_row.get("amount_adjusted", 0)
                    total_amount_paid += payment_row.get("amount_paid", 0)
                    if vendor_booking_id not in payment_detail_node:
                        payment_detail_node[vendor_booking_id] = dict()
                        payment_detail_node[vendor_booking_id]["ingo_booking_id"] = payment_row.get("ingo_booking_id",
                                                                                                    "")
                        payment_detail_node[vendor_booking_id]["company"] = payment_row.get("company", "")
                        payment_detail_node[vendor_booking_id]["booking_status"] = payment_row.get("booking_status", "")
                        payment_detail_node[vendor_booking_id]["hotel_name"] = payment_row.get("hotel_name", "")
                        payment_detail_node[vendor_booking_id]["booking_date"] = payment_row.get("booking_date", "")
                        payment_detail_node[vendor_booking_id]["checkin"] = payment_row.get("checkin", "")
                        payment_detail_node[vendor_booking_id]["checkout"] = payment_row.get("checkout", "")
                        payment_detail_node[vendor_booking_id]["net_payable"] = payment_row.get("net_payable", 0)
                        payment_detail_node[vendor_booking_id]["amount_paid"] = payment_row.get("amount_paid", 0)
                        payment_detail_node[vendor_booking_id]["payment_reference"] = payment_row.get(
                            "payment_reference",
                            "")
                        payment_detail_node[vendor_booking_id]["amount_adjusted"] = payment_row.get("amount_adjusted",
                                                                                                    0)
                        payment_detail_node[vendor_booking_id]["adjustment_refernence"] = payment_row.get(
                            "adjustment_ref",
                            "")
                        payment_detail_node[vendor_booking_id]["vendor_booking_id"] = vendor_booking_id
                    else:
                        payment_detail_node[vendor_booking_id]["ingo_booking_id"] = payment_row[
                            "ingo_booking_id"] if not \
                            payment_detail_node[vendor_booking_id]["ingo_booking_id"] else \
                            payment_detail_node[vendor_booking_id]["ingo_booking_id"]
                        payment_detail_node[vendor_booking_id]["amount_paid"] += payment_row.get("amount_paid", 0)
                        if payment_detail_node[vendor_booking_id]["payment_reference"] and payment_row.get(
                                "payment_reference", ""):
                            payment_detail_node[vendor_booking_id]["payment_reference"] = \
                            payment_detail_node[vendor_booking_id]["payment_reference"] + ", " + \
                            payment_row.get("payment_reference", "")
                        elif payment_row.get("payment_reference", "") and not payment_detail_node[vendor_booking_id][
                            "payment_reference"]:
                            payment_detail_node[vendor_booking_id]["payment_reference"] = payment_row.get(
                                "payment_reference", "")
                        payment_detail_node[vendor_booking_id]["net_payable"] += payment_row.get("net_payable", 0)
                        payment_detail_node[vendor_booking_id]["amount_adjusted"] += payment_row.get(
                            "adjustment_amount", 0)
                        if payment_row.get("adjustment_ref"):
                            payment_detail_node[vendor_booking_id]["adjustment_refernence"] = \
                                payment_detail_node[vendor_booking_id][
                                    "adjustment_refernence"] + ", " + payment_row.get(
                                    "adjustment_ref", "")
        except Exception, e:
            api_logger.critical(message="create_payment_detail_node: %s" % (repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="MOHotelPaymentViewset",
                                stage="create_payment_detail_node")
        return total_amount_paid, total_amount_adjusted

    def write_payment_detail_csv(self, writer, payment_summary, payment_detail_node, total_amount_paid,
                                 total_amount_adjusted):
        try:
            writer.writerow(["Transaction Date", "Account Number", "IFSC Code", "Amount Paid (Rs.)", "Payment Ref. No.",
                             "Amount Adjusted "])
            for payment_row in payment_summary:
                writer.writerow([payment_row["payment_date"], payment_row["account_number"], payment_row["ifsccode"],
                                 payment_row["amount_paid"], payment_row["payment_reference"],
                                 payment_row["amount_adjusted"]])
            writer.writerow([])
            writer.writerow([])
            writer.writerow(["Payment/Adjustment Details"])
            writer.writerow(["Vendor Booking ID", "Ingo PNR", "Booking Vendor", "Booking Status", "Hotel Name",
                             "Booking date", "Checkin", "CheckOut", "Payable", "Amount Paid", "Payment Ref.",
                             "Amount Adjusted", "Adjustment Ref."])
            for vendor_booking_id in payment_detail_node:
                writer.writerow([
                    payment_detail_node[vendor_booking_id]["vendor_booking_id"],
                    payment_detail_node[vendor_booking_id]["ingo_booking_id"],
                    payment_detail_node[vendor_booking_id]["company"],
                    payment_detail_node[vendor_booking_id]["booking_status"],
                    payment_detail_node[vendor_booking_id]["hotel_name"],
                    payment_detail_node[vendor_booking_id]["booking_date"],
                    payment_detail_node[vendor_booking_id]["checkin"],
                    payment_detail_node[vendor_booking_id]["checkout"],
                    payment_detail_node[vendor_booking_id]["net_payable"],
                    payment_detail_node[vendor_booking_id]["amount_paid"],
                    payment_detail_node[vendor_booking_id]["payment_reference"],
                    payment_detail_node[vendor_booking_id]["amount_adjusted"],
                    payment_detail_node[vendor_booking_id]["adjustment_refernence"]
                ])
            writer.writerow(["", "", "", "", "", "", "", "", "Total amount paid", total_amount_paid,
                             "Total amount adjusted", total_amount_adjusted])
        except Exception, e:
            api_logger.critical(message="write_payment_detail_csv: %s" % (repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="MOHotelPaymentViewset",
                                stage="write_payment_detail_csv")


class MOHotelFRNViewSet(viewsets.GenericViewSet):
    '''
    MO Phase III View set
    '''
    authentication_classes = (CustomTokenNewAuthentication,)
    pagination_class = StandardResultsSetPagination
    permission_classes = (HotelViewAccessibilityCheck,)

    def __init__(self, **kwargs):
        self.response_format = ResponseInfo().response_message
        self.frn_query_set = FreeRoomNightDetails.objects.all().order_by('-id')
        self.adt_query_set = AdjustmentEntry.objects.all().order_by('-id')
        self.plb_query_set = PerformanceLinkBonus.objects.all().order_by('-id')
        self.mo_adt_handler = MOAdjustmentHandler()
        super(MOHotelFRNViewSet, self).__init__(**kwargs)

    def get_plb_rules(self, request, hotelcode):
        status_code = HTTP_200_OK
        query_set = self.plb_query_set
        request_params = {
            'from_date': request.GET.get('from_date'),
            'to_date': request.GET.get('to_date'),
            'hotelcode': hotelcode,
            'brand': request.GET.get('brand', None)
        }
        try:
            validate_request = MOPLBRequestValidator(data=request_params)
            is_valid = validate_request.is_valid()
            if is_valid:
                from_date = datetime.strptime(request_params.get('from_date'), '%Y-%m-%d')
                to_date = datetime.strptime(request_params.get('to_date'), '%Y-%m-%d')
                query_set = query_set.filter(hotel__hotelcode=request_params.get('hotelcode')). \
                    exclude(plbruletype='vdiroomnights').exclude(rulestartdate__gt=to_date). \
                    exclude(ruleenddate__lt=from_date).exclude(plbruletype='fixedamount')
                if request_params.get('brand'):
                    if str(request_params.get('brand')).lower() == 'makemytrip':
                        vendor_id = 24
                    else:
                        vendor_id = 14
                    query_set = query_set.filter(vendor__id=vendor_id)
                page = self.paginate_queryset(queryset=query_set)
                plb_serializer = MOPLBRuleSerializer(type='PLB', data=page, many=True)
                plb_serializer.is_valid()
                results = plb_serializer.data
                response_data = self.get_paginated_response(results)
                self.response_format['responseData'] = response_data.data
                self.response_format['responseMessage'] = 'Data has been fetched.'
            else:
                status_code = HTTP_400_BAD_REQUEST
                self.response_format['responseError'] = validate_request.errors
                self.response_format['responseMessage'] = 'Validation Error'
                self.response_format['responseStatus'] = False
            return Response(self.response_format, status=status_code)
        except Exception as e:
            status_code = HTTP_500_INTERNAL_SERVER_ERROR
            self.response_format['responseMessage'] = 'Error {error}, Please contact ' \
                                                      'to <EMAIL>.'.format(error=str(e))
            self.response_format['responseStatus'] = False
        return Response(self.response_format, status=status_code)

    def get_vdi_rules(self, request, hotelcode):
        status_code = HTTP_200_OK
        query_set = self.plb_query_set
        request_params = {
            'from_date': request.GET.get('from_date'),
            'to_date': request.GET.get('to_date'),
            'hotelcode': hotelcode,
            'brand': request.GET.get('brand', None)
        }
        try:
            validate_request = MOPLBRequestValidator(data=request_params)
            is_valid = validate_request.is_valid()
            if is_valid:
                from_date = datetime.strptime(request_params.get('from_date'), '%Y-%m-%d')
                to_date = datetime.strptime(request_params.get('to_date'), '%Y-%m-%d')
                query_set = query_set.filter(hotel__hotelcode=request_params.get('hotelcode'),
                                             plbruletype='vdiroomnights', isactive=1). \
                    exclude(rulestartdate__gt=to_date).exclude(ruleenddate__lt=from_date)

                if request_params.get('brand'):
                    if str(request_params.get('brand')).lower() == 'makemytrip':
                        vendor_id = 24
                    else:
                        vendor_id = 14
                    query_set = query_set.filter(vendor__id=vendor_id)
                page = self.paginate_queryset(queryset=query_set)
                plb_serializer = MOPLBRuleSerializer(type='VDI', data=page, many=True)
                plb_serializer.is_valid()
                results = plb_serializer.data
                response_data = self.get_paginated_response(results)
                self.response_format['responseData'] = response_data.data
                self.response_format['responseMessage'] = 'Data has been fetched.'
            else:
                status_code = HTTP_400_BAD_REQUEST
                self.response_format['responseStatus'] = False
                self.response_format['responseError'] = validate_request.errors
                self.response_format['responseMessage'] = 'Validation Error'
            return Response(self.response_format, status=status_code)
        except Exception as e:
            status_code = HTTP_500_INTERNAL_SERVER_ERROR
            self.response_format['responseMessage'] = 'Error {error}, Please contact ' \
                                                      'to <EMAIL>.'.format(error=str(e))
            self.response_format['responseStatus'] = False
        return Response(self.response_format, status=status_code)

    def get_frn_summary(self, request, hotelcode):
        status_code = HTTP_200_OK
        query_set = self.frn_query_set
        try:
            plb_id = long(request.GET.get('plbid'))
            performance_link_records = PerformanceLinkRecords.objects.filter(plb__id__in=
                                                                             [plb_id]).values_list('id', flat=True)
            query_set = query_set.filter(plb_record__in=performance_link_records)
            page = self.paginate_queryset(queryset=query_set)
            frn_serializer = MOFreeRoomNightSerializer(data=page, many=True)
            frn_serializer.is_valid()
            results = frn_serializer.data
            response_data = self.get_paginated_response(results)
            self.response_format['responseData'] = response_data.data
            self.response_format['responseMessage'] = 'Data has been fetched.'
        except Exception as e:
            status_code = HTTP_500_INTERNAL_SERVER_ERROR
            self.response_format['responseMessage'] = 'Error {error}, Please contact ' \
                                                      'to <EMAIL>.'.format(error=str(e))
            self.response_format['responseStatus'] = False
        return Response(self.response_format, status=status_code)

    def get_adt_summary(self, request, hotelcode):
        status_code = HTTP_200_OK
        query_set = self.adt_query_set
        try:
            plb_id = long(request.GET.get('plbid'))
            hotel_id = get_hotel_id_from_code(hotelcode)
            performance_link_bonus = PerformanceLinkBonus.objects.filter(id=plb_id, hotel_id=hotel_id)
            if performance_link_bonus:
                performance_link_records = PerformanceLinkRecords.objects.filter(plb__id__in=
                                                                                 [plb_id]).values_list('id', flat=True)
                query_set = query_set.filter(object_id__in=performance_link_records)
                page = self.paginate_queryset(queryset=query_set)
                adt_serializer = MOAdjustmentSerializer(data=page, many=True)
                adt_serializer.is_valid()
                results = adt_serializer.data
                results = self.mo_adt_handler.populate_adjustment_summary_result(hotelcode, results)
                response_data = self.get_paginated_response(results)
                self.response_format['responseData'] = response_data.data
                self.response_format['responseMessage'] = 'Data has been fetched.'
            else:
                api_logger.critical(message="Permission denied", log_type='ingoibibo', bucket='HotelAPI',
                                    stage='hotel.permissions.hotel_accessibility_check')
                raise PermissionDenied('Permission Denied')
        except Exception as e:
            status_code = HTTP_500_INTERNAL_SERVER_ERROR
            self.response_format['responseMessage'] = 'Error {error}, Please contact ' \
                                                      'to <EMAIL>.'.format(error=str(e))
            self.response_format['responseStatus'] = False
        return Response(self.response_format, status=status_code)

    def get_adt_detail(self, request, hotelcode):
        status_code = HTTP_200_OK
        adt_id = request.GET.get('adtid')
        try:
            results, success = self.mo_adt_handler.populate_adjustment_details_result(hotel_code=hotelcode,
                                                                                      adt_id=adt_id)
            self.response_format['responseData'] = results
            if not success:
                self.response_format['responseMessage'] = 'ADT Details not found.'
            else:
                self.response_format['responseMessage'] = 'Data has been fetched.'
        except Exception as e:
            status_code = HTTP_500_INTERNAL_SERVER_ERROR
            self.response_format['responseMessage'] = 'Error {error}, Please contact ' \
                                                      'to <EMAIL>.'.format(error=str(e))
        return Response(self.response_format, status=status_code)

    def get_mib_summary(self, request, hotelcode):
        status_code = HTTP_200_OK
        request_params = {
            'from_date': request.GET.get('from_date'),
            'to_date': request.GET.get('to_date'),
            'hotelcode': hotelcode,
            'brand': request.GET.get('brand'),
            'lob_filter': request.GET.get('lob_filter')
        }
        try:
            validate_request = MOPLBRequestValidator(data=request_params)
            is_valid = validate_request.is_valid()
            if is_valid:
                results = self.mo_adt_handler.populate_advanced_summary_result(
                    hotel_code=request_params.get('hotelcode'),
                    params=request_params)
                self.response_format['responseData'] = results
            else:
                status_code = HTTP_400_BAD_REQUEST
                self.response_format['responseError'] = validate_request.errors
                self.response_format['responseMessage'] = 'Validation Error'
            return Response(self.response_format, status=status_code)
        except Exception as e:
            status_code = HTTP_500_INTERNAL_SERVER_ERROR
            self.response_format['responseMessage'] = 'Error {error}, Please contact ' \
                                                      'to <EMAIL>.'.format(error=str(e))
        return Response(self.response_format, status=status_code)

    def get_mib_detail(self, request, hotelcode):
        status_code = HTTP_200_OK
        request_params = {
            'reference_id': request.GET.get('reference_id'),
            'brand': request.GET.get('brand')
        }
        try:
            validate_request = MOMIBDetailRequestValidator(data=request_params)
            is_valid = validate_request.is_valid()
            if is_valid:
                results = self.mo_adt_handler.populate_advanced_reference_detail_result(hotel_code=hotelcode,
                                                                                        params=request_params)
                self.response_format['responseData'] = results
            else:
                status_code = HTTP_400_BAD_REQUEST
                self.response_format['responseError'] = validate_request.errors
                self.response_format['responseMessage'] = 'Validation Error'
            return Response(self.response_format, status=status_code)
        except Exception as e:
            status_code = HTTP_500_INTERNAL_SERVER_ERROR
        self.response_format['responseMessage'] = 'Error {error}, Please contact ' \
                                                  'to <EMAIL>.'.format(error=str(e))
        return Response(self.response_format, status=status_code)


class MOHotelReportsViewSet(viewsets.GenericViewSet):
    '''
    MO phase III viewset for reports
    '''
    authentication_classes = (CustomTokenNewAuthentication,)
    permission_classes = (HotelViewAccessibilityCheck,)

    def __init__(self, **kwargs):
        self.mo_adt_handler = MOAdjustmentHandler()
        self.response_format = ResponseInfo().response_message

        super(MOHotelReportsViewSet, self).__init__(**kwargs)

    def frn_report(self, request, hotelcode):
        try:
            booking_id = request.GET.get('bookingid')
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = 'attachment; filename={}_{}_FRN' \
                                              '.csv'.format(hotelcode, booking_id)
            csvwriter = csv.writer(response)
            self.mo_adt_handler.get_frn_report(csvwriter=csvwriter, hotel_code=hotelcode,
                                               booking_id=booking_id)
        except Exception as e:
            return Response({'Error': 'Error Occurred {}'.format(e)})
        return response

    def adt_acrn_report(self, request, hotelcode):
        try:
            adt_id = request.GET.get('adtid')
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = 'attachment; filename={}_{}_ACRN' \
                                              '.csv'.format(hotelcode, adt_id)
            csvwriter = csv.writer(response)
            self.mo_adt_handler.get_acrn_report(csvwriter=csvwriter,
                                                hotel_code=hotelcode, adt_id=adt_id)
        except Exception as e:
            return Response({"Error": "Error Occurred {} please contact "
                                      "<EMAIL>. ".format(e)},
                            status=HTTP_500_INTERNAL_SERVER_ERROR)
        return response

    def adt_vdi_report(self, request, hotelcode):
        try:
            adt_id = request.GET.get('adtid')
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = 'attachment; filename={}_{}_VDI' \
                                              '.csv'.format(hotelcode, adt_id)
            csvwriter = csv.writer(response)
            self.mo_adt_handler.get_vdi_report(csvwriter=csvwriter, adt_id=adt_id,
                                               hotel_code=hotelcode)
        except Exception as e:
            return Response({"Error": "Error Occurred {} please contact "
                                      "<EMAIL>. ".format(e)},
                            status=HTTP_500_INTERNAL_SERVER_ERROR)
        return response

    def mib_report(self, request, hotelcode):
        try:
            reference_id = request.GET.get('referenceid')
            brand = request.GET.get('brand')
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = 'attachment; filename={}_{}_MIB' \
                                              '.csv'.format(hotelcode, reference_id)
            csvwriter = csv.writer(response)
            self.mo_adt_handler.get_mib_report(csvwriter=csvwriter, reference_id=reference_id, brand=brand,
                                               hotel_code=hotelcode)
        except Exception as e:
            return Response({"Error": "Error Occurred {} please contact "
                                      "<EMAIL>. ".format(e)},
                            status=HTTP_500_INTERNAL_SERVER_ERROR)
        return response

    def mo_report_multi(self, request, hotelcode):
        status_code = HTTP_200_OK
        try:
            data = request.data
            if data.get('email'):
                email_id = data.get('email')
            elif not (hasattr(request.user, 'email') and request.user.email):
                status_code = HTTP_400_BAD_REQUEST
                self.response_format['responseError'] = ['User Email Id is not present']
                self.response_format['responseMessage'] = 'Please setup your email id.'
                self.response_format['responseStatus'] = False
                return Response(self.response_format, status=status_code)
            else:
                email_id = request.user.email
            if data.get('brand'):
                data['brand'] = str(data.get('brand')).lower()
            validate_request = MOMultiReportRequestValidator(data=data)
            is_valid = validate_request.is_valid()
            if is_valid:
                report_type = data.get('type')
                ids = data.get('ids', [])
                brand = data.get('brand')
                if settings.DEBUG:
                    send_plb_vdi_multi_reports(ids, email_id, hotelcode, report_type, brand)
                else:
                    send_plb_vdi_multi_reports.apply_async(args=(ids, email_id, hotelcode, report_type, brand))
                self.response_format['responseMessage'] = 'Reports has been sent at email id {}' \
                                                          ' for hotel {}'.format(email_id, hotelcode)
            else:
                self.response_format['responseError'] = validate_request.errors
                self.response_format['responseMessage'] = 'Validation Error.'
                self.response_format['responseStatus'] = False
                status_code = HTTP_400_BAD_REQUEST
        except Exception as e:
            self.response_format['responseError'] = ['Error while creating the reports {}.'.format(e)]
            self.response_format['responseMessage'] = 'Could not send the reports.'
            self.response_format['responseStatus'] = False
            status_code = HTTP_500_INTERNAL_SERVER_ERROR
        return Response(self.response_format, status=status_code)

    def mo_combined_report(self, request, hotelcode):
        status_code = HTTP_200_OK
        try:
            data = request.data
            if data.get('email'):
                email_id = data.get('email')
            elif not (hasattr(request.user, 'email') and request.user.email):
                status_code = HTTP_400_BAD_REQUEST
                self.response_format['responseError'] = ['User Email Id is not present']
                self.response_format['responseMessage'] = 'Please setup your email id.'
                self.response_format['responseStatus'] = False
                return Response(self.response_format, status=status_code)
            else:
                email_id = request.user.email
            validate_request = MOCombinedReportRequestValidator(data=data)
            is_valid = validate_request.is_valid()
            if is_valid:
                if settings.DEBUG:
                    if data.get('type') == 'vdi':
                        send_adt_combine_zip_report(data.get('id'), email_id, hotelcode, data.get('type'))
                    else:
                        send_plb_combined_report(data.get('id'), email_id, hotelcode, data.get('type'))
                else:
                    if data.get('type') == 'vdi':
                        send_adt_combine_zip_report.apply_async(
                            args=(data.get('id'), email_id, hotelcode, data.get('type')))
                    else:
                        send_plb_combined_report.apply_async(args=(
                            data.get('id'), email_id, hotelcode, data.get('type')))
                self.response_format['responseMessage'] = 'Reports has been sent at email id {}' \
                                                          ' for hotel {}'.format(email_id, hotelcode)
            else:
                self.response_format['responseError'] = validate_request.errors
                self.response_format['responseMessage'] = 'Validation Error.'
                self.response_format['responseStatus'] = False
                status_code = HTTP_400_BAD_REQUEST
        except Exception as e:
            self.response_format['responseError'] = ['Error while creating the reports {}.'.format(e)]
            self.response_format['responseMessage'] = 'Could not send the reports.'
            self.response_format['responseStatus'] = False
            status_code = HTTP_500_INTERNAL_SERVER_ERROR
        return Response(self.response_format, status=status_code)
