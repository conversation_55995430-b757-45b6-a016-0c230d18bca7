import csv
import os
import traceback

from django.conf import settings
from django.core.servers.basehttp import FileWrapper
from django.http import HttpResponse, HttpResponseRedirect
from rest_framework import status
from rest_framework import viewsets, filters
from rest_framework.authentication import SessionAuthentication, \
    TokenAuthentication, BasicAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from utils.logger import Logger

from api.v1.reports.permissions import *
from api.v1.reports.resources import get_invoice_context, \
    get_context_for_january, get_context_for_june
from api.v1.reports.views import (OffersReportViewSet,
                                  UserActivityReportViewSet, InventoryReportViewSet,
                                  BookingReportViewSet, HotelRatesReportViewSet, InvoiceReportViewSet)
from api.v1.users.authentication.token_authentication import CustomTokenNewAuthentication

from hotels.methods import HotelMethods
from reports.hotelierreports import send_commission_invoice_report, download_tax_invoice, \
    pre_gst_plb_invoice, post_gst_plb_invoice
from reports.hotelierreports import zipping_files
from reports.plbreports import (send_plb_statment_report, send_plb_statement_filter_reports,
                                send_vdi_statement_report)

hotel_methods = HotelMethods()
log_usage = hotel_methods.extranet_web_log_format
api_logger = Logger(logger='inventoryAPILogger')


class DownloadReportViewSet(viewsets.GenericViewSet):

    authentication_classes = (BasicAuthentication, SessionAuthentication, TokenAuthentication)
    permission_classes = (IsAuthenticated, GuardianPermissionCheck)
    filter_backends = (filters.DjangoFilterBackend, )
    http_method_names = ['get']
    success = 200

    def list(self, request, *args, **kwargs):
        headers = {
            'offer_filters': ['Offer On', 'Room Type', 'Rate Plan',
                              'Description', 'Booking Date End', 'Checkin Date End'],
            'availability_filters': ['Room Type', 'Type', 'Details'],
            'cp_filters': ['Policy On', 'Room Type', 'Rate Plan', 'Rules',
                           'Dates without Policy'],
            'activity_filters': ['User Name', 'Last Login',
                                 'Percentage of Total Updates', 'Number of Updates',
                                 'Inventory Updates', 'Rates Updates'],
            'bookings_filters': ['BookingID', 'Check-in', 'Check-out', 'Booked On', 'PAH Booking?', 'Amount to be collected from customer (only for PAH booking)', 'Payable Amount', 'Nett Payable to Hotel',
                                 'Customer Name', 'Booking Status', 'Payment Status', 'Booking Vendor', 'Vendor Booking ID', 'Parent Booking ID (only for Cart booking)', 'No. of Rooms', 'No. of Nights',
                                 'Room Charges','Extra Adult/Child Charges','Hotel Taxes',
                                 'Hotel Gross Charges','Commission Charges','GST Charges',
                                 'Commission (Including GST)', 'Amount Paid', 'Payment Date','Payment Id',
                                 'Bank Ref/VCC No.', 'Amount adjusted', 'Adjustment ID'],
            'rates_filters': ['Room Type', 'Rate Plan', 'Type', 'Details'],
            'invoice_filters': ['Hotel Name','Hotel Legal Entity Name','CIN','GSTIN','PAN No', 'Invoice Serial No.',
                                'Advanced Receipt No.', 'Date of Booking', 'Date of Invoice', 'Place of Supply', 'SAC/HSN',
                                'Description', 'Traveller Name', 'Traveller Email', 'Traveller Phone', 'Company Name',
                                'Company Email', 'Compnay Address', 'Company GSTIN No', 'Booking ID', 'Check-In', 'Check-out',
                                'Room Type', 'No of Rooms', 'No of Nights', 'Room Charges', 'Extra', 'Hotel Discount', 'Taxable Value',
                                'Total Declared Tariff', 'Total GST Tax', 'Net Amount'],
        }
        keys = {
            'invoice_filters': ['hotel_name', 'hotel_legal_name', 'cin', 'gstin', 'pan', 'invoice_serial_no', 'advanced_receipt_no',
                                'booking_date', 'invoice_date', 'place_of_supply', 'hsn', 'description_of_service', 'traveller_name',
                                'traveller_email', 'traveller_phone', 'company_name', 'company_email', 'company_address',
                                'company_gstin', 'booking_id', 'checkin', 'checkout', 'room_type', 'no_of_rooms', 'no_of_nights', 'room_charges',
                                'extra', 'hotel_discount', 'taxable_value', 'declared_tariff_agg', 'gst_tax_info_agg', 'total_amount']
        }
        data_provider = {'offer_filters': (OffersReportViewSet, 'Offers'),
                        'availability_filters': (InventoryReportViewSet, 'Inventory'),
                        'activity_filters': (UserActivityReportViewSet, 'User_Activity'),
                        'bookings_filters': (BookingReportViewSet, 'Bookings'),
                        'rates_filters': (HotelRatesReportViewSet, 'Rates'),
                        'invoice_filters': (InvoiceReportViewSet,'Invoices'),
        }

        try:
            form_name = request.GET.get('formname')
            api = data_provider[form_name][0]
            view = api.as_view({'get': 'retrieve'})
            response = view(request)
            data = response.data['results'] if response.status_code == self.success else []
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = 'attachment; filename=%s_report_from %s ' \
                                              'to %s .csv' % (data_provider[form_name][1],
                                                              request.GET.get('fromDate'),
                                                              request.GET.get('toDate'))
            writer = csv.writer(response, dialect='excel', quoting=csv.QUOTE_ALL)
            writer.writerow(headers[form_name])
            if data:
                if form_name == 'offer_filters':
                    for entry in data:
                        writer.writerow([entry['offeron'],entry['room_name'],
                                         entry['rateplan_name'],
                                         entry['offerdetails'],
                                         entry['bookingdateend'],
                                         entry['checkindateend']
                                         ])
                if form_name == 'availability_filters':
                    for entry in data:
                        writer.writerow([entry['room_name'],entry['type'], entry['details']])
                if form_name == 'cp_filters':
                    for entry in data:
                        writer.writerow([entry['cpon'],entry['room_name'],
                                         entry['rateplan_name'],
                                         entry['status'], entry['details']])
                if form_name == 'activity_filters':
                    for entry in data:
                        writer.writerow([entry['username'],entry['lastlogin'],
                                         entry['percentagetotal'],
                                         entry['total'], entry['inventory'],
                                         entry['rates']])
                if form_name == 'bookings_filters':
                    for entry in data:
                        api_logger.info(
                            message='entry data: %s' % (entry),
                            log_type='ingoibibo', bucket='ReportsAPI',
                            stage='Reports.DownloadReportViewSet')
                        # Appended "`" string before bank_reference_number in order to avoid issue of exponential
                        # formatting while opening csv file in MS Excel.
                        writer.writerow([entry['bookingid'], entry['checkin'], entry['checkout'],
                                         entry['bookingdate'], entry['pah_booking'], entry['amount_collected'],
                                         entry['net_payable_amount'], entry['net_rate'], entry['customername'], entry['status'],
                                         entry['payment_status'], entry['bookingvendor'], entry['vendorbookingid'],
                                         entry['parent_booking_id'], entry['no_roomstay'], entry['no_of_nights'],
                                         entry['room_charge'], entry['extra_adult_charge'],
                                         entry['hotel_tax'], entry['hotel_gross_charge'],
                                         entry['gi_commission'], entry['gi_gst_charge'],
                                         entry['gi_total_commission'], entry['total_amount_paid'],
                                         entry['payment_date'], entry['payment_ids'],
                                         str("`")+str(entry['bank_reference_number']),
                                         entry['adjustment_data']['total_adjustment_amount'],
                                         entry['adjustment_data']['adjustment_detail']])
                if form_name == 'rates_filters':
                    for entry in data:
                        writer.writerow([entry['room_name'],entry['rateplan_name'], entry['type'],
                                         entry['details']])
                if form_name == 'invoice_filters':
                    for entry in data:
                        writer.writerow([entry.get(key,'') for key in keys[form_name]])
        except Exception as e:
            api_logger.critical(message='Error: %s %s %s' % (e, repr(traceback.format_exc()),request.query_params,),
                                log_type='ingoibibo', bucket='ReportsAPI',
                                stage='Reports.DownloadReportViewSet')
            return HttpResponseRedirect('/extranet/')
        hotel_methods.extranet_web_log_format(request.user.id, request.GET['hid'],
                                              'Reports', request.GET.get('formname'), 'download', stage=hotel_methods.get_device_type(request))
        return response


class PLBDownloadReportViewSet(viewsets.GenericViewSet):

    authentication_classes = (BasicAuthentication, SessionAuthentication,
                              TokenAuthentication)
    permission_classes = (IsAuthenticated, PLBPermissionCheck)
    filter_backends = (filters.DjangoFilterBackend, )
    http_method_names = ['get']

    def list(self, request, *args, **kwargs):
        try:
            plb_statement_records = request.plbrecord
            if request.query_params.get('hid') and not plb_statement_records:
                report_file = send_plb_statement_filter_reports(request)
            else:
                if plb_statement_records and plb_statement_records[0].plb.plbruletype == 'vdiroomnights':
                    report_file = send_vdi_statement_report(plb_statement_records, download_statement=True)
                else:
                    report_file = send_plb_statment_report(plb_statement_records, download_statement=True)
            wrapper = FileWrapper(file(report_file.name))
            response = HttpResponse(wrapper, content_type='text/csv')
            response['Content-Disposition'] = 'attachment; filename=%s ' % \
                                              (os.path.basename(report_file.name))
            response['Content-Length'] = os.path.getsize(report_file.name)
            if plb_statement_records:
                hotel_methods.extranet_web_log_format(
                    request.user.id, plb_statement_records[0].plb.hotel.hotelcode,
                    'Reports', 'plbrecordsFilters', 'download', hotel_methods.get_device_type(request))
            return response
        except Exception as e:
            api_logger.critical(message='Error: %s  %s' % (e, request.query_params,),
                                log_type='ingoibibo', bucket='ReportsAPI',
                                stage='Reports.PLBDownloadReportViewSet')
            return HttpResponseRedirect('/extranet/')


class InvoiceDownloadViewSet(viewsets.GenericViewSet):

    authentication_classes = (BasicAuthentication,
                              CustomTokenNewAuthentication)
    permission_classes = (IsAuthenticated, GuardianPermissionCheck)
    filter_backends = (filters.DjangoFilterBackend, )
    http_method_names = ['get']

    def retrieve(self, request, *args, **kwargs):
        response = {'success': False}
        report_type = request.query_params.get('reporttype', 'commission')
        response_status = status.HTTP_200_OK
        result_file = None
        try:
            from hotels.models import HotelBooking
            booking_id = request.query_params.get('bookingId', None)
            if booking_id:
                try: 
                    booking_obj = HotelBooking.objects.filter(id=booking_id).first()
                    booking_hotel = booking_obj.hotel.hotelcode
                    if booking_hotel != request.hotel.hotelcode:
                        response['detail'] = 'You do not have permission to perform this action.'
                        return Response(response, status.HTTP_403_FORBIDDEN)
                except HotelBooking.DoesNotExist:
                    response['message'] = 'Booking not found'
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)
                
            vendor = request.query_params.get('vendor', 'GI')
            invoice_month = request.query_params.get('invoiceMonth', '')
            actiontrigger = request.query_params.get('actiontrigger', None)
            if report_type == 'commission':
                hotel = request.hotel
                api_logger.info("Request to download")
                if hotel.duplicated_from:
                    api_logger.info("INTO parent property")
                    parent_hotel = HotelDetail.objects.filter(hotelcode = hotel.duplicated_from).first()
                    hotel=parent_hotel
                    vendor = parent_hotel.vendor
                if not (hotel.first_active_date and hotel.country.lower() == 'india'):
                    return Response(response, response_status)
                commission_invoice_file = InvoiceDownloadViewSet.send_commission_invoice_report(
                    invoice_month, hotel, vendor, actiontrigger)

                response['success'] = True
                if actiontrigger and commission_invoice_file:
                    result_file = open(commission_invoice_file.filename, 'r')
                    response = HttpResponse(result_file.read(), content_type="application/zip")
                    response['Content-Disposition'] = 'attachment; filename={}'.format(result_file.name.replace('/tmp/', ''))
                    result_file.close()
                    os.remove(result_file.name)
                    return response

            elif report_type == 'hotel-tax':
                booking_id = request.query_params.get('bookingId', '')
                ## sql_injection id: 5, 8, 9 /api/v1/reports/invoice/1000142727/ NR
                result_file = InvoiceDownloadViewSet.trigger_tax_invoice_download(booking_id)
                response = HttpResponse(result_file.read(), content_type="application/pdf")
                response['Content-Disposition'] = 'attachment; filename={}'.format(result_file.name.replace('/tmp/',''))
                result_file.close()
                os.remove(result_file.name)
                return response
            else:
                hotel = request.hotel
                ## sql_injection id: 2 /api/v1/reports/invoice/1000142727/ NR
                response = InvoiceDownloadViewSet.send_plb_invoice_report(
                    invoice_month, hotel, vendor, actiontrigger)
                result_files = response.get('result_files',None)
                if actiontrigger:
                    if result_files:
                        invoice_file = zipping_files(result_files, hotel)
                        result_file = open(invoice_file.filename, 'r')
                        response = HttpResponse(result_file.read(), content_type="application/zip")
                        response['Content-Disposition'] = 'attachment; filename={}'.format(
                            result_file.name.replace('/tmp/', ''))
                        result_file.close()
                        os.remove(result_file.name)
                        return response
                    else:
                        response['reason'] = 'No bookings found for this period'
        except Exception as e:
            api_logger.critical(message='Error: %s  %s' % (e, request.query_params,),
                                log_type='ingoibibo', bucket='ReportsAPI',
                                stage='Reports.InvoiceDownloadViewSet')
            if result_file != None:
                os.remove(result_file.name)
            response_status = status.HTTP_500_INTERNAL_SERVER_ERROR
        return Response(response, response_status)

    @staticmethod
    def send_commission_invoice_report(invoice_month, hotel, vendor, actiontrigger=None):
        if invoice_month == 'January_2017':
            contexts = get_context_for_january(hotel, vendor)
        elif invoice_month == 'June_2017':
            contexts = get_context_for_june(hotel, vendor)
        else:
            contexts = [get_invoice_context(invoice_month=invoice_month, hotel=hotel, vendor=vendor)]

        if settings.HOST not in settings.PROD_HOSTS and not actiontrigger:
            for context in contexts:
                send_commission_invoice_report(context, hotel.id)
        elif actiontrigger:
            report_files = []
            for context in contexts:
                context['actiontrigger'] = actiontrigger
                invoice_files = send_commission_invoice_report(context, hotel.id)
                if invoice_files:
                    report_files.extend(invoice_files)
            report_file = zipping_files(report_files,hotel,True) #report_files is the list of file pointers
            for file in report_files:
                file.close()
                os.remove(file.name)
            return report_file
        else:
            from reports.tasks import send_hotel_commission_invoice_task
            for context in contexts:
                send_hotel_commission_invoice_task.apply_async(args=(
                    [context, hotel.id]),)


    @staticmethod
    def send_plb_invoice_report(invoice_month, hotel, vendor, actiontrigger=None):
        if invoice_month == 'January_2017':
            contexts = get_context_for_january(hotel, prefix='plb', vendor=vendor)
        else:
            contexts = [get_invoice_context(invoice_month, hotel, prefix='plb', vendor=vendor)]
        if contexts[0]['invoice_no'] < 16 and vendor == 'GI':
            response = pre_gst_plb_invoice(contexts, hotel, actiontrigger)
        else:
            response = post_gst_plb_invoice(contexts, hotel, actiontrigger)
        return response

    @staticmethod
    def trigger_tax_invoice_download(booking_id):
        return download_tax_invoice(booking_id)
