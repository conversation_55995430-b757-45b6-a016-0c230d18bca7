from rest_framework.decorators import detail_route
from rest_framework.viewsets import GenericViewSet
from rest_framework.exceptions import ValidationError
from rest_framework.authentication import TokenAuthentication, SessionAuthentication
from rest_framework.permissions import IsAuthenticated
from django.http import HttpResponse, HttpResponseServerError
from django.core.cache import cache
from api.v1.analytics.serializers.persuasion_serializer import PersuasionUpdateSerializer, PersuasionDismissSerializer
from api.v1.analytics.permissions.permissions import GuardianPermissionCheck
from utils.logger import Logger
from api.v1.analytics.persuasions.constants import PROPERTY_MAPPING_PERSUASION_V1_V2,REVERSE_PROPERTY_MAPPING_PERSUASION_V2_V1

import requests

import traceback
import json

from api.v1.analytics.persuasions.helper import PersuasionUpdator,WatsonApiHolder

logger_api = Logger(logger='inventoryAPILogger')
PERSUASION_ID = "persuasionId"

class PersuasionViewSet(GenericViewSet):
    lookup_field = "hotelcode"
    authentication_classes = (TokenAuthentication, SessionAuthentication, )
    permission_classes = (IsAuthenticated, GuardianPermissionCheck, )
    @detail_route(methods=["post"], url_path="update")
    def update_persuasion(self, request, hotelcode=None):
        persuasions = json.loads(request.POST.get('persuasions'))
        result_list = []
        persuasion_list_for_update_on_watson = []
        logger_api.info(message="Update persuasion request received with data %s"%(persuasions),
                            log_type="ingoibibo", bucket="AnalyticsAPI",
                            stage="analytics.persuasions.views")
        for persuasion in persuasions:
            try:
                serializer = PersuasionUpdateSerializer(data=persuasion)
                serializer.is_valid(raise_exception=True)
                persuasion_cache_json = serializer.validated_data['cached_data']
                hotel_code = persuasion_cache_json['hotelCode']
                persuasion_type = persuasion_cache_json['persuasionType']
                p_type = persuasion_cache_json['pType']
                persuasion_cache_json['user'] = request.user
                updated_fields = serializer.validated_data['updated_fields']
                update_response_dict = PersuasionUpdator.update(persuasion_type, persuasion_cache_json, updated_fields)
                result_list.append(update_response_dict)
                if update_response_dict.get('success'):
                    persuasion_list_for_update_on_watson.append(
                        {'id': serializer.validated_data.get(PERSUASION_ID),
                         'status': PROPERTY_MAPPING_PERSUASION_V1_V2.get(serializer.data.get('status')),
                         'sub_type': persuasion_type,
                         'type': p_type,
                         'hotel_code': hotel_code})
            except ValueError as e:
                logger_api.critical(message="Value Error Occurred while Updating persuasions: Error : %s, Traceback :"
                                            "%s"
                                            % (str(e), repr(traceback.format_exc())),
                                    log_type="ingoibibo", bucket="AnalyticsAPI",
                                    stage="analytics.persuasions.views")
                result_list.append({PERSUASION_ID: persuasion[PERSUASION_ID], "success": False,
                                    "message": e.message})
            except Exception as e:
                logger_api.critical(message="Exception Occurred while Updating persuasions: Error : %s, Traceback :"
                                            "%s"
                                            % (str(e), repr(traceback.format_exc())),
                                    log_type="ingoibibo", bucket="AnalyticsAPI",
                                    stage="analytics.persuasions.views")
                result_list.append({PERSUASION_ID: persuasion[PERSUASION_ID], "success": False,
                                "message":str(e)})

        try:
            WatsonApiHolder.update_persuasions(persuasion_list_for_update_on_watson)
        except Exception as e:
            logger_api.critical(message="Exception Occurred while Updating persuasions on watson: Error : %s, Traceback :"
                                        "%s"
                                        % (str(e), repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="AnalyticsAPI",
                                stage="analytics.persuasions.views")
            return HttpResponse(json.dumps(result_list))
        return HttpResponse(json.dumps(result_list))


    @detail_route(methods=["post"], url_path="dismiss")
    def dismiss_persuasion(self, request, hotelcode=None):
        response_for_ui = {'success':False,'message':'Unfortunately , We cannot dismiss card at the moment .','dismissCard':False}
        try:
            persuasions = json.loads(request.POST.get('persuasions'))
            persuasion_list_for_update_on_watson = []
            logger_api.info(message="Dismiss persuasion request received with data %s" % (persuasions),
                            log_type="ingoibibo", bucket="AnalyticsAPI",
                            stage="analytics.persuasions.views")
            for persuasion in persuasions:
                serializer = PersuasionDismissSerializer(data=persuasion)
                serializer.is_valid(raise_exception=True)
                persuasion_cache_json = serializer.validated_data['cached_data']
                persuasion_type = persuasion_cache_json['persuasionType']
                p_type = persuasion_cache_json['pType']
                hotel_code = persuasion_cache_json['hotelCode']

                persuasion_list_for_update_on_watson.append(
                    {'id': serializer.validated_data.get(PERSUASION_ID),
                     'status': PROPERTY_MAPPING_PERSUASION_V1_V2.get(serializer.data.get('status')),
                     'sub_type': persuasion_type,
                     'type': p_type,
                     'hotel_code': hotel_code
                     })
            result_array, message = WatsonApiHolder.update_persuasions(persuasion_list_for_update_on_watson)
            # dismiss persuasion from UI using HSA AP
            response_for_ui['message'] =message
            if result_array:
                response_for_ui['success'] = True
                response_for_ui['dismissCard'] = True
        except Exception as e:
            logger_api.critical(
                message="Exception Occurred while serializing persuasion, Reason : %s"%(str(e)),
                log_type="ingoibibo", bucket="AnalyticsAPI",
                stage="analytics.persuasions.views")
        return HttpResponse(json.dumps(response_for_ui))