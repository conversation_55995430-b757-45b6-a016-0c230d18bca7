from django.db.models import Q
from rest_framework import serializers
from api.v1.validators import Required<PERSON><PERSON><PERSON><PERSON>
from common.object_guardian import check_permission
from hotels.models import SecretDeal, HotelDetail, RatePlan, SecretDealBounced
from hotels.hotelchoice import SECRET_DEAL_BOUNCED_NON_APPLICABLE_DAYS



class SecretDealSerializer(serializers.ModelSerializer):
    relatedto = serializers.SerializerMethodField()
    relatedname = serializers.SerializerMethodField()
    relatedcode = serializers.SerializerMethodField()

    def validate(self, data):

        if not data.get('deal_type') in ['custom', 'discount']:
            raise serializers.ValidationError({'deal_type': 'Please specify valid deal type'})

        if data.get('deal_type') == 'custom':
            if not data.get('message'):
                raise serializers.ValidationError({'message': 'Please specify message'})
            data['discount_type'] = None
            data['discount_value'] = None
        else:
            data['message'] = None
            if not data.get('discount_type'):
                raise serializers.ValidationError({'message': 'Please specify discount type'})
            if not data.get('discount_value') or int(data.get('discount_value')) < 0:
                raise serializers.ValidationError({'message': 'Please specify valid discount value'})
            if data.get('discount_type') == 'percent':
                    if int(data.get('discount_value')) > 100:
                        raise serializers.ValidationError({'message': 'Please specify discount less than 100%'})

        if data.get('booking_validity_start_date') >= data.get('booking_validity_end_date'):
            raise serializers.ValidationError({'booking_validity_start_date': 'booking start should be less than booking end'})

        if data.get('checkin_validity_start_date') >= data.get('checkin_validity_end_date'):
            raise serializers.ValidationError({'checkin_validity_start_date': 'checkin start should be less than checkin end'})

        if data.get('booking_validity_end_date') > data.get('checkin_validity_end_date'):
            raise serializers.ValidationError({'booking_validity_end_date': 'booking end should be less than checkin end'})

        return data

    class Meta:
        model = SecretDeal
        fields = ('secret_deal_code', 'isactive', 'booking_validity_start_date', 'booking_validity_end_date',
                  'checkin_validity_start_date', 'checkin_validity_end_date', 'discount_type', 'discount_value',
                  'content_type', 'object_id', 'user', 'message', 'cutoff', 'deal_type', 'email', 'firstname',
                  'lastname', 'phonenumber', 'relatedto',  'relatedname', 'relatedcode', 'available_count',
                  'reedemed_count', 'description', 'source', 'secret_deal_rule', 'bounced_hotel',
                  )

        read_only_fields = ('secret_deal_code', 'relatedto', 'relatedname', 'relatedcode', 'description',
                            )
        validators = [
            RequiredValidator(
                fields=('booking_validity_start_date', 'booking_validity_end_date',
                        'checkin_validity_start_date', 'checkin_validity_end_date', 'content_type', 'object_id',
                         'user', 'deal_type', 'email',
                        #'firstname', 'lastname', 'phonenumber'
                        )
            )
        ]

    def get_relatedto(self, obj):
        try:
            if obj.content_type.model == 'hoteldetail':
                return 'Hotel'
            else:
                return 'Rateplan'
        except:
            return ''

    def get_relatedname(self, obj):
        try:
            if obj.content_type.model == 'hoteldetail':
                return obj.content_object.hotelname
            else:
                return obj.content_object.roomtype.roomtypename + ' (' + obj.content_object.rateplanname + ')'
        except:
            return ''

    def get_relatedcode(self, obj):
        try:
            if obj.content_type.model == 'hoteldetail':
                return obj.content_object.hotelcode
            else:
                return obj.content_object.rateplancode
        except:
            return ''


class SecretDealStatusSerializer(serializers.ModelSerializer):

    class Meta:
        model = SecretDeal
        fields = ('isactive', )


class NotApplicableWeekdays(serializers.Field):
    def to_representation(self, value):
        return value

    def to_internal_value(self, data):
        possible_choices = set(choice[0] for choice in SECRET_DEAL_BOUNCED_NON_APPLICABLE_DAYS)
        if not all(value in possible_choices for value in data):
            raise serializers.ValidationError({'weekdays': 'invalid weekday list'})
        return data


class SecretDealBouncedSerializer(serializers.ModelSerializer):
    weekdays = NotApplicableWeekdays()

    class Meta:
        model = SecretDealBounced

