import json, ast
import re
import traceback
from datetime import datetime

from django.conf import settings
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import Group
from ingouser.models import User
from django.contrib.contenttypes.models import ContentType
from django.http.response import JsonResponse, HttpResponse
from guardian.models import UserObjectPermission
from lib.custom_decorators import token_required, custom_required
from rest_framework import status
from rest_framework import viewsets
from rest_framework.authentication import SessionAuthentication, TokenAuthentication, \
    BasicAuthentication
from rest_framework.decorators import detail_route, list_route, api_view, \
    authentication_classes, permission_classes
from rest_framework.exceptions import ValidationError
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON>, JSONParser
from rest_framework.permissions import IsAuthenticated, AllowAny, IsAdminUser
from rest_framework.response import Response
from rest_framework.settings import api_settings
from common.constants import RESELLER_AGREEMENT_MAPPING, LegalEntityTypeConstants

from actions.action_helper import stuck_case_action_runner
from actions.models.action_detail import ActionJob, JOB_PENDING
from actions.tasks import manage_actions
from activity_stream.helper import get_target_activity
from api.hotels.handler import CommonHandler
from api.v1.common_permissions.permissions import IsGenericAPIConsumer
from api.v1.hotels.authentication import VendorAuthentication

from api.v1.hotels.exceptions.custom_exceptions import ResourceNotFoundException
from api.v1.hotels.permissions.permissions import FraudTeamPermissionCheck, \
    HotelCreatePermission
from api.v1.hotels.resources import agreement_resources
from api.v1.hotels.resources.hotel_resources import create_hotel, update_hotel, \
    get_hoteldetail_from_mobile, update_amendment_name_change_allowed, get_tax_rate_for_hotel, fetch_active_snooze
from api.v1.hotels.resources.hotel_resources import get_inventory_list_room, get_rate_list_rateplan
from api.v1.hotels.resources.snooze_resources import validate_snooze_data, block_booking, unblock_booking
from api.v1.hotels.serializers import GSTDetailSerializer, InvitationPostSerializer
from api.v1.hotels.serializers import HotelSerializer, HotelActivitySerializer, \
    HotelBulkEmailerSerializer, BasicInfoSerializer, ContactInfoSerializer, \
    PolicyInfoSerializer, UsefulInfoSerializer, MapsInfoSerializer, \
    PayathotelInfoSerializer, AmenitiesInfoSerializer, PricingParamSerializer, \
    HotelDetailSerializer, MobiletoHotelSerializer, HotelDetailPlusContact, HotelTaxSerializer
from api.v1.hotels.serializers.fcl_serializers import AgreementMasterSerializer, HotelAgreementMappingSerializer
from api.v1.inventory.views import InventoryViewset
from api.v1.invitation.permission import PostInvitationPermission
from api.v1.pagination import StandardResultsSetPagination
from api.v1.hotels.permissions import HotelActivityPermission, GuardianPermissionCheck, HotelAccessibilityCheck, \
    GstnOptinPermissionCheck
from api.v1.hotels.resources import hotel_resources
from api.v1.hotels.proto.renderers import MobiletoHotelProtoRenderer
from api.v1.hotels.proto.definitions.mobile_to_hoteldetail_pb2 import MobiletoHotelResponse
from django.core.cache import cache
from django.db.models import Q
from api.v1.rooms.serializers.slot_room import SlotRoomSerializer
from api.v2.policy.resources.helper import get_all_hotel_policy, update_hotel_policy
from api.v2.users.service import HardBlockUserHelper

from common.commonhelper import fetch_client_details, get_log_identifier, update_specific_identifier,\
    update_error_identifier, remove_error_dict
from common.models import ItemNote, get_ifsc_to_bankname_dict, get_bank_names
from common.object_guardian import assign_permissions
from communication.hotelMailsSMS import send_bulk_booking_mailer
from communication.common_comms.communications import sendMail
from communication.notifications import Notify
from communication.tasks import snooze_event_mail, snooze_end_mail
from extranet import extranetvariables
from extranet.propertydetails import update_hotel_locality
from extranet.services import services
from goibibo_inventory.settings import GET_RATES_PHOENIX_CALL_FLAG
from hotels import hotelchoice
from hotels.configuration import BLACKLIST_CHAIN_ID_LIST
from hotels.hotelchoice import EXTRANET_FCL, HOSTEL_TYPE, HOTEL_DETAIL_CONTENT_TYPE, SNOOZE_TYPE_VALUES, SELL_BLOCK, \
    BOOK_BLOCK, SELL_BOOK_BLOCK, SNOOZE_TYPE_VALUES_DB_MAP, SNOOZE_ACTION_VALUES_DB_MAP
from hotels.hotelchoice import TAN_VENDORS
from hotels.methods import HotelMethods
from hotels.constants.constants import NAME_CHANGE_MODIFICATION_VERSION
from hotels.models import HotelDetail, HotelAdminUser, Amenities, Inclusions, \
    RoomDetail, SlotRoom, RatePlan, configuration as HotelConf, helper as HotelHelper, \
    GSTDetail, VendorMapping, VendorDetail, AgreementMaster, HotelAgreementMapping, GenericContactDetail
from hotels.models.helper import validate_checkin_end_time
from hotels.models.hoteldetail import AmendmentPolicy, Snooze
from lib.sanitize_request import sanitize_request
from lib.aes_encryption.helpers import hash_value
from rates_service.grpc_rates_client import GetRatesClient
from rates_service.rates_formatter.grpc_rates_formatter import get_formatted_request_for_getting_rates_and_restrictions, \
     get_formatted_response_for_room_rates_inventory_mobile
from utils.logger import Logger
from hotels.constants.constants import OYO_CHAIN_ID
from django.core.exceptions import ObjectDoesNotExist
from django.core.exceptions import ValidationError as DjangoValidationError
from rest_framework import serializers



hard_block_user_helper = HardBlockUserHelper.get_instance()
api_logger = Logger(logger='inventoryAPILogger')

hotel_methods = HotelMethods()
get_rates_client = GetRatesClient()

hotel_content_type = ContentType.objects.get_for_model(HotelDetail)

display_dict = {"contactinfo": "Contacts", "policyinfo": "Policy", "usefulinfo": "Settings", "mapsinfo": "Maps",
                "payathotelinfo": "Pay@Hotel", "basicinfo": "Basic Info", "amenitiesinfo": "Amenities",
                "pricingparams": "Pricing Parameters"}


class HotelViewset(viewsets.ModelViewSet):

    queryset = HotelDetail.objects.filter(~Q(
            chainname__id__in=BLACKLIST_CHAIN_ID_LIST))
    authentication_classes = (TokenAuthentication, SessionAuthentication, BasicAuthentication, )
    lookup_field = 'hotelcode'
    serializer_class = HotelSerializer
    pagination_class = StandardResultsSetPagination
    http_method_names = ['get', 'post', 'put']
    allowed_filter_params = ['hotelname', 'hotelname__contains']
    filter_map = {
        'hotelname__contains': 'hotelname__icontains'
    }
    allowed_fields = ['hotelname', 'hotelcode', 'city']
    default_fields = ['flag_bits_1', 'flag_bits_2', 'flag_bits_3', 'displayname', 'voyagerid']

    def get_permissions(self):

        if self.request.method in ['GET', 'PUT'] and not self.permission_classes:
            if self.request.method == 'GET' and re.match('/api/v1/hotel/[0-9]', self.request.path):
                self.permission_classes = [IsAuthenticated, HotelAccessibilityCheck, ]
            else:
                self.permission_classes = [IsAuthenticated, GuardianPermissionCheck, ]
        elif self.request.method == 'POST' and not self.permission_classes:
            self.permission_classes = [HotelCreatePermission, ]
        elif not self.permission_classes:
            self.permission_classes = [IsAuthenticated, ]

        return super(HotelViewset, self).get_permissions()

    def get_queryset(self):
        """
        Overriding queryset to handle permission according to our application
        :return: A filtered django queryset which should be used as a base
                    for any further filtering and other operations.
        """

        # By default getting all the hotels for which the user has permission
        # for view_hoteldetail

        filter_params = {}
        for field, value in self.request.query_params.iteritems():
            if field in self.allowed_filter_params:
                filter_params[self.filter_map.get(field, field)] = value

        fields = filter(lambda field: field in self.allowed_fields,
                            self.request.query_params.get('fields', '').split(','))
        fields.extend(self.default_fields)

        if self.request.user.is_superuser or (
                self.request.method == 'GET' and self.request.user.is_staff):
            return self.queryset.filter(**filter_params).only(*fields)

        hotel_ids = UserObjectPermission.objects.\
            filter(user_id=self.request.user.pk, content_type=hotel_content_type.id,
                   permission__codename='view_hoteldetail').distinct().values_list('object_pk', flat=True)

        return HotelDetail.objects.filter(id__in=hotel_ids).filter(**filter_params).only(*fields)

    def retrieve(self, request, *args, **kwargs):
        include_fields = request.query_params.get('include_fields', '').split(',')
        hotel = request.hotel
        serializer_class = HotelDetailSerializer
        if 'primary_contact' in include_fields:
            serializer_class = HotelDetailPlusContact

        response_data = serializer_class(hotel).data

        hostel_type = {k: v for k, v in HOSTEL_TYPE}

        if response_data.get('hostel_type') and response_data['hostel_type'] in hostel_type:
            response_data['hostel_type'] = hostel_type[response_data['hostel_type']]
        response_data['is_user_hard_blocked'] = False

        # Checking If user's request is blocked or not.
        if not request.user.is_staff:
            hotel_code = kwargs.get("hotelcode")
            try:
                response_data['is_user_hard_blocked'] = hard_block_user_helper.check_is_user_hard_blocked(hotel_code)
            except Exception as e:
                api_logger.critical(message="Exception Occurred while checking hard blocking of user: %s \t\t %s" %
                                            (str(e), repr(traceback.format_exc())), log_type='ingoibibo',
                                    bucket='HotelViewsetAPI', stage='api.v1.hotels.views.HotelViewset.retrieve')

        return Response(response_data, status=status.HTTP_200_OK)

    def list(self, request, *args, **kwargs):
        if 'user_mobile' in request.query_params:
            mobile = request.query_params['user_mobile']
            response_format = request.query_params.get('response_format')
            return self.search_hotel_by_mobile(mobile, response_format)
        return super(HotelViewset, self).list(self, request, args, kwargs)

    def update(self, request, *args, **kwargs):
        response = {'message': ''}
        updated_hotel_data = None

        hotelcode = kwargs.get("hotelcode")

        try:
            if hotelcode:
                hotel_data = request.data
                user = request.user
                hotel = HotelDetail.objects.get(hotelcode=hotelcode)

                is_extraguest_flag_updated = ('add_extraguest_to_declared_flag' in hotel_data) and (hotel.add_extraguest_to_declared_flag != hotel_data['add_extraguest_to_declared_flag'])
                if is_extraguest_flag_updated and not user.is_superuser:
                    error_response = {'success': False,
                                      'message': 'You do not have permission to change add_extraguest_to_declared_flag attribute.'}
                    return Response(error_response, status=status.HTTP_403_FORBIDDEN)

                if hotel.get_chain_id == OYO_CHAIN_ID and hotel_data.get('starrating'):
                    hotel_data.pop('starrating', '')

                if 'name_change_allowed' in hotel_data:
                    update_amendment_name_change_allowed(hotel.id, hotel_data['name_change_allowed'], user)
                    response["data"].update({'name_change_allowed':hotel_data['name_change_allowed']})
                    hotel_data.pop('name_change_allowed')
                serialized_data = HotelDetailSerializer(hotel, data=hotel_data, partial=True)

                if serialized_data.is_valid():
                    serialized_data.save()
                    response["data"] = serialized_data.data
                else:
                    response["data"] = serialized_data.errors
        except Exception, e:
            response['message'] = str(e)
            api_logger.critical(message="Exception Occurred: %s \t\t %s" % (str(e), repr(traceback.format_exc())),
                                log_type='ingoibibo', bucket='HotelViewsetAPI',
                                stage='api.v1.hotels.views.HotelViewset.update')
            return Response(response, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        api_logger.info(message='Hotel updated successfully: %s' % (str(updated_hotel_data)),
                            log_type='ingoibibo', bucket='HotelViewsetAPI',
                            stage='api.v1.hotels.views.HotelViewset.update')

        return Response(response, status=status.HTTP_201_CREATED)

    def create(self, request, *args, **kwargs):
        request_data = fetch_client_details(request)
        response = {'message': '', 'success': False}
        try:
            hotel_data = request.data.get('data')
            if hotel_data.get('ingoid'):
                new_hotel_data = update_hotel(hotel_data, request.user, request_data)
            else:
                new_hotel_data = create_hotel(hotel_data, request.user, request_data)
            new_hotel_data.pop('hotel', None)
            hotel = HotelDetail.objects.get(hotelcode=new_hotel_data['hotelcode'])
            assign_permissions(request.user, hotel)
            response['success'] = True
            response['new_hotel_data'] = new_hotel_data
        except Exception, e:
            response['message'] = str(e)
            api_logger.critical(message="Exception Occured: %s \t\t %s" % (str(e), repr(traceback.format_exc())),
                                log_type='ingoibibo', bucket='HotelViewsetAPI',
                                stage='api.v1.hotels.views.HotelViewset.create')
            return Response(response, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        api_logger.info(message='Hotel created successfully --->>>>> %s' % (str(new_hotel_data)),

                            log_type='ingoibibo', bucket='HotelViewsetAPI',
                            stage='api.v1.hotels.views.HotelViewset.create')
        return Response(response, status=status.HTTP_201_CREATED)

    @detail_route(methods=['get'], permission_classes=[HotelActivityPermission])
    def activity(self, request, hotelcode=None):

        username = request.query_params.get('username', None)

        try:
            hotel = HotelDetail.objects.get(hotelcode=hotelcode)
            actions = get_target_activity(hotel, username)

            page = self.paginate_queryset(actions)
            if page is not None:
                serializer = HotelActivitySerializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = HotelActivitySerializer(actions, many=True)
            return Response(serializer.data)

        except User.DoesNotExist:
            raise ValidationError({"error": "User with username '%s' does not exist" % username})

        except Exception, e:
            raise ValidationError({"error": str(e)})

    @list_route(methods=['post'], url_path="bulk-booking-mailer", authentication_classes=(
        BasicAuthentication, VendorAuthentication, TokenAuthentication, SessionAuthentication), permission_classes=(IsAuthenticated, ))
    def bulk_booking_mailer(self, request):
        user = request.user
        vendor = hotel_methods.validateVendor(user=user)['vendor']
        request.data['vendor_logo'] = vendor.image.url() if vendor.image and \
                                vendor.image.isactive and vendor.image.url() else ''
        request.data['vendor_code'] = vendor.code
        hotel_bulk_emailer_serializer = HotelBulkEmailerSerializer(data=request.data)
        if not hotel_bulk_emailer_serializer.is_valid():
            return Response({
                "msg": "",
                "success": False,
                "errors": hotel_bulk_emailer_serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        else:
            self.pagination_class = None
            response = send_bulk_booking_mailer(hotel_bulk_emailer_serializer.data)
            return Response(response, status=status.HTTP_200_OK)

    @detail_route(methods=['post'], url_path="send-feedback", permission_classes=(IsAuthenticated, GuardianPermissionCheck,))
    def send_feedback(self, request, hotelcode=None):
        """
        This method sends feedback via mail to ingoibiob-team. market manager and bdo, received from hotelier.

        POST Request

        * *Request Details:* Sample Request:
        {
            "name" : "Rahul",
            "phone":9023581321,
            "email": "<EMAIL>",
            "feedback": "Dear Feedback, Hello Feedback Test",
            "related_to": "Promotion and Offers",
            "source": "mobile"
        }

        * *Supports Authentication classes:
            BasicAuthentication
            SessionAuthentication
            TokenAuthentication

        * *Sample SendFeedback URL*::

            http://in.goibibo.com/api/v1/hotel/feedback-related-fields/
            Credentials "<username>:<password>" as base64 encoded "Authorization" header

        * *Response Details:* Sample Response::
            if failure:
                {
                    message: "error message",
                }
            if success:
                {
                    message: "Feedback sent successfully"
                }
        """
        data = request.data
        post_data = dict()
        post_data["hotel_code"] = hotelcode
        post_data["user"] = request.user
        post_data["name"] = data.get("name", "")
        post_data["email"] = data.get("email", "")
        post_data["phone"] = data.get("phone", "")
        post_data["feedback"] = data.get("feedback", "")
        post_data["related_to"] = data.get("related_to", "")
        post_data["source"] = data.get("source", "")

        return hotel_resources.feedback(post_data)

    @list_route(methods=['get'], url_path="feedback-related-fields")
    def feedback_related_fields(self, request):
        """
        This method return type of possible fields related to feedback for mobile.

        GET Request

        * *Supports Authentication classes:
            BasicAuthentication
            SessionAuthentication
            TokenAuthentication

        * *Sample SendFeedback URL*::

            http://in.goibibo.com/api/v1/hotel/feedback-related-fields/
            Credentials "<username>:<password>" as base64 encoded "Authorization" header

        * *Response Details:* Sample Response::
        {
              "feedback_related_fields": [
                "General",
                "Content",
                "Booking Payment",
                "Inventory and Rates",
                "Hotel Rank",
                "Offers and Promotions",
                "Reviews and Ratings",
                "Mobile App"
              ]
        }

        """

        return JsonResponse({"feedback_related_fields": hotelchoice.MOBILE_FEEDBACK_RELATED_FIELDS})

    @list_route(methods=['get'], url_path='get-hotel-info', permission_classes=(GuardianPermissionCheck, ))
    def get_hotel_info(self, request, *args, **kwargs):
        response = {"message": "Error occurred"}
        serializer_dict = {"basicinfo": BasicInfoSerializer, "contactinfo": ContactInfoSerializer,
                           "policyinfo": PolicyInfoSerializer, "usefulinfo": UsefulInfoSerializer,
                           "mapsinfo": MapsInfoSerializer, "payathotelinfo": PayathotelInfoSerializer,
                           "amenitiesinfo": AmenitiesInfoSerializer}
        serializer_class = HotelSerializer
        try:
            if not request.hotel:
                return Response("Hotel Not Found", status=status.HTTP_404_NOT_FOUND)

            hotel_object = request.hotel
            detail = request.query_params.get("detail")
            hotel_methods.extranet_web_log_format(request.user.id, hotel_object.hotelcode, "Property", display_dict[detail], "view", stage=hotel_methods.get_device_type(request))
            if serializer_dict.get(detail):
                serializer_class = serializer_dict[detail]

            response = serializer_class(hotel_object).data
            if detail == "payathotelinfo":
                today = datetime.today().date()
                admin_user = HotelAdminUser.objects.get(hoteluser=request.user)
                cc_password = admin_user.password_for_cc_access
                response['cc_password'] = True if cc_password else False

            if detail == "policyinfo":
                success, policy_data = get_all_hotel_policy(hotel_object.id)
                response['rules'] = policy_data
            response["message"] = "Hotel details are received successfully."
        except Exception, e:
            api_logger.critical(message="Exception occurred: %s" % (str(e)), log_type="ingoibibo", bucket="HotelAPI",
                            stage="hotel.views.get_hotel_info")
            return Response(response, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response(response, status=status.HTTP_200_OK)

    @list_route(methods=["post"], url_path="register-hotel")
    def register_hotel(self, request):
        response = {"success": False}
        try:
            hotel_data = request.data.copy()
            hotel_data["user"] = request.user.id
            hotel_serializer = BasicInfoSerializer(data=hotel_data)
            if hotel_serializer.is_valid():
                new_hotel = HotelDetail.construct_data(**hotel_serializer.validated_data)
                new_hotel.save()
                new_hotel.hotelmanagers.add(request.user)
                hotel_methods.updateLogMsg(request.user, new_hotel, "Hotel Details Added.")
                cache_key = "all_child_hotels_list_" + request.user.username
                code_cache_key = "all_hotelcodelist_" + request.user.username
                cache.delete(cache_key)
                cache.delete(code_cache_key)
                hotel_serializer = BasicInfoSerializer(new_hotel)
                response["message"] = "Hotel details are created successfully. "
                response.update(hotel_serializer.data)
                response["success"] = True
            else:
                response["message"] = hotel_serializer.errors
                return Response(response, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            api_logger.critical(message="Exception occurred: %s" % (str(e)), log_type="ingoibibo", bucket="HotelAPI",
                            stage="hotel.views.create")
            return Response(response, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        api_logger.info(message="Hotel created: %s" % (str(response)), log_type="ingoibibo", bucket="HotelAPI",
                        stage="hotel.views.create")
        return Response(response, status=status.HTTP_201_CREATED)

    @list_route(methods=['post'], url_path="update-hotel-info", permission_classes=(GuardianPermissionCheck, ))
    def update_hotel_info(self, request):
        response = {"success": False}
        serializer_dict = {"contactinfo": ContactInfoSerializer, "policyinfo": PolicyInfoSerializer,
                           "usefulinfo": UsefulInfoSerializer, "mapsinfo": MapsInfoSerializer,
                           "payathotelinfo": PayathotelInfoSerializer, "basicinfo": BasicInfoSerializer,
                           "amenitiesinfo": AmenitiesInfoSerializer, "pricingparams": PricingParamSerializer}
        try:
            # sanitize request data, we can't set request.data directly so using request._data
            request._data = sanitize_request(request.data)
            hotel_object = request.hotel

            if not hotel_object:
                return Response("Hotel Not Found", status=status.HTTP_404_NOT_FOUND)

            checkintime = request.data.get("checkintime",None)
            checkinendtime = request.data.get("checkinendtime",None)
            if checkinendtime and checkintime :
                _ = validate_checkin_end_time(checkintime, checkinendtime)


            hotel_data = request.data.copy()
            user = request.user
            is_extraguest_flag_updated = ('add_extraguest_to_declared_flag' in hotel_data) and (hotel_object.add_extraguest_to_declared_flag != hotel_data['add_extraguest_to_declared_flag'])

            if is_extraguest_flag_updated and not user.is_superuser:
                error_response = { 'success': False, 'error': 'You do not have permission to change add_extraguest_to_declared_flag attribute.' }
                return Response(error_response, status=status.HTTP_403_FORBIDDEN)

            detail = hotel_data.get("detail")
            cityfk = None
            if serializer_dict.get(detail):
                serializer_class = serializer_dict[detail]
                hotel_methods.extranet_web_log_format(user.id, hotel_object.hotelcode, "Property", display_dict[detail], "update", stage=hotel_methods.get_device_type(request))

                if serializer_class == BasicInfoSerializer:
                    hotel_data.pop("timezone", None)
                    # hotel_data['timezone'] = hotel_object.timezone
                    cityfk = hotel_data.pop('cityfk', '')
                    hotel_data["hotelname"] = hotel_object.hotelname
                    hotel_data["displayname"] = hotel_object.displayname

                if 'name_change_allowed' in hotel_data:
                    update_amendment_name_change_allowed(hotel_object.id, hotel_data['name_change_allowed'], request.user)
                    response.update({'name_change_allowed':hotel_data['name_change_allowed']})
                    hotel_data.pop('name_change_allowed')

                hotel_serializer = serializer_class(hotel_object, data=hotel_data, partial=True)
                if hotel_serializer.is_valid():
                    validated_data = hotel_serializer.validated_data
                    validated_data.pop('acceptsBookingSince', None)
                    hotel_object.update(validated_data)
                    list_updated_fields = list(serializer_class.Meta.fields)
                    if 'acceptsBookingSince' in list_updated_fields:
                        list_updated_fields.remove('acceptsBookingSince')
                    if hotel_data.get('customercare_phonelist'):
                        if hotel_object.hotel_related_json_data:
                            hotel_object.hotel_related_json_data['customercare_phonelist'] = hotel_data['customercare_phonelist']
                        else:
                            related_json = dict()
                            related_json['customercare_phonelist'] = hotel_data['customercare_phonelist']
                            hotel_object.hotel_related_json_data = related_json
                    if hotel_data.get('acceptsBookingSince'):
                        if hotel_object.hotel_related_json_data:
                            hotel_object.hotel_related_json_data['acceptsBookingSince'] = int(hotel_data['acceptsBookingSince'])
                        else:
                            related_json = dict()
                            related_json['acceptsBookingSince'] = int(hotel_data['acceptsBookingSince'])
                            hotel_object.hotel_related_json_data = related_json
                        hotel_data.pop('acceptsBookingSince')

                    if serializer_class == PolicyInfoSerializer:
                        list_updated_fields = ['name_change_allowed']
                    elif serializer_class == PayathotelInfoSerializer:
                        list_updated_fields = ['pay_at_hotel_model','pay_at_hotel_pre_pay_policy','payathotelflag']
                    from hotels.models.hotel_flag_configuration import HOTEL_RELATED_FIELDS, FLAG_TWO_DICT
                    if len(set(list_updated_fields) - set(HOTEL_RELATED_FIELDS)) < len(list_updated_fields):
                        list_updated_fields.append('hotel_related_json_data')
                        if 'customercare_phonelist' in list_updated_fields:
                            list_updated_fields.remove("customercare_phonelist")
                    elif len(set(list_updated_fields) - set(FLAG_TWO_DICT)) < len(list_updated_fields):
                        list_updated_fields.append('flag_bits_2')
                    if serializer_class == ContactInfoSerializer:
                        hotel_object.save()
                    else:
                        hotel_object.save(update_fields=list_updated_fields)
                    response["message"] = "Hotel details are updated successfully. "
                    response["success"] = True
                    response.update(hotel_serializer.data)
                    if request.user.is_staff:
                        Notify.add_notification(obj=hotel_object, action='updated', fields=serializer_class)
                    if cityfk:
                        old_cityfk_id = hotel_object.cityfk_id
                        new_cityfk_id = cityfk.citycode
                        if old_cityfk_id != new_cityfk_id:
                            hotel_object.cityfk = cityfk
                            hotel_object.save(update_fields=["cityfk"])
                            response["message"] += "cityfk Updated from %s to %s." % (old_cityfk_id, new_cityfk_id)
                    hotel_methods.updateLogMsg(request.user, hotel_object, response["message"])
                else:
                    message = "".join(
                        ["%s %s\n" % (k, v[0]) for k, v in hotel_serializer.errors.items()])
                    response["message"] = message
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)
            else:
                response["message"] = "Please specify valid details"
                return Response(response, status=status.HTTP_400_BAD_REQUEST)
            if detail == 'policyinfo':
                policy_data = {
                    "rules": hotel_data.get('rules', []),
                    "hotel_id": hotel_object.id
                }
                response['success'], response["message"] = update_hotel_policy(policy_data)
                if not response['success']:
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)
                response["message"] = "Policy details are updated successfully. "
        except DjangoValidationError as e:
            response['message'] = e.message
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        except (ValidationError, serializers.ValidationError, DjangoValidationError, ObjectDoesNotExist) as e:
            response['message'] = e.detail
            api_logger.info(message="Exception occurred: %s" % (str(e)), log_type="ingoibibo", bucket="HotelAPI",
                                stage="hotel.views.update_hotel_info")
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            api_logger.critical(message="Exception occurred: %s" % (str(e)), log_type="ingoibibo", bucket="HotelAPI",
                            stage="hotel.views.update_hotel_info")
            response["message"] = "Exception occurred"
            return Response(response, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        api_logger.info(message="Hotel updated: %s" % (str(response)), log_type="ingoibibo", bucket="HotelAPI",
                        stage="hotel.views.update_hotel_info")
        return Response(response, status=status.HTTP_200_OK)

    @list_route(methods=['put'], url_path="apply-tax-on-effective-sell-rate", permission_classes=(GuardianPermissionCheck, ))
    def update_apply_tax_on_effective_sell_rate(self, request):
        response = {"success": False}
        try:
            hotel_data = request.data.copy()
            hotel_object = request.hotel
            hotel_object.apply_tax_on_effective_sell_rate = hotel_data['add_extraguest_to_declared_flag']
            hotel_object.save(update_fields=['flag_bits_1'])
        except Exception, e:
            api_logger.critical(message="Exception occurred: %s" % (str(e)), log_type="ingoibibo", bucket="HotelAPI",
                            stage="hotel.views.update_apply_tax_on_effective_sell_rate")
            response["message"] = "Exception occurred"
        return Response(response, status=status.HTTP_200_OK)

    @list_route(methods=['put'], url_path="gstn-assured-opt-in", permission_classes=(GstnOptinPermissionCheck, ))
    def update_gstn_assured_flag(self, request):
        response = {"success": False}
        # One we enable GST V2 flow this API will get deprecated.
        if settings.GST_FLOW_V2:
            return Response(response, status=status.HTTP_410_GONE)
        try:
            hotel_data = request.data.copy()
            hotel_object = request.hotel
            hotel_object.gstn_assured = hotel_data['gstn_assured']
            # if hotel chooses to not become gstn-assured we should not show him pop-up next time
            hotel_object.can_optin_for_gstn_invoice = hotel_data['gstn_assured']
            hotel_object.save(update_fields=['flag_bits_1'])
            response["success"] = True
        except Exception, e:
            api_logger.critical(message="Exception occurred: %s" % (str(e)), log_type="ingoibibo",
                                bucket="HotelAPI", stage="hotel.views.update_gstn_assured_flag")
            response["message"] = "Exception occurred"
        return Response(response, status=status.HTTP_200_OK)

    @detail_route(methods=['get'], url_path='disable-promo-code', permission_classes=(IsGenericAPIConsumer,))
    def disable_promo_code(self, request, hotelcode):
        """
        This API is used to disable promo code discount flag from fraud engine.

        :param request:
        :param hotelcode:
        :return:
        """
        response = {}
        try:
            hotel_obj = HotelDetail.objects.get(hotelcode=hotelcode)
            HotelDetail.objects.filter(hotelcode=hotelcode).update(promo_code_discount=False)
            message = "Flag promo_code_discount value was updated " \
                   "Old:%s New:False via API." % hotel_obj.promo_code_discount
            ItemNote(notes=message, content_object=hotel_obj, user=request.user).save()
            response["message"] = "Promo code discount disabled successfully."
            try:
                # CHECKUSERCHANGE - DONE
                to_email = HotelAdminUser.objects.get(hoteluser__username_backup=hotel_obj.contractmanager).email
                subject = "Promo code disabled for the hotel %s (%s)" \
                          % (hotel_obj.hotelname, hotel_obj.hotelcode)
                sendMail('', from_email=settings.EMAIL_ALERT_SENDER, body=message, subject=subject, template_id='',
                         cc_emails=[to_email], attached_file=None, bcc_emails=[])
            except Exception, e:
                api_logger.critical(message="User does not exist or mail couldn't be sent.",
                                    log_type="ingoibibo", bucket="HotelAPI",
                                stage="hotel.views.update_hotel_info")
                return Response(response, status=status.HTTP_200_OK)

            return Response(response, status=status.HTTP_200_OK)
        except Exception, e:
            response["message"] = "Exception occurred!"
            api_logger.critical(message="Exception occurred: %s" % (str(e.message)),
                                log_type="ingoibibo", bucket="HotelAPI",
                                stage="hotel.views.update_hotel_info")
            return Response(response, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @list_route(methods=['get'], url_path="fetch-general-details")
    def fetch_general_details(self, request):
        response = {'success': False}
        cache_key_hotel = 'hotels_amenity_list'
        cache_key_room = 'room_all_amenity'
        try:
            allamenities = cache.get(cache_key_hotel, [])
            allamenities_room = cache.get(cache_key_room, [])

            if not allamenities:
                allamenities_temp = list(
                    Amenities.objects.filter(amenitycat='hotel', isactive=True).values('id', 'amenityname', 'amenitysubcat',
                                                                        'priorityflag').order_by('amenityname'))
                allamenities = sorted(allamenities_temp, key=lambda k: k['amenitysubcat'])
                cache.set(cache_key_hotel, allamenities, 60 * 60 * 24)
            if not allamenities_room:
                allamenities_room_temp = list(
                    Amenities.objects.filter(amenitycat='room', isactive=True).values('id', 'amenityname', 'amenitysubcat',
                                                                       'priorityflag').order_by('amenityname'))
                allamenities_room = sorted(allamenities_room_temp, key=lambda k: k['amenitysubcat'])
                cache.set(cache_key_room, allamenities_room, 60 * 60 * 24)

            response['allamenitydetails'] = allamenities
            response['roomallfacilities'] = allamenities_room
            response['allifsccodes'] = get_ifsc_to_bankname_dict()
            response['allbanknames'] = get_bank_names()
            response['allbedtype'] = hotelchoice.BedType
            response['allroomview'] = hotelchoice.RoomView
            response['allextrabedtype'] = hotelchoice.extra_bed_choices
            response['allhoteltypes'] = hotelchoice.HotelType
            response['mealplantype'] = hotelchoice.MEAL_PLAN_CHOICES
            response['roomtype_options'] = hotelchoice.RoomType
            response['allinclusions'] = list(Inclusions.objects.values('id', 'displayname'))
            response['channelmanagerlist'] = extranetvariables.ChannelManagersDict.values()
            response['amenities_subcat'] = dict(hotelchoice.AmenitySubCat)
            response['title_list'] = dict(hotelchoice.name_title)
            response['designation_list'] = dict(hotelchoice.contact_designations)
            response['categories'] = dict(hotelchoice.contact_category)
            response['contact_category_desc'] = dict(hotelchoice.contact_category_desc)
            response['rates_and_inv_calender_span_days'] = hotelchoice.RATES_AND_INV_CALENDER_SPAN_DAYS
            response['success'] = True
            response['message'] = 'successfully retrieved'
        except Exception, e:
            api_logger.critical(message="Exception occurred: %s" % (str(e)), log_type="ingoibibo", bucket="HotelAPI",
                                stage="hotel.views.fetch_general_details")
            response['message'] = 'Exception occurred'
            return Response(response, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response(response, status=status.HTTP_200_OK)

    @list_route(methods=['post'], url_path='enable-disable-hotel', permission_classes=(FraudTeamPermissionCheck, ))
    def enable_disable_hotel(self, request):
        response = dict()
        response['success'] = False
        response['message'] = 'Error Occured'
        try:
            activate_hotel_ids = request.data.get('enable', [])
            deactivate_hotel_ids = request.data.get('disable', [])
            if not activate_hotel_ids and not deactivate_hotel_ids:
                response['message'] = 'Empty Hotels List'
                return Response(response, status=status.HTTP_200_OK)

            owner_group = Group.objects.filter(name__in=['Fraud Owner','FRAUD_OWNER']).first()
            staff_group = Group.objects.filter(name__in=['Fraud Staff', 'FRAUD_STAFF']).first()

            fraud_team_users = list(owner_group.user_set.all().values_list('id', flat=True))\
                          + list(staff_group.user_set.all().values_list('id', flat=True))

            activate_hotel_details = HotelDetail.objects.exclude(voyagerid__in=[""]).filter(voyagerid__in=activate_hotel_ids,
                                                                deactivated_by_id__in=fraud_team_users)\
                .only(*['id', 'hotelcode'])
            deactivate_hotel_details = HotelDetail.objects.exclude(voyagerid__in=[""]).filter(voyagerid__in=deactivate_hotel_ids)\
                .only(*['id', 'hotelcode', 'flag_bits_1', 'flag_bits_2', 'flag_bits_3'])
            if not activate_hotel_details.exists() and not deactivate_hotel_details.exists():
                response['message'] = 'No Matching Hotels'
                return Response(response, status=status.HTTP_200_OK)

            is_owner = owner_group.user_set.filter(username=request.user.username).exists()
            delist_actionables = []
            for hotel in deactivate_hotel_details:
                delist_actionables.append({'hotelcode': hotel.hotelcode, 'action': 'disable_hotel', 'mail_type': ''})

            for hotel in activate_hotel_details:
                delist_actionables.append({'hotelcode': hotel.hotelcode, 'action': 'enable_hotel', 'mail_type': ''})

            notifier_mail = list(owner_group.user_set.all().values_list('email', flat=True)) \
                + list(staff_group.user_set.all().values_list('email', flat=True))

            response = stuck_case_action_runner(request.user, delist_actionables=delist_actionables,
                                                notifier_mail=notifier_mail)
            if not response['success']:
                raise Exception(json.dumps(response))

            response['message'] = 'Action Added system Please ask your owner to approve'
            if is_owner:
                ## sql_injection id: 15, 16 NR
                if settings.DEBUG:
                    manage_actions(ActionJob.objects.using('default')
                                   .filter(status=JOB_PENDING, created_by_id=request.user.id))
                else:
                    manage_actions.apply_async(args=(ActionJob.objects.using('default')
                                                     .filter(status=JOB_PENDING, created_by_id=request.user.id), ))
                response['message'] = 'Actions Successfully Completed'

            response['success'] = True
            return Response(response, status=status.HTTP_200_OK)
        except Exception, e:
            api_logger.critical(message="Exception occurred: %s" % (str(e)), log_type="ingoibibo", bucket="HotelAPI",
                                stage="hotel.views.deactivate_hotel")
            return Response(response, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @list_route(methods=['post'], url_path='bulk/info', permission_classes=(FraudTeamPermissionCheck, ))
    def bulk_info(self, request):
        # This API is used by FRAUD team right now
        # Request POST data:
        # {
        #   "hvids": [] // max 1000 hotel voyager codes
        # }
        # Response
        # Success:
        # {
        #   "data": {
        #       "<hotel_voyager_code1>": {
        #           "starrating": 4
        #           "city": 4
        #           ...
        #       },
        #       "<hotel_voyager_code2>": {
        #           "starrating": 4
        #           "city": 4
        #           ...
        #       },
        #       ...
        #   }
        # }
        response = {
            "data": {}
        }
        response_status = status.HTTP_200_OK
        hotel_voyager_codes = request.data.get("hvids", [])

        if not hotel_voyager_codes:
            response["error"] = "No hotel voyager codes provided"
            response_status = status.HTTP_400_BAD_REQUEST
        elif len(hotel_voyager_codes) > 1000:
            response["error"] = "Max accepted data - 1000 hvids"
            response_status = status.HTTP_400_BAD_REQUEST
        else:
            response["data"] = {}
            for hotel_data in HotelDetail.objects.select_related("chainname").filter(
                    voyagerid__in=hotel_voyager_codes).values("starrating", "noofrooms", "physically_verified_flag",
                                                              "city", "state","voyagerid", "chainname__chainname",
                                                              "chainname"):
                hotel_data["chain_name"] = hotel_data.pop("chainname__chainname")
                hotel_data["chain_hotel_vhids"] = [str(hotel.voyagerid) for hotel in
                                                   HotelDetail.objects.filter(chainname=
                                                   hotel_data.get('chainname')).only("voyagerid", "flag_bits_1",
                                                                                     "flag_bits_2", "flag_bits_3",
                                                                                     "hotel_related_json_data")
                                                   if hotel.voyagerid] if hotel_data["chain_name"] else []
                hotel_data.pop("chainname", "")
                response["data"].update({
                    hotel_data.pop("voyagerid"): hotel_data
                })
        return Response(response, status=response_status)


    @detail_route(methods=['get'], url_path='slots', permission_classes=(GuardianPermissionCheck,))
    def get_all_slots(self, request, hotelcode):
        room_types = RoomDetail.objects.filter(
            hotel__hotelcode=request.hotel.hotelcode).exclude(
            parent_id__isnull=False).values_list("roomtypecode", flat=True)

        data = SlotRoomSerializer(
            SlotRoom.objects.filter(parent_room__roomtypecode__in=room_types),
            many=True)

        view = InventoryViewset.as_view({'get': 'room_inventory'})

        start_date = request.GET.get("start_date",
                                     datetime.now().date().strftime("%Y-%m-%d"))
        no_of_results = request.GET.get("no_of_results", 1)

        for index, room in enumerate(data.data):
            request.GET = {
                "hid": hotelcode,
                "roomtypecode": room["roomtypecode"],
                "startdate": start_date,
                "no_of_results": no_of_results
            }

            response = view(request)

            if response.status_code == 200:
                response = json.loads(response.content)

                if response["inventory_list"]:
                    room["inventory"] = response["inventory_list"][0]["available"]

        return Response({"data": data.data}, status=status.HTTP_200_OK)

    @detail_route(methods=['get'], url_path='rooms-rates')
    def get_all_rooms_and_rates(self, request, hotelcode):
        """
            It returns all the rooms and rates corresponding to given hotel.

            * *Supports Authentication classes:
                BasicAuthentication
                SessionAuthentication
                TokenAuthentication

            GET Request:
               * *Sample SendFeedback URL*::
                 in.goibibo.com/api/v1/hotel/1000000519/rooms-rates?start_date=2017-01-01&end_date=2017-01-02

            * *Response Details:* Sample Response::
            if failure:
                {
                   "hotelcode": "1000000519"
                }
            if success:
            {
              "hotelcode": "1000000519",
              "hoteldata": [
                {
                  "rateplancode": "990000001348",
                  "roomtypename": "DLx",
                  "rates": {
                    "2017-01-05": {
                      "nettsingle": 89,
                      "extraadult": 20,
                      "selltriple": "",
                      "racktriple": "",
                      "selldouble": 123,
                      "nettdouble": 98,
                      "racksingle": "",
                      "sellsingle": 112,
                      "netttriple": "",
                      "rackdouble": "",
                      "extrachild2": 10,
                      "extrachild1": 10
                    },
                    "2017-01-06": {
                      "nettsingle": 89,
                      "extraadult": 20,
                      "selltriple": "",
                      "racktriple": "",
                      "selldouble": 123,
                      "nettdouble": 98,
                      "racksingle": "",
                      "sellsingle": 112,
                      "netttriple": "",
                      "rackdouble": "",
                      "extrachild2": 10,
                      "extrachild1": 10
                    }
                  },
                  "inventory": {
                    "2017-01-05": {
                      "available": 11,
                      "booked": 0,
                      "block": 0
                    },
                    "2017-01-06": {
                      "available": 11,
                      "booked": 0,
                      "block": 0
                    }
                  },
                  "roomtypecode": "45000001250",
                  "rateplanname": "laziz"
                },
                {
                  "rateplancode": "990000001350",
                  "roomtypename": "DLx",
                  "rates": {
                    "2017-01-05": {
                      "nettsingle": 89,
                      "extraadult": 20,
                      "selltriple": "",
                      "racktriple": "",
                      "selldouble": 123,
                      "nettdouble": 98,
                      "racksingle": "",
                      "sellsingle": 112,
                      "netttriple": "",
                      "rackdouble": "",
                      "extrachild2": 10,
                      "extrachild1": 10
                    },
                    "2017-01-06": {
                      "nettsingle": 89,
                      "extraadult": 20,
                      "selltriple": "",
                      "racktriple": "",
                      "selldouble": 123,
                      "nettdouble": 98,
                      "racksingle": "",
                      "sellsingle": 112,
                      "netttriple": "",
                      "rackdouble": "",
                      "extrachild2": 10,
                      "extrachild1": 10
                    }
                  },
                  "inventory": {
                    "2017-01-05": {
                      "available": 11,
                      "booked": 0,
                      "block": 0
                    },
                    "2017-01-06": {
                      "available": 11,
                      "booked": 0,
                      "block": 0
                    }
                  },
                  "roomtypecode": "45000001250",
                  "rateplanname": "new rateplan"
                },
                {
                  "rateplancode": "990000001351",
                  "roomtypename": "DLx",
                  "rates": {
                    "2017-01-05": {
                      "nettsingle": 89,
                      "extraadult": "",
                      "selltriple": "",
                      "racktriple": "",
                      "selldouble": "",
                      "nettdouble": "",
                      "racksingle": "",
                      "sellsingle": 112,
                      "netttriple": "",
                      "rackdouble": "",
                      "extrachild2": "",
                      "extrachild1": ""
                    },
                    "2017-01-06": {
                      "nettsingle": 89,
                      "extraadult": "",
                      "selltriple": "",
                      "racktriple": "",
                      "selldouble": "",
                      "nettdouble": "",
                      "racksingle": "",
                      "sellsingle": 112,
                      "netttriple": "",
                      "rackdouble": "",
                      "extrachild2": "",
                      "extrachild1": ""
                    }
                  },
                  "inventory": {
                    "2017-01-05": {
                      "available": 11,
                      "booked": 0,
                      "block": 0
                    },
                    "2017-01-06": {
                      "available": 11,
                      "booked": 0,
                      "block": 0
                    }
                  },
                  "roomtypecode": "45000001250",
                  "rateplanname": "working rateplan"
                }
              ]
            }
        """
        response = {"hotelcode": hotelcode, "hoteldata": []}
        arg_dict = {'dateformat': "%Y-%m-%d"}
        contract_type = 'b2c'
        start_date = datetime.strptime(request.GET.get("start_date"),
                                       "%Y-%m-%d").date()
        end_date = datetime.strptime(request.GET.get('end_date'),
                                     "%Y-%m-%d").date()
        try:
            room_details = RoomDetail.objects.only("id").filter(hotel__hotelcode=hotelcode,
                                                                isactive=True)

            rate_plan_details = RatePlan.objects.values(
                "rateplanname", "rateplancode", "roomtype_id", "roomtype__roomtypename",
                "roomtype__roomtypecode", "roomtype__desc", "roomtype__max_adult_occupancy",
                "roomtype__base_adult_occupancy", "parent_id", "pay_at_hotel",
                "nonrefundable", 'sellcommission').\
                select_related("roomtype").filter(
                roomtype_id__in=[room.id for room in room_details], isactive=True)

            for rate_plan_detail in rate_plan_details:
                hotel_data_block = {"inventory":{}, "rates": {}}
                hotel_data_block['roomtypecode'] = rate_plan_detail['roomtype__roomtypecode']
                hotel_data_block['roomtypename'] = rate_plan_detail['roomtype__roomtypename']
                hotel_data_block['rateplancode'] = rate_plan_detail['rateplancode']
                hotel_data_block['rateplanname'] = rate_plan_detail['rateplanname']
                hotel_data_block['parent_id'] = rate_plan_detail['parent_id']
                hotel_data_block['pay_at_hotel'] = rate_plan_detail['pay_at_hotel']
                hotel_data_block['max_adult_occupancy'] = rate_plan_detail['roomtype__max_adult_occupancy']
                hotel_data_block['base_adult_occupancy'] = rate_plan_detail['roomtype__base_adult_occupancy']
                hotel_data_block['nonrefundable'] = rate_plan_detail['nonrefundable']
                hotel_data_block['commission'] = rate_plan_detail['sellcommission']

                get_inventory_list_room(rate_plan_detail['roomtype__roomtypecode'],
                                        start_date, end_date, arg_dict, hotel_data_block)

                get_rate_list_rateplan(rate_plan_detail['rateplancode'], start_date,
                                       end_date, arg_dict, contract_type, hotel_data_block)
                response['hoteldata'].append(hotel_data_block)
            try:
                hotel_obj = HotelDetail.objects.get(hotelcode=hotelcode)
                formatted_request = get_formatted_request_for_getting_rates_and_restrictions('hotel',
                                                                                             [hotel_obj.hotelcode],
                                                                                             ['b2c'],
                                                                                             start_date.strftime('%Y-%m-%d'),
                                                                                             end_date.strftime('%Y-%m-%d'),
                                                                                             hotel_obj.hotelcode,
                                                                                             'mobile', True, True)
                grpc_response = get_rates_client.get_rates(formatted_request)
                formatted_response = get_formatted_response_for_room_rates_inventory_mobile(grpc_response,
                                                                                            hotelcode,
                                                                                            response['hoteldata'])
                return Response(formatted_response, status=status.HTTP_200_OK)
            except Exception as exx:
                api_logger.critical(
                    message='Exception occurred fetching room rates %s %s %s %s' %
                            (hotelcode, start_date, end_date, exx),
                    log_type='ingoibibo', bucket='phoenix',
                    stage='hotel.views.get_all_rooms_and_rates')
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            api_logger.critical(
                message='Failed fetching room rates %s %s %s %s' %
                        (hotelcode, start_date, end_date, e),
                log_type='ingoibibo', bucket='HotelAPI',
                stage='hotel.views.get_all_rooms_and_rates')
            return Response(response, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @detail_route(methods=['get'], url_path='rateplans')
    def rateplans(self, request, hotelcode):
        """
            This returns all the rateplans for given hotelcode.

            * *Supports Authentication classes:
                BasicAuthentication
                SessionAuthentication
                TokenAuthentication

            GET Request:
               * *Sample SendFeedback URL*::
                 in.goibibo.com/api/v1/hotel/1000000519/rateplans

            * *Response Details:* Sample Response::
            if failure:
                {
                   "rateplans": []
                }
            if success:
                {
                    "rateplans": [
                                ["990000001348", "DLx","laziz"],
                                ["990000001350", "DLx","new rateplan"],
                                ["990000001351", "DLx","working rateplan"]
                                ]
                }

        """
        response = {}
        try:
            hid = HotelHelper.get_id_from_code(hotelcode, HotelConf.HotelCodeLength, HotelConf.HotelCodePrefix)

            rooms = RoomDetail.objects.filter(hotel_id=hid, isactive=True).values_list("id", "roomtypename")
            room_dict = {}
            for room in rooms:
                room_dict[room[0]] = room[1]
            rateplans = RatePlan.objects.filter(roomtype_id__in=room_dict.keys(),
                                                isactive=True).values_list("rateplancode", "rateplanname", "roomtype_id")
            rateplans_list = [[rp[0], room_dict[rp[2]], rp[1]] for rp in rateplans]
            response['rateplans'] = rateplans_list
            return Response(response, status=status.HTTP_200_OK)
        except Exception as e:
            api_logger.critical(
                message="Exception while retrieving rateplan data for hotelcode %s %s" % (hotelcode, e),
                log_type="ingoibibo", bucket="HotelAPI", stage="hotel.views.get_rateplans")
            return Response(response, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


    @list_route(methods=['get'], url_path='namechange', permission_classes=(IsAuthenticated,))
    def name_change_allowed_status(self, request):
        log_identifier = {}
        response = {'success': False, 'result': []}
        lookup_by = request.query_params.get('lookup')
        codes = request.query_params.get('hotels', '').split(',')
        response_status = status.HTTP_500_INTERNAL_SERVER_ERROR
        try:
            response_fields = ['mmt_id', 'voyagerid', 'hotelcode', 'name_change_allowed', 'id']
            if lookup_by not in {'hotelcode', 'voyagerid', 'mmt_id'}:
                response.update({'message': '%s is not a valid lookup parameter' % lookup_by})
                response_status = status.HTTP_400_BAD_REQUEST
                return Response(response, status=response_status)
            hotels = list(HotelDetail.objects.filter(**{lookup_by+'__in': codes}).values(*response_fields))
            if NAME_CHANGE_MODIFICATION_VERSION > 1:
                for hotel in hotels:
                    amendment_objs = AmendmentPolicy.objects.only('hotel_id', 'is_active').filter(hotel_id=hotel['id'],
                                                                                                  policytype='namechange',
                                                                                                  is_active=True)
                    name_change_allowed = amendment_objs[0].is_active if amendment_objs else False
                    hotel['name_change_allowed'] = name_change_allowed
            for hotel in hotels:
                hotel.pop('id')

            response.update({'success': True, 'result': hotels})
            response_status = status.HTTP_200_OK
        except Exception as e:
            update_error_identifier(error_message=str(e), traceback=repr(traceback.format_exc()),
                                    log_identifier=log_identifier)
            log_msg = 'Failed fetching name change status %s %s' % (lookup_by, codes)
            update_specific_identifier('remark', log_msg, log_identifier)
            api_logger.critical(log_type='ingoibibo', bucket='HotelAPI',
                                stage='hotel.views.name_change_allowed_status',
                                identifier='{}'.format(log_identifier))
            response.update({'message': str(e)})
        return Response(response, status=response_status)

    @detail_route(methods=['get'], url_path='gst', permission_classes=(GuardianPermissionCheck, ))
    def gst(self, request, hotelcode):
        hotel_id = HotelHelper.get_id_from_code(hotelcode, HotelConf.HotelCodeLength,
                                                HotelConf.HotelCodePrefix)
        try:
            gst_details_records = GSTDetail.objects.filter(hotel_id=hotel_id, isactive=True)
            if gst_details_records:
                gst_details = gst_details_records[0]
                gst_details = GSTDetailSerializer(instance=gst_details).data
            else:
                gst_details = {}
        except GSTDetail.DoesNotExist:
            gst_details = {}

        return Response(gst_details, status=status.HTTP_200_OK)

    @staticmethod
    def search_hotel_by_mobile(mobile, format):
        """
        :param mobile:
        :param format:
        :return:
        Also Refer: api/v1/hotels/serializers/hotel_serializers.py:123
        """
        api_logger.info(message='Getting hoteldetail from phone no %s' % mobile,
                        log_type='ingoibibo', bucket='HotelAPI',
                        stage='hotel.views.search_hotel_by_mobile')
        import hashlib
        mobile_hash = hashlib.sha256(mobile.encode()).hexdigest() if mobile else ""
        mobile_91 = '+91%s' % mobile
        mobile_hash_91 = hashlib.sha256(mobile_91.encode()).hexdigest() if mobile_91 else ""

        contact_detail = GenericContactDetail.objects.filter(Q(contact_hash=mobile_hash_91) | Q(contact_hash=mobile_hash))
        if contact_detail.count() == 1:
            hotel = contact_detail[0].content_object
        else:
            # this fallback is temporary. We will rely on the above table.
            hotel = get_hoteldetail_from_mobile(mobile)
        serialized_data = MobiletoHotelSerializer(instance=hotel, context={
            'mobile': mobile}).data
        response = {'success': True, 'data': serialized_data}
        if format == 'proto':
            proto_serialized_str = MobiletoHotelProtoRenderer().render(
                data=response, renderer_context={'proto_model': MobiletoHotelResponse})
            return HttpResponse(proto_serialized_str)
        return Response(response)

    @detail_route(methods=['get'], url_path="hotel-agreement")
    def get_hotel_agreements(self, request, hotelcode):
        """
        :param request: Http request obj
        :param hotelcode: hotel detail obj
        :return: {'success': False/True
                    'data': [],         # List of all active agreement for a hotel. AgreementMaster serialized data
                    'msg': str          # string msg for understand response
                    }

        * *Supports Authentication classes:
            BasicAuthentication
            SessionAuthentication
            TokenAuthentication

        GET Request:
                   * *Sample SendFeedback URL*::
                    {Domain_url}/api/v1/hotels/{hotelcode}/hotel-agreement/

                * *Response Details:* Sample Response::
                if failure:
                    {
                        'success': False,
                        'agreement_list': [],
                        'msg': 'Something went wrong or incorrect input.'
                    }
                if success:
                {
                    "msg": "All agreement for hotel 1000000505",
                    "agreement_list": [
                        {
                            "agreement_name": "vcc agreement",
                            "agreement_url": "a409f14c941b11e8b8500c4de9d4efa6.html",
                            "agreement_id": 1,
                            "mode": "online",
                            "status": "accepted",
                            "agreement_number": 0,
                            "sample_url": "a3769a66941b11e8b8dd0c4de9d4efa6.pdf",
                            "agreement_display_name": "vcc agreement India"
                        }
                    ],
                    "success": true
                }

        """
        response = {
            'success': False,
            'agreement_list': [],
            'msg': 'Something went wrong, please try again.'
        }
        try:
            api_logger.info(message='get_hotel_agreements request hotelcode: %s' % (hotelcode),
                            log_type='ingoibibo', bucket='fcl',
                            stage='api.v1.hotels.views.get_hotel_agreements')
            if request.user and hotelcode not in set(request.user.hotels.values_list('hotelcode', flat=True)):
                response['msg'] = "Permission Denied"
                return Response(response, status=status.HTTP_403_FORBIDDEN)

            hotel_agreement_objs = HotelAgreementMapping.objects.filter(
                hotel__hotelcode=hotelcode, agreement__status=True
            ).exclude(status__in=EXTRANET_FCL.get('EXCLUDE_STATUS'))
            serialized_obj = HotelAgreementMappingSerializer(hotel_agreement_objs, many=True)
            response['success'] = True
            response['agreement_list'] = serialized_obj.data
            response['msg'] = "All agreement for hotel %s" % hotelcode
        except Exception as e:
            api_logger.critical(message="Exception Occured: %s \t\t %s hotelcode %s" % (
            str(e), repr(traceback.format_exc()), hotelcode),
                                log_type='ingoibibo', bucket='fcl',
                                stage='api.v1.hotels.views.get_hotel_agreements')
        return Response(response, status=status.HTTP_200_OK)

    @detail_route(methods=['post'], url_path="accept-agreement", permission_classes=(GuardianPermissionCheck,),
                  parser_classes=(FormParser, JSONParser,))
    def accept_agreement(self, request, hotelcode):
        """
        :param request: Http request object
        :return: {'success': False/True}

        URL: {root_url}/api/v1/hotel/1000000505/accept-agreement/

        Body: {
                "name": "xyz",
                "designation": "abc",
                "mobile_number": 100,
            }

        ** function inside:
            * capture user input and make key value pair.
            * process FCL terms and condition.
        """
        response = {'success': False}
        try:
            from common.commonhelper import is_user_assigned_to_hotel
            if not is_user_assigned_to_hotel(hotelcode, request.user.id):
                return Response(response, status=status.HTTP_403_FORBIDDEN)
            
            response['success'] = True
            new_tnc_info = {}
            new_tnc_info['name'] = request.data.get('name', None)
            new_tnc_info['designation'] = request.data.get('designation', None)
            new_tnc_info['mobile_number'] = request.data.get('mobile_number', None)
            new_tnc_info['remote_host_ip'] = services.get_client_ip(request)
            new_tnc_info['logged_in_user_id'] = request.user.id
            new_tnc_info['hotelcode'] = hotelcode
            new_tnc_info['email'] = request.data.get('email', None)
            new_tnc_info["service"] = request.data.get('service', {})
            new_tnc_info.update(request.data.get('service', {}))

            api_logger.info("hotel_viewset | api | accept_fcl_agreement: %s" % new_tnc_info,
                            log_type="ingoibibo", bucket="fcl", stage="accept_fcl_agreement")

            agreement_resources.process_fcl_terms_and_conditions(new_tnc_info)
            response['success'] = True
        except Exception as e:
            api_logger.critical(
                message='hotel_viewset | api | accept_fcl_agreement error: %s: %s' % (e, repr(traceback.format_exc())),
                log_type='ingoibibo', bucket='fcl',
                stage='accept_fcl_agreement')

        return JsonResponse(response)

    @detail_route(methods=['get'], url_path="decide-agreement-popup", permission_classes=(GuardianPermissionCheck,))
    def get_fcl_agreement_data_decide_fcl_popup(self, request, hotelcode):
        """
        :param request: HTTP request obj
        :param hotelcode: hotelcode from hotel object
        :return: {'showpopup': False/True,
                    'msg': '',
                    agreement_list: []      # list of all agreement for a hotel, exclude EXPIRED status
                    }
        * *Supports Authentication classes:
            BasicAuthentication
            SessionAuthentication
            TokenAuthentication

        GET Request:
                   * *Sample SendFeedback URL*::
                    {{Domain_url}}/api/v1/hotel/1000000505/decide-agreement-popup/

                * *Response Details:* Sample Response::

                if success:
                    {
                        "msg": "all agreement for hotel 1000000505",
                        "agreement_list": [{
                            "agreement_name": "vcc agreement",
                            "agreement_url": "646aecf38f2811e8a5530c4de9d4efa6.html",
                            "agreement_id": 1,
                            "mode": "Online",
                            "status": "Pending",
                            "agreement_number": "GOMMT10000001",
                            "sample_url": "64fc5bb08f2811e89db10c4de9d4efa6.pdf"
                        }],
                        "showpopup": true,
                        "insurmountable": False
                    }
                if failure:
                    {
                        "msg": "Something went wrong",
                        "agreement_list": [],
                        "showpopup": false
                        "insurmountable": False
                    }
        """
        response = {'showpopup': False, 'msg': 'Something went wrong', 'agreement_list': [], 'insurmountable': False}
        try:
            hotelcode = getattr(request.hotel, 'hotelcode', '')
            if not hotelcode:
                return JsonResponse(response)

            is_staff = getattr(request.user, 'is_staff', False)
            api_logger.info("EXTRANET | Dashboard | get_fcl_agreement_data_decide_fcl_popup: %s, "
                            "is_staff: %s" % (hotelcode, is_staff), log_type="ingoibibo", bucket="fcl",
                            stage="get_fcl_agreement_data_decide_fcl_popup")

            insurmountable = False
            if is_staff:
                response['msg'] = "Staff user"
                return HttpResponse(json.dumps(response))
            hotelagreement_objs = HotelAgreementMapping.objects.using('default').filter(hotel__hotelcode=hotelcode,
                                                                       status__in=EXTRANET_FCL.get('INCLUDE_STATUS'),
                                                                       mode__in=EXTRANET_FCL.get('INCLUDE_MODE'),
                                                                       agreement__status=True,
                                                                       ).order_by('createdon',
                                                                                  'agreement__agreement_type')
            response['showpopup'] = True if hotelagreement_objs else False
            hotelagreement_objs = hotelagreement_objs.exclude(status__in=EXTRANET_FCL.get('EXCLUDE_STATUS'))
            # We need to exclude the hotelcloud agreement from the list, because the popup is not required for it.
            # hotelcloud agreement is handled in API /api/v3/hotel/>/hotel-agreement/
            hotelagreement_objs = hotelagreement_objs.exclude(agreement_id__in=RESELLER_AGREEMENT_MAPPING[LegalEntityTypeConstants.HOTELTRAVEL])
            # For the time being if any tnc is insurmount then we are making it mandatory to accept
            for hotel_agreement in hotelagreement_objs:
                if hotel_agreement.is_insurmount_tnc:
                    insurmountable = True
            serializer_obj = HotelAgreementMappingSerializer(hotelagreement_objs, many=True)
            # meta_obj = HotelMetaData.objects.filter(hotel__hotelcode=hotelcode).last()
            # if meta_obj and meta_obj.is_insurmount_tnc:
            #     insurmountable = True
            response['insurmountable'] = insurmountable
            response['agreement_list'] = serializer_obj.data
            response['msg'] = 'all agreement for hotel %s' % hotelcode
        except Exception as e:
            api_logger.critical(message='Failed get_fcl_agreement_data_decide_fcl_popup due to %s, traceback %s' %
                                        (e, repr(traceback.format_exc())), log_type='ingoibibo', bucket='fcl',
                                stage='get_fcl_agreement_data_decide_fcl_popup')
        return HttpResponse(json.dumps(response))

    @detail_route(methods=["get"], url_path="tax-info", permission_classes=(GuardianPermissionCheck,))
    def tax_info(self, request, *args, **kwargs):
        """
        API to fetch tax info for a hotel.
        URL : {{BASE_URL}}/api/v1/hotel/{{hotelcode}}/tax-info
        Type : GET
        Request params : corporate_gstn (optional)
        Response :
        {
            "SGST": 9,
            "CGST": 9
        }
        OR
        {
            "IGST": 18
        }
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        response = {}
        try:
            serializer = HotelTaxSerializer(data=request.query_params)
            serializer.is_valid(raise_exception=True)
            serialized_data = serializer.data

            hotelcode = kwargs.get("hotelcode")
            corporate_gstn = serialized_data.get("corporate_gstn")

            response = get_tax_rate_for_hotel(hotelcode, corporate_gstn)
            status_code = status.HTTP_200_OK

            api_logger.info("Fetched tax rate {tr} for hotelcode {h} corporate_gstn {cg}"
                            .format(tr=response, h=hotelcode, cg=corporate_gstn),
                            log_type="ingoibibo", bucket="api.v1.hotel", stage="tax_info")

        except ResourceNotFoundException as e:
            status_code = status.HTTP_404_NOT_FOUND
            api_logger.critical("Hotelcode does not exist for tax info API.",
                                log_type="ingoibibo", bucket="api.v1.hotel", stage="tax_info")

        except ValidationError as e:
            status_code = status.HTTP_400_BAD_REQUEST
            api_logger.critical("Client side error in get tax info API exception {e} traceback {t}"
                                .format(e=e, t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v1.hotel", stage="tax_info")

        except Exception as e:
            status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical("Server side error in get tax info API exception {e} traceback {t}"
                                .format(e=e, t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v1.hotel", stage="tax_info")

        return Response(response, status=status_code)

    @detail_route(methods=['post'], url_path="send-invitations", permission_classes=(IsAuthenticated, PostInvitationPermission))
    def send_invitation(self, request, hotelcode):
        """
        :param request: Http request object
        :return: {'success': False/True}

        URL: {base_url}/api/v1/hotel/1000000505/send-invitations/

        Body:
            {
                "existing_invitees": ["1498", "99333"],
                "new_invitees": [
                    {
                        "fullname": "Archit Gupta",
                        "email": "<EMAIL>",
                        "rights": {
                            "blocked":[
                                {
                                    "tab_id": 1,
                                    "permission_type": 1
                                },
                                {
                                    "tab_id": 2,
                                    "permission_type": 0
                                }
                            ]
                        }
                    },
                    {
                        "fullname": "Archit Gupta",
                        "email": "<EMAIL>",
                        "rights":{
                            "blocked":[
                                {
                                    "tab_id":1,
                                    "permission_type":0
                                },
                                {
                                    "tab_id":2,
                                    "permission_type":1
                                }
                            ]
                        }
                    }
                ]
            }
        """
        response = {
            'success': False,
            'message': '',
            'data': {
            }
        }
        try:
            invitation_post_serializer = InvitationPostSerializer(data=request.data, context={
                'current_user': request.user, 'hotelcode': hotelcode, 'source': request.source_of_management}
            )
            invitation_post_serializer.is_valid(raise_exception=True)

            invitation_sent = invitation_post_serializer.save()
            api_logger.info(
                "hotel_viewset | %s invitation_sent" % len(invitation_sent),
                log_type="ingoibibo", bucket="invitation", stage="send invitation"
            )

            if len(invitation_sent) == 1:
                response["message"] = "Invitation sent to %s" % invitation_sent[0].full_name
            elif len(invitation_sent) > 1:
                response["message"] = "%s Invitations Sent" % len(invitation_sent)
            response['success'] = True
            response_status = status.HTTP_200_OK

        except ValidationError as e:
            response["message"] = "Failed to send invitation"
            response['data'] = e.msg
            response_status = status.HTTP_400_BAD_REQUEST

        except Exception as e:
            response["message"] = "Something went wrong. Please try again later."
            response_status = status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical(
                message='hotel_viewset | api | send_invitation error: %s: %s' % (e, repr(traceback.format_exc())),
                log_type='ingoibibo', bucket='invitation', stage='send_invitation'
            )

        return Response(response, status=response_status)


@api_view(["POST"])
@authentication_classes([TokenAuthentication])
@permission_classes([AllowAny])
def update_hotel_voyager_locality(request):
    response = {'success': False}
    request_dict = request.data
    try:
        paramDict = {
            'hotelcode': request_dict['hotelcode'],
            'old_locality_voy_id': request_dict.get('old_locality_id'),
            'locality_voy_id': request_dict['locality_id'],
            'locality_name': request_dict.get('locality_name'),
            'locality_lat_lng': request_dict.get('locality_lat_lng'),
            'locality_source': request_dict.get('locality_source'),
        }
        success, msg = update_hotel_locality(paramDict, request.user)
        msg, error = (msg, '') if success else ('', msg)
        response = {
            'success': success,
            'error': error,
            'msg': msg
        }
    except Exception as e:
        api_logger.critical(message="Exception Occured: %s \t\t %s Request data %s" % (str(e), repr(traceback.format_exc()), request_dict),
                            log_type='ingoibibo', bucket='HotelViewsetAPI',
                            stage='api.v1.hotels.views.HotelViewset.create')
        response = {
            'success': False,
            'error': str(e)
        }
    return Response(response, status=status.HTTP_200_OK)

@api_view(["POST"])
@authentication_classes([TokenAuthentication, IsAuthenticated])
@permission_classes([AllowAny])
def update_tan_number(request):
    """
    :param request: HTTP request variable
    :return: {'success': False/True, 'msg': 'msg'}
    """
    response = {'showpopup': True, 'msg': 'Fail to update TAN number for vendor'}
    request_data = request.POST
    hotel_code = request_data.get('hotelcode')
    tan_status = request_data.get('tan_status') == 'true'   # convert unicode true/false to python bool value
    tan_number = request_data.get('tan_number')
    audit_request_data = fetch_client_details(request)
    permission_check = hotel_methods.checkHotelAccessibility(user=request.user, hotelcode=hotel_code)
    api_logger.info(message='update_tan_number: hotelcode %s, request_data=%s, '
                            'permission_check: %s' % (hotel_code, str(request_data), permission_check),
                    log_type='ingoibibo', bucket='update_tan_number',
                    stage='api.v1.hotels.views.update_tan_number')
    if not permission_check.get('success', None):
        response['msg'] = 'Permission Denied'
        response['showpopup'] = False
    try:
        if hotel_code and (tan_status or tan_number) and permission_check.get('success', None):
            vendor_codes = VendorMapping.objects.filter(hotel__hotelcode=hotel_code, isactive=True).values_list('vendor__vendor_code', flat=True)
            api_logger.info(message='update_tan_number: hotelcode %s, request_data=%s, vendor_codes=%s' % (
                            hotel_code, str(request_data), vendor_codes),
                            log_type='ingoibibo', bucket='update_tan_number',
                            stage='api.v1.hotels.views.update_tan_number')
            if vendor_codes and tan_status:
                vendor_objs = VendorDetail.objects.filter(vendor_code__in=vendor_codes)
                for vendor_obj in vendor_objs:
                    vendor_obj.tan_status = False
                    vendor_obj.tan_number = ''
                    vendor_obj.save(request_data=audit_request_data)
                response['showpopup'] = False
                response['msg'] = 'Successfully update TAN Number for hotel: %s' % hotel_code
            if vendor_codes and tan_number:
                vendor_objs = VendorDetail.objects.filter(vendor_code__in=vendor_codes)
                for vendor_obj in vendor_objs:
                    vendor_obj.tan_status = True
                    vendor_obj.tan_number = tan_number
                    vendor_obj.save(request_data=audit_request_data)
                response['showpopup'] = False
                response['msg'] = 'Successfully update TAN Number for hotel: %s' % hotel_code
    except Exception, e:
        api_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % ('extranet', 'views', 'update_tan_number', '',
                                                      str(e), repr(traceback.format_exc())))
    return JsonResponse(response)

@custom_required(token_required, login_required)
def decide_show_tan_popup(request):
    """
    :param request: HTTP request object
    :return: {'success': True/False,
            'msg': 'msg'}
    this function return true or false base upon, either vendor provide tan for both platform or he
    accepted that he don't have TAN.

    api url: https://newprodppin.goibibo.com/api/v1/hotels/decide_tan_popup/?hotelcode=1000142809

    """
    response = {"showpopup": True, "msg": "Vendor do not have TAN Number"}
    request_data = request.GET
    hotel_code = request_data.get("hotelcode")
    is_staff = request.user.is_staff
    api_logger.info(message='decide_show_tan_popup: hotelcode %s, request_data=%s, is_staff=%s user=%s' % (hotel_code, str(request_data), is_staff, request.user),
                    log_type='ingoibibo', bucket='decide_show_tan_popup',
                    stage='api.v1.hotels.views.decide_show_tan_popup')
    try:
        from common.commonhelper import is_user_assigned_to_hotel
        if not is_user_assigned_to_hotel(hotel_code, request.user.id):
            response["showpopup"] = False
            response["msg"] = "Permission Denied"
            return JsonResponse(response)
        hotel_obj = HotelDetail.objects.filter(hotelcode=hotel_code).last()
        if hotel_obj and hotel_obj.country.lower() == 'india' and not is_staff:
            vendor_mappings = VendorMapping.objects.filter(hotel__hotelcode=hotel_code,
                                                              isactive=True,
                                                              vendor__vendor__code__in=TAN_VENDORS)
            if not vendor_mappings:
                response["showpopup"] = False
                response["msg"] = "go-mmt have zero active vendor for hotel %s" % hotel_code

            for vendor_mapping in vendor_mappings:
                vendor = vendor_mapping.vendor
                if (vendor.tan_status and vendor.tan_number) \
                        or (not vendor.tan_status and not vendor.tan_number == None):
                    response["showpopup"] = False
                    response["msg"] = "go-mmt have tan for MMT/GI vendor or hotel accept that he do not have tan"
                if vendor.tan_status and not vendor.tan_number:
                    response["showpopup"] = True
                    response['msg'] = "Vendor do not have TAN Number for vendor %s" % vendor.vendor.code
                    break

        elif (hotel_obj and hotel_obj.country.lower() != 'india') or is_staff:
            response["showpopup"] = False
            response["msg"] = "International hotel do not need TAN number or user is staff"

    except Exception, e:
        response["showpopup"] = False
        response["msg"] = "Exception happened"
        api_logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % ('extranet', 'views', 'decide_show_tan_popup', '',
                                                  str(e), repr(traceback.format_exc())))
    return JsonResponse(response)


class MasterAgreementViewSet(viewsets.ModelViewSet):
    """
        A simple ViewSet for listing or retrieving users.
    """
    queryset = AgreementMaster.objects.filter(status=True)
    authentication_classes = (TokenAuthentication, SessionAuthentication, BasicAuthentication,)
    lookup_field = 'id'
    serializer_class = AgreementMasterSerializer
    pagination_class = StandardResultsSetPagination
    http_method_names = ['get', 'post']

    def list(self, request, *args, **kwargs):
        return super(MasterAgreementViewSet, self).list(self, request)

    def retrieve(self, request, *args, **kwargs):
        return super(MasterAgreementViewSet, self).retrieve(request, *args, **kwargs)


class WhatsAppSearchViewSet(viewsets.GenericViewSet):
    """
    WhatsApp number search viewset.
    """
    authentication_classes = (TokenAuthentication,)
    http_method_names = ["get"]
    allowed_methods = ["get"]
    lookup_field = "mobile_number"

    @detail_route(methods=['get'], url_path="hotels")
    def get_all_hotels(self, request, mobile_number):
        """
        Get all hotelcode where a WhatsApp mobile number mapped.
        :param request: HTTP request object
        :param mobile_number: mobile number
        :return: HTTP response.

        URL: {base_url}/api/v1/generic-contact/{mobile_number}/hotels/

        Authorization: TokenAuthentication

        Sample response:

        {
            "status": true,
            "hotel": [{
               "display_name": "Test umang 001 property",
               "hotelcode": 1000204285,
               "city": "Ghaziabad"
            }]
        }

        """

        response = {
            "status": False,
            "hotel": []
        }
        http_status = status.HTTP_400_BAD_REQUEST
        try:
            hotel_ids = GenericContactDetail.objects.filter(contact_wcc=mobile_number).filter(
                                                            contact_type=3, verified=True, active=True,
                                                            content_type=HOTEL_DETAIL_CONTENT_TYPE).values_list(
                'object_id', flat=True)
            if hotel_ids:
                hotel_objs = HotelDetail.objects.filter(id__in=hotel_ids)
                for hotel_obj in hotel_objs:
                    response["hotel"].append({"hotelcode": int(hotel_obj.hotelcode),
                                              "display_name": str(hotel_obj.displayname),
                                              "city": str(hotel_obj.city_id.cityname)})
                http_status = status.HTTP_200_OK
                response["status"] = True
        except Exception as e:
            api_logger.critical(message="WhatsAppSearchViewSet: get_all_hotels: {mobile_number}, "
                                        "error={error}".format(mobile_number=mobile_number,
                                                               error=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="WhatsAppSearchViewSet",
                                stage="WhatsAppSearchViewSet.get_all_hotels")
            http_status = status.HTTP_500_INTERNAL_SERVER_ERROR
        return Response(response, status=http_status)


class SnoozeViewset(viewsets.ModelViewSet):
    """
    This viewset controls all the Snooze
    related APIs
    ---

    """
    authentication_classes = (TokenAuthentication,  SessionAuthentication, BasicAuthentication, )
    permission_classes = ()
    http_method_names = ['get', 'put', 'post']

    def list(self, request, *args, **kwargs):
        log_identifier = get_log_identifier(api_name="GET api/v1/snooze")
        params = request.query_params
        search_by = params.get('search_by', 'hotelcode')
        response = {'status': False, 'message': '', 'snooze_info': {}}
        objectcode = ''
        try:
            objectcode = params.get('object_code')
            update_specific_identifier("object_code",objectcode,log_identifier)

            if not objectcode:
                response['message'] = 'objectcode is missing or invalid'
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

            message, snooze_set = fetch_active_snooze(params, search_by, objectcode)
            response['snooze_info'] = snooze_set
            response['message'] = message
            if not message:
                response['status'] = True
            return Response(response, status=status.HTTP_200_OK)

        except Exception as e:
            update_error_identifier(error_message=str(e),traceback=repr(traceback.format_exc()),log_identifier=log_identifier)
            api_logger.critical(log_type='ingoibibo', bucket='SnoozeAPI',identifier='{}'.format(log_identifier),
                                stage='hotels.views.snooze_listing')
            remove_error_dict(log_identifier)
            return Response(response, status=status.HTTP_200_OK)

    def create(self, request, *args, **kwargs):
        response = {'message': '', 'success': False}
        try:
            request_data = request.data
            log_identifier = get_log_identifier(api_name="POST api/v1/snooze",data_dict=request_data,data_dict_key_identifiers=["hotelcode"])
            user = request.user
            
            from common.commonhelper import is_user_assigned_to_hotel
            if not is_user_assigned_to_hotel(request_data.get('hotelcode', ''), user.id):
                return Response(response, status=status.HTTP_403_FORBIDDEN)
            
            validation_response = validate_snooze_data(request_data)
            if not validation_response['success']:
                api_logger.info(
                    message='Snooze Creation Validation failed for request %s and validation response : %s' %
                            (request_data, validation_response), log_type='ingoibibo',
                    bucket='SnoozeViewset',identifier='{}'.format(log_identifier),
                    stage='SnoozeViewset.create')
                return Response(validation_response, status=status.HTTP_400_BAD_REQUEST)

            if request_data.get('type', SNOOZE_TYPE_VALUES['sell_book_block']) \
                    in (SELL_BLOCK, BOOK_BLOCK, SELL_BOOK_BLOCK):
                resp, sobj = block_booking(request_data, user)

            if not resp['success']:
                response['message'] = resp['message']
                api_logger.info(
                    message='Snooze Creation failed for request %s due to : %s' %
                            (request_data, resp['message']), log_type='ingoibibo',
                    bucket='SnoozeViewset',identifier='{}'.format(log_identifier),
                    stage='SnoozeViewset.create')
                return Response(validation_response, status=status.HTTP_400_BAD_REQUEST)

            snooze_detail = {
                'snooze_code': sobj.snooze_code,
                'hotelcode': sobj.hotelcode,
                'start_date': sobj.start_date,
                'action': ([k for k,v in SNOOZE_ACTION_VALUES_DB_MAP.items() if v == sobj.action][0])
            }
            if sobj.end_date:
                snooze_detail.update({'end_date': sobj.end_date})
            response.update(snooze_detail)
            response['success'] = True
            api_logger.info(message='Snooze created successfully--> %s' % (str(request_data)),
                            log_type='ingoibibo', bucket='SnoozeViewset',identifier='{}'.format(log_identifier),
                            stage='SnoozeViewset.create')

        except Exception as e:
            update_error_identifier(error_message=str(e),traceback=repr(traceback.format_exc()),log_identifier=log_identifier)
            response['message'] = str(e)
            api_logger.critical(log_type='ingoibibo', bucket='SnoozeViewset',identifier='{}'.format(log_identifier),
                                stage='SnoozeViewset.create')
            remove_error_dict(log_identifier)
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        if settings.DEBUG:
            snooze_event_mail(sobj)
        else:
            snooze_event_mail.apply_async(args=(sobj,))

        return Response(response, status=status.HTTP_200_OK)

    @list_route(methods=['put'], url_path='delete_snooze')
    def delete_snooze(self, request, *args, **kwargs):
        response = {'success': False, 'message': ''}
        snooze_code = request.data.get('snooze_code', '')
        log_identifier = get_log_identifier(request_id=snooze_code,api_name="api/v1/delete_snooze")
        user = request.user
        try:
            if not snooze_code:
                response['message'] = "Snooze code is mandatory"
                return Response(response, status=status.HTTP_400_BAD_REQUEST)
            sobj = Snooze.objects.get(snooze_code=snooze_code)
            from common.commonhelper import is_user_assigned_to_hotel
            if not is_user_assigned_to_hotel(sobj.hotelcode, request.user.id):
                return Response(response, status=status.HTTP_403_FORBIDDEN)
            if sobj.type in (SNOOZE_TYPE_VALUES_DB_MAP['sell_block'],
                             SNOOZE_TYPE_VALUES_DB_MAP['book_block'], SNOOZE_TYPE_VALUES_DB_MAP['sell_book_block']):
                resp = unblock_booking(sobj, user)
                if not resp['success']:
                    response['message'] = resp['message']
                    return Response(response, status=status.HTTP_400_BAD_REQUEST)
            response['success'] = True
            response['snooze_code'] = snooze_code
        except Exception as e:
            update_error_identifier(error_message=str(e),traceback=repr(traceback.format_exc()),log_identifier=log_identifier)
            response['message'] = str(e)
            api_logger.critical(log_type='ingoibibo', bucket='SnoozeViewset',identifier='{}'.format(log_identifier),
                                stage='SnoozeViewset.delete_snooze')
            remove_error_dict(log_identifier)
            return Response(response, status=status.HTTP_400_BAD_REQUEST)
        snooze_end_mail(snooze_code)
        return Response(response, status=status.HTTP_200_OK)
