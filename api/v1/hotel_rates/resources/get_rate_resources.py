from django.conf import settings

from hotels.hotelinventory import get_list_of_dates_from_range_list
from hotels.models import RoomDetail, RatePlan
from hotels.configuration import CONTRACTYPE_RATE_MAPPING_DICT, CONTRACTYPE_RATE_REVERSE_MAPPING_DICT
from utils.logger import Logger

api_logger = Logger(logger="inventoryAPILogger")

RATE_PLAN_FIELDS = ['rateplanname', 'mealplan', 'editable_only_from_admin', 'citycode', 'roomtype__hotel', 'parent_id',
                    'pay_at_hotel', 'nonrefundable', 'contracttype', 'roomtype__roomtypecode', 'roomtype__roomtypename',
                    'roomtype__base_adult_occupancy']


def bulk_get_hotel_rates_and_restrictions(rates_data, hotel_object, user_object, random_number, device_type):
    response = {'message': '', 'response_data': [], 'success': False, 'errors': [], 'error_code': ''}
    try:
        info_dict = {
            'user': user_object,
            'hotel_object': hotel_object,
            'device_type': device_type,
            'random_number': random_number
        }
        partial_success = False
        get_response_dict_list = []
        for get_request_dict in rates_data['data']:
            level = get_request_dict.get('level')
            contract_type_int_list = get_converted_contract_type_list(get_request_dict.get('contract_type_list', ['b2c']))
            date_range_list = get_request_dict.get('date_range_list')
            code_list = get_request_dict.get('code_list')
            date_list = get_list_of_dates_from_range_list(date_range_list)
            get_response_dict = get_hotel_rates_and_restrictions(code_list, level, contract_type_int_list, date_list,
                                                                 info_dict)
            if not get_response_dict['success']:
                partial_success = True
            get_response_dict_list.append(get_response_dict)
        response['message'] = ''
        response['response_data'] = get_response_dict_list
        response['success'] = False if partial_success else True
    except Exception, e:
        api_logger.critical(
            message="rates and restrictions update failed for rates_data %s exception %s" % (rates_data, str(e)),
            log_type="ingoibibo", bucket="update_rates_and_restrictions", stage="api.v1.rates.resources.")
        response['message'] = e.message
    return response


def get_hotel_rates_and_restrictions(code_list, level, contract_type_int_list, date_list, info_dict):
    hotel_object = info_dict['hotel_object']
    rate_plan_objects = []
    if level == 'rate_plan':
        rate_plan_objects = list(RatePlan.objects.select_related('roomtype').only(*RATE_PLAN_FIELDS).filter(
            rateplancode__in=code_list, isactive=1))
    if level == 'room':
        room_ids = RoomDetail.objects.select_related('hotel').filter(roomtypecode__in=code_list, isactive=1).values_list('id', flat=True)
        rate_plan_objects = list(RatePlan.objects.select_related('roomtype').only(*RATE_PLAN_FIELDS).filter(
            roomtype__in=room_ids, isactive=1))
    if level == 'hotel':
        room_ids = RoomDetail.objects.filter(hotel=hotel_object.id, isactive=1).values_list('id', flat=True)
        rate_plan_objects = list(RatePlan.objects.select_related('roomtype').only(*RATE_PLAN_FIELDS).
                                 filter(roomtype__in=room_ids, isactive=1))
    response_dict = get_rates_and_restrictions(rate_plan_objects, contract_type_int_list, date_list)
    return response_dict


def get_rates_and_restrictions(rate_plan_objects, contract_type_int_list, date_list):
    response = {'success': False, 'data': [], 'message': '', 'error_code': '', 'errors': []}
    try:
        rate_plan_ids = []
        rate_plan_dict = dict()
        for rate_plan_object in rate_plan_objects:
            rate_plan_ids.append(rate_plan_object.id)
            rate_plan_dict[rate_plan_object.id] = rate_plan_object

        rate_objects = []
        rateplan_contracttype_ratelist = dict()
        for rate_object in rate_objects:
            if rate_object.rateplanid not in rateplan_contracttype_ratelist:
                rateplan_contracttype_ratelist[rate_object.rateplanid] = dict()
            if rate_object.contracttype not in rateplan_contracttype_ratelist[rate_object.rateplanid]:
                rateplan_contracttype_ratelist[rate_object.rateplanid][rate_object.contracttype] = []
            rateplan_contracttype_ratelist[rate_object.rateplanid][rate_object.contracttype].append(
                get_formatted_rates_and_restrictions_from_rate_object(rate_object, rate_plan_dict[rate_object.rateplanid].roomtype.base_adult_occupancy))
        for rate_plan in rate_plan_objects:
            rate_plan_valid_contract_types = rate_plan.get_valid_contract_types()
            for contract_type_int in contract_type_int_list:
                if CONTRACTYPE_RATE_REVERSE_MAPPING_DICT[contract_type_int] not in rate_plan_valid_contract_types:
                    continue
                rate_dict = dict()
                rate_dict['product_code'] = rate_plan.rateplancode
                rate_dict['room_type_code'] = rate_plan.roomtype.roomtypecode
                rate_dict['room_type_name'] = rate_plan.roomtype.roomtypename
                rate_dict['product_name'] = rate_plan.rateplanname
                rate_dict['parent_id'] = rate_plan.parent_id
                rate_dict['non_refundable'] = rate_plan.nonrefundable
                rate_dict['pay_at_hotel'] = rate_plan.pay_at_hotel
                rate_dict['contracttype'] = CONTRACTYPE_RATE_REVERSE_MAPPING_DICT[contract_type_int]
                rate_dict['valid_contract_types'] = rate_plan_valid_contract_types
                rate_dict['product_data'] = []
                if rate_plan.id in rateplan_contracttype_ratelist and contract_type_int in rateplan_contracttype_ratelist[rate_plan.id]:
                    rate_dict['product_data'] = rateplan_contracttype_ratelist[rate_plan.id][contract_type_int]
                response['data'].append(rate_dict)
        response['success'] = True
    except Exception, e:
        api_logger.critical(
            message="rates and restrictions get failed for exception %s" % str(e),
            log_type="ingoibibo", bucket="get_rates_and_restrictions", stage="api.v1.rates.resources.")
        response['message'] = e.message
    return response


def get_formatted_rates_and_restrictions_from_rate_object(rate_object, base_adult_occupancy):
    rate_data = dict()
    rate_data['idate'] = rate_object.idate.strftime('%Y-%m-%d')
    rate_data['rates'] = get_formatted_rates_from_rate_data(rate_object.ratedata, base_adult_occupancy)
    rate_data['restrictions'] = get_formatted_restrictions_from_rate_data(rate_object)
    return rate_data


def get_formatted_rates_from_rate_data(rate_data, base_adult_occupancy):
    rates = dict()
    rate_strings = rate_data.split("#")

    sell_price = [-1.0] * base_adult_occupancy
    if base_adult_occupancy > 0:
        sell_price[0] = float(rate_strings[0])
    if base_adult_occupancy > 1:
        sell_price[1] = float(rate_strings[1])
    if base_adult_occupancy > 2:
        sell_price[base_adult_occupancy - 1] = float(rate_strings[2])
    rates['sell_price'] = sell_price

    extra_guest_price = [-1.0, -1.0, -1.0, -1.0]
    extra_guest_price[0] = float(rate_strings[9])
    extra_guest_price[1] = float(rate_strings[10])
    extra_guest_price[2] = float(rate_strings[11])
    rates['extra_guest_price'] = extra_guest_price

    prebuy_nett_price = [-1.0] * base_adult_occupancy
    if base_adult_occupancy > 0:
        prebuy_nett_price[0] = float(rate_strings[15])
    if base_adult_occupancy > 1:
        prebuy_nett_price[1] = float(rate_strings[16])
    if base_adult_occupancy > 2:
        prebuy_nett_price[2] = float(rate_strings[17])
    rates['prebuy_nett_price'] = prebuy_nett_price
    return rates


def get_formatted_restrictions_from_rate_data(rate_object):
    restrictions = dict()
    restrictions['block'] = rate_object.blocked
    restrictions['prebuy_nett_block'] = rate_object.prebuy_blocked
    return restrictions


def get_formatted_get_rates_response(rates_response, priority_config_room_codes):
    remove_grp_element = []
    rates_response_count = len(rates_response['response_data'])
    error_count = 0
    for get_rates_dict in rates_response['response_data']:
        if get_rates_dict.get('errors') and len(get_rates_dict['errors']) > 0:
            error_count += 1
        for rates_and_restrictions in get_rates_dict['data']:
            if rates_and_restrictions["contract_type"] == "grp" and (
                    len(rates_and_restrictions["valid_contract_types"]) > 1 or priority_config_room_codes.get(
                        rates_and_restrictions['room_type_code'], False)):
                remove_grp_element.append(rates_and_restrictions)
            for date_rates_and_restrictions in rates_and_restrictions['product_data']:
                for rate_key in ['prebuy_nett_price_base', 'prebuy_sell_price_base', 'day_of_arrival_price_base', 'static_rate', 'copy_rate']:
                    if rate_key in date_rates_and_restrictions.get('rates', {}):
                        date_rates_and_restrictions['rates'][rate_key] = date_rates_and_restrictions['rates'][rate_key].pop('value', None)
                for key, value in date_rates_and_restrictions.get('restrictions', {}).iteritems():
                    date_rates_and_restrictions['restrictions'][key] = value.pop('value', None)
        for element in remove_grp_element:
            get_rates_dict['data'].remove(element)
    if error_count == rates_response_count:
        rates_response['success'] = False
        rates_response['message'] = "Received error from Phoenix service"
    return rates_response


def get_converted_contract_type_list(contract_type_list):
    contract_type_int_list = []
    for contract_type in contract_type_list:
        contract_type_int_list.append(CONTRACTYPE_RATE_MAPPING_DICT[contract_type])
    return contract_type_int_list

