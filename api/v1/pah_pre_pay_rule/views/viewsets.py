__author__ = 'prerna_arya'


import django_filters
from django.db.models import Q

from django.contrib.contenttypes.models import ContentType
from guardian.models import UserObjectPermission
from rest_framework.authentication import SessionAuthentication, BasicAuthentication, TokenAuthentication

from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet

from rest_framework import filters
from rest_framework import status
from api.v1.pah_pre_pay_rule.permissions.permissions import IsHotelAccessibility, GuardianPermissionCheck
from api.v1.pah_pre_pay_rule.serializers.serializers import PrePaySerializer, PrePayDisplaySerializer

from api.v1.pah_pre_pay_rule.resources.resources import break_existing_rules

from hotels.methods import HotelMethods
from hotels.models import HotelDetail, PayAtHotelPrePayRule

from utils.logger import Logger
from api.v1.pagination import StandardResultsSetPagination

import datetime

api_logger = Logger(logger='inventoryAPILogger')
logger = Logger(logger="inventoryLogger")
hotel_methods = HotelMethods()
hotel_content_type = ContentType.objects.get_for_model(HotelDetail)

class CustomFilter(django_filters.FilterSet):
    class Meta:
        model = PayAtHotelPrePayRule
        fields = ['id', 'rule_payment_value', 'rule_payment_basis' ,
                  'days_before_checkin', 'start_date', 'end_date', 'isactive',
                  'hotel', 'text']


class PrePayRuleViewSet(ModelViewSet):
    authentication_classes = (TokenAuthentication, SessionAuthentication, BasicAuthentication)
    filter_backends = (filters.DjangoFilterBackend,)
    filter_class = CustomFilter
    http_method_names = ['get', 'put', 'post', 'delete']
    pagination_class = StandardResultsSetPagination
    serializer_class = PrePaySerializer
    permission_classes = [IsAuthenticated, IsHotelAccessibility]
    lookup_field = 'id'

    def get_permissions(self):
        if self.request.method == 'PUT':
            self.permission_classes = [IsAuthenticated, IsHotelAccessibility, GuardianPermissionCheck]
        else:
            if not self.permission_classes:
                self.permission_classes = [IsAuthenticated, IsHotelAccessibility]

        return super(PrePayRuleViewSet, self).get_permissions()

    def get_queryset(self):
        hotel_ids = []
        today = datetime.date.today()
        past_10_days = today - datetime.timedelta(days=10)
        try:
            if self.request.GET.get('hotelcode', ''):
                hotel_ids.append(self.request.hotel.id)
            else:
                hotel_ids = UserObjectPermission.objects.filter(
                            user_id=self.request.user.pk,
                            content_type=hotel_content_type.id,
                            permission__codename="view_hoteldetail").distinct().values_list(
                            "object_pk", flat=True)

            queryset = PayAtHotelPrePayRule.objects.filter((Q(start_date__isnull=True) | Q(end_date__gt=past_10_days)),
                                                           hotel_id__in=hotel_ids, isactive=1)

        except Exception:
            queryset = PayAtHotelPrePayRule.objects.none()

        return queryset

    def list(self, request, *args, **kwargs):
        response = {'message': 'Error occurred', 'default_rule_flag' : False}
        try:

            query_set = self.get_queryset()
            page = self.paginate_queryset(PrePayDisplaySerializer(self.filter_queryset(query_set), many=True).data)
            return self.get_paginated_response(page)
        except Exception, e:
            api_logger.critical(message='Permission denied: %s' % (str(e)), log_type='ingoibibo',
                                    bucket='PrePayRuleAPI',
                                    stage='pah_pre_pay_rule.views.PrePayRuleViewSet')
            return Response(response, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        return super(PrePayRuleViewSet, self).list(request, *args, **kwargs)


    def create(self, request, *args, **kwargs):
        response = {'message': '', 'success': False}
        try:
            rule_data = request.data.copy()
            rule_data['user'] = request.user.id
            rule_data['hotel'] = request.hotel.id
            hotel_methods.extranet_web_log_format(request.user.id, request.hotel.hotelcode, 'Property', 'Prepayrule', 'add', stage=hotel_methods.get_device_type(request))
            rule_serializer = PrePaySerializer(data=rule_data)
            rule_data['isactive'] = 1
            if rule_serializer.is_valid():
                rule_object = PayAtHotelPrePayRule(**rule_serializer.validated_data)
                if rule_object.start_date:
                    existing_rule_list = PayAtHotelPrePayRule.objects.filter(hotel__id=rule_object.hotel.id, isactive=1).exclude(
                        Q(start_date__isnull=True) | Q(end_date__isnull=True) |
                    Q(end_date__lt=rule_object.start_date) | Q(start_date__gt=rule_object.end_date)).order_by('start_date')
                    break_existing_rules(existing_rule_list, rule_serializer.validated_data)
                    rule_object.save()
                    response['rule'] = PrePaySerializer(rule_object).data
                    response['message'] = 'Rule created successfully'
                    response['success'] = True
                    hotel_methods.updateLogMsg(request.user, request.hotel, '%s %s ' %(rule_data, response['message']))

                else:
                    existing_rule_list = PayAtHotelPrePayRule.objects.filter(hotel__id=rule_object.hotel.id,
                                                                             start_date__isnull=True, end_date__isnull=True,
                                                                             isactive=1)
                    if not existing_rule_list:
                        rule_object.save()
                        response['rule'] = PrePaySerializer(rule_object).data
                        response['message'] = 'Rule created successfully'
                        response['success'] = True
                        hotel_methods.updateLogMsg(request.user, request.hotel,
                                                   '%s %s ' % (rule_data, response['message']))
                    else:
                        response['message'] = 'First deactivate old rules.'
                        return Response(response, status=status.HTTP_400_BAD_REQUEST)
            else:
                response['message'] = 'some error occurred'
                response['error'] = rule_serializer.errors
                return Response(response, status=status.HTTP_400_BAD_REQUEST)
        except Exception, e:
            response['message'] = 'Exception occurred'
            api_logger.critical(message='Permission denied: %s' % (str(e)), log_type='ingoibibo', bucket='PrePayRuleAPI',
                                    stage='pah_pre_pay_rule.views.PrePayRuleViewSet')
            return Response(response, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        api_logger.info(message='Rule created: %s' % (str(response)), log_type='ingoibibo', bucket='PrePayRuleAPI',
                        stage='pah_pre_pay_rule.views.create')
        return Response(response, status=status.HTTP_200_OK)


    def update(self, request, *args, **kwargs):
        response = {}
        try:
            rule_data = request.data.copy()
            rule_data['user'] = request.user.id
            if not request.pre_pay_rule_object:
                response['message'] = 'Pre pay Rule not found'
                return Response(response, status=status.HTTP_404_NOT_FOUND)

            pre_pay_rule_object = request.pre_pay_rule_object

            rule_serializer = PrePaySerializer(pre_pay_rule_object,
                                                     data=rule_data, partial=True)
            if rule_serializer.is_valid():
                rule_serializer.save()
                response['rule'] = rule_serializer.data
                response['message'] = 'Pre Pay rule updated successfully'
                hotel_methods.updateLogMsg(request.user, request.hotel, '%s' % (response))
            else:
                response['message'] = rule_serializer.errors
                hotel_methods.updateLogMsg(request.user, request.hotel, '%s' % (response))
                return Response(response, status=status.HTTP_400_BAD_REQUEST)

        except Exception, e:
            response['message'] = 'Exception occurred'
            api_logger.critical(message='Exception occurred: %s' % (str(e)),
                                log_type='ingoibibo', bucket='PrePayRuleAPI',
                            stage='pah_pre_pay_rule.views.update')
            return Response(response, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response(response, status=status.HTTP_200_OK)



    def delete(self , request, id=None):
        response = {'success': False, 'message' : ''}
        try:
            data = request.data.copy()
            id = data.get('id','')
            pre_pay_rule_list = PayAtHotelPrePayRule.objects.filter(id=id)
            if pre_pay_rule_list:
                hotel_methods.updateLogMsg(request.user, request.hotel, 'DELETED Pre Pay Rule : %s - %s , %s'
                                        %(pre_pay_rule_list[0].start_date,
                                         pre_pay_rule_list[0].end_date,
                                         pre_pay_rule_list[0].text))
                pre_pay_rule_list.delete()

            response['success'] = True
            response['message'] = 'Pay at hotel pre pay rule deleted successfully.'
        except Exception, e:
            response['message'] = 'Exception occurred'
            api_logger.critical(message='Exception occurred: %s' % (str(e)), log_type='ingoibibo',
                            bucket='PayAtHotelPrePayAPI', stage='pah_pre_pay_rule.viewsets.delete')

        return Response(response, status=status.HTTP_200_OK)
