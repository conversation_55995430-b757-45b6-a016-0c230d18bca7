syntax = "proto3";

package get_booking_detail;

import "google/protobuf/wrappers.proto";

message GetBookingDetailRequest {
    string booking_code = 1;
    string hotel_code = 2;
    bool fetch_booking_add_ons = 3;
    bool fetch_payment_details = 4;
    bool fetch_hotel_details = 5;
    bool fetch_cancellation_details = 6;
    string lang = 7;
}

message GetBookingDetailResponse {
    bool success = 1;
    Booking booking = 2;
}

message Booking {
    string confirm_booking_id = 1;
    string check_in = 2;
    string check_out = 3;
    string booking_name = 4;
    string booking_date = 5;
    string confirm_status = 6;
    uint32 no_of_rooms = 7;
    string hotel_code = 8;
    string room_type_name = 9;
    string room_type_code = 10;
    string booking_email = 11;
    double nett_amount = 12;
    string vendor_booking_id = 13;
    string base_currency = 14;
    double collection_amount = 15;
    string booking_vendor = 16;
    PriceBreakup price_breakup = 17;
    repeated AddOnInfo add_on_info = 18;
    string corporate_gstn = 19;
    uint32 number_of_nights = 20;
    string rate_plan_name = 21;
    string rate_plan_code = 22;
    string hotel_name = 23;
    string contract_type = 24;
    string guest_phone_number = 25;
    string special_requests = 26;
    bool special_requests_status = 27;
    string expected_arrival_time = 28;
    string createdon = 29;
    string modifiedon = 30;
    bool is_multi_room_booking = 31;
    CustomerGstInfo customer_gst = 32;
    bool is_pah = 33;
    bool is_tax_payable_at_hotel = 34;
    PreBuyDetails prebuy_details_info = 35;
    Payment payment_info = 36;
    HotelInfo hotel_info = 37;
    repeated CancellationRule cancellation_rule = 38;
    map<string, Room> room_stay = 39;
    string hotel_checkin = 40;
    CancellationInfo cancellation_info = 41;
    ModifiedBookingInfo modified_booking_info = 42;
    string booking_status = 43;
    Services services = 44;
    LangAwareData lang_aware_data = 45;
    bool gstn_invoice_required = 46;
    bool is_slot_booking = 47;
    bool is_tax_on_commission_inclusive=48;
    string booking_source = 49;
    string old_vendor_booking_id = 50;
    string amend_type = 51;
}

message Services {
    repeated Inclusion inclusions = 1;
}

message Inclusion {
    string category = 1;
    repeated string applicableDays = 2;
    string code = 3;
    string leafCategory = 4;
    string name = 5;
    float priority = 6;
    string currency = 7;
    string subCategory = 8;
    string type = 9;
    string description = 10;
}

message ModifiedBookingInfo {
    bool is_modified = 1;
    string modified_booking_id = 2;
}

message CancellationInfo {
    string cancellation_id = 1;
    HotelCancellationInfo hotel = 2;
    string cancellation_date = 3;
    string cancellation_status = 4;
}

message HotelCancellationInfo {
    double refund = 1;
    double cancellation_charges = 2;
    double booking_amount = 3;
}

message HotelInfo {
    bool push_customer_info_pah_to_chm = 1;
    bool push_customer_info_pas_to_chm = 2;
    string checkin_time = 3;
    uint32 max_infant_age = 4;
    bool is_push_net_amt_enabled = 5;
    bool is_vcc_payment_enabled = 6;
    string city = 7;
}

message CustomerGstInfo{
    string gstn = 1;
    string gst_ca = 2;
    string gst_cn = 3;
    string gst_ph = 4;
    string gst_ce = 5;
    string gst_ae = 6;
    string type = 7;
}

message Payment {
    string expiry_date = 1;
    string credit_card_type = 2;
    string credit_card_number = 3;
    string credit_card_name = 4;
    string credit_card_cvv = 5;
}

message PreBuyDetails {
    DayBreakup day_break_up = 1;
}

message DayBreakup  {
    repeated Day days = 1;
}

message Day {
    google.protobuf.StringValue date = 1;
    Inventory inventory = 2;
}

message Inventory {
    uint32 pre_buy_pool = 1;
    uint32 common_pool = 2;
}

message CancellationRule {
    string charges_type = 1;
    double charges = 2;
    int32 end_day = 3;
    int32 start_day = 4;
    string description = 5;
}

message RoomStay {
    repeated Room rooms = 1;
}

message Room {
    uint32 adult = 1;
    uint32 child = 2;
    repeated uint32 child_age = 3;
}

message PriceBreakup {
    TotalTaxBreakup total_tax_breakup = 1;
    string offer = 2;
    map<string, RoomBreakup> day_room_breakup = 3;
    string offer_code = 4;
    double commission = 5;
    double sell_price = 7;
    bool is_tax_pah = 8;
    double tax_pay_at_hotel_amount = 9;
    double total_tcs_amount = 10;
    string cug_offer_description = 11;
    string cug_code = 12;
    double total_tds_amount = 13;
    float tds_percent = 14;
    string offer_name = 15;
}

message TotalTaxBreakup {
    TaxInfo taxinfo = 2;
    double gst_tax = 3;
    double LT = 4;
    double TT = 5;
    double MT = 6;
    double CT = 7;
    double ST = 8;
    double OT = 9;
}

message TaxInfo {
    double municipalitytax = 1;
    double servtax = 2;
    bool margin_on_tax = 3;
    double othertax = 4;
    double luxtax = 5;
    double tourismtax = 6;
    double citytax = 7;
    double GST = 8;
}

message RoomBreakup {
    repeated RoomWiseBreakup room_breakup = 1;
}

message RoomWiseBreakup {
    double tax_on_extra_guest = 1;
    double pre_discount_price = 2;
    double tcs_info = 3;
    double extra_guest_nett_charge = 4;
    double extra_guest_charge = 5;
    float commission_percent = 6;
    double commission = 7;
    double pre_purchase_price = 8;
    float pre_buy_config = 9;
    bool is_zcr = 10;
    double nett_amount = 11;
    double service_charge = 12;
    float orc_percent = 13;
    bool is_cug_applied = 14;
    bool is_orc_applied = 15;
    double original_extra_guest_nett_charge = 16;
    bool is_cpp_rate_used = 17;
    bool is_coupon_applied = 18;
    float tax_percent = 19;
    double original_extra_guest_charge = 20;
    double tax_amount = 21;
    bool is_discount_applied = 22;
    double original_nett_amount = 23;
    double coupon_value_for_hotel = 24;
    float coupon_percent = 25;
    double hotelieramt_breakup = 26;
    float cug_percent = 28;
    float discount_percent = 29;
    double base_price = 30;
    string date = 31;
    ServiceTax service_tax = 32;
    double tds_amount = 33;
    int32 sell_rate_type_info = 34;
}

message ServiceTax {
    double total_service_tax = 1;
    TaxBreakup tax_breakup = 2;
}

message TaxBreakup {
    double SGST = 1;
    double CGST = 2;
    double IGST = 3;
}

message AddOnInfo {
    string charge_type = 1;
    float add_on = 2;
    double sell_amount = 3;
    CommissionBreakup commission_breakup = 4;
    string cancellation_rule = 5;
    float pay_mode = 6;
    string confirmation_type = 7;
    string text = 8;
    double nett_amount = 9;
    string based_on = 10;
    uint32 units = 11;
    string message = 12;
    string type = 13;
    string charge_value = 14;
    string booking_code = 15;
}

message CommissionBreakup {
    ServiceTaxWithPercentForCommissionBreakup service_tax = 1;
    double commission_amt = 2;
}

message ServiceTaxWithPercentForCommissionBreakup {
    double total_service_tax = 1;
}

message LangAwareData {
    string lang = 1;
    string room_type_name = 2;
    string rate_plan_name = 3;
    string hotelname = 4;
    LangAwareServices services = 5;
    LangAwarePriceBreakup price_breakup = 6;
}

message LangAwareServices {
repeated LangService notices = 1;
repeated LangService inclusions = 2;
}

message LangService {
string code = 1;
string description = 2;
string name = 3;
string subCategory = 4;
string leafCategory = 5;
}

message LangAwarePriceBreakup {
    string offer = 1;
    string cug_offer_description = 2;
    string commission_offer = 3;
    string commission_offer_type = 4;
    double commission_offer_value = 5;
}