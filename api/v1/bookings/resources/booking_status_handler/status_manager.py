import traceback
from api.v1.bookings.resources.booking_status_handler import *
from api.v1.bookings.resources.validate_booking_helper import validate_booking_status_request
from api.v1.utils.newrelic.push_insights import populate_booking_status_push_insights
from common.commonhelper import update_error_identifier, update_specific_identifier
from hotels.methods import HotelMethods
from hotels.tasks1 import push_booking_status_to_partners_async

from utils.logger import Logger

logger = Logger(logger='inventoryLogger')

hotel_methods = HotelMethods()


class StatusManager(object):
    __status = {'noshow': NoShowStatus,
                'cancelled': CancelStatus,
                'confirmed': ConfirmedStatus,
                'checkedin': CheckInStatus,
                'rejected': RejectStatus,
                'reject_service_issue': RejectStatus
               }

    __web_hook = {
        'Redbus': 'http://mojobusinessrestapi.mmt.mmt/v1/hotel/syncingostatus',
        'Goibibo':  'http://mojobusinessrestapi.mmt.mmt/v1/hotel/syncingostatus',
        'MakeMyTrip': 'http://mojobusinessrestapi.mmt.mmt/v1/hotel/syncingostatus',
        'MMTGCC': 'http://mojobusinessrestapi.mmt.mmt/v1/hotel/syncingostatus',
        'HotelCloud': 'http://mojobusinessrestapi.mmt.mmt/v1/hotel/syncingostatus',
        # 'Goibibo': 'http://************:9179/v1/hotel/syncingostatus'
    }
    __web_hook_header = {
        'Redbus': {'org': 'GI'},
        'Goibibo': {'org': 'GI'},
        'MakeMyTrip': {'org': 'MMT'},
        'MMTGCC': {'org': 'MMT'},
        'HotelCloud': {'org': 'MMT'},
    }

    @staticmethod
    def get_status_handler_obj(name):
        status_handler = StatusManager.__status.get(name, None)
        return status_handler

    @staticmethod
    def get_web_hook_url(vendor_id):
        web_hook = StatusManager.__web_hook.get(vendor_id, None)
        return web_hook

    @staticmethod
    def get_web_hook_header(vendor_id):
        web_hook_header = StatusManager.__web_hook_header.get(vendor_id, {})
        return web_hook_header


    @staticmethod
    def handle_status_change(data=None, bobj=None):
        log_identifier = {}
        update_specific_identifier('bookingId', bobj.confirmbookingid, log_identifier)
        update_specific_identifier('bookingVendorCode', bobj.bookingvendor.code, log_identifier)
        resp = {'success': False, 'message': ''}
        try:
            status = data.get('status', None)
            update_specific_identifier('status', status, log_identifier)
            override_cancellation_charges = data.get('override_cancellation_charges', 0)
            override_cancellation_percent = data.get('override_cancellation_percent', None)
            apply_cancellation_percentage = None
            if override_cancellation_percent is not None and override_cancellation_percent in [1, '1', True]:
                apply_cancellation_percentage = override_cancellation_charges
            trigger_vendor_push = data.get('trigger_vendor_push', True)
            if not data.get('initiated_by'):
                data['initiated_by'] = 'Staff'
            status_handler = StatusManager.get_status_handler_obj(status)(bobj, apply_cancellation_percentage, data)
            if not status_handler:
                resp['message'] = 'status handler not defined for status : %s' % status
                return resp

            resp.update(status_handler.update_status(data))
            update_specific_identifier("remark", "status update response", log_identifier)
            logger.info(message='Data : %s, Status Update Response : %s' % (repr(data), repr(resp)),
                        log_type='ingoibibo', bucket='StatusUpdateAPI',
                        stage='StatusManager.handle_status_change', identifier="{}".format(log_identifier))
            if not resp['success']:
                resp['message'] = resp.get('message', 'Error Occured')
                return resp

            if not trigger_vendor_push:
                return resp

            request_body = status_handler.get_request_body()
            web_hook = StatusManager.get_web_hook_url(bobj.bookingvendor.code)
            web_hook_header = StatusManager.get_web_hook_header(bobj.bookingvendor.code)
            if not web_hook:
                return resp
            wait_time = 5
            if (request_body.get('reason') == 'checkin_stuck_customer_at_hotel'
                    and request_body.get('status') == 'rejected'):
                # If reason checkin stuck than we will push after 15 min.
                wait_time = 15
            else:
                web_hook_resp = status_handler.push_info_to_web_hook(web_hook, json.dumps(request_body), web_hook_header)
                message = 'Request Body : {}, web_hook_resp : {}'.format(
                    repr(request_body),
                    repr(web_hook_resp))
                update_specific_identifier("remark", "MOJO push response", log_identifier)
                logger.info(message=message, log_type='ingoibibo', bucket='StatusUpdateAPI',
                            stage='StatusManager.handle_status_change', identifier="{}".format(log_identifier))
                hotel_methods.updateLogMsg(data.get('user', ''), bobj, message)

                if web_hook_resp.get('success', False):
                    return resp

            push_booking_status_to_partners_async \
                .apply_async(args=(bobj.confirmbookingid, data, apply_cancellation_percentage,), countdown=wait_time*60)

        except Exception, e:
            resp['success'] = False
            resp['message'] = 'Exception Occured : %s' %(str(e))
        return resp

    @staticmethod
    def push_status_to_partners(booking_id, data, apply_cancellation_percentage):
        log_identifier = {}
        web_hook_resp = {'success': False}
        try:
            from hotels.models import HotelBooking
            status = data['status']
            booking_obj = HotelBooking.objects.get(confirmbookingid=booking_id)
            update_specific_identifier('bookingId', booking_id, log_identifier)
            update_specific_identifier('bookingVendorCode', booking_obj.bookingvendor.code, log_identifier)
            update_specific_identifier('status', status, log_identifier)
            if status == 'checkedin':
                update_specific_identifier("remark", "ASYNC TRY : Booking Id : %s, Status is %s" % (booking_id, status), log_identifier)
                logger.info(log_type='ingoibibo', bucket='StatusUpdateAPI',
                            stage='StatusManager.push_status_to_partners', identifier="{}".format(log_identifier))
                web_hook_resp['success'] = True
                return web_hook_resp

            if booking_obj.confirmstatus != status:
                update_specific_identifier("remark", "ASYNC TRY : Booking Id : %s, Status changed" % booking_id, log_identifier)
                logger.info(log_type='ingoibibo', bucket='StatusUpdateAPI',
                            stage='StatusManager.push_status_to_partners', identifier="{}".format(log_identifier))
                return web_hook_resp

            status_push_handler = StatusManager.get_status_handler_obj(status)(booking_obj,
                                                                               apply_cancellation_percentage,
                                                                               data)
            request_body = status_push_handler.get_request_body()
            web_hook = StatusManager.get_web_hook_url(booking_obj.bookingvendor.code)
            web_hook_header = StatusManager.get_web_hook_header(booking_obj.bookingvendor.code)
            web_hook_resp = status_push_handler.push_info_to_web_hook(web_hook, json.dumps(request_body),
                                                                      web_hook_header)
            message = 'ASYNC TRY : Request Body : {}, web_hook_resp : {}'.format(
                repr(request_body),
                repr(web_hook_resp))
            update_specific_identifier("remark", "MOJO push response", log_identifier)
            logger.info(message=message, log_type='ingoibibo', bucket='StatusUpdateAPI',
                        stage='StatusManager.push_status_to_partners', identifier="{}".format(log_identifier))
            hotel_methods.updateLogMsg(data.get('user', ''), booking_obj, message)

            if not web_hook_resp.get('success', False):
                populate_booking_status_push_insights(data, booking_obj, web_hook_resp, 'Booking_MO_Push', celery_task=True)

        except Exception, e:
            update_error_identifier(error_message=repr(e), traceback=repr(traceback.format_exc()), log_identifier=log_identifier)
            logger.critical(log_type='ingoibibo', bucket='StatusUpdateAPI',
                            stage='StatusManager.push_status_to_partners', identifier="{}".format(log_identifier))

        return web_hook_resp

    @staticmethod
    def status_push_wrapper(status_update_data, booking):
        log_identifier = {}
        response = {'success': False, 'message': ''}
        update_specific_identifier('bookingId', booking.confirmbookingid, log_identifier)
        update_specific_identifier('bookingVendorCode', booking.bookingvendor.code, log_identifier)
        update_specific_identifier('status', status_update_data.get('status'), log_identifier)
        try:
            # Adding this to check if confirmstatus is already updated in db from some other flow
            from hotels.models import HotelBooking
            booking = HotelBooking.objects.using('default').get(confirmbookingid=booking.confirmbookingid)
            valid_resp = validate_booking_status_request(status_update_data, booking)
            if valid_resp['success']:
                response = StatusManager.handle_status_change(status_update_data, booking)
            else:
                response['message'] = valid_resp.get('error', '')
                response['error_code'] = valid_resp.get('error_code', '')
            if not response['success']:
                try:
                    populate_booking_status_push_insights(status_update_data, booking, response, 'Booking_Push', celery_task=False)
                except Exception, e:
                    message = 'populate_booking_status_push_insights, Booking Id: %s, Data: %s, Exception: %s' % (
                                booking.confirmbookingid, status_update_data, repr(e))
                    update_error_identifier(error_message=repr(e), traceback=repr(traceback.format_exc()), log_identifier=log_identifier)
                    update_specific_identifier("remark", "exception in populate_booking_status_push_insights", log_identifier)
                    logger.critical(message=message, log_type='ingoibibo', bucket='StatusUpdateAPI',
                                stage='StatusManager.status_push_wrapper', identifier="{}".format(log_identifier))

        except Exception, e:
            message = 'Booking Id: %s, Data: %s, Exception: %s' % (
                booking.confirmbookingid, status_update_data, repr(e))
            update_specific_identifier("remark", "exception in status_push_wrapper", log_identifier)
            logger.critical(message=message, log_type='ingoibibo', bucket='StatusUpdateAPI',
                            stage='StatusManager.status_push_wrapper', identifier="{}".format(log_identifier))

        return response

