from django.contrib.contenttypes.models import ContentType

from hotels.methods import HotelMethods
from hotels.models import HotelDetail, GenericContactDetail
from hotels.models import configuration as HotelConf, helper as HotelHelper
from lib.aes_encryption.helpers import hash_value
from utils.logger import Logger

hotel_content_type = ContentType.objects.get_for_model(HotelDetail)
api_logger = Logger(logger='inventoryAPILogger')
hotel_method_object = HotelMethods()

"""
Contains actions to be called post an OTP is verified.
"""


def verify_hotel_contact(identifer, request):
    content_type_id = hotel_content_type.id
    hotelcode, phone = identifer['code'], identifer['contact']
    object_id = HotelHelper.get_id_from_code(hotelcode, HotelConf.HotelCodeLength,
                                             HotelConf.HotelCodePrefix)
    allowed = hotel_method_object.checkHotelAccessibility(request.user,
                                                          hotelcode=hotelcode)
    if allowed:
        contact_detail = GenericContactDetail.objects.filter(
            object_id=object_id, content_type_id=content_type_id,
            contact=phone).last()
        contact_detail.verified = True
        contact_detail.subscribed = True
        contact_detail.save()
        hotel_method_object.updateLogMsg(request.user, contact_detail,
                                         'Verified via OTP')
        api_logger.info(message='Verified contact detail {id} and subscribed set to {status}'
                        .format(id=identifer, status=contact_detail.subscribed),
                        log_type='ingoibibo',
                        bucket='otp.actions',
                        stage='actions.verify_hotel_contact')
    else:
        api_logger.error(message='Verification request denied %s %s' %
                                 (identifer, request.user),
                         log_type='ingoibibo',
                         bucket='otp.actions',
                         stage='actions.verify_hotel_contact')
