__author__ = 'Archit'

import traceback
import time
from django.contrib.auth.hashers import check_password, make_password
from django.conf import settings
import json
from api.v2.vcc.serializers.vcc import VCCCreateSerializer, \
    VCCDeleteSerializer, VCCModifySerializer, VCCExpenseHistoryGetSerializer, \
    VCCGetSerializer, VCCSupplierEmailSerializer, VCCRealizationSerializer, \
    VCCVoucherForgotSerializer, VCCVoucherResetSerializer, \
    VCCVoucherValidateSerializer
from common.commonhelper import get_log_identifier, update_specific_identifier,update_error_identifier

from hotels.models import HotelAdminUser, HotelDetail
from communication.hotelMailsSMS import send_vcc_password_reset_event, sendHotelVCCMail, HotelBooking
from lib.check_permission import has_permission

from rest_framework.parsers import J<PERSON><PERSON>arser
from rest_framework.exceptions import ValidationError
from rest_framework.response import Response
from rest_framework.authentication import (BasicAuthentication, SessionAuthentication, TokenAuthentication)
from rest_framework.authtoken.models import Token
from rest_framework import viewsets
from api.v2.vcc.resources import <PERSON><PERSON>and<PERSON>, VCCOTPHandler, VCCForceForgetPwdHandler, IRIS_TEMPLATE_MAPPING
from api.v2.vcc.resources.helpers import create_vcc_token, validate_vcc_token
from hotels.methods import HotelMethods
from communication.tasks import send_vcc_otp_notification
from utils.logger import Logger
from rest_framework.status import HTTP_200_OK, HTTP_400_BAD_REQUEST,HTTP_401_UNAUTHORIZED, \
    HTTP_500_INTERNAL_SERVER_ERROR, HTTP_403_FORBIDDEN
from ingo_partners.config_manager.common_methods import config_keeper
from communication.tasks import send_otp_login_notification,send_otp_notification_via_whatsapp
from api.v2.users.resources.helpers import UserManagementWrapper
from api.v2.vcc.permissions.permissions import IsHotelAccessibility, DirectConnectBookingPermission, IsHotelAccessibleForBookingView
hotel_methods = HotelMethods()
api_logger = Logger(logger='inventoryAPILogger')


class ResponseInfo(object):
    def __init__(self):
        self.response_message = {
            'responseMessage': '',
            'responseData': {
            },
            'responseStatus': '',
            'responseErrors': []
        }


class VCCViewSet(viewsets.GenericViewSet, VCCHandler):
    authentication_classes = (BasicAuthentication, TokenAuthentication)
    parser_classes = [JSONParser]

    def __init__(self, **kwargs):
        self.response_format = ResponseInfo().response_message
        self.VCC_REALIZATION_RETRY_TIME = 1
        super(VCCViewSet, self).__init__(**kwargs)

    @staticmethod
    def _validate_response(vcc_response, action):
        '''
        This function validates the response from the payment team
        returns status and message.
        '''
        message_map = {
            'VCC already deleted': 'Booking has been cancelled.'
        }
        if vcc_response.get('error_code') == '500':
            return 'failure', 'Failed to fetch VCC details. Please try again later or contact your BD manager if' \
                              ' issue persists.'
        elif vcc_response.get('msg') or not vcc_response.get('status'):
            vcc_error_message = vcc_response.get('msg') or vcc_response.get('error')
            error_message = message_map.get(vcc_error_message) \
                if message_map.get(vcc_error_message, None) else None

            if error_message:
                return 'failure', 'VCC could not be {} due to ' \
                                  '{}'.format(action, error_message)
            else:
                return 'failure', 'Failed to fetch VCC details. Please try again later or contact your BD manager if' \
                                  ' issue persists.'

        elif str(vcc_response.get('status')).lower() == 'true':
            return 'success', 'VCC {} Successfully'.format(action)
        else:
            return 'success', 'VCC {} Successfully'.format(action)

    def create(self, request, *args, **kwargs):
        try:
            requested_data = request.data
            serializer = VCCCreateSerializer(data=requested_data)
            is_valid = serializer.is_valid()
            if is_valid:
                status = HTTP_200_OK
                vcc_response = self.post_data(action='CREATE_VCC', params=requested_data)
                self.response_format['responseStatus'], \
                    self.response_format['responseMessage'] = VCCViewSet._validate_response(vcc_response, 'created')
                self.response_format['responseData'].update(vcc_response)
            else:
                errors = serializer.errors
                api_logger.critical("Validation while vcc card creation with parameters %s is %s" % (
                    requested_data, errors), log_type="ingoibibo",
                                    bucket="hotel_vcc_v2_payments", stage="vcc_v2_api")
                status = HTTP_400_BAD_REQUEST
                self.response_format['responseStatus'] = 'failure'
                self.response_format['responseMessage'] = 'Validation Error while creating VCC'
                self.response_format['responseErrors'] = errors
            return Response(self.response_format, status=status)
        except Exception as e:
            api_logger.critical("Exception while vcc card creation with parameters %s is %s Traceback: %s" % (
                requested_data, str(e), repr(traceback.format_exc())), log_type="ingoibibo",
                                bucket="hotel_vcc_v2_payments", stage="vcc_v2_api")
            self.response_format['responseMessage'] = 'Error : {}, Please try again'.format(e)
            self.response_format['responseStatus'] = 'failure'
            status = HTTP_500_INTERNAL_SERVER_ERROR
            return Response(self.response_format, status=status)

    def modify(self, request, *args, **kwargs):
        try:
            requested_data = request.data
            serializer = VCCModifySerializer(data=requested_data)
            is_valid = serializer.is_valid()
            if is_valid:
                status = HTTP_200_OK
                vcc_response = self.post_data(action='MODIFY_VCC', params=requested_data)
                self.response_format['responseStatus'], \
                    self.response_format['responseMessage'] = VCCViewSet._validate_response(vcc_response, 'modified')
                self.response_format['responseData'].update(vcc_response)
            else:
                errors = serializer.errors
                api_logger.critical("Validation while vcc card modification with parameters %s is %s Traceback %s" % (
                    requested_data, errors, repr(traceback.format_exc())), log_type="ingoibibo",
                                    bucket="hotel_vcc_v2_payments", stage="vcc_v2_api")
                status = HTTP_400_BAD_REQUEST
                self.response_format['responseStatus'] = 'failure'
                self.response_format['responseMessage'] = 'Validation Error while modifying VCC'
                self.response_format['responseErrors'] = errors

            return Response(self.response_format, status=status)
        except Exception as e:
            api_logger.critical("Exception while vcc card modification with parameters %s is %s Traceback: %s" % (
                requested_data, str(e), repr(traceback.format_exc())), log_type="ingoibibo",
                                bucket="hotel_vcc_v2_payments", stage="vcc_v2_api")
            self.response_format['responseMessage'] = 'Error : {}, Please try again'.format(e)
            self.response_format['responseStatus'] = 'failure'
            status = HTTP_500_INTERNAL_SERVER_ERROR
            return Response(self.response_format, status=status)

    def delete(self, request, *args, **kwargs):
        try:
            requested_data = request.data
            serializer = VCCDeleteSerializer(data=requested_data)
            is_valid = serializer.is_valid()
            if is_valid:
                status = HTTP_200_OK
                vcc_response = self.post_data(action='DELETE_VCC', params=requested_data)
                self.response_format['responseStatus'], \
                    self.response_format['responseMessage'] = VCCViewSet._validate_response(vcc_response, 'deleted')
                self.response_format['responseData'].update(vcc_response)
            else:
                errors = serializer.errors
                api_logger.critical("Validation while vcc card deletion with parameters %s is %s" % (
                    requested_data, errors), log_type="ingoibibo",
                                    bucket="hotel_vcc_v2_payments", stage="vcc_v2_api")
                status = HTTP_400_BAD_REQUEST
                self.response_format['responseStatus'] = 'failure'
                self.response_format['responseMessage'] = 'Validation Error while deleting VCC'
                self.response_format['responseErrors'] = errors
            return Response(self.response_format, status=status)
        except Exception as e:
            api_logger.critical("Exception while vcc card deletion with parameters %s is %s Traceback: %s" % (
                requested_data, str(e), repr(traceback.format_exc())), log_type="ingoibibo",
                                bucket="hotel_vcc_v2_payments", stage="vcc_v2_api")
            self.response_format['responseMessage'] = 'Error : {}, Please try again'.format(e)
            self.response_format['responseStatus'] = 'failure'
            status = HTTP_500_INTERNAL_SERVER_ERROR
            return Response(self.response_format, status=status)

    @staticmethod
    def mask_vcc_details(response):
        for key in ["card_number", "expiry_month", "expiry_year", "card_type", "card_cvv", "card_name", "bank_name"]:
            if key in response:
                response[key] = "***"
        return response

    def get_details(self, request, *args, **kwargs):
        log_identifier = get_log_identifier(request_id=request.user.id, api_name='vcc/get_details')
        try:
            # Check permission
            is_vcc_special_user, is_user_hotelier = False, False
            txn_id = request.data.get('txnid', None)

            update_specific_identifier("bookingId",txn_id,log_identifier)

            permission = DirectConnectBookingPermission()
            if not permission.has_permission(request, self):
                self.response_format['responseStatus'] = 'failure'
                self.response_format['responseMessage'] = 'You do not have permission to perform this action.'
                return Response(self.response_format, status=HTTP_403_FORBIDDEN)
            
            if settings.VCC_MASK_SETTINGS['enabled']:
                token = request.COOKIES.get('vcc_token')
                is_valid, message = validate_vcc_token(token,request.user.id)
                is_vcc_special_user = True if has_permission(request.user, 'vcc_admin_permission') else False
                is_user_hotelier = False if request.user.is_staff or not request.user.id else True

                if not is_valid and not is_vcc_special_user:
                    errmsg="Vcc Fetch Permission token issue : {}".format(message)
                    update_error_identifier(errmsg, traceback=repr(traceback.format_exc()),
                                            log_identifier=log_identifier)
                    api_logger.critical(identifier='{}'.format(log_identifier), log_type='ingoibibo', bucket='hotel_vcc_v2_payments',
                                                  stage='vcc_v2_api')
                    self.response_format['responseStatus'] = 'failure'
                    self.response_format['responseMessage'] = str(message)
                    return Response(self.response_format, status=HTTP_200_OK)

            requested_data = request.data
            serializer = VCCDeleteSerializer(data=requested_data)
            is_valid = serializer.is_valid()
            if is_valid:

                update_specific_identifier("remark","VCC Details Accessed.  is_vcc_special_user: %s | is_user_hotelier: %s | "
                                "Configkeeper VCC_MASKING_ENABLED: %s | vcc fetch" % (
                                     is_vcc_special_user, is_user_hotelier,
                                    settings.VCC_MASK_SETTINGS['enabled']),log_identifier)
                api_logger.info(identifier='{}'.format(log_identifier,log_type="ingoibibo",bucket='hotel_vcc_v2_payments',stage='vcc_v2_api'))


                status = HTTP_200_OK
                vcc_response = self.post_data(action='GET_VCC', params=requested_data)
                if settings.VCC_MASK_SETTINGS['enabled'] and not(is_vcc_special_user or is_user_hotelier):
                    vcc_response = VCCViewSet.mask_vcc_details(vcc_response)
                self.response_format['responseStatus'], \
                    self.response_format['responseMessage'] = VCCViewSet._validate_response(vcc_response, 'fetched')
                self.response_format['responseData'].update(vcc_response)
            else:
                errors = serializer.errors
                errmsg = "Validation while fetching vcc card details with parameters %s is %s" % (requested_data, errors)
                update_error_identifier(errmsg,log_identifier=log_identifier)
                api_logger.critical(identifier='{}'.format(log_identifier), log_type='hotel_vcc_v2_payments', bucket='hotel_vcc_v2_payments',
                                                  stage='vcc_v2_api')
                status = HTTP_400_BAD_REQUEST
                self.response_format['responseStatus'] = 'failure'
                self.response_format['responseMessage'] = 'Validation Error while fetching VCC'
                self.response_format['responseErrors'] = errors
            return Response(self.response_format, status=status)
        except Exception as e:
            errmsg ="Exception while fetching vcc card details is {}".format(str(e))
            update_error_identifier(errmsg, traceback=repr(traceback.format_exc()),log_identifier=log_identifier)
            api_logger.critical(identifier='{}'.format(log_identifier), log_type='ingoibibo', bucket='hotel_vcc_v2_payments',stage='vcc_v2_api')
            self.response_format['responseMessage'] = 'Error : {}, Please try again'.format(e)
            self.response_format['responseStatus'] = 'failure'
            status = HTTP_500_INTERNAL_SERVER_ERROR
            return Response(self.response_format, status=status)

    def get_expense_history(self, request, *args, **kwargs):
        try:
            requested_data = request.data
            serializer = VCCDeleteSerializer(data=requested_data)
            is_valid = serializer.is_valid()
            if is_valid:
                status = HTTP_200_OK
                vcc_response = self.post_data(action='GET_EXPENSE_HISTORY', params=requested_data)
                self.response_format['responseStatus'], \
                    self.response_format['responseMessage'] = VCCViewSet._validate_response(vcc_response,
                                                                                            'fetched expense history')
                self.response_format['responseData'].update(vcc_response)
            else:
                errors = serializer.errors
                api_logger.critical("Validation while fetching expense history "
                                    "vcc card with parameters %s is %s" % (
                                        requested_data, errors), log_type="ingoibibo",
                                    bucket="hotel_vcc_v2_payments", stage="vcc_v2_api")
                status = HTTP_400_BAD_REQUEST
                self.response_format['responseStatus'] = 'failure'
                self.response_format['responseMessage'] = 'Validation Error while fetching Expense History VCC'
                self.response_format['responseErrors'] = errors
            return Response(self.response_format, status=status)
        except Exception as e:
            api_logger.critical("Exception while fetching expense history with parameters %s is %s Traceback %s" % (
                requested_data, str(e), repr(traceback.format_exc())), log_type="ingoibibo",
                                bucket="hotel_vcc_v2_payments", stage="vcc_v2_api")
            self.response_format['responseMessage'] = 'Error : {}, Please try again'.format(e)
            self.response_format['responseStatus'] = 'failure'
            status = HTTP_500_INTERNAL_SERVER_ERROR
            return Response(self.response_format, status=status)

    def send_supplier_mail(self, request, *args, **kwargs):
        try:
            requested_data = request.data
            serializer = VCCDeleteSerializer(data=requested_data)
            is_valid = serializer.is_valid()
            if is_valid:
                status = HTTP_200_OK
                vcc_response = self.post_data(action='SEND_SUPPLIER_EMAIL', params=requested_data)
                self.response_format['responseStatus'], \
                    self.response_format['responseMessage'] = VCCViewSet._validate_response(vcc_response, 'mailed')
                self.response_format['responseData'].update(vcc_response)
            else:
                errors = serializer.errors
                api_logger.critical("Validation while sending vcc mail with parameters %s is %s" % (
                    requested_data, errors), log_type="ingoibibo",
                                    bucket="hotel_vcc_v2_payments", stage="vcc_v2_api")
                status = HTTP_400_BAD_REQUEST
                self.response_format['responseStatus'] = 'failure'
                self.response_format['responseMessage'] = 'Validation Error while sending mail to supplier'
                self.response_format['responseErrors'] = errors
            return Response(self.response_format, status=status)
        except Exception as e:
            api_logger.critical("Exception while sending vcc mail with parameters %s is %s Traceback %s" % (
                requested_data, str(e), repr(traceback.format_exc())), log_type="ingoibibo",
                                bucket="hotel_vcc_v2_payments", stage="vcc_v2_api")
            self.response_format['responseMessage'] = 'Error : {}, Please try again'.format(e)
            self.response_format['responseStatus'] = 'failure'
            status = HTTP_500_INTERNAL_SERVER_ERROR
            return Response(self.response_format, status=status)

    def get_realization(self, request, *args, **kwargs):
        try:
            booking_id = request.GET.get('booking_id') or kwargs.get('booking_id')
            permission = IsHotelAccessibleForBookingView()
            if not permission.has_permission(request, self, booking_id=booking_id):
                self.response_format['responseStatus'] = 'failure'
                self.response_format['responseMessage'] = 'You do not have permission to perform this action.'
                return Response(self.response_format, status=HTTP_403_FORBIDDEN)
            self.response_format['responseStatus'] = True
            status = HTTP_200_OK
            requested_data = {'booking_id': booking_id}
            serializer = VCCRealizationSerializer(data=requested_data)
            serializer.is_valid(raise_exception=True)

            vcc_response = self.post_data(action='GET_VCC_REALIZATION', params={'txnid': requested_data.get('booking_id')})
            retry_count = 1
            while retry_count <= 3:
                vcc_response = self.post_data(action='GET_VCC_REALIZATION', params=
                {'txnid': requested_data.get('booking_id')})
                # Get Transaction History
                if vcc_response.get('status'):
                    break
                retry_count += 1
                # For the last retry
                if retry_count == 3:
                    self.VCC_REALIZATION_RETRY_TIME = 5
                time.sleep(self.VCC_REALIZATION_RETRY_TIME)

            if vcc_response.get('status'):
                total_amount =  vcc_response.get('vcc_amount')
                currency= vcc_response.get('currency')
                
                booking = HotelBooking.objects.using('default').filter(confirmbookingid=requested_data.get('booking_id')).first()
                payment_obj = booking.payments.using('default').all()[0].payment
                if payment_obj:
                    paymentref = json.loads(payment_obj.paymentreference)
                    total_amount = paymentref.get('vcc_amount', total_amount)
                    currency = paymentref.get('vcc_currency', currency)
                    
                self.response_format['responseData'] = {
                    'totalAmount': total_amount,
                    'currency': currency,
                    'chargedAmount': vcc_response.get('charged_amount'),
                    'transactions': []
                }
                transactions = []
                for transaction in vcc_response.get('vcc_merchant_transactions', []):
                    transactions.append({
                        'merchantAmount': transaction.get('merchant_amount'),
                        'merchantCurrency': transaction.get('merchant_currency_code'),
                        'merchantName': transaction.get('merchant_name'),
                        'settled': transaction.get('settled'),
                        'transactionTime': transaction.get('txn_date_time_with_time').split('+')[0]
                    })
                self.response_format['responseData'].update({
                    'transactions': transactions
                })
                self.response_format['responseMessage'] = 'VCC Realization has been fetched.'
            else:
                self.response_format['responseMessage'] = 'Could not Fetch VCC Realization.'
                self.response_format['responseStatus'] = False

        except ValidationError as e:
            status = HTTP_400_BAD_REQUEST
            self.response_format['responseStatus'] = False
            self.response_format['responseMessage'] = 'Validation Error while fetching the realization error'
            self.response_format['responseErrors'] = e

        except Exception as e:
            self.response_format['responseMessage'] = 'Error : {}, Please try again'.format(e)
            self.response_format['responseStatus'] = False
            status = HTTP_500_INTERNAL_SERVER_ERROR
        return Response(self.response_format, status=status)

    def send_vcc_mail(self, request, *args, **kwargs):
        try:
            self.response_format['responseStatus'] = False
            status = HTTP_200_OK
            booking_id = request.GET.get('booking_id')
            requested_data = {'booking_id': booking_id}
            serializer = VCCRealizationSerializer(data=requested_data)
            serializer.is_valid(raise_exception=True)
            status = sendHotelVCCMail(booking_id)
            self.response_format['responseMessage'] = 'VCC has been sent on registered email.'
            self.response_format['responseStatus'] = True

        except ValidationError as e:
            status = HTTP_400_BAD_REQUEST
            self.response_format['responseStatus'] = False
            self.response_format['responseMessage'] = 'Validation Error while sending the VCC mail'
            self.response_format['responseErrors'] = e
            api_logger.critical("Exception while sending vcc mail with parameters %s is %s Traceback %s" % (
                requested_data, str(e), repr(traceback.format_exc())), log_type="ingoibibo",
                                bucket="hotel_vcc_v2_payments", stage="send_vcc_mail")
        except Exception as e:
            self.response_format['responseMessage'] = 'Please try again after sometime.'
            self.response_format['responseStatus'] = False
            status = HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical("Exception while sending vcc mail with parameters %s is %s Traceback %s" % (
                requested_data, str(e), repr(traceback.format_exc())), log_type="ingoibibo",
                                bucket="hotel_vcc_v2_payments", stage="send_vcc_mail")

        return Response(self.response_format, status=status)


class VCCPasswordViewSet(viewsets.GenericViewSet):
    authentication_classes = (TokenAuthentication, SessionAuthentication)
    parser_classes = [JSONParser]
    permission_classes = [IsHotelAccessibility]


    def __init__(self, **kwargs):
        self.otp_handler = VCCOTPHandler()
        self.vcc_force_forgot_pwd_handler = VCCForceForgetPwdHandler()
        self.response_format = {
            'message': '',
            'errors': '',
            'success': False
        }
        super(VCCPasswordViewSet, self).__init__(**kwargs)
        
    def _get_template_name(self, flow):
        try:
            return IRIS_TEMPLATE_MAPPING[flow]
        except KeyError:
            return IRIS_TEMPLATE_MAPPING['default']

    def _push_notifications(self, email, otp, user):
        '''
        Helper function to push the notification to the user
        about the OTP.
        '''
        context = {'OTP': str(otp)}
        token = Token.objects.get(user=user).key
        log_identifier = get_log_identifier(request_id=user.id, api_name='_push_notifications')
        api_logger.info("Sending email to user for OTP",bucket="api.v2.vcc",stage='push_notifications',identifier='{}'.format(log_identifier))
        if settings.DEBUG:
            send_vcc_otp_notification(context=context, to=email, user_token=token,log_identifier=log_identifier)
        else:
            send_vcc_otp_notification.apply_async(args=(context, email, token,log_identifier))
            
    def _send_otp_via_email(self, user, email, otp, flow, expiry, log_identifier={}):
        '''
        Helper function to push the notification to the user via email for the OTP.
        This function calls the interlink service, which in turn calls IRIS to send the email.
        '''

        update_specific_identifier("remark","Sending VCC OTP via Email",log_identifier=log_identifier)
        api_logger.info("Sending email to {} for OTP".format(email),bucket="api.v2.vcc",stage='send_otp_via_email',identifier='{}'.format(log_identifier))
        template_name = self._get_template_name(flow)

        template_data = {
            'timeout': '{minutes} minutes'.format(minutes=str(expiry/60)),
            'otp': str(otp)
        }

        send_otp_login_notification(type='email', template_data=template_data, to=email, flow="vcc_otp_flow",
                                    template_name=template_name, publish_time=time.time())
        

    def _send_otp_via_whatsapp(self, user, otp, flow, log_identifier={}):
        '''
        Helper function to push the notification to the user via WA for the OTP.
        This function calls the interlink service, which in turn calls IRIS to send the WA msg.
        '''
        
        update_specific_identifier("remark","Sending VCC OTP via Whatsapp",log_identifier=log_identifier)
        template_name = self._get_template_name(flow)

        try:
            template_data = {
                '1': str(otp)
            }
        
            ums_user = UserManagementWrapper(user=user)
            instant_messenger = ums_user.get_instant_messenger()

            if instant_messenger:
                whatsapp_countrycode = instant_messenger.country.dialing_prefix
                whatsapp_number = instant_messenger.contact_no
                whatsapp_subscribed = instant_messenger.subscription_status

                if whatsapp_subscribed == 'subscribed':
                    api_logger.info("Sending Whatsapp to {} {} for OTP".format(whatsapp_countrycode, whatsapp_number),bucket="api.v2.vcc",stage='_send_otp_via_whatsapp',identifier='{}'.format(log_identifier))
                    
                    send_otp_notification_via_whatsapp(phoneno=whatsapp_number,countrycode=whatsapp_countrycode,template_data=template_data,
                                                flow='vcc_wa_otp_flow',template_name=template_name,log_identifier=log_identifier)
                    
        except Exception as e:
            update_error_identifier(error_message="Exception while sending whatsapp vcc OTP {}".format(str(e)), traceback=repr(traceback.format_exc()),log_identifier=log_identifier)
            api_logger.critical(bucket="api.v2.vcc", stage="_send_otp_via_whatsapp",identifier = '{}'.format(log_identifier))

    def _push_email_notification(self, hotelcode, user):
        log_identifier = get_log_identifier(request_id=user.id, api_name='_push_email_notification')
        try:
            hotel = HotelDetail.objects.get(hotelcode=hotelcode)
            hotel_primary_email_list = hotel.hotelemail or []
            hotel_primary_email_list = ','.join(hotel_primary_email_list)
            user_email = user.email or ''
            api_logger.info("Sending email to hotelcode %s for user %s" % (hotelcode, user), bucket="api.v2.vcc",
                            stage="vcc_v2_api", identifier='{}'.format(log_identifier))
            send_vcc_password_reset_event(primary_email_ids=hotel_primary_email_list, user_email=user_email,
                                          user_name=user.username, user_id=user.id, hotel_name=hotel.hotelname,
                                          hotel_code=hotelcode)
        except Exception as e:
            api_logger.critical("Exception {error} while sending the email to hotelcode {hotelcode}"
                                " for user {user} Traceback {trace}".format(hotelcode=hotelcode, user=user,
                                                                            error=e,
                                                                            trace=repr(traceback.format_exc())),
                                bucket="api.v2.vcc", stage="vcc_v2_api",identifier = '{}'.format(log_identifier))
            
    
    
    def _validate_new_password(self, new_password, user, admin_user=None):
        """
        Helper function to validate the new password.
        """
        if not new_password:
            return
        
        if admin_user is None:
            admin_user = HotelAdminUser.objects.get(hoteluser=user)
        
        login_password = user.password
        old_cc_password = admin_user.password_for_cc_access

        if check_password(new_password, old_cc_password):
            raise IOError('New password should be different from the old password.')

        if check_password(new_password, login_password):
            raise IOError('VCC password should be different from Login password.')

    def trigger_otp(self, request, *args, **kwargs):
        '''
        API which triggers the VCC password reset otp for a user.
        '''
        status = HTTP_200_OK
        hotelcode=kwargs.get('hotelcode')
        log_identifier = {'api_specific_identifier': {'user_id': request.user.id, 'hotelcode':hotelcode}}   

        try:

            update_specific_identifier('remark', 'VCC Password OTP API called', log_identifier)
            api_logger.info(log_type='ingoibibo', bucket="api.v2.vcc", stage="trigger_otp", identifier='{}'.format(log_identifier))

            data = request.data or {}
            if data.get('email_id'):
                email_id = data.get('email_id')
            else:
                if not (hasattr(request.user, 'email') and request.user.email):
                    raise IOError('Could not find the email id for your user in the '
                                  'system. Please get your contact details updated.')
                else:
                    email_id = request.user.email

            # Validate the new password if present
            self._validate_new_password(new_password=data.get('newpassword',''), user=request.user)
            # set otp number
            self.otp_handler.set_otp_for_user(username=request.user.username)

            expiry = self.otp_handler.config.get('vcc_otp_timeout')
            otp=self.otp_handler.get_otp_for_user(username=request.user.username)

            self._send_otp_via_email(request.user, email_id, otp, 'reset_vcc_password', expiry, log_identifier)
            self._send_otp_via_whatsapp(request.user, otp, 'reset_vcc_password', log_identifier)

                
            self.response_format['message'] = 'Email has been sent to {}.'.format(email_id)
            self.response_format['success'] = True

        except IOError as e:
            status = HTTP_400_BAD_REQUEST
            self.response_format['message'] = str(e)

        except Exception as e:
            update_error_identifier("Exception while trigger vcc voucher OTP with parameters {data} is {error}".format(data=str(data), error=str(e)),traceback=repr(traceback.format_exc()),log_identifier=log_identifier)
            api_logger.critical(bucket="api.v2.vcc", stage="vcc_v2_api",identifier = '{}'.format(log_identifier))
            status = HTTP_500_INTERNAL_SERVER_ERROR
            self.response_format['message'] = 'Could not send the email.'
            self.response_format['errors'] = str(e)

        return Response(self.response_format, status=status)

    def validate(self, request, *args, **kwargs):
        '''
        API which validates the Credit card password.
        '''
        status = HTTP_200_OK
        log_data = {}
        vcc_token = None

        try:
            
            if not log_data.get('api_specific_identifiers'):
                log_data['api_specific_identifiers'] = {}
            
            log_data['api_specific_identifiers']['ingo_user_id'] = request.user.id if request.user else ''
            log_data['headers'] = {key: value for key, value in request.META.items() if key.startswith('HTTP_')}
            api_logger.info("VCC Password Validation API called",bucket="api.v2.vcc",
                            stage="vcc_v2_api", identifier='{}'.format(log_data))
            data = request.data
            serializer = VCCVoucherValidateSerializer(data=data)
            serializer.is_valid(raise_exception=True)
            admin_user = HotelAdminUser.objects.get(hoteluser=request.user)
            cc_password = admin_user.password_for_cc_access
            vcc_token = None
            if not cc_password:
                raise IOError('Please setup your password!!.')

            if check_password(data['password'], cc_password):
                self.response_format['message'] = 'Correct Password!.'
                self.response_format['success'] = True
                vcc_token = create_vcc_token(request.user.id)
            else:
                self.response_format['message'] = 'Incorrect Password!.'

        except HotelAdminUser.DoesNotExist:
            status = HTTP_403_FORBIDDEN
            self.response_format['message'] = 'Your hotel admin user does not exist.'

        except IOError as e:
            status = HTTP_400_BAD_REQUEST
            self.response_format['message'] = str(e)

        except ValidationError as e:
            status = HTTP_400_BAD_REQUEST
            self.response_format['message'] = 'Validation Errors.'
            self.response_format['errors'] = str(e)

        except Exception as e:

            api_logger.critical("Exception while validate vcc voucher with parameters {data} is {error} "
                                "Traceback {trace}".format(
                data=str(data), error=str(e), trace=repr(traceback.format_exc())),
                bucket="api.v2.vcc", stage="vcc_v2_api")
            status = HTTP_500_INTERNAL_SERVER_ERROR
            self.response_format['message'] = 'Could not validate the email id.'
            self.response_format['errors'] = str(e)
        response = Response(self.response_format, status=status)
        if vcc_token:
            response.set_cookie('vcc_token', vcc_token)
        return response

    def forgot(self, request, *args, **kwargs):
        '''
        API which validates the otp and sets a new credit card password.
        '''
        status = HTTP_200_OK
        hotelcode=kwargs.get('hotelcode')
        log_identifier = {'api_specific_identifier': {'user_id': request.user.id, 'hotelcode':hotelcode}}

        try:

            update_specific_identifier('remark', 'VCC Forgot Password API called', log_identifier)
            api_logger.info(log_type='ingoibibo', bucket="api.v2.vcc", stage="forgot", identifier='{}'.format(log_identifier))
        
            if settings.VCC_PASSWORD_SETTINGS['forgot_disabled']:
                status = HTTP_403_FORBIDDEN
                self.response_format['message'] = 'Forgot vcc password is not allowed'
                return Response(self.response_format, status=status)

            data = request.data
            serializer = VCCVoucherForgotSerializer(data=data)
            serializer.is_valid(raise_exception=True)
            otp = int(data.get('otp'))

            if len(str(otp)) != self.otp_handler.config.get('forgot_vcc_otp_size'):
                raise IOError("We couldn't verify the OTP you entered. Please refresh the page and try again.")

            if not self.otp_handler.validate_otp(expected_otp=otp, username=request.user.username):
                update_specific_identifier("remark","Invalid OTP",log_identifier=log_identifier)
                api_logger.info(bucket="api.v2.vcc",stage='vcc_v2_api',identifier='{}'.format(log_identifier))
                raise IOError("Invalid OTP.")

            self.otp_handler.delete_otp_keys(request.user.username,
                                             keytype='vcc_otp')
            admin_user = HotelAdminUser.objects.get(hoteluser=request.user)
        
            # Validate the new password
            self._validate_new_password(data.get('newpassword'), request.user, admin_user)
            
            admin_user.password_for_cc_access = make_password(data.get('newpassword'))
            admin_user.save()
            
            self.vcc_force_forgot_pwd_handler.set_force_forgot_vcc_password_key(user_id=request.user.id, hotelcode=hotelcode)
            self._push_email_notification(hotelcode=hotelcode, user=request.user)
            self.response_format['message'] = 'Password has been updated successfully.'
            self.response_format['success'] = True

        except HotelAdminUser.DoesNotExist:
            status = HTTP_403_FORBIDDEN
            self.response_format['message'] = 'Your hotel admin user does not exist.'

        except IOError as e:
            status = HTTP_400_BAD_REQUEST
            self.response_format['message'] = str(e)

        except ValidationError as e:
            status = HTTP_400_BAD_REQUEST
            self.response_format['message'] = str(e)
            self.response_format['errors'] = str(e)

        except Exception as e:
            update_error_identifier("Exception while forgot vcc voucher with parameters {data} is {error}".format(data=str(data), error=str(e)),traceback=repr(traceback.format_exc()),log_identifier=log_identifier)
            api_logger.critical(bucket="api.v2.vcc", stage="vcc_v2_api",identifier = '{}'.format(log_identifier))
            status = HTTP_500_INTERNAL_SERVER_ERROR
            self.response_format['message'] = 'Could not update the credit card password.'
            self.response_format['errors'] = str(e)

        return Response(self.response_format, status=status)

    def reset(self, request, *args, **kwargs):
        '''
        API which validates the old cc password and sets a new password
        for the same.
        '''
        status = HTTP_200_OK
        try:
            if settings.VCC_PASSWORD_SETTINGS['reset_disabled']:
                status = HTTP_403_FORBIDDEN
                self.response_format['message'] = 'Vcc password reset is not allowed'
                return Response(self.response_format, status=status)

            data = request.data
            hotelcode=kwargs.get('hotelcode')
            serializer = VCCVoucherResetSerializer(data=data)
            serializer.is_valid(raise_exception=True)
            admin_user = HotelAdminUser.objects.get(hoteluser=request.user)
            cc_password = admin_user.password_for_cc_access
            if not cc_password:
                raise IOError('Please setup your password!!.')

            if not check_password(data['oldpassword'], cc_password):
                raise IOError('Invalid Old Password')

            admin_user.password_for_cc_access = make_password(data.get('newpassword'))
            admin_user.save()
            self.vcc_force_forgot_pwd_handler.set_force_forgot_vcc_password_key(user_id=request.user.id, hotelcode=hotelcode)
            self._push_email_notification(hotelcode=hotelcode, user=request.user)
            self.response_format['message'] = 'Password has been updated successfully.'
            self.response_format['success'] = True

        except HotelAdminUser.DoesNotExist:
            status = HTTP_403_FORBIDDEN
            self.response_format['message'] = 'Your hotel admin user does not exist.'

        except IOError as e:
            self.response_format['message'] = str(e)

        except ValidationError as e:
            status = HTTP_400_BAD_REQUEST
            self.response_format['message'] = 'Validation Errors.'
            self.response_format['errors'] = str(e)

        except Exception as e:
            api_logger.critical(
                "Exception while reset vcc voucher with parameters {data} is {error} Traceback "
                "{trace}".format(
                    data=str(data), error=str(e), trace=repr(traceback.format_exc())),
                bucket="api.v2.vcc", stage="vcc_v2_api")
            status = HTTP_500_INTERNAL_SERVER_ERROR
            self.response_format['message'] = 'Could not update the credit card password.'
            self.response_format['errors'] = str(e)
        return Response(self.response_format, status=status)
