import jwt
import datetime
from rest_framework import exceptions
from django.utils.translation import ugettext_lazy as _
from goibibo_inventory.settings import VCC_TOKEN_UUID, VCC_TOKEN_TIMEOUT
from utils.logger import Logger
from common.commonhelper import update_specific_identifier



api_logger = Logger(logger='inventoryAPILogger')

def create_vcc_token(user_id):
    token_expiry = datetime.datetime.utcnow() + datetime.timedelta(minutes=VCC_TOKEN_TIMEOUT)
    payload = {"exp": token_expiry, "user_id": user_id}
    jwt_token = jwt.encode(payload, VCC_TOKEN_UUID, algorithm='HS256')
    return jwt_token

def validate_vcc_token(jwt_token, user_id, log_identifier={}):
    
    try:
        decoded_token = jwt.decode(jwt_token, VCC_TOKEN_UUID, algorithms=["HS256"])
        update_specific_identifier('remark', 'vcc token user_id {} extranet user_id {}'.format(decoded_token.get("user_id"), user_id), log_identifier)
        api_logger.info(bucket="api.v2.vcc",stage="validate_vcc_token",identifier="{}".format(log_identifier))
        if decoded_token.get("user_id") != user_id:
            update_specific_identifier('remark', 'Invalid vcc token user mismatch {}'.format(decoded_token.get("user_id")), log_identifier)
            api_logger.critical(bucket="api.v2.vcc",stage="validate_vcc_token",identifier="{}".format(log_identifier))
            return False, 'Invalid Token'
    except jwt.ExpiredSignatureError:
        return False, 'Token expired'
    except jwt.InvalidTokenError:
        return False, 'Invalid Token'
    except Exception as e:
        return False, str(e)
    return True, ''
