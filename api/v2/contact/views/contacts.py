import traceback

from django.contrib.contenttypes.models import ContentType
from rest_framework import viewsets, status
from rest_framework.authentication import TokenAuthentication, \
    SessionAuthentication, BasicAuthentication
from rest_framework.exceptions import ValidationError
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status as http_status
from rest_framework.viewsets import GenericViewSet

from api.v1.pagination import StandardResultsSetPagination

from api.hotels.handler import string_to_bool
from api.v2.contact.serializers import GenericContactDetailSerializer
from api.v2.contact.permissions import GenericContactDetailPermission
from api.v2.contact.resources import check_contact_exists, send_otp_for_contact, verify_otp_for_contact
from api.v2.contact.serializers.contacts import OTPSerializer
from hotels.models import HotelDetail, GenericContactDetail
from hotels.methods import HotelMethods
from utils.logger import Logger

api_logger = Logger(logger='inventoryAPILogger')
hotel_method_object = HotelMethods()
hotel_content_type = ContentType.objects.get_for_model(HotelDetail)


class GenericContactDetailViewSet(viewsets.ModelViewSet):

    """
    This api will fetch contacts for a hotel and user and any other entity in
    future. Right now only hoteldetail.
    """

    serializer_class = GenericContactDetailSerializer
    authentication_classes = (TokenAuthentication, BasicAuthentication, SessionAuthentication)
    permission_classes = (IsAuthenticated, GenericContactDetailPermission)
    pagination_class = StandardResultsSetPagination

    http_method_names = ['get', 'post', 'put']

    def list(self, request, *args, **kwargs):
        """
        Gets contact detail for a hotel/user with verification status.
        Verified filter tells if the contact has gone through the OTP
        verification process.

        URL: /api/v2/contacts/?hotel=1000000498&verified=false
        METHOD: GET
        Response:

        {
           "message":[
              {
                 "code":"1000000498",
                 "verified_by":"ujjwal",
                 "verified":false,
                 "relatedto":"hoteldetail",
                 "contact":"+918130924584",
                 "contact_type":"Phone & Whatsapp",
                 "verified_on":"2018-08-07T13:22:08"
              }
           ],
           "success":true
        }
        """
        response = {'success': False, 'message': {}}
        try:
            hotel = getattr(request, 'hotel', None)
            contact_type = request.query_params.get('contact_type', [])
            verified = string_to_bool(request.query_params.get('verified', True))
            contact_filter = {'content_type_id': hotel_content_type,
                              'verified': verified, 'object_id': hotel.id}
            if contact_type:
                contact_filter['contact_type'] = contact_type
            if hotel:
                contact_objs = GenericContactDetail.objects.filter(**contact_filter)
                data = GenericContactDetailSerializer(contact_objs, many=True).data
                response['success'] = True
                response['message'] = data
        except Exception as e:
            api_logger.critical(message='Failed fetching contact detail %s %s %s' %
                                        (request.query_params, e,
                                         repr(traceback.format_exc())),
                                log_type='ingoibibo',
                                bucket='genericcontactdetailviewset',
                                stage='genericcontactdetailviewset.list')
        return Response(response)

    def create(self, request, *args, **kwargs):

        """
        Adds a contact. Before that checks that a similar entry is already there
        or not.

        :param request:
        :param args:
        :param kwargs:
        :return:

        URL: /api/v2/contacts/
        METHOD: POST
        Request :

        {
            "country_id": 103,
            "code": "1000011490",
            "country_code": "+91",
            "contact_wcc": "7086831381",
            "contact_type": "Whatsapp",
            "relatedto": "hoteldetail",
            "verified": false
        }


        Response:

       {
            "message": {
                "country_code": "+91",
                "contact_wcc": "7086831381",
                "country": 103,
                "contact_type": "Whatsapp",
                "verified": false,
                "verified_on": "2020-06-01T18:10:27.662244",
                "verified_by": "<EMAIL>",
                "relatedto": "hoteldetail",
                "code": "1000011490",
                "object_id": 26324
            },
            "success": true
        }
        """
        contact_data = request.data
        response = {'success': False, 'message': {}}
        api_status = http_status.HTTP_200_OK
        try:
            instance = check_contact_exists(contact_data)
            if instance:
                response['success'] = True
                response['message'] = 'Already exists'
            else:
                contact_data["country"] = contact_data.pop("country_id", None)
                serializer = GenericContactDetailSerializer(data=contact_data,
                                                            context={'request': request})
                serializer.is_valid(raise_exception=True)
                instance = serializer.save()
                api_status = http_status.HTTP_201_CREATED
                response['message'] = serializer.data
                response['message']['object_id'] = instance.id
                response['success'] = True

        except ValidationError as e:
            api_status = http_status.HTTP_400_BAD_REQUEST
            response["message"] = str(e)
            api_logger.critical(message="Error creating new contact entry {query_params} {traceback}"
                                .format(query_params=contact_data, traceback=repr(traceback.format_exc())),
                                log_type="ingoibibo",
                                bucket="genericcontactdetailviewset",
                                stage="genericcontactdetailviewset.create")

        except Exception as e:
            api_status = http_status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical(message='Failed saving contact detail %s %s %s' %
                                        (request.query_params, e, repr(traceback.format_exc())),
                                log_type='ingoibibo',
                                bucket='genericcontactdetailviewset',
                                stage='genericcontactdetailviewset.create')
        return Response(response, status=api_status)

    def update(self, request, *args, **kwargs):
        contact_data = request.data
        response = {'success': False, 'message': {}}
        api_status = http_status.HTTP_200_OK
        try:
            serializer = GenericContactDetailSerializer(
                data=contact_data, instance=request.contact, context={'request': request},
                partial=True)
            if serializer.is_valid():
                instance = serializer.save()
                serializer.data['object_id'] = instance.id
                response['message'] = serializer.data
                response['success'] = True
        except Exception as e:
            api_logger.critical(message='Failed updating contact detail %s %s %s' %
                                        (request.query_params, e, repr(traceback.format_exc())),
                                log_type='ingoibibo',
                                bucket='genericcontactdetailviewset',
                                stage='genericcontactdetailviewset.update')
        return Response(response, status=api_status)


def get_gcd(request):
    serialized_data = OTPSerializer(data=request.data)
    serialized_data.is_valid(raise_exception=True)

    data = serialized_data.data
    contact = GenericContactDetail(
        contact=data.get('contact'), contact_type=data.get('contact_type'), country_id=data.get('country'))

    return contact


class GenericContactSendOTPViewSet(GenericViewSet):
    authentication_classes = (TokenAuthentication, BasicAuthentication, SessionAuthentication)
    permission_classes = (IsAuthenticated,)
    http_method_names = ['post']

    def create(self, request, *args, **kwargs):
        """
            This is a generic API to send otp of a contact details provided

            URL : {{base_url}}/api/v2/generic-contacts/send-otp/
            REQUEST TYPE: POST
        """
        response = {
            'success': False,
            'message': '',
            'data': {
            }
        }
        try:
            hash_key = None
            if request.data and 'hashKey' in request.data:
                hash_key = request.data.get('hashKey')
            contact = get_gcd(request)
            send_otp_for_contact(request.user, contact,
                                 source=request.META.get('HTTP_META_DATA_SOURCE', ''), hash_key=hash_key)

            response['success'] = True
            response['message'] = "OTP Sent Successfully"
            response_status = status.HTTP_200_OK

        except (ValidationError, IOError) as e:
            response['message'] = str(e)
            response_status = status.HTTP_400_BAD_REQUEST

        except Exception as e:
            response['message'] = str(e)
            response_status = status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical(
                message='Error: %s' % str(e), log_type='ingoibibo',
                bucket='GenericContactOTPViewSetAPIv2',
                stage='contact.views.contacts.GenericContactOTPViewSet.send_otp'
            )

        return Response(response, status=response_status)


class GenericContactVerifyOTPViewSet(GenericViewSet):
    authentication_classes = (TokenAuthentication, BasicAuthentication, SessionAuthentication)
    permission_classes = (IsAuthenticated,)
    http_method_names = ['post']

    def create(self, request, *args, **kwargs):
        """
            This is a generic API to verify otp of a contact details provided

            URL : {{base_url}}/api/v2/generic-contacts/verify-otp/
            REQUEST TYPE: POST
        """
        response = {
            'success': False,
            'message': '',
            'data': {
            }
        }
        try:
            if 'otp' not in request.data:
                raise ValidationError("OTP is mandatory")

            contact = get_gcd(request)
            result, key = verify_otp_for_contact(request.user, request.data['otp'], contact)

            if result:
                response['message'] = "OTP Verified Successfully"
                response['data']['key'] = key
            else:
                response['message'] = "Invalid OTP"

            response['success'] = True
            response_status = status.HTTP_200_OK

        except (ValidationError, IOError) as e:
            response['message'] = str(e)
            response_status = status.HTTP_400_BAD_REQUEST

        except Exception as e:
            response['message'] = str(e)
            response_status = status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical(
                message='Error: %s' % str(e), log_type='ingoibibo',
                bucket='GenericContactOTPViewSetAPIv2',
                stage='contact.views.contacts.GenericContactOTPViewSet.verify_otp'
            )

        return Response(response, status=response_status)
