import time
import requests
import traceback

from rest_framework import viewsets
from rest_framework.authentication import BasicAuthentication, SessionAuthentication, TokenAuthentication
from rest_framework.decorators import detail_route, list_route
from rest_framework.exceptions import ValidationError
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status as http_status

from api.v1.pagination import StandardResultsSetPagination
from api.v1.reports.permissions.permissions import GuardianPermissionCheck
from api.v2.guest_chat.exceptions.custom_exceptions import ActionForbiddenException, ResourceNotFoundException
from api.v2.guest_chat.resources.constants import *
from api.v2.guest_chat.permissions.permissions import BulkHotelPermission,IsHotelAccessibility
from api.v2.guest_chat.resources.resources import block_customer, call_close_conversation_api, check_existing_session, \
    create_chat_session, create_custom_firebase_token, fetch_booking_details_for_funnel, fetch_broadcast_bookings, \
    fetch_policies_for_hotel, get_block_status, get_bulk_pending_chats, get_filter_message_count, \
    get_pending_chat_count, is_booking_id_valid, push_session_data_to_kafka, request_for_review, update_myra_session, \
    user_is_allotted_hotel
from api.v2.guest_chat.serializers.serializers import BulkHotelSessionSerializer, BroadcastSerializer, \
    BroadcastCategorySerializer, CloseSessionSerializer, CreateSessionSerializer, DomainSerializer, \
    FunnelBookingDetailSerializer, GetSessionSerializer, GetChatTemplatesSerializer, PushToKafkaSerializer, \
    RequestForReviewSerializer, TemplateListingSerializer, UpdateSessionSerializer
from communication.tasks import broadcast_message
from hotels.models.chat_box import HotelChatTemplate
from utils.logger import Logger
from hotels.models import HotelBooking
from hotels.models import HotelDetail

api_logger = Logger(logger='inventoryAPILogger')


class GuestChatHelperViewSet(viewsets.GenericViewSet):

    authentication_classes = (TokenAuthentication, BasicAuthentication)
    permission_classes = (IsAuthenticated,)
    pagination_class = StandardResultsSetPagination
    http_method_names = ["get", "post", "put"]

    @detail_route(methods=["get"], url_path="auth")
    def auth(self, request, *args, **kwargs):
        """
        API to fetch a custom token for client(frontend) to sign in to Firestore. It is unique for a combination of user
        ID and hotelcode.
        URL : {{BASE_URL}}/api/v2/guest-chat/helper/{{hotelcode}}/auth
        Type : GET
        Response :
        1. If the user is allotted that hotel
        {
            "token": "{{JWT token}}",
            "hotelcode": "1000011490"
        }
        2. If the user is not allotted that hotel / Server side error
        {
            "token": "",
            "hotelcode": ""
        }
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        response = {
            "token": "",
            "hotelcode": ""
        }
        try:
            hotelcode = kwargs.get("pk")
            user_obj = request.user

            if user_is_allotted_hotel(user_obj, hotelcode):
                jwt_token = create_custom_firebase_token(hotelcode, user_obj)
                response["token"] = jwt_token
                response["hotelcode"] = hotelcode
                status_code = http_status.HTTP_200_OK
            else:
                status_code = http_status.HTTP_403_FORBIDDEN

            api_logger.info("Fetched token {t} for hotelcode {h} and user ID {uid} with status code {sc}"
                            .format(t=response["token"], h=hotelcode, uid=user_obj.id, sc=status_code),
                            log_type="ingoibibo", bucket="api.v2.guest_chat", stage="auth")

        except Exception as e:
            status_code = http_status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical("Server side error creating custom guest chat token for hotelcode {h} user_id {u} "
                                "exception {e} traceback {t}".format(h=kwargs.get("hotelcode"), u=request.user.id, e=e,
                                                                     t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="auth")

        return Response(response, status=status_code)

    @detail_route(methods=["get"], url_path="pending-count")
    def pending_count(self, request, *args, **kwargs):
        """
        API to get pending chat count for both pre and post chats for a particular hotelcode.
        URL : {{BASE_URL}}/api/v2/guest-chat/helper/{{hotelcode}}/pending-count
        Type : GET
        Response :
        {
            "open_chats": {
                "pre_chats": {
                    "total": 3,
                    "pending": 1
                },
                "post_chats": {
                    "total": 1,
                    "pending": 0
                },
                "merged_chats": {
                    "total": 2,
                    "pending": 1
                }
            },
            "closed_chats": {
                "pre_chats": {
                    "total": 2,
                    "pending": 2
                },
                "post_chats": {
                    "total": 5,
                    "pending": 1
                },
                "merged_chats": {
                    "total": 3,
                    "pending": 0
                }
            }
        }
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        response = {
            "open_chats": {
                "pre_chats": {
                    "total": 0,
                    "pending": 0
                },
                "post_chats": {
                    "total": 0,
                    "pending": 0
                },
                "merged_chats": {
                    "total": 0,
                    "pending": 0
                }
            },
            "closed_chats": {
                "pre_chats": {
                    "total": 0,
                    "pending": 0
                },
                "post_chats": {
                    "total": 0,
                    "pending": 0
                },
                "merged_chats": {
                    "total": 0,
                    "pending": 0
                }
            }
        }
        try:
            hotelcode = kwargs.get("pk")

            if user_is_allotted_hotel(request.user, hotelcode):
                response = get_pending_chat_count(hotelcode)
                status_code = http_status.HTTP_200_OK
            else:
                status_code = http_status.HTTP_403_FORBIDDEN

            api_logger.info("Pending count API called for hotelcode {h} with response {r} status_code {sc}"
                            .format(h=hotelcode, r=response, sc=status_code),
                            log_type="ingoibibo", bucket="api.v2.guest_chat", stage="pending-count")

        except Exception as e:
            status_code = http_status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical("Server side error in calling pending count API with exception {e} traceback {t}"
                                .format(e=e, t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="pending-count")

        return Response(response, status=status_code)

    @detail_route(methods=["get"], url_path="message-count")
    def message_count(self, request, *args, **kwargs):
        """
        API to get filter messages count for a particular hotelcode.
        URL : {{BASE_URL}}/api/v2/guest-chat/helper/{{hotelcode}}/message-count
        Type : GET
        Response :
        {
            "total_open": 1,
            "total_unread": 0,
            "total_closed": 0,
            "open_prechat": 0,
            "open_postchat": 1,
            "open_mergedchat": 0,
            "open_pending": 1,
            "open_starred": 1
        }
        """
        response = {
            "total_open": 0,
            "total_unread": 0,
            "total_closed": 0,
            "open_prechat": 0,
            "open_postchat": 0,
            "open_mergedchat": 0,
            "open_pending": 0,
            "open_starred": 0
        }
        try:
            hotelcode = kwargs.get("pk")

            if user_is_allotted_hotel(request.user, hotelcode):
                response = get_filter_message_count(hotelcode)
                status_code = http_status.HTTP_200_OK
            else:
                status_code = http_status.HTTP_403_FORBIDDEN

            api_logger.info("Message count count API called for hotelcode {h} with response {r} status_code {sc}"
                            .format(h=hotelcode, r=response, sc=status_code),
                            log_type="ingoibibo", bucket="api.v2.guest_chat", stage="message-count")

        except Exception as e:
            status_code = http_status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical("Server side error in calling message count API exception {e} traceback {t}"
                                .format(e=e, t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="message-count")

        return Response(response, status=status_code)

    @detail_route(methods=["post"], url_path="broadcast")
    def broadcast(self, request, *args, **kwargs):
        """
        API to get broadcast a message to a list of bookings.
        URL : {{BASE_URL}}/api/v2/guest-chat/helper/{{hotelcode}}/broadcast
        Type : POST
        Request data :
        {
            "message": "Hello test",
            "attachments": "a",
            "bookings": [
                {
                    "booking_id": "HTLXY97NBF",
                    "vendor": "GI"
                },
                {
                    "booking_id": "NH7307658619338",
                    "vendor": "MMT"
                },
                {
                    "booking_id": "HTLFM2DED4",
                    "vendor": "GI"
                },
                {
                    "booking_id": "HTL2MHVBQS",
                    "vendor": "GI"
                },
                {
                    "booking_id": "HTL7HK9UQM",
                    "vendor": "GI"
                },
                {
                    "booking_id": "HTL7CNNA6Z",
                    "vendor": "GI"
                },
                {
                    "booking_id": "NH9307658618990",
                    "vendor": "GI"
                },
                {
                    "booking_id": "NH7403358619342",
                    "vendor": "MMT"
                },
                {
                    "booking_id": "HTLPDV34DD",
                    "vendor": "GI"
                },
                {
                    "booking_id": "HTLB2B6A7GWGA",
                    "vendor": "GI"
                },
                {
                    "booking_id": "HTLMTTALYU",
                    "vendor": "GI"
                }
            ]
        }
        Response :
        {
            "success": True,
            "message": "Messages broadcast successfully."
        }
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        response = {
            "success": False,
            "message": ""
        }
        try:
            hotelcode = kwargs.get("pk")

            if user_is_allotted_hotel(request.user, hotelcode):
                serializer = BroadcastSerializer(data=request.data)
                serializer.is_valid(raise_exception=True)
                serialized_data = serializer.data

                bookings = serialized_data.get("bookings")
                message = serialized_data.get("message")
                attachments = serialized_data.get("attachments")

                broadcast_message(hotelcode, bookings, message, attachments)

                response["success"] = True
                response["message"] = API_SUCCESS_MESSAGE["broadcast"]
                status_code = http_status.HTTP_202_ACCEPTED

            else:
                status_code = http_status.HTTP_403_FORBIDDEN

            api_logger.info("Broadcast API called for hotelcode {h} status code {sc}"
                            .format(h=hotelcode, sc=status_code),
                            log_type="ingoibibo", bucket="api.v2.guest_chat", stage="broadcast")

        except ValidationError as e:
            response["message"] = str(e)
            status_code = http_status.HTTP_400_BAD_REQUEST
            api_logger.critical("Client side error in broadcast message API with exception {e} traceback {t}"
                                .format(e=e, t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="broadcast")

        except Exception as e:
            status_code = http_status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical("Server side error in broadcast message API with exception {e} traceback {t}"
                                .format(e=e, t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="broadcast")

        return Response(response, status=status_code)

    @detail_route(methods=["get"], url_path="detail",permission_classes=(IsHotelAccessibility, ))
    def booking_details(self, request, *args, **kwargs):
        """
        API used by funnel to fetch booking details, for eg., Ingo hotelcode, checkin, checkout, etc.
        URL : {{BASE_URL}}/api/v2/guest-chat/helper/{{vendor_booking_id}}/detail
        Type : GET
        Query params : domain (mandatory)
        Eg. : {{BASE_URL}}/api/v2/guest-chat/helper/HTLMTTALYU/detail/?domain=GI
        Response :
        {
            "message": "",
            "data": {
                "status": "Confirmed",
                "hotelcode": "1000009561",
                "adults": 2,
                "iconUrl": "",
                "mobile": "+917086831381",
                "checkout": "2021-01-27",
                "checkin": "2021-01-24",
                "mmtBlack": false,
                "email": "<EMAIL>",
                "contextDesc": "Thomas Villa, Hotel and Cottages, Manali",
                "nameOfCustomer": "Praneet Kumar",
                "platform": "goibibo",
                "child": 0,
                "roomCount": 1,
                "isStarred": false,
                "goTribe": true,
                "bookingDate": "2020-04-03"
            },
            "success": true
        }
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        response = {
            "success": False,
            "message": "",
            "data": {}
        }
        try:
            booking_id = kwargs.get("pk")
            serializer = FunnelBookingDetailSerializer(data=request.query_params)
            serializer.is_valid(raise_exception=True)
            serialized_data = serializer.data

            domain = serialized_data.get("domain")
            uuid = serialized_data.get("custUuid")
            
            # Check if the booking ID is valid
            # if not booking_id:
            #     raise ValidationError("Booking ID is required")
                
            # try:
            #     booking = HotelBooking.objects.get(vendorbookingid=booking_id)
                
            #     # Check if the request object has a hotel attribute
            #     if not hasattr(request, 'hotel'):
            #         raise ValidationError("Hotel object is not present in the request")
            #     if booking.hotel.id != request.hotel.id:
            #         raise ActionForbiddenException("This booking does not belong to the hotel you are trying to access.")
            # except HotelBooking.DoesNotExist:
            #     raise ActionForbiddenException("Invalid Booking Id")
            success, message, data, status_code = fetch_booking_details_for_funnel(booking_id, domain, uuid)

            response["success"] = success
            response["message"] = message
            response["data"] = data

            api_logger.info("Booking detail API called by funnel for booking ID {b} domain {d} with status code {sc} "
                            "message {m} success {s}".format(b=booking_id, d=domain, sc=status_code, m=message,
                                                             s=success),
                            log_type="ingoibibo", bucket="api.v2.guest_chat", stage="booking_details")

        except ActionForbiddenException as e:
            response["message"] = str(e)
            status_code = http_status.HTTP_403_FORBIDDEN
            api_logger.critical("Unauthorized user {u} trying to access booking details for booking ID {b}"
                                .format(u=uuid, b=booking_id), log_type="ingoibibo", bucket="api.v2.guest_chat",
                                stage="booking_details")

        except ValidationError as e:
            response["message"] = str(e)
            status_code = http_status.HTTP_400_BAD_REQUEST
            api_logger.critical("Client side error in fetching booking detail API called by funnel for query params "
                                "{qp} exception {e} traceback {t}".format(qp=request.query_params, e=e,
                                                                          t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="booking_details")

        except Exception as e:
            status_code = http_status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical("Server side error in fetching booking detail API called by funnel for query params "
                                "{qp} exception {e} traceback {t}".format(qp=request.query_params, e=e,
                                                                          t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="booking_details")

        return Response(response, status=status_code)

    @list_route(methods=["get"], url_path="broadcast-categories")
    def broadcast_categories(self, request, *args, **kwargs):
        """
        API to return list of broadcast categories.
        URL : {{BASE_URL}}/api/v2/guest-chat/helper/broadcast-categories
        Type : GET
        Response :
        [
            {
                "id": 1,
                "label": "Yesterday's Check-ins"
            },
            {
                "id": 2,
                "label": "Today's Check-ins"
            },
            {
                "id": 3,
                "label": "Today's Check-outs"
            },
            {
                "id": 4,
                "label": "Tomorrow's Check-ins"
            },
            {
                "id": 5,
                "label": "Tomorrow's Check-outs"
            },
            {
                "id": 6,
                "label": "Upcoming confirmed bookings"
            }
        ]
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        return Response(BROADCAST_CATEGORIES_DESCRIPTION, status=http_status.HTTP_200_OK)

    @detail_route(methods=["get"], url_path="broadcast-bookings")
    def broadcast_bookings(self, request, *args, **kwargs):
        """
        API to fetch list of bookings for a hotel based on broadcast category ID.
        URL : {{BASE_URL}}/api/v2/guest-chat/helper/{{hotelcode}}/broadcast-bookings
        Type : GET
        Query params : category_id (mandatory)
        Eg : {{BASE_URL}}/api/v2/guest-chat/helper/1000009561/broadcast-bookings?category_id=4
        Response :
        {
            "count": 3,
            "message": "Broadcast booking list fetched successfully.",
            "data": [
                {
                    "guest_name": "Ashish Kumar",
                    "vendor_booking_id": "HTLS7CYNQQ",
                    "checkin": "2021-02-10",
                    "booking_domain": "GI",
                    "pah": false,
                    "checkout": "2021-02-13"
                },
                {
                    "guest_name": "Vinod  Kumar",
                    "vendor_booking_id": "NH7525467100832",
                    "checkin": "2021-02-10",
                    "booking_domain": "MMT",
                    "pah": false,
                    "checkout": "2021-02-12"
                },
                {
                    "guest_name": "Sils Cyriac",
                    "vendor_booking_id": "HTL3LG75TB",
                    "checkin": "2021-02-10",
                    "booking_domain": "GI",
                    "pah": false,
                    "checkout": "2021-02-13"
                }
            ],
            "success": true
        }
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        response = {
            "success": False,
            "message": "",
            "data": [],
            "count": 0,
        }
        try:
            hotelcode = kwargs.get("pk")
            if user_is_allotted_hotel(request.user, hotelcode):
                serializer = BroadcastCategorySerializer(data=request.query_params)
                serializer.is_valid(raise_exception=True)
                serializer_data = serializer.data

                category_id = serializer_data.get("category_id")

                booking_list = fetch_broadcast_bookings(hotelcode, category_id, request.META)

                response["success"] = True
                response["message"] = API_SUCCESS_MESSAGE["broadcast_bookings"]
                response["data"] = booking_list
                response["count"] = len(response["data"])
                status_code = http_status.HTTP_200_OK

            else:
                status_code = http_status.HTTP_403_FORBIDDEN

            api_logger.info("Get broadcast booking list API called for hotelcode {h} response {r} status code {sc}"
                            .format(h=hotelcode, r=response, sc=status_code),
                            log_type="ingoibibo", bucket="api.v2.guest_chat", stage="broadcast_bookings")

        except ValidationError as e:
            response["message"] = str(e)
            status_code = http_status.HTTP_400_BAD_REQUEST
            api_logger.critical("Client side error in calling get broadcast booking list API for query params {qp} "
                                "exception {e} traceback {t}".format(qp=request.query_params, e=e,
                                                                     t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="broadcast_bookings")

        except Exception as e:
            response["message"] = API_ERROR_MESSAGE["broadcast_bookings_unavailable"]
            status_code = http_status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical("Server side error in calling get broadcast booking list API for query params {qp} "
                                "exception {e} traceback {t}".format(qp=request.query_params, e=e,
                                                                     t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="broadcast_bookings")

        return Response(response, status=status_code)

    @detail_route(methods=["get"], url_path="policies", permission_classes=[GuardianPermissionCheck])
    def get_hotel_policies(self, request, *args, **kwargs):
        """
        API to fetch all the answered policies for a property.
        URL : {{BASE_URL}}/api/v2/guest-chat/helper/{{hotelcode}}/policies
        Type : GET
        Request params : None
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        response = {
            "success": False,
            "message": "",
            "data": []
        }
        try:
            hotelcode = kwargs.get("pk")

            response["data"] = fetch_policies_for_hotel(hotelcode)
            response["success"] = True
            status_code = http_status.HTTP_200_OK

            api_logger.info("Fetched policies API called for hotelcode {h}".format(h=hotelcode),
                            log_type="ingoibibo", bucket="api.v2.guest_chat", stage="get_hotel_policies")

        except KeyError as e:
            response["message"] = "Mandatory key not present in response."
            status_code = http_status.HTTP_404_NOT_FOUND
            api_logger.critical("Key error in fetch policies API exception {e} traceback {t}"
                                .format(e=e, t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="get_hotel_policies")

        except TypeError as e:
            response["message"] = "Unexpected error. Please try again later!"
            status_code = http_status.HTTP_409_CONFLICT
            api_logger.critical("Type error in fetch policies API exception {e} traceback {t}"
                                .format(e=e, t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="get_hotel_policies")

        except Exception as e:
            response["message"] = "Unexpected error. Please try again later!"
            status_code = http_status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical("Server side error in fetch policies API exception {e} traceback {t}"
                                .format(e=e, t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="get_hotel_policies")

        return Response(response, status=status_code)

    @detail_route(methods=["post"], url_path="request-review", permission_classes=[GuardianPermissionCheck])
    def request_for_review(self, request, *args, **kwargs):
        """
        API to request a user for review of his / her / them booking.
        URL : {{BASE_URL}}/api/v2/guest-chat/helper/{{hotelcode}}/request-review/
        Type : POST
        Request data :
        {
            "booking_id": "HTLMTTALYU",
            "domain": "GI"
        }
        Response :
        1. When request is sent successfully.
        {
            "success": True,
            "message": "Your request has been sent successfully."
        }
        2. When user has already requested a review.
        {
            "success": False,
            "message": "You have already requested this user for review!"
        }
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        response = {
            "success": False,
            "message": ""
        }
        try:
            hotelcode = kwargs.get("pk")

            serializer = RequestForReviewSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            serialized_data = serializer.data

            booking_id = serialized_data.get("booking_id")
            domain = serialized_data.get("domain")

            request_for_review(hotelcode, booking_id, domain)

            response["success"] = True
            response["message"] = API_SUCCESS_MESSAGE["request_for_review"]
            status_code = http_status.HTTP_200_OK

            api_logger.info("Request for review API called for booking ID {bid} domain {d} hotelcode {h} response {r}"
                            .format(bid=booking_id, d=domain, h=hotelcode, r=response),
                            log_type="ingoibibo", bucket="api.v2.guest_chat", stage="request_for_review")

        except ValidationError as e:
            response["message"] = str(e)
            status_code = http_status.HTTP_400_BAD_REQUEST
            api_logger.critical("Client side error in request to review API exception {e} traceback {t}"
                                .format(e=e, t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="request_for_review")

        except IOError as e:
            response["message"] = API_ERROR_MESSAGE["generic_error"]
            status_code = http_status.HTTP_400_BAD_REQUEST
            api_logger.critical("IOError while calling request for review API request data {d} exception {e} "
                                "traceback {t}".format(d=request.data, e=e, t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="request_for_review")

        except ResourceNotFoundException as e:
            response["message"] = str(e)
            status_code = http_status.HTTP_404_NOT_FOUND
            api_logger.info("Resource not found exception for request to review API exception {e}".format(e=e),
                            log_type="ingoibibo", bucket="api.v2.guest_chat", stage="request_for_review")

        except ActionForbiddenException as e:
            response["message"] = str(e)
            status_code = http_status.HTTP_403_FORBIDDEN
            api_logger.critical("Action forbidden exception {e} for request to review API".format(e=e),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="request_for_review")

        except Exception as e:
            response["message"] = API_ERROR_MESSAGE["generic_error"]
            status_code = http_status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical("Server side error in request to review API exception {e} traceback {t}"
                                .format(e=e, t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="request_for_review")

        return Response(response, status=status_code)

    @list_route(methods=["post"], url_path="templates", permission_classes=[BulkHotelPermission])
    def fetch_templates(self, request, *args, **kwargs):
        """
        API to fetch active templates for a list of hotelcodes.
        URL : {{BASE_URL}}/api/v2/guest-chat/helper/templates/
        Type : POST
        Request data :
        {
            "hotelcodes": ["1000115873", "1000144126"]  (mandatory)
            "category": "Welcome Mail" (optional)
            "is_scheduled": True    (optional)
        }
        Response :
        {
            "message": "",
            "data": {
                "count": 3,
                "next": null,
                "previous": null,
                "results": [
                    {
                        "id": 1,
                        "hotelcode": "1000102753",
                        "template_name": null,
                        "schedule_type": "Day(s)",
                        "filter_key": "checkin",
                        "filter_operator": "before",
                        "filter_value": "5",
                        "is_scheduled": true,
                        "message": "Hello how r u?",
                        "category": "Welcome Mail"
                    },
                    {
                        "id": 6,
                        "hotelcode": "1000102753",
                        "template_name": null,
                        "schedule_type": "Day(s)",
                        "filter_key": "checkin",
                        "filter_operator": "before",
                        "filter_value": "5",
                        "is_scheduled": true,
                        "message": "Hello how r u?",
                        "category": "Welcome Mail"
                    },
                    {
                        "id": 5,
                        "hotelcode": "1000102753",
                        "template_name": null,
                        "schedule_type": "Day(s)",
                        "filter_key": "checkin",
                        "filter_operator": "before",
                        "filter_value": "5",
                        "is_scheduled": true,
                        "message": "Hello how r u?",
                        "category": "Welcome Mail"
                    }
                ]
            },
            "success": false
        }
        :param request:
        :param args:
        :param kwargs:
        """
        response = {
            "success": False,
            "message": "",
            "data": []
         }
        try:
            template_serializer = GetChatTemplatesSerializer(data=request.data)
            template_serializer.is_valid(raise_exception=True)
            template_serializer_data = template_serializer.data

            hotelcodes = template_serializer_data.get("hotelcodes")
            category = template_serializer_data.get("category")
            is_scheduled = template_serializer_data.get("is_scheduled")

            # Fetch all active templates.
            queryset = HotelChatTemplate.objects.filter(hotel__hotelcode__in=hotelcodes, isactive=True)\
                .order_by("-modifiedon")

            # Filter by category if required.
            if category:
                queryset = queryset.filter(category=category)

            # Filter by is_scheduled if required.
            if is_scheduled is not None:
                queryset = queryset.filter(is_scheduled=is_scheduled)

            # Paginate the response
            page = self.paginate_queryset(queryset)
            listing_serializer = TemplateListingSerializer(page, many=True)
            paginated_response = self.get_paginated_response(listing_serializer.data)

            response["data"] = paginated_response.data
            status_code = http_status.HTTP_200_OK
            api_logger.info("Successfully fetched templates for request data {rd}".format(rd=request.data),
                            log_type="ingoibibo", bucket="api.v2.guest_chat", stage="fetch_templates")

        except ValidationError as e:
            response["message"] = str(e)
            status_code = http_status.HTTP_400_BAD_REQUEST
            api_logger.critical("Client side error in get chat templates API request data {rd} exception {e} traceback "
                                "{t}".format(rd=request.data, e=e, t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="fetch_templates")

        except Exception as e:
            status_code = http_status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical("Server side error in get chat templates API request data {rd} exception {e} traceback "
                                "{t}".format(rd=request.data, e=e, t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="fetch_templates")

        return Response(response, status=status_code)

    @list_route(methods=["post"], url_path="bulk-pending-chats", permission_classes=[BulkHotelPermission])
    def bulk_pending_chats(self, request, *args, **kwargs):
        """
        API to fetch count of pending chats for list of hotelcodes. It's a wrapper API over Sandesh.
        URL : {{BASE_URL}}/api/v2/guest-chat/helper/bulk-pending-chats/
        Type : POST
         Request data :
        {
            "hotelcodes": ["1000000983", "1000001881", "1000002781", "1000009561"],
            "brand": ["all"]
        }
        Response :
        {
            "success": true,
            "message": "",
            "data": [
                {
                    "hotelcode": "1000000983",
                    "total_unreplied": 2,
                    "prechat_unreplied": 2,
                    "postchat_unreplied": 0
                },
                {
                    "hotelcode": "1000001881",
                    "total_unreplied": 1,
                    "prechat_unreplied": 0,
                    "postchat_unreplied": 1
                },
                {
                    "hotelcode": "1000002781",
                    "total_unreplied": 2,
                    "prechat_unreplied": 1,
                    "postchat_unreplied": 1
                }
            ]
        }
        :param request:
        :param args:
        :param kwargs:
        """
        response = {
            "success": False,
            "message": "",
            "data": []
        }
        try:
            serializer = BulkHotelSessionSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            serialized_data = serializer.data

            response = get_bulk_pending_chats(serialized_data)
            status_code = response.status_code
            response = response.json()

            api_logger.info("Bulk hotel pending chats API called for request data {rd}".format(rd=request.data),
                            log_type="ingoibibo", bucket="api.v2.guest_chat", stage="bulk_pending_chats")

        except ValidationError as e:
            response["message"] = str(e)
            status_code = http_status.HTTP_400_BAD_REQUEST
            api_logger.critical("Client side error in bulk hotel pending chats API exception {e} traceback {t}"
                                .format(e=e, t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="fetch_templates")

        except Exception as e:
            response["message"] = str(repr(traceback.format_exc()))
            status_code = http_status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical("Client side error in bulk hotel pending chats API exception {e} traceback {t}"
                                .format(e=e, t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="fetch_templates")

        return Response(response, status=status_code)


class GuestChatUserViewSet(viewsets.ViewSet):

    authentication_classes = (TokenAuthentication, BasicAuthentication)
    permission_classes = (IsAuthenticated,)
    http_method_names = ["get", "post"]
    lookup_field = "uuid"

    @detail_route(methods=["post"], url_path="block")
    def block_user(self, request, *args, **kwargs):
        """
        API to block a user based on his/her UUID. It's a wrapper API which calls Myra's block user API.
        URL : {{BASE_URL}}/api/v2/guest-chat/user/{{customer_uuid}}/block/
        Type : POST
        Request data :
        {
            "domain": "GI"
        }
        Response :
        {
            "message": "User blocked successfully.",
            "success": true
        }
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        response = {
            "success": False,
            "message": ""
        }
        try:
            uuid = kwargs.get("uuid")

            serializer = DomainSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            serialized_data = serializer.data

            success, message, status_code = block_customer(uuid, serialized_data.get("domain"))

            response["success"] = success
            response["message"] = message

            api_logger.info("Block User API called for UUID {id} with success {s} and status code {sc}"
                            .format(id=uuid, s=success, sc=status_code),
                            log_type="ingoibibo", bucket="api.v2.guest_chat", stage="block_user")

        except ValidationError as e:
            response["message"] = str(e)
            status_code = http_status.HTTP_400_BAD_REQUEST
            api_logger.critical("Client side error in calling block user API for request data {rd} with exception {e} "
                                "traceback {t}".format(rd=request.data, e=e, t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="block_user")

        except Exception as e:
            response["message"] = API_ERROR_MESSAGE["block_user_failed"]
            status_code = http_status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical("Client side error in calling block user API for request data {rd} with exception {e} "
                                "traceback {t}".format(rd=request.data, e=e, t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="block_user")

        return Response(response, status_code)

    @detail_route(methods=["get"], url_path="block-status")
    def get_user_block_status(self, request, *args, **kwargs):
        """
        API to check whether a user is blocked or not. It is a wrapper API which calls Myra's block status API.
        URL : {{BASE_URL}}/api/v2/guest-chat/user/{{customer_uuid}}/block-status
        Type : GET
        Query params : domain (mandatory)
        Eg. : {{BASE_URL}}/api/v2/guest-chat/user/g2ky0x2w1kyj3xde5/block-status?domain=GI
        Response :
        {
            "success": true,
            "user_blocked": true
        }
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        response = {
            "success": False,
            "user_blocked": False
        }
        try:
            uuid = kwargs.get("uuid")

            serializer = DomainSerializer(data=request.query_params)
            serializer.is_valid(raise_exception=True)
            serialized_data = serializer.data

            success, user_blocked, status_code = get_block_status(uuid, serialized_data.get("domain"))

            response["success"] = success
            response["user_blocked"] = user_blocked

            api_logger.info("Get block status of user API called for UUID {id} with status code {sc}"
                            .format(id=uuid, sc=status_code),
                            log_type="ingoibibo", bucket="api.v2.guest_chat", stage="get_user_block_status")

        except ValidationError as e:
            status_code = http_status.HTTP_400_BAD_REQUEST
            api_logger.critical("Client side error in calling get block user status API for request data {rd} with "
                                "exception {e} traceback {t}".format(rd=request.data, e=e,
                                                                     t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="get_user_block_status")

        except Exception as e:
            status_code = http_status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical("Server side error in calling get block user status API for request data {rd} with "
                                "exception {e} traceback {t}".format(rd=request.data, e=e,
                                                                     t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="get_user_block_status")

        return Response(response, status_code)


class GuestChatSessionViewSet(viewsets.ViewSet):

    authentication_classes = (TokenAuthentication, BasicAuthentication, SessionAuthentication)
    permission_classes = (IsAuthenticated,)
    http_method_names = ["get", "post", "put"]

    @staticmethod
    def list(request, *args, **kwargs):
        """
        API to get a session ID if exists, for a particular hotel and booking ID.
        URL : {{BASE_URL}}/api/v2/guest-chat/session/
        Type : GET
        Query Params : hotelcode (mandatory), vendor_booking_id (mandatory)
        Eg. : {{BASE_URL}}/api/v2/guest-chat/session/?hotelcode=1000009561&vendor_booking_id=HTLMTTALYU
        Response :
        1. If session ID exists
        {
            "session_id": "MMT-GIA-gpd1ox64nd3lrqgnz-1600151878468"
        }
        2. If session ID does not exist / Client side error / Server side error
        {
            "session_id": ""
        }
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        response = {
            "session_id": ""
        }
        try:
            serializer = GetSessionSerializer(data=request.query_params)
            serializer.is_valid(raise_exception=True)

            serialized_data = serializer.data
            hotelcode = serialized_data.get("hotelcode")
            booking_id = serialized_data.get("vendor_booking_id")

            # Fetch session ID only if user is allotted that hotel.
            if is_booking_id_valid(booking_id, "")[0]:
                if user_is_allotted_hotel(request.user, hotelcode):
                    response["session_id"] = check_existing_session(hotelcode, booking_id)
                    status_code = http_status.HTTP_200_OK
                else:
                    status_code = http_status.HTTP_403_FORBIDDEN
            else:
                status_code = http_status.HTTP_400_BAD_REQUEST

            api_logger.info("Session ID {sid} returned for hotelcode {h} booking_id {bid} with status code {sc}"
                            .format(sid=response["session_id"], h=hotelcode, bid=booking_id, sc=status_code),
                            log_type="ingoibibo", bucket="api.v2.guest_chat", stage="get_session")

        except ValidationError as e:
            status_code = http_status.HTTP_400_BAD_REQUEST
            api_logger.critical("Client side error in get session API query_params {qp} exception {e} traceback {t}"
                                .format(qp=request.query_params, e=e, t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="get_session")

        except Exception as e:
            status_code = http_status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical("Server side error in get session API query_params {qp} exception {e} traceback {t}"
                                .format(qp=request.query_params, e=e, t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="get_session")

        return Response(response, status=status_code)

    @staticmethod
    def create(request, *args, **kwargs):
        """
        API to create a new session. It's a wrapper API which calls Myra's create session API.
        URL : {{BASE_URL}}/api/v2/guest-chat/session/
        Type : POST
        Request data :
        {
            "hotelcode": "1000009561",
            "vendor_booking_id": "HTLMTTALYU",
            "booking_domain": "GI"
        }
        Response :
        1. If a session is created successfully
        {
            "message": "Session created successfully.",
            "session_id": "MMT-GIA-gpd1ox64nd3lrqgnz-1600151762987",
            "success": true
        }
        2. If a session already exists for this booking
        {
            "message": "A session already exists for this booking ID.",
            "session_id": "MMT-GIA-gpd1ox64nd3lrqgnz-1600151878468",
            "success": false
        }
        3. If an invalid booking is passed
        {
            "message": "Unable to form metadata for this booking ID.",
            "session_id": "",
            "success": false
        }
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        response = {
            "success": False,
            "message": "",
            "session_id": None
        }
        try:
            start_time = time.time()
            serializer = CreateSessionSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            success, message, session_id, status_code = create_chat_session(serializer.data, request.user,
                                                                            check_hotel_allotment=True)

            response["success"] = success
            response["message"] = message
            response["session_id"] = session_id

            end_time = time.time()
            api_response_time = round(end_time - start_time, 2)

            api_logger.info("Create session API response {r} for request data {d} response time {s} seconds."
                            .format(r=response, d=serializer.data, s=api_response_time),
                            log_type="ingoibibo", bucket="api.v2.guest_chat", stage="create_session")

        except ActionForbiddenException as e:
            response["message"] = str(e)
            status_code = http_status.HTTP_403_FORBIDDEN
            api_logger.critical("Forbidden exception {e} in create session API request data {d}"
                                .format(e=e, d=request.data), log_type="ingoibibo", bucket="api.v2.guest_chat",
                                stage="create_session")

        except ValidationError as e:
            response["message"] = str(e)
            status_code = http_status.HTTP_400_BAD_REQUEST
            api_logger.critical("Client side error in create session API with request data {d} exception {e} traceback "
                                "{t}".format(d=request.data, e=e, t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="create_session")

        except Exception as e:
            status_code = http_status.HTTP_500_INTERNAL_SERVER_ERROR
            response["message"] = API_ERROR_MESSAGE["session_not_created"]
            api_logger.critical("Server side error in create session API with request data {d} exception {e} traceback "
                                "{t}".format(d=request.data, e=e, t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="create_session")

        return Response(response, status=status_code)

    @staticmethod
    def put(request, *args, **kwargs):
        """
        API to update infoAttr of a session. Usually used to update is_pending value for a session. It's a wrapper API
        which calls Myra's update session API.
        URL : {{BASE_URL}}/api/v2/guest-chat/session/
        Type : PUT
        Request data :
        {
            "booking_id": "HTLMTTALYU",
            "customer_uuid": "gpd1ox64nd3lrqgnz",
            "attributes": {
                "is_pending": false
            }
        }
        Any number of key value pairs can be passed in "attributes" key. If the corresponding key exists in the
        infoAttr of that session, it's value will be updated. If it doesn't exist, the key value pair will be added.
        Response :
        1. If the infoAttr was updated successfully.
        {
            "success": true
        }
        2. If the infoAttr was not updated successfully.
        {
            "success": false
        }
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        response = {
            "success": False
        }
        try:
            serializer = UpdateSessionSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            response["success"], status_code = update_myra_session(serializer.data)

            api_logger.info("Update session API response {r} for request data {d}"
                            .format(r=response, d=serializer.data),
                            log_type="ingoibibo", bucket="api.v2.guest_chat", stage="update_session")

        except ValidationError as e:
            status_code = http_status.HTTP_400_BAD_REQUEST
            api_logger.critical("Client side error in update session API with request data {d} exception {e} traceback "
                                "{t}".format(d=request.data, e=e, t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="update_session")

        except Exception as e:
            status_code = http_status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical("Server side error in update session API with request data {d} exception {e} traceback "
                                "{t}".format(d=request.data, e=e, t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="update_session")

        return Response(response, status=status_code)

    @list_route(methods=["put"], url_path="close")
    def close(self, request, *args, **kwargs):
        """
        API to close a session. It's a wrapper API which calls Myra's close session API.
        URL : {{BASE_URL}}/api/v2/guest-chat/session/close/
        Type : PUT
        Request data :
        {
            "hotelcode": "1000009561",
            "domain": "MMT",
            "session_id": "MMT-MYRA-UPM0FGV9X2L-1609233461489"
        }
        Response :
        {
            "success": true
        }
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        response = {
            "success": False
        }
        try:
            serializer = CloseSessionSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            serializer_data = serializer.data

            hotelcode = serializer_data.get("hotelcode")
            domain = serializer_data.get("domain")
            session_id = serializer_data.get("session_id")

            if user_is_allotted_hotel(request.user, hotelcode):
                response["success"] = call_close_conversation_api(session_id, domain)
                status_code = http_status.HTTP_200_OK
            else:
                status_code = http_status.HTTP_403_FORBIDDEN

            api_logger.info("Close conversation API called for session ID {sid} with response {r}"
                            .format(sid=session_id, r=response),
                            log_type="ingoibibo", bucket="api.v2.guest_chat", stage="close_session")

        except ValidationError as e:
            status_code = http_status.HTTP_400_BAD_REQUEST
            api_logger.critical("Client side error in calling close conversation API with request data {rd} exception "
                                "{e} traceback {t}"
                                .format(rd=request.data, e=e, t=repr(traceback.format_exc()),
                                        log_type="ingoibibo", bucket="api.v2.guest_chat", stage="update_session"))

        except Exception as e:
            status_code = http_status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical("Server side error in calling close conversation API with request data {rd} exception "
                                "{e} traceback {t}"
                                .format(rd=request.data, e=e, t=repr(traceback.format_exc()),
                                        log_type="ingoibibo", bucket="api.v2.guest_chat", stage="update_session"))

        return Response(response, status=status_code)

    @list_route(methods=["post"], url_path="kafka")
    def push_to_kafka(self, request, *args, **kwargs):
        """
        API to push any changes to session to Kafka. It is called only via Google cloud functions whenever a session
        update happens.
        URL : {{BASE_URL}}/api/v2/guest-chat/session/kafka
        Type : POST
        Request data :
        {
            "agentName": null,
            "attr1": null,
            "attr2": null,
            "attr3": null,
            "attr4": null,
            "chatOwner": "HOST",
            "chatStatus": "OPEN",
            "chatType": null,
            "contextAttr": {},
            "contextDesc": "Southern Residency, Chennai",
            "contextKey": "PostSalesBooking_HotelHTLFM2DED4HOSTg9erpx1kowl9eqvj1",
            "custChannel": "GIA",
            "custUUD": "g9erpx1kowl9eqvj1",
            "entityKey": "HTLFM2DED4",
            "entityType": "PostSalesBooking_Hotel",
            "iconUrl": "https://gos3.ibcdn.com/user_icon-1567150785.png",
            "infoAttr": {
                "adults": "1",
                "bookingDate": "2020-11-04",
                "checkin": "2020-11-04",
                "checkout": "2020-11-05",
                "child": "0",
                "email": "<EMAIL>",
                "isStarred": "false",
                "mobile": "+919550661971",
                "platform": "goibibo",
                "roomCount": "1",
                "status": "Reconfirmed"
            },
            "intentLabel": null,
            "lastChat": "Hello testing.",
            "merged": false,
            "nameOfCustomer": "Suresh U",
            "notificationEnabled": true,
            "sessionId": "GI-GIA-g9erpx1kowl9eqvj1-1604680750612",
            "taskId": null,
            "time": {
                "_seconds": 1604682053,
                "_nanoseconds": 788000000
            }
        }
        Response :
        {
            "success": True
        }
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        response = {
            "success": False
        }
        try:
            serializer = PushToKafkaSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            serializer_data = serializer.data

            status = push_session_data_to_kafka(serializer_data)

            if status:
                response["success"] = True
                status_code = http_status.HTTP_200_OK
            else:
                status_code = http_status.HTTP_500_INTERNAL_SERVER_ERROR

            api_logger.info("Push Kafka packet API called with request data {rd} and response {r} status_code {sc}"
                            .format(rd=request.data, r=response, sc=status_code),
                            log_type="ingoibibo", bucket="api.v2.guest_chat", stage="push_to_kafka")

        except ValidationError as e:
            status_code = http_status.HTTP_400_BAD_REQUEST
            api_logger.critical("Client side error in push to Kafka API with request data {d} exception {e} traceback "
                                "{t}".format(d=request.data, e=e, t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="push_to_kafka")

        except Exception as e:
            status_code = http_status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical("Server side error in push to Kafka API with request data {d} exception {e} traceback "
                                "{t}".format(d=request.data, e=e, t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="push_to_kafka")

        return Response(response, status=status_code)
    

"""
GuestChatServicesViewSet: Handles internal guest chat operations.
This service class is restricted to internal use only, receiving requests through Myra service.
Note: This API is not accessible on ingoibibo.com and is exclusively available on ingo-inventory-api-svc.ecs.mmt
"""
class GuestChatServicesViewSet(viewsets.GenericViewSet):    
    pagination_class = StandardResultsSetPagination
    http_method_names = ["get", "post", "put"]


    @detail_route(methods=["get"], url_path="detail")
    def booking_details(self, request, *args, **kwargs):
        """
        API used by funnel to fetch booking details, for eg., Ingo hotelcode, checkin, checkout, etc.
        URL : {{BASE_URL}}/api/v2/guest-chat/helper/{{vendor_booking_id}}/detail
        Type : GET
        Query params : domain (mandatory)
        Eg. : {{BASE_URL}}/api/v2/guest-chat/helper/HTLMTTALYU/detail/?domain=GI
        Response :
        {
            "message": "",
            "data": {
                "status": "Confirmed",
                "hotelcode": "1000009561",
                "adults": 2,
                "iconUrl": "",
                "mobile": "+917086831381",
                "checkout": "2021-01-27",
                "checkin": "2021-01-24",
                "mmtBlack": false,
                "email": "<EMAIL>",
                "contextDesc": "Thomas Villa, Hotel and Cottages, Manali",
                "nameOfCustomer": "Praneet Kumar",
                "platform": "goibibo",
                "child": 0,
                "roomCount": 1,
                "isStarred": false,
                "goTribe": true,
                "bookingDate": "2020-04-03"
            },
            "success": true
        }
        :param request:
        :param args:
        :param kwargs:
        :return:
        """
        response = {
            "success": False,
            "message": "",
            "data": {}
        }
        try:
            booking_id = kwargs.get("pk")
            serializer = FunnelBookingDetailSerializer(data=request.query_params)
            serializer.is_valid(raise_exception=True)
            serialized_data = serializer.data

            domain = serialized_data.get("domain")
            uuid = serialized_data.get("custUuid")

            success, message, data, status_code = fetch_booking_details_for_funnel(booking_id, domain, uuid)

            response["success"] = success
            response["message"] = message
            response["data"] = data

            api_logger.info("Booking detail API called by funnel for booking ID {b} domain {d} with status code {sc} "
                            "message {m} success {s}".format(b=booking_id, d=domain, sc=status_code, m=message,
                                                             s=success),
                            log_type="ingoibibo", bucket="api.v2.guest_chat", stage="booking_details")

        except ActionForbiddenException as e:
            response["message"] = str(e)
            status_code = http_status.HTTP_403_FORBIDDEN
            api_logger.critical("Unauthorized user {u} trying to access booking details for booking ID {b}"
                                .format(u=uuid, b=booking_id), log_type="ingoibibo", bucket="api.v2.guest_chat",
                                stage="booking_details")

        except ValidationError as e:
            response["message"] = str(e)
            status_code = http_status.HTTP_400_BAD_REQUEST
            api_logger.critical("Client side error in fetching booking detail API called by funnel for query params "
                                "{qp} exception {e} traceback {t}".format(qp=request.query_params, e=e,
                                                                          t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="booking_details")

        except Exception as e:
            status_code = http_status.HTTP_500_INTERNAL_SERVER_ERROR
            api_logger.critical("Server side error in fetching booking detail API called by funnel for query params "
                                "{qp} exception {e} traceback {t}".format(qp=request.query_params, e=e,
                                                                          t=repr(traceback.format_exc())),
                                log_type="ingoibibo", bucket="api.v2.guest_chat", stage="booking_details")

        return Response(response, status=status_code)

   
