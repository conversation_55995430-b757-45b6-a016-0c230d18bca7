DIRECTION_TEMPLATE_HEADER = """\
From {{from_location}}; \
Take the {{direction_mode}}: {{direction_attributes}} {% if show_difficulty %}difficulty level{% endif %}; \
"""

DIRECTION_TEMPLATE_ATTR = """\
The {{direction_mode}} starts at {{start_time}} and ends at {{end_time}} and runs every {{frequency}} {{frequency_unit}}; \
"""

ROUTE_TEMPLATE = """\
Total journey time is: {{total_time}} {{time_unit}} and distance - {{total_distance}} {{distance_unit}}. \
Estimated Price {{total_price}} - {{currency}}.\
"""

ROUTE_INDEX = "Route {}: "

# Prefix templates by route source
ROUTE_PREFIX = """The property is located at a distance of {{total_distance}} {{distance_unit}} from the \
{{from_location}}."""

# Direction templates by Mode of transport
TRAIN = """Take a {{type}} from {{from_location}} and de-board at {{stop}}. {% if start_time and  end_time%}The {{type}} \
services start at {{start_time}} and terminate at {{end_time}}.{% endif %} {% if frequency %} You can get a {{type}} \
every {{frequency}} {{frequency_unit}}.{% endif %}"""

TAXI = """Take a {{type}} from {{from_location}} and de-board at {{stop}}."""

BUS = """Take a {{type}} from {{from_location}} and de-board at {{stop}}. {% if start_time and  end_time%}The {{type}} \
services start at {{start_time}} and terminate at {{end_time}}.{% endif %} {% if frequency %} You can get a {{type}} \
every {{frequency}} {{frequency_unit}}.{% endif %}"""

FERRY = """Take a {{type}} from {{from_location}} and de-board at {{stop}}. {% if start_time and  end_time%}The {{type}} \
services start at {{start_time}} and terminate at {{end_time}}.{% endif %} {% if frequency %} You can get a {{type}} \
every {{frequency}} {{frequency_unit}}.{% endif %}"""

AUTO = """Take a {{type}} from {{from_location}} and de-board at {{stop}}."""

METRO = """Take a {{type}} from {{from_location}} and de-board at {{stop}}. {% if start_time and  end_time%}The {{type}} \
services start at {{start_time}} and terminate at {{end_time}}.{% endif %} {% if frequency %} You can get a {{type}} \
every {{frequency}} {{frequency_unit}}.{% endif %}"""

WALK = """From {{from_location}}, take a walk of {{distance}} {{distance_unit}} with the {{difficulty_level}} trek."""

CAB = """Take a {{type}} from {{from_location}} and de-board at {{stop}}."""

SUV = """Take a {{type}} from {{from_location}} and de-board at {{stop}}."""

CRUISE = """Take a {{type}} from {{from_location}} and de-board at {{stop}}. {% if start_time and  end_time%}The {{type}} \
services start at {{start_time}} and terminate at {{end_time}}.{% endif %} {% if frequency %} You can get a {{type}} \
every {{frequency}} {{frequency_unit}}.{% endif %}"""

TRAM = """Take a {{type}} from {{from_location}} and de-board at {{stop}}. {% if start_time and  end_time%}The {{type}} \
services start at {{start_time}} and terminate at {{end_time}}.{% endif %} {% if frequency %} You can get a {{type}} \
every {{frequency}} {{frequency_unit}}.{% endif %}"""

# suffix templates by route source
ROUTE_SUFFIX = """The estimated travel fare from the {{from_location}} to the property is {{total_price}} {{currency}} and \
total travel time is {{total_time}} {{time_unit}}."""

ROUTE_WALK = """The property is located at a {{total_distance}} {{distance_unit}} walking distance from the {{from_location}} \
with an {{difficulty_level}} trek. Total travel time is {{total_time}} {{time_unit}}."""

DIRECTION_TEMPLATES = {
    "taxi": TAXI,
    'bus': BUS,
    'ferry': FERRY,
    'auto': AUTO,
    'metro': METRO,
    'walk': WALK,
    'cab': CAB,
    'suv': SUV,
    'cruise': CRUISE,
    'tram': TRAM,
    'train': TRAIN
}