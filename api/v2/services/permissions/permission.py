from rest_framework import permissions
from common.object_guardian import check_permission
from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import PermissionDenied

from hotels.models import HotelDetail, RatePlan
from hotels.models.basic_models import Services, ServicesTemplate
from hotels import hotelchoice
from utils.logger import Logger
from api.v2.services.resources import constant
from django.db.models.query import Q

api_logger = Logger(logger='inventoryAPILogger')


class IsSuperUser(permissions.BasePermission):

    def has_permission(self, request, view):
        """
        checks if the incoming request user is super user otherwise false
        """
        return request.user.is_superuser


class IsHotelAccessibility(permissions.BasePermission):
    def has_permission(self, request, view):
        hotel_code = request.data.get('hotel_code', '') or request.data.get('hotel_id', '') \
                     or request.data.get('hotelcode','') or view.kwargs.get('hotelcode', '')
        if hotel_code:
            hobj = HotelDetail.objects.get(Q(hotelcode=hotel_code) | Q(pk=hotel_code))
            request.hotel = hobj
            request.rateplan_object = None
            try:
                return check_permission(request.user, request.hotel, ['view_hoteldetail', 'edit_hoteldetail'])
            except Exception, e:
                api_logger.critical(message='Permission denied: %s' % (str(e)), log_type='ingoibibo', bucket='services',
                                    stage='services.permission.IsHotelAccessibility')
                return False
        else:
            return False
        return True


class GuardianPermissionCheck(permissions.BasePermission):
    def __init__(self):
        self.message = ''

    def is_servicetype_supported_for_rateplan(self, service_type, service_level):
        if service_type == 1:
            return service_level == 'rateplan' or not service_level
        if service_type == 3:
            return service_level == 'rateplan'
        return False

    def has_permission(self, request, view):
        try:
            object_list = request.data.get('object_ids', [])
            service_type = request.data.get('service_type', '')
            service_level = request.data.get('service_level', '')

            if request.method in ['PUT', 'GET']:
                service_id = view.kwargs.get('pk', '')
                query_params = request.query_params
                if service_id:
                    service_obj = Services.objects.get(id=service_id)
                    # may check from leaf_category, avoid db call here and get service_type from snapshot
                    service_type = service_obj.metadata.get(
                        's_type') if service_obj.metadata and service_obj.metadata.get(
                        's_type') else ''
                    content_type = ContentType.objects.get_for_id(service_obj.content_type_id)
                    modelobj = content_type.get_object_for_this_type(pk=service_obj.object_id)
                    object_list = [modelobj.rateplancode] if content_type.model == 'rateplan' else [modelobj.hotelcode]
                    service_level = 'rateplan' if service_type in [1, 3] and content_type.model == 'rateplan' else 'hotel'
                    # common validation for notices and inclusion in below if block
                elif query_params and request.method == 'GET' and query_params.get('hotelcode'):
                    hotel_obj = HotelDetail.objects.get(hotelcode=query_params.get('hotelcode'))
                    request.hotel = hotel_obj
                    return check_permission(request.user, hotel_obj, ['view_hoteldetail'])

            # 1: inclusion, 2: experience, 3:notices
            if service_type in hotelchoice.SERVICE_CODE.values() and object_list:
                # for backward comaptibility, otherwise service_level=='rateplan'
                if self.is_servicetype_supported_for_rateplan(service_type, service_level):
                    rateplan_obj = RatePlan.objects.select_related('roomtype__hotel__hotelcode').only(
                        'roomtype__hotel__hotelcode').filter(rateplancode__in=object_list)

                    # check whether all rateplan have valid detail
                    valid_rateplan_list = rateplan_obj.values_list('rateplancode', flat=True)
                    invalid_data = set(object_list) - set(valid_rateplan_list)
                    if invalid_data:
                        raise PermissionDenied('requets have invalid rateplan %s' % str(invalid_data))

                    if len(rateplan_obj) > 1:
                        hotel_obj = rateplan_obj[0].roomtype.hotel
                        request.hotel = hotel_obj
                        status = check_permission(request.user, hotel_obj, ['view_hoteldetail', 'edit_hoteldetail'])
                        request_rateplan_id = rateplan_obj.values_list('id', flat=True)
                        request.data['modified_by'] = request.user.id
                        request.data['object_ids'] = request_rateplan_id
                        # check if all rate plan belongs to a hotel, ensure user hotel permisssion
                        if status:
                            same_hotel_rateplan = RatePlan.objects.filter(roomtype__hotel=hotel_obj).values_list('id',
                                                                                                                 flat=True)
                            for each in request_rateplan_id:
                                if not each in same_hotel_rateplan:
                                    return False
                            return True
                        else:
                            return False

                    elif len(rateplan_obj) < 1:
                        raise PermissionDenied('rate plan ids are not valid')
                    else:
                        request.hotel = rateplan_obj[0].roomtype.hotel
                        if request.method in ['PUT', 'POST']:
                            request.data['object_ids'] = [rateplan_obj[0].id]
                            request.data['modified_by'] = request.user.id
                            return check_permission(request.user, request.hotel,
                                                    ['view_hoteldetail', 'edit_hoteldetail'])

                elif service_type == 1 and service_level == 'hotel':
                    # in a request only one hotel is acceptable, hotel level inclusion permission check
                    if len(object_list) == 1:
                        hotel_code = object_list[0]
                        hotels_obj = HotelDetail.objects.get(hotelcode=hotel_code)
                        request.hotel = hotels_obj
                        if request.method in ['PUT', 'POST']:
                            request.data['object_ids'] = [hotels_obj.id]
                            request.data['modified_by'] = request.user.id
                            return check_permission(request.user, hotels_obj, ['view_hoteldetail', 'edit_hoteldetail'])
                    else:
                        self.message = 'request objects_id have more than one hotel %s' % str(object_list)
                        raise PermissionDenied()

                else:
                    # this block is for notice permission check
                    hotel_code = object_list[0]
                    hotels_obj = HotelDetail.objects.get(hotelcode=hotel_code)
                    request.hotel = hotels_obj
                    if request.method in ['PUT', 'POST']:
                        request.data['object_ids'] = [hotels_obj.id]
                        request.data['modified_by'] = request.user.id
                        return check_permission(request.user, hotels_obj, ['view_hoteldetail', 'edit_hoteldetail'])

            else:
                # object_ids and service_type is mandatory, for inclsuion: 1, experience: 2, notices: 3
                self.message = 'User does not have the permission to perform this action'
                raise PermissionDenied()

            # this is for get call, have hotel view permission
            return check_permission(request.user, request.hotel, ['view_hoteldetail'])

        except Exception as e:
            api_logger.critical(message='Permission denied: %s' % (str(e)), log_type='ingoibibo', bucket='services',
                                stage='services.permission.GuardianPermissionCheck')
            return False


class ExternalGuardianPermissionCheckBulkServices(GuardianPermissionCheck):

    def is_servicetype_supported_for_rateplan(self, service_type, service_level):
        return service_type in [1, 3] and (service_level == 'rateplan' or not service_level)


class IsServiceAccessibility(permissions.BasePermission):
    def has_permission(self, request, view):
        query_params = request.query_params
        hotel_code = query_params['hotelcode'] if query_params.get('hotelcode') else view.kwargs.get('pk', '')
        if hotel_code:
            hobj = HotelDetail.objects.get(hotelcode=hotel_code)
            request.hotel = hobj
            try:
                if request.user.is_superuser or request.user.is_staff:
                    return True
                return check_permission(request.user, request.hotel, ['view_hoteldetail'])
            except Exception, e:
                api_logger.critical(message='Permission denied: %s' % (str(e)), log_type='ingoibibo', bucket='services',
                                    stage='services.permission.IsServiceAccessibility')
                return False

        return False


class ExternalGuardianPermissionCheck(permissions.BasePermission):
    def __init__(self):
        self.message = ''

    def has_permission(self, request, view):
        try:
            if request.method in ['POST']:
                object_ids = request.data.get('object_code', [])
                if not isinstance(object_ids, list):
                    object_ids = [object_ids]
                updating_dict = {"object_ids": object_ids}
                service_type = request.data.get('service_type', '')
                if service_type in [1, 3] and request.data.get('content_type', '') == 'rateplan':
                    updating_dict.update({"service_level": "rateplan"})
                else:
                    updating_dict.update({"service_level": "hotel"})
                request.data.update(updating_dict)
                return request.data
            elif request.method in ['GET']:
                return True
        except Exception as e:
            api_logger.critical(message='External Permission denied: %s' % (str(e)), log_type='ingoibibo', bucket='services',
                                stage='services.permission.ExternalGuardianPermissionCheck')
            return False


class CustomNoticePermissionCheck(permissions.BasePermission):
    """
        We do not allow a user to create or update a custom notice from existing service apis which are used on
        extranet. This Custom notice option is only available to nexus team for bulk creation, for derby.
    """
    def __init__(self):
        self.message = 'Create/Update functionality for Custom Notice is not allowed from the Extranet'

    @staticmethod
    def get_leaf_category_template(request_method, leaf_category_id, service_id):
        if request_method == 'POST':
            if not leaf_category_id:
                return None
            return ServicesTemplate.objects.get(id=leaf_category_id)

        service_obj = Services.objects.get(id=service_id)
        return service_obj.leaf_category

    def has_permission(self, request, view):

        # Fallback: use object_ids if hotel_code is not present
        hotel_code = request.data.get('hotel_code') or request.data.get('object_ids',[])
        if request.method == 'POST':
            from common.commonhelper import is_user_assigned_to_hotel
            if not is_user_assigned_to_hotel(hotel_code, request.user.id):
                return False
    
        if request.method not in ['POST', 'PUT']:
            return True

        # Custom notice validation does not apply on multi-create endpoint as it create inclusions for mmt-black hotels
        if 'multi-create' in request.path:
            return True

        leaf_obj = self.get_leaf_category_template(request.method,
                                                   request.data.get('leaf_category'),
                                                   view.kwargs.get('pk', ''))
        if not leaf_obj:
            return False

        if leaf_obj.leaf_category_name == constant.CUSTOM_NOTICE_TEMPLATE_NAME:
            return False
        return True
