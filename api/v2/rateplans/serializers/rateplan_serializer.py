from django.conf import settings
from hotels.models.rateplan import RatePlanEmail
from rest_framework import serializers

from api.v1.validators import RequiredValidator
from lib.validators import validate_source_special_tags
from ingo_partners.resources.configure import get_config_data
from extranet.propertydetails import formatCancellationPolicy
from hotels.models import RatePlan, Inclusions, LinkedRateRule
from api.v2.inclusions.serializers.inclusions_serializers import InclusionsSerializer
from api.v2.rateplans.serializers.fields import CustomBoolField, CustomIntegerField, \
    CustomRateplanNameField, RoomtypeField, RatePlanField, ContractTypeField
from hotels.models.hotel_flag_configuration import compute_updated_flag_bits_value, RATE_PLAN_FLAG_DICT, RATE_PLAN_PLATFORM_FLAG_DICT
from common.commonchoice import VENDOR_SOURCE_MAP
from hotels.hotelchoice import HOTEL_TRAVEL_CONTRACT, HOTEL_TRAVEL, MYPARTNER_CUG_CONTRACT_TYPE
from hotels.models import RatePlan
from common.constants import CORPORATERFP_PAH_CONTRACTTYPE, CORPORATERFP_PAH_CONTRACTTYPE_ERROR
from ingo_partners.resources.configure import get_valid_contract_type
from utils.logger import Logger

api_logger = Logger(logger='inventoryAPILogger')

class CustomInclusionsField(serializers.Field):
    def to_representation(self, obj):
        return InclusionsSerializer(obj, many=True).data

    def to_internal_value(self, idlist):
        return Inclusions.objects.filter(id__in=idlist)


"""RatePlan Serializer """


class RatePlanSerializer(serializers.ModelSerializer):
    inclusions = CustomInclusionsField(required=False)
    cancellationrules = serializers.SerializerMethodField()
    roomtype = RoomtypeField()
    contract_type = ContractTypeField(source='contracttype', required=False)
    meal_plan = serializers.CharField(source='mealplan')
    tax_included = CustomBoolField(source='taxincluded', required=False, default=0)
    non_refundable = CustomBoolField(source='nonrefundable', required=False, default=0)
    is_active = CustomBoolField(source='isactive')
    cut_off_days = CustomIntegerField(source='cutoffdays', required=False, default=0)
    minimum_length_of_stay = serializers.IntegerField(source='minnumofnights', required=False, default=1)
    max_los = serializers.IntegerField(required=False, default=450)
    # rateplan_name = CustomRateplanNameField(source='rateplanname', required=True)
    rateplan_name = serializers.CharField(source='rateplanname', required=True)
    commission = serializers.FloatField(source='sellcommission', required=False)
    pay_at_hotel = serializers.BooleanField(required=False, default=0)
    flag_bits_1 = serializers.IntegerField(default=0)
    platform_flag_bits = serializers.IntegerField(default=1)
    is_staycation = serializers.BooleanField(required=False, read_only=True)
    is_day_zero = serializers.BooleanField(required=False, read_only=True)
    is_only_rateplan_cug = serializers.BooleanField(required=False)
    is_only_rateplan_hcp = serializers.BooleanField(required=False)
    is_tax_on_commission_inclusive = serializers.BooleanField(required=False, read_only=True)
    is_net_rate_model = serializers.BooleanField(required=False)
    source_rateplancode = serializers.CharField(allow_null=True, allow_blank=True, required=False)
    description = serializers.CharField(allow_null=True, allow_blank=True, required=False)
    long_description = serializers.CharField(allow_null=True, allow_blank=True, required=False)
    only_rateplan_offers_flag = serializers.BooleanField(required=False, default=0)
    is_package = serializers.BooleanField(required=False, read_only=True)
    is_suppress_desc = serializers.BooleanField(required=False, read_only=True)
    access_code = serializers.CharField(allow_null=True, allow_blank=True, required=False)
    apply_commission_on_post_tax = serializers.BooleanField(required=False, read_only=True)
    exclude_service_charge = serializers.BooleanField(required=False,read_only=True)
    rp_booking_model = serializers.CharField(allow_null=True, allow_blank=True, required=False)
    is_gommt_platform = serializers.BooleanField(required=False, read_only=True)
    is_hoteltravel_platform = serializers.BooleanField(required=False, read_only=True)
    editable_only_from_admin = serializers.BooleanField(required=False, read_only=True)

    def to_internal_value(self, data):
        # here flag bit values is setting to flag_bits_1
        self.flag_bits_1 = self.instance.flag_bits_1 if self.instance and self.instance.flag_bits_1  else 0
        self.platform_flag_bits = self.instance.platform_flag_bits if self.instance and self.instance.platform_flag_bits else 1
        available_flag_list = [x for x in RATE_PLAN_FLAG_DICT.keys() if x in data.keys()]
        available_platform_flag_bits = [x for x in RATE_PLAN_PLATFORM_FLAG_DICT.keys() if x in data.keys()]

        for key in RATE_PLAN_FLAG_DICT:
            if key in available_flag_list:
                value = data.get(key)
                self.flag_bits_1 = compute_updated_flag_bits_value(self.flag_bits_1, key, value, RATE_PLAN_FLAG_DICT)
            data['flag_bits_1'] = self.flag_bits_1

        for key in RATE_PLAN_PLATFORM_FLAG_DICT:
            if key in available_platform_flag_bits:
                value = data.get(key)
                self.platform_flag_bits = compute_updated_flag_bits_value(self.platform_flag_bits, key, value,
                                                                   RATE_PLAN_PLATFORM_FLAG_DICT)
            data['platform_flag_bits'] = self.platform_flag_bits

        data = super(RatePlanSerializer, self).to_internal_value(data)
        return data

    def validate(self, data):
        if not data.get('roomtype'):
            raise serializers.ValidationError('Room doesn\'t Exists')

        if data.get('slot_duration') and not data['roomtype'].is_slot_room:
            raise serializers.ValidationError({'slot_duration': 'Cannot be passed for non slot rooms'})

        minnumofnight = data.get('minnumofnights') if data.get('minnumofnights') else self.instance.minnumofnights
        if minnumofnight < 1:
            raise serializers.ValidationError({'minimum_length_of_stay': 'Should be Positive Integer greater than or '
                                                                         'equal to 1'})

        max_los = data.get('max_los') if data.get('max_los') else self.instance.max_los
        if max_los < 1:
            raise serializers.ValidationError({'max_los': 'Should be Positive Integer greater than or equal to 1'})

        if minnumofnight and max_los:
            if minnumofnight > max_los:
                raise serializers.ValidationError({'minimum_length_of_stay': 'Should be less than max_los value'})

        # raising error for the chain hotels if hotel doesn't has GSTN and taxincluded flag set to true
        if data['roomtype'].hotel.country.lower() == 'india' and not data['roomtype'].hotel.gstn \
                and data.get('taxincluded') and data['roomtype'].hotel.chainname_id:
            raise serializers.ValidationError(
                {'tax_included': 'Hotel does not have gstn detail. Please update gstn for the hotel.'})

        contract_type = data.get('contracttype', '')
        contract_type_list = get_valid_contract_type(data['roomtype'].hotel.source_config_id)
        if contract_type and ((isinstance(contract_type, list) and (HOTEL_TRAVEL in contract_type)) or (HOTEL_TRAVEL == contract_type))  and  HOTEL_TRAVEL_CONTRACT not in contract_type_list:
            contract_type_list.append(HOTEL_TRAVEL_CONTRACT)
        contracttypes = dict(contract_type_list).keys()
        is_not_valid_contracttype = False

        if not self.validate_mypartner_cug_contract_type(contract_type):
            raise serializers.ValidationError({'contract_type': 'Cannot club ' + MYPARTNER_CUG_CONTRACT_TYPE + ' with any other contract types'})
        
        if self.instance and self.instance.parent_id and self.instance.parent_id != 0 and self.instance.parent_id != self.instance.id:
                parent = RatePlan.objects.filter(id=self.instance.parent_id).first()
                if parent:
                    if (
                        MYPARTNER_CUG_CONTRACT_TYPE in parent.contracttype) and (MYPARTNER_CUG_CONTRACT_TYPE not in contract_type
                    ) or (MYPARTNER_CUG_CONTRACT_TYPE not in parent.contracttype) and (MYPARTNER_CUG_CONTRACT_TYPE in contract_type):
                        raise serializers.ValidationError({'contract_type': 'parent and child ratePlan both should have same mypartner_cug contracttype'})


        ### ex: contract_type --> ['b2c', 'b2b'] or 'b2c'
        
        if contract_type:
            if isinstance(contract_type, list) and not set(contract_type).issubset(set(contracttypes)):
                is_not_valid_contracttype = True
            elif not isinstance(contract_type, list) and contract_type not in contracttypes:
                is_not_valid_contracttype = True

        if is_not_valid_contracttype:
            raise serializers.ValidationError(
                {'contract_type': 'Not a valid value of contract type'})

        if 'contracttype' in data or not self.instance:
            data["contracttype"] = contract_type
        if data.get('source_config'):
            hotel = data['roomtype'].hotel
            partner_commission = hotel.partner_commission
            hotel_source = hotel.source_config.name if hotel.source_config else VENDOR_SOURCE_MAP["default"]
            if data['source_config'].name != VENDOR_SOURCE_MAP["ingo"]:
                # both hotel_vendor and source config should be same
                if hotel_source != data['source_config'].name:
                    raise serializers.ValidationError(
                        {'source_config': 'Rateplan Source config mismatch, Hotel source config {}, Rateplan source '
                                          'config {}'.format(hotel_source, data['source_config'].name)})
                
                # hotel partner_commission is override on rateplan sell_commission for not ingo source
                instance_commission = True if (self.instance and self.instance.sellcommission is not None) else False
                data_sellcommission = data.get('sellcommission')
                is_commission_none_or_empty = False
                if (data_sellcommission is None or (isinstance(data_sellcommission, str) and len(data_sellcommission) == 0)):
                    is_commission_none_or_empty  = True

                if is_commission_none_or_empty and not instance_commission:
                    if partner_commission:
                        if float(partner_commission) < 0.0 or float(partner_commission) > 100.0:
                            raise serializers.ValidationError(
                                {'sellcommission': '{} is not valid partner_commission'.format(partner_commission)})
                        data['sellcommission'] = partner_commission
                    else:
                        raise serializers.ValidationError(
                            {'sellcommission': 'Both partner_commission and sellcommission is missing'})

        rateplanname = data.get('rateplanname', '')
        if rateplanname:
            if data.get('source_config') and data.get('source_config') != VENDOR_SOURCE_MAP["ingo"]:
                valid_data = validate_source_special_tags(rateplanname, html=True)
                error_msg = "rateplan name cannot have HTML tags"
            else:
                valid_data = validate_source_special_tags(rateplanname)
                error_msg = "rateplan name cannot accept special tags"
            if not valid_data:
                raise serializers.ValidationError({'rateplanname': '{}'.format(error_msg)})

        description = data.get('description', '')
        if description:
            if data.get('source_config') and data.get('source_config') != VENDOR_SOURCE_MAP["ingo"]:
                valid_data = validate_source_special_tags(description, html=True)
                error_msg = "description name cannot have HTML tags"
            else:
                valid_data = validate_source_special_tags(description)
                error_msg = "Description cannot have special characters"
            if not valid_data:
                raise serializers.ValidationError({'description': '{}'.format(error_msg)})

        long_description = data.get('long_description', '')
        if long_description:
            if data.get('source_config') and data.get('source_config') != VENDOR_SOURCE_MAP["ingo"]:
                valid_data = validate_source_special_tags(long_description, html=True)
                error_msg = "Long description name cannot have HTML tags"
            else:
                valid_data = validate_source_special_tags(long_description)
                error_msg = "Long description name cannot accept special tags"
            if not valid_data:
                raise serializers.ValidationError({'description': '{}'.format(error_msg)})

        return data

    def validate_mypartner_cug_contract_type(self, contract_type):
        if not contract_type:
            return True
        if (isinstance(contract_type, list) and (len(contract_type) > 1) and MYPARTNER_CUG_CONTRACT_TYPE in contract_type):
            return False
        return True

    class Meta:
        model = RatePlan
        fields = (
            'rateplancode', 'roomtype', 'inclusions', 'minimum_length_of_stay','max_los', 'cut_off_days', 'cancellationrules',
            'non_refundable', 'tax_included', 'pay_at_hotel', 'meal_plan', 'is_active', 'rateplan_name', 'user',
            'commission', 'contract_type', 'parent_id', 'is_staycation', 'is_day_zero', 'is_only_rateplan_cug',
            'is_only_rateplan_hcp', 'flag_bits_1', 'platform_flag_bits', 'is_gommt_platform', 'is_hoteltravel_platform','source_rateplancode', 'source_config', 'description',
            'only_rateplan_offers_flag', 'is_tax_on_commission_inclusive', 'is_package', 'is_net_rate_model',
            'is_suppress_desc', 'long_description','pricing_model', 'inventory_model', 'slot_duration', 'access_code', 'apply_commission_on_post_tax',
            'rp_booking_model', 'exclude_service_charge', 'editable_only_from_admin')

        read_only_fields = ('rateplancode', 'cancellationrules',)

    validators = [
        RequiredValidator(
            fields=('roomtype', 'user',)
        )
    ]

    def get_cancellationrules(self, obj):
        return []

    def get_contract_type(self, obj):
        if not obj.contracttype or (isinstance(obj.contracttype, list) and
                                    len(obj.contracttype) == 1 and obj.contracttype[0].strip() == ''):
            return dict(settings.CONTRACTTYPE).keys()
        else:
            return obj.contracttype
    def to_representation(self, instance):
        configs_dict = get_config_data()
        data = super(RatePlanSerializer, self).to_representation(instance)
        source_config = configs_dict.get(data.get("source_config")) if data.get("source_config") and configs_dict.get(data.get("source_config")) else VENDOR_SOURCE_MAP["default"]
        data["source_config"] = source_config
        data["contract_type"] = instance.get_valid_contract_types()
        return data


class OnBoardRatePlanSerializer(RatePlanSerializer):
    inclusions = CustomInclusionsField(required=False)
    cancellationrules = serializers.SerializerMethodField(required=False)
    roomtype = RoomtypeField()
    contract_type = serializers.ListField(source='contracttype', default=[])
    meal_plan = serializers.CharField(source='mealplan')
    tax_included = CustomBoolField(source='taxincluded', required=False, default=0)
    non_refundable = CustomBoolField(source='nonrefundable', required=False, default=0)
    is_active = CustomBoolField(source='isactive')
    cut_off_days = CustomIntegerField(source='cutoffdays', required=False, default=0)
    minimum_length_of_stay = serializers.IntegerField(source='minnumofnights', required=False, default=1)
    rateplan_name = CustomRateplanNameField(source='rateplanname', required=True)
    commission = serializers.FloatField(source='sellcommission', required=False)


class LinkedRatePlanSerializer(serializers.ModelSerializer):
    """
    Linkage_type --> high/low
    linkage_basis --> percent/fixed
    link_block --> true/false
    """
    linked_rateplan = RatePlanField()
    link_block = serializers.BooleanField(default=True, required=False)
    def validate(self, data):
        request = self.context.get("request")
        if request == None:
            return data
        elif request.method == 'POST':
            if not data.get('linkage_amount'):
                data['linkage_amount'] = 0
            if not data.get('extra_guest_linkage_amount'):
                data['extra_guest_linkage_amount'] = 0
            return data
        if data.get('linkage_type') not in ['high', 'low']:
            raise serializers.ValidationError({'linkage_type': 'Invalid. Allowed value: high/low only'})
        if data.get('linkage_basis') not in ['percent', 'fixed']:
            raise serializers.ValidationError({'linkage_basis': 'Invalid. Allowed value: percent/fixed only'})
        if data.get('link_block') not in [0, 1]:
            raise serializers.ValidationError({'link_block', 'Invalid. Allowed value: 0/1 only'})
        if type(data.get('linkage_amount')) != int and type(data.get('linkage_amount')) != float:
            raise serializers.ValidationError({'linkage_amount', 'Invalid linkage amount'})

        if type(data.get('extra_guest_linkage_amount')) != int:
            data['extra_guest_linkage_amount'] = 0
        return data

    class Meta:
        model = LinkedRateRule
        fields = (
            'linked_rateplan', 'linkage_type', 'linkage_amount', 'linkage_basis', 'link_block',
            'extra_guest_linkage_amount', 'user', 'parent_rateplan_contract_type', 'linked_rateplan_contract_type'
        )

    validators = [
        RequiredValidator(
                fields=('user', 'linked_rateplan',)
        )
    ]



class RatePlanEmailSerializer(serializers.ModelSerializer):
    rateplan = RatePlanField()
    sales_channel = serializers.CharField(required=True)
    email_ids = serializers.CharField(required=False)
    is_active = serializers.BooleanField(required=False, default=1)

    class Meta:
        model = RatePlanEmail
        fields = (
            'rateplan', 'sales_channel', 'email_ids', 'is_active'
        )

    validators = [
        RequiredValidator(
                fields=('rateplan', 'sales_channel','email_ids')
        )
    ]
