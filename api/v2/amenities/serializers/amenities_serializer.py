__author__ = '<PERSON>run <PERSON>'

import json
from django.utils import six
from rest_framework import serializers
from common.amenities_models import AmenitiesV2, AmenitiesV2Mapping
from hotels.models.basic_models import Amenities
from api.v2.amenities.serializers.fields import CustomBoolField


class CustomJ<PERSON><PERSON>ield(serializers.JSONField):
    def to_internal_value(self, data):
        try:
            if self.binary:
                if isinstance(data, six.binary_type):
                    data = data.decode('utf-8')
                return json.loads(data)
            else:
                data = json.dumps(data)
        except (TypeError, ValueError):
            self.fail('invalid')
        return data

    def to_representation(self, value):
        return json.loads(value)


class AmenitiesSerializer(serializers.ModelSerializer):
    amenity_description = serializers.CharField(source='amenityname')
    tag = serializers.CharField(source='amenitytag')
    amenity_category = serializers.CharField(source='amenitycat')
    amenity_sub_category = serializers.CharField(source='amenitysubcat')
    is_highlighted = CustomBoolField(source='highlighted_flag')
    is_popular = CustomBoolField(source='popular_flag')

    class Meta:
        model = Amenities
        fields = ('id', 'amenity_description', 'tag', 'amenity_category', 'amenity_sub_category', 'is_highlighted',
                  'is_popular')

    def get_amenity_description(self, obj):
        return obj.amenityname

    def get_amenity_level(self, obj):
        return obj.amenitycat

    def get_id(self, obj):
        return obj.id

    def get_amenity_category(self, obj):
        return obj.amenitycubcat

    def get_highlighted(self, obj):
        return obj.highlighted_flag

    def get_popular(self, obj):
        return obj.popular_flag


class AmenitiesV2Serializer(serializers.ModelSerializer):
    template = CustomJSONField()
    tags = CustomJSONField()
    amenity_id = serializers.IntegerField(source='id', required=False)

    class Meta:
        model = AmenitiesV2
        fields = (
            'amenity_id', 'level', 'main_category', 'amenity_name', 'tags', 'icon', 'template',
            'is_active', 'category_order', 'category_icon')

class HotelAmenitiesSerializer(serializers.Serializer):
    amenity_mapping_id = serializers.CharField(source='amenity_mapping_id')
    hotelcode = serializers.CharField(source='hotelcode')
    amenity_id = serializers.CharField(source='amenity')
    attributes = serializers.CharField(source='attributes')
    source_config = serializers.CharField(source='source_config')
    is_selected = serializers.BooleanField(source='is_selected')
    object_id = serializers.CharField(source='object_id')
    content_type = serializers.CharField(source='content_type')
    createdon = serializers.DateTimeField(source='createdon')
    modifiedon = serializers.DateTimeField(source='modifiedon')

class AmenitiesV2TemplateSerializer(serializers.ModelSerializer):
    template = CustomJSONField()
    tags = CustomJSONField()
    amenity_id = serializers.IntegerField(source='id', required=False)

    class Meta:
        model = AmenitiesV2
        fields = (
            'amenity_id', 'main_category', 'amenity_name', 'tags', 'icon', 'template',
            'category_order', 'category_icon')

    def to_representation(self, instance):
        amenities_data = super(AmenitiesV2TemplateSerializer, self).to_representation(instance)
        amenities_data['is_selected'] = None
        return amenities_data


class AmenitiesV2MappingSerializer(serializers.ModelSerializer):
    attributes = CustomJSONField()
    amenity_id = serializers.PrimaryKeyRelatedField(source='amenity', queryset=AmenitiesV2.objects.all())

    class Meta:
        model = AmenitiesV2Mapping
        fields = ('id', 'amenity_id', 'attributes', 'is_selected', 'source_config')
