import logging
import traceback

from django.db import connection

from constants import *
from api.v2.cancellation_rules.resources.errors import CANCELLATION_RULES_ERROR_MAPPING
from api.v2.cancellation_rules.serializers.serializers import CancellationRuleSerializer
from api.v2.cancellation_rules.resources.helpers import get_formatted_cancellation_policy, get_cancellation_data, \
    format_cancellation_policy, get_content_object
from communication.notifications import Notify
from hotels.models import CancellationRules, RatePlan, CancellationBlackOutDates, RoomDetail
from hotels.methods import HotelMethods
from utils.logger import Logger
from validators import *
from hotels.models.hotel_flag_configuration import ROOM_DETAIL_FLAG_DICT
from copy import deepcopy
from django.forms.models import model_to_dict
from django.db.models import Q

hotel_methods = HotelMethods()
api_logger = Logger(logger='inventoryAPILogger')
enLogger = logging.getLogger('extranetLogger')
logger_stats = Logger(logger="extranetLogger")


def create_cancellation_policies(related_code_list, related_to, cancellation_policy_list, hotel, user, segment=None):
    created_cancellation_policies = []
    errors = []
    is_success = True
    try:
        cancellation_rules_data_list = []
        for related_code in related_code_list:
            for cancellation_policy in cancellation_policy_list:
                formatted_cancellation_policy = get_formatted_cancellation_policy(cancellation_policy, related_code,
                                                                                  related_to, segment)
                cancellation_rule_data, are_rules_valid, msg = get_cancellation_data(formatted_cancellation_policy,
                                                                                     user.id, segment)
                if not are_rules_valid:
                    is_success = False
                    error = {}
                    error['message'] = CANCELLATION_RULES_ERROR_MAPPING['CR_E400'] % msg
                    error['error_code'] = 'CR_E400'
                    errors.append(error)
                    return created_cancellation_policies, errors, is_success

                cancellation_rules_data_list.append(cancellation_rule_data)

        for cancellation_rules_data in cancellation_rules_data_list:
            cancellation_rule_serializer = CancellationRuleSerializer(data=cancellation_rules_data, many=True,
                                                                      context=hotel.hotelcode if hotel else None)
            if cancellation_rule_serializer.is_valid():
                cancellation_rule_obj = cancellation_rule_serializer.save()
                serializer = CancellationRuleSerializer(cancellation_rule_obj, many=True)
                serialized_data = serializer.data
                serialized_data[0].pop('blackouts')
                blackouts = dict()
                cancel_rule_id = serialized_data[0]['id']
                blackouts_list = list(
                    CancellationBlackOutDates.objects.using('default').filter(cancel_rule=cancel_rule_id))
                blackouts['add_blackout'] = [x.black_out_date for x in blackouts_list if x.is_active is True]
                blackouts['delete_blackout'] = [x.black_out_date for x in blackouts_list if x.is_active is False]
                serialized_data[0]['blackouts'] = blackouts
                created_cancellation_policies.append(serialized_data)
                cancellation_rule_obj = cancellation_rule_obj[0]
                hotel_methods.updateLogMsg(user, cancellation_rule_obj, 'Created from API', 'add')
                if user.is_staff and cancellation_rule_obj:
                    Notify.add_notification(cancellation_rule_obj, action='added')
            else:
                is_success = False
                error = {}
                error['message'] = CANCELLATION_RULES_ERROR_MAPPING['CR_E400'] % str(
                    cancellation_rule_serializer.errors)
                error['error_code'] = 'CR_E400'
                errors.append(error)
    except Exception, e:
        is_success = False
        error = {}
        error['message'] = CANCELLATION_RULES_ERROR_MAPPING['CR_E500']
        error['error_code'] = 'CR_E500'
        errors.append(error)
        api_logger.critical(message="Error: %s related_code_list %s related_to %s cancellation_policy_list %s "
                                    "traceback %s" % (str(e), related_code_list, related_to, cancellation_policy_list,
                                                      repr(traceback.format_exc())),
                            log_type='ingoibibo', bucket='CancellationPolicy',
                            stage='create_cancellation_policies')
    return created_cancellation_policies, errors, is_success


def deactivate_cancellation_policies(policy_id, policy_list, hotel, user):
    success = False
    try:
        cancellation_rules_query_set_list = []
        if policy_id:
            cancellation_rule = CancellationRules.objects.filter(id=policy_id)
            cancellation_rules_query_set_list.append(cancellation_rule)
        else:
            for policy_data in policy_list:
                stay_start = policy_data.get('staystart', None)
                stay_end = policy_data.get('stayend', None)
                related_to = policy_data.get('relatedto', '')
                rate_plan_code = policy_data.get('related_code')
                cancellation_rules_query_set_list.append(
                    get_cancellation(stay_start, stay_end, related_to, hotel, rate_plan_code))

        if cancellation_rules_query_set_list:
            for cancellation_rules_query_set in cancellation_rules_query_set_list:
                cancellation_rules_object_copy = cancellation_rules_query_set.first()
                if cancellation_rules_object_copy:
                    cancellation_rules_query_set.update(is_active=0)
                    hotel_methods.updateLogMsg(user, cancellation_rules_object_copy, 'Deactivated from API')
                    if user.is_staff:
                        Notify.add_notification(cancellation_rules_object_copy, action='deleted',
                                                fields='Cancellation rule for %s to %s was deleted' %
                                                       (cancellation_rules_object_copy.stay_start,
                                                        cancellation_rules_object_copy.stay_end), force=True)
            message = "cancellation policies removed successfully"
            success = True
        else:
            message = "No rules removed"
            success = False
    except Exception, e:

        message = str(e)
        api_logger.critical(message="Error: %s policy_id %s policy_list %s "
                                    "traceback %s" % (str(e), policy_id, policy_list, repr(traceback.format_exc())),
                            log_type='ingoibibo', bucket='CancellationPolicy',
                            stage='delete_cancellation_policies')
    return message, success


def get_cancellation(stay_start, stay_end, related_to, hotel, rateplan_code):
    rules = None
    if stay_end and stay_start and related_to:
        stay_end = datetime.datetime.strptime(stay_end, '%Y-%m-%d').date()
        stay_start = datetime.datetime.strptime(stay_start, '%Y-%m-%d').date()
        if related_to == 'hoteldetail' and hotel:
            rules = CancellationRules.objects.filter(
                object_id=hotel.id, content_type_id=27, stay_start=stay_start, stay_end=stay_end, is_active=True,
                priority=60
            )
        elif related_to == 'rateplan' and rateplan_code:
            rateplan = RatePlan.objects.get(rateplancode=rateplan_code)
            rules = CancellationRules.objects.filter(
                object_id=rateplan.id, content_type_id=31, stay_start=stay_start, stay_end=stay_end, is_active=True,
                priority=50
            )
        return rules


def get_cancellation_policy(hotel, rule_type, filter_type, filter_value_list, explode_policy, fetch_all=False, source_config=None):
    rate_plans, rooms = [], []
    if filter_type == 'rateplancode':
        rateplan_filter = {'rateplancode__in': filter_value_list, 'roomtype__isactive': True, 'isactive': True}
        if source_config:
            rateplan_filter['source_config'] = source_config
        rate_plans = RatePlan.objects.filter(**rateplan_filter).select_related('rateplans') \
                .values('id', 'roomtype__roomtypename', 'rateplanname', 'rateplancode',
                        'source_rateplancode')
    elif filter_type == 'roomcode':
        room_filter = {'roomtypecode__in': filter_value_list, 'isactive': True}
        if source_config:
            room_filter['source_config'] = source_config
        rooms = RoomDetail.objects.filter(**room_filter) \
            .values('id', 'roomtypename', 'roomtypecode', 'source_roomtypecode')
    else:
        rateplan_filter = {'roomtype__hotel_id': hotel.id, 'roomtype__isactive': True, 'isactive': True}
        room_filter = {'hotel_id': hotel.id, 'isactive': True}
        if source_config:
            rateplan_filter['source_config'] = source_config
            room_filter['source_config'] = source_config
        rate_plans = RatePlan.objects.filter(**rateplan_filter).select_related('rateplans')\
            .values('id', 'roomtype__roomtypename', 'rateplanname', 'rateplancode',
                    'source_rateplancode')
        rooms = RoomDetail.objects.filter(**room_filter) \
            .values('id', 'roomtypename', 'roomtypecode', 'source_roomtypecode').exclude(flag_bits_1__truth=ROOM_DETAIL_FLAG_DICT['is_slot_room'])

    rate_plans = list(rate_plans)
    rate_plan_dict = dict((rate['id'], rate) for rate in rate_plans)
    rooms = list(rooms)
    room_dict = dict((room['id'], room) for room in rooms)

    # Build priority_condition and priority_params
    priority_condition = ''
    priority_params = []
    if rule_type == 'fc':
        priority_condition = 'AND priority = %s'
        priority_params = [priority_map['FC']]
    elif rule_type == 'nfc':
        priority_condition = 'AND priority != %s'
        priority_params = [priority_map['FC']]

    # Build filter_condition and filter_params
    filter_condition = ''
    filter_params = []
    if filter_type == 'segment' and filter_value_list:
        filter_condition = ' AND hcr.segment IN %s'
        filter_params = [tuple(filter_value_list)]

    is_active_condition = 'hcr.is_active = %s'
    is_active_params = [1]
    if fetch_all:
        is_active_condition = ''
        is_active_params = []

    cursor = connection.cursor()
    object_code = ''
    object_code_params = []
    if source_config == 2 or source_config == 3:
        rateplan_ids = tuple(rate_plan_dict.keys()) if len(rate_plan_dict.keys()) > 0 else (0,)
        room_ids = tuple(room_dict.keys()) if len(room_dict.keys()) > 0 else (0,)
        object_code = "((django_content_type.model = 'rateplan' and hcr.object_id in %s) or " \
                      "(django_content_type.model = 'roomdetail' and hcr.object_id in %s))"
        object_code_params = [rateplan_ids, room_ids]
    else:
        rateplan_ids = tuple(rate_plan_dict.keys()) if len(rate_plan_dict.keys()) > 0 else (0,)
        hotel_id = hotel.id if not (filter_type and (filter_type == 'rateplancode' or filter_type == 'roomcode')) else 0
        room_ids = tuple(room_dict.keys()) if len(room_dict.keys()) > 0 else (0,)
        object_code = "((django_content_type.model = 'rateplan' and hcr.object_id in %s) or " \
                      "(django_content_type.model = 'hoteldetail' and hcr.object_id = %s) or " \
                      "(django_content_type.model = 'roomdetail' and hcr.object_id in %s))"
        object_code_params = [rateplan_ids, hotel_id, room_ids]

    query = (
        "SELECT CONCAT_WS('$',hcr.content_type_id, hcr.object_id, priority), "
        "MAX(CONCAT_WS('$',hcr.modified_on,penalty_rules,"
        "hcr.id, IF(stay_start IS null,'',stay_start),"
        "IF(stay_end IS null,'',stay_end), IF(first_name is null,'',first_name),"
        "IF(last_name is null,'',last_name), IF(tm.snapshot is null,'', tm.snapshot),"
        "IF(hcr.segment is null,'',hcr.segment),"
        "IF(hcr.flagbit1 IS null, 0, hcr.flagbit1))),"
        "IF(na_checkin_weekday IS null,'',na_checkin_weekday) "
        "FROM hotels_cancellationrules hcr "
        "INNER JOIN django_content_type ON hcr.content_type_id = django_content_type.id "
        "INNER JOIN auth_user ON hcr.user_id = auth_user.id "
        "LEFT JOIN common_templatemapping tm ON hcr.id=tm.object_id "
        "AND tm.content_type_id=%s "
        "WHERE 1=1"
    )
    params = [264]  # tm.content_type_id constant
    if is_active_condition:
        query += ' AND ' + is_active_condition
        params.extend(is_active_params)
    if object_code:
        query += ' AND ' + object_code
        params.extend(object_code_params)
    if priority_condition:
        query += ' ' + priority_condition
        params.extend(priority_params)
    if filter_condition:
        query += filter_condition
        params.extend(filter_params)
    query += (
        " GROUP BY stay_start,stay_end, hcr.content_type_id, hcr.object_id, priority, hcr.created_on,"
        " na_checkin_weekday, hcr.segment ORDER BY hcr.created_on DESC"
    )
    cursor.execute(query, params)
    row = cursor.fetchall()
    formatted_canc_policy = format_cancellation_policy(row, rate_plan_dict, room_dict, hotel, explode_policy)
    return formatted_canc_policy


def deactivate_all_cancellation_policies(id, content_id):
    success = False
    try:
        cancellation_rule = CancellationRules.objects.filter(object_id=id, is_active=1, content_type_id = content_id)
        if cancellation_rule:
            cancellation_rule.update(is_active=0)
        message = "All cancellation policies removed successfully"
        success = True
        
    except Exception as e:
        message = str(e)
        api_logger.critical(message="Error: %s hotel_id %s "
        "traceback %s" % (str(e), id, repr(traceback.format_exc())),
        log_type='ingoibibo', bucket='CancellationPolicy',
        stage='delete_cancellation_policies')
    return message, success

def update_cancellation_policy(related_code_list, related_to, cancellation_policy, hotel, user, object_id):
    is_success = True
    error = {}
    try:
        for related_code in related_code_list:
            formatted_cancellation_policy = get_formatted_cancellation_policy(cancellation_policy, related_code, related_to)
            cancellation_rule_data, are_rules_valid, msg = get_cancellation_data(formatted_cancellation_policy, user.id, cancellation_policy.get('segment'))
            if not are_rules_valid:
                is_success = False
                error = {}
                error['message'] = CANCELLATION_RULES_ERROR_MAPPING['CR_E400'] % msg
                error['error_code'] = 'CR_E400'
                return is_success, error

            update_dict = {
                "stay_start": formatted_cancellation_policy.get('stayStart'),
                "stay_end": formatted_cancellation_policy.get('stayEnd'),
                "content_type_id": cancellation_rule_data[0].get('content_type'),
                "priority": formatted_cancellation_policy.get('priority'),
                "ref_type": cancellation_rule_data[0].get('ref_type'),
                "na_checkin_weekday": formatted_cancellation_policy.get('na_checkin_weekday'),
                "penalty_rules": cancellation_rule_data[0].get('penalty_rules'),
                "segment": formatted_cancellation_policy.get('segment')
            }

            update_rule(update_dict, object_id)
            from api.v1.templates.resources.resources import update_template_mapping
            update_template_mapping(object_id, cancellation_rule_data[0])

    except Exception, e:
        is_success = False
        error = {}
        error['message'] = CANCELLATION_RULES_ERROR_MAPPING['CR_E500']
        error['error_code'] = 'CR_E500'
        api_logger.critical(message="Error from update_cancellation_policy id %s traceback %s" % (object_id, repr(traceback.format_exc())),
                            log_type='ingoibibo', bucket='CancellationPolicy',
                            stage='update_cancellation_policy')
    return is_success, error


def update_rule(update_dict, cancellation_id):
    try:
        cancel_rule = CancellationRules.objects.filter(id=cancellation_id)
        if cancel_rule:
            cancel_rule.update(**update_dict)
    except Exception, e:
        api_logger.info(message="Error from update_c traceback %s" % (repr(traceback.format_exc())),
                            log_type='ingoibibo', bucket='CancellationPolicy',
                            stage='update_cancellation_policy')


def copy_source_cancellation_policies(related_code_list, related_to, related_source_code, user):
    created_cancellation_policies_ids = []
    errors = []
    is_success = False

    try:
        source_content_object = get_content_object(related_to, related_source_code)
        source_object_id = None
        if related_to == 'rateplan':
            source_object_id = source_content_object['object_id']

        #Fetching all cancellation policies of source_related_code
        source_cancellation_policies = CancellationRules.objects.filter(object_id=source_object_id,
                                                                        content_type__id=source_content_object['content_type'],
                                                                        is_active=True).filter(Q(booking_date_end__isnull=False, \
                                                                                                booking_date_end__gte = datetime.datetime.now().date()) \
                                                                                              | (Q(stay_end__isnull=False, stay_end__gte = datetime.datetime.now().date())))

        #Fetching object ids corresponding to related_code_list
        related_object_ids = RatePlan.objects.only('id').filter(rateplancode__in=related_code_list)

        cloned_canc_policies_instances = []
        for rateplan in related_object_ids:
            for cancellation_policy in source_cancellation_policies:
                policy_data = model_to_dict(cancellation_policy)
                policy_data.pop('id', None)
                policy_data.pop('created_on', None)
                policy_data['user'] = user
                policy_data['content_type'] = cancellation_policy.content_type
                policy_data['object_id'] = rateplan.id
                cloned_canc_policies_instances.append(CancellationRules(**policy_data))

        CancellationRules.objects.bulk_create(cloned_canc_policies_instances)
        cloned_cancellation_policy_id = CancellationRules.objects.using('default').only('id').filter(object_id=related_object_ids[0].id,
                                                                                        content_type__id=source_content_object['content_type']).last()
        #Formatting to match the response format of function create_cancellation_policies so that a single function
        #can be used to format the API response
        created_cancellation_policies_ids.append([{'id': cloned_cancellation_policy_id.id}])
        is_success = True
    except Exception as e:
        error = {}
        error['message'] = CANCELLATION_RULES_ERROR_MAPPING['CR_E500']
        error['error_code'] = 'CR_E500'
        errors.append(error)
        api_logger.critical(message="Error: %s related_code_list %s related_to %s user %s "
                                    "traceback %s" % (str(e), related_code_list, related_to, user.id,
                                                      repr(traceback.format_exc())),
                            log_type='ingoibibo', bucket='CancellationPolicy',
                            stage='create_cancellation_policies')

    return created_cancellation_policies_ids, errors, is_success
