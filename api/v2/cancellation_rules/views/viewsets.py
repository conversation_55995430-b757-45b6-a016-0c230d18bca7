import django_filters
from django.core.paginator import EmptyPage
from rest_framework.exceptions import NotFound

from rest_framework.response import Response
from rest_framework import status, filters
from rest_framework.authentication import TokenAuthentication, SessionAuthentication, BasicAuthentication
from rest_framework.decorators import list_route
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON>, JSONParser, MultiPartParser
from rest_framework.permissions import IsAuthenticated
from rest_framework.viewsets import ModelViewSet

from common.constants import EVENT_NAME
from common.threads import ContentPipelineEventHandler, PTCPIngoEvents
from api.v2.cancellation_rules.permissions.permissions import *
from api.v2.pagination import StandardResultsSetPagination
from api.v2.cancellation_rules.serializers.serializers import CancellationRuleSerializer
from api.v2.cancellation_rules.resources.resources import create_cancellation_policies, \
    deactivate_cancellation_policies, get_cancellation_policy
from hotels.models import CancellationRules
from ingo_partners.resources.configure import get_config_data
from django.conf import settings
from api.v2.cancellation_rules.resources.constants import CancellationRulesMetadata

config_data = get_config_data(value_key=True)
api_logger = Logger(logger='inventoryAPILogger')


class CustomFilter(django_filters.FilterSet):
    class Meta:
        model = CancellationRules
        fields = ['id', 'object_id', 'content_type', 'stay_start', 'stay_end']


class CancellationRuleViewSet(ModelViewSet):
    authentication_classes = (BasicAuthentication, TokenAuthentication)
    filter_backends = (filters.DjangoFilterBackend,)
    filter_class = CustomFilter
    http_method_names = ['get', 'put', 'post']
    pagination_class = StandardResultsSetPagination
    serializer_class = CancellationRuleSerializer
    parser_classes = (FormParser, MultiPartParser, JSONParser)
    lookup_field = 'id'

    def get_permissions(self):

        if self.request.method == 'POST':
            self.permission_classes = [IsAuthenticated, IsHotelAccessibility, GuardianPermissionCheck]
        else:
            self.permission_classes = [IsAuthenticated, IsHotelAccessibility]
        return super(CancellationRuleViewSet, self).get_permissions()

    def get_metadata_value(self, cancellation_type, description, day):
        metadata = {}
        metadata[CancellationRulesMetadata.TYPE] = cancellation_type
        metadata[CancellationRulesMetadata.DESCRIPTION] = description
        metadata[CancellationRulesMetadata.FREE_TILL_DAY] = day
        return metadata

    def get_metadata(self):
        return [
            self.get_metadata_value(CancellationRulesMetadata.FREE_CANCEL,
                                CancellationRulesMetadata.FREE_CANCEL_DESCRIPTION,
                                CancellationRulesMetadata.FREE_CANCEL_DAY),
            self.get_metadata_value(CancellationRulesMetadata.FREE_CANCEL_24HR,
                                    CancellationRulesMetadata.FREE_CANCEL_24HR_DESCRIPTION,
                                    CancellationRulesMetadata.FREE_CANCEL_24HR_DAY),
            self.get_metadata_value(CancellationRulesMetadata.FREE_CANCEL_48HR,
                                    CancellationRulesMetadata.FREE_CANCEL_48HR_DESCRIPTION,
                                    CancellationRulesMetadata.FREE_CANCEL_48HR_DAY),
            self.get_metadata_value(CancellationRulesMetadata.FREE_CANCEL_72HR,
                                    CancellationRulesMetadata.FREE_CANCEL_72HR_DESCRIPTION,
                                    CancellationRulesMetadata.FREE_CANCEL_72HR_DAY),
            self.get_metadata_value(CancellationRulesMetadata.NON_REFUNDABLE,
                                    CancellationRulesMetadata.NON_REFUNDABLE_DESCRIPTION,
                                    CancellationRulesMetadata.NON_REFUNDABLE_DAY)
        ]



    def list(self, request, *args, **kwargs):
        response = {'message': '', 'success': False}
        if not request.hotel:
            response['message'] = 'Please provide correct hotelcode'
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        try:
            rule_type = request.GET.get('rule_type', '')
            filter_type = request.GET.get('filter_type', '')
            filter_value_list = request.GET.get('filter_values', '').split(',')
            fetch_all = request.GET.get('all', False)
            source_config = request.GET.get('source_config','')
            source_config_id = config_data.get(source_config, None)
            ## sql_injection id: 12 R
            formatted_canc_policy = get_cancellation_policy(request.hotel, request.GET.get('rule_type', ''),
                                                            filter_type, filter_value_list, False, fetch_all, source_config_id)
            if rule_type == 'fc':
                response.update({'count': len(formatted_canc_policy), 'results': formatted_canc_policy,
                                 'success': True})
                return Response(response, status=status.HTTP_200_OK)

            page = self.paginate_queryset(formatted_canc_policy)
            response = self.get_paginated_response(page)
            response.data['metadata'] = self.get_metadata()
            response.data['success'] = True
            return response
        except EmptyPage, e:
           return self.send_empty_page_response()
        except NotFound, e:
            return self.send_empty_page_response()
        except Exception, e:
            response['message'] = 'Some Error Occurred'
            api_logger.critical(message="Permission denied: %s" % (str(e)), log_type='ingoibibo',
                                bucket='CancellationRulesAPI',
                                stage='cancellation_policies.views.list')
        return Response(response, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @list_route(methods=['GET'], url_path='retrieve')
    def retrieve_rules(self, request, *args, **kwargs):
        response = {'message': '', 'success': False}
        if not request.hotel:
            response['message'] = 'Please provide correct hotelcode'
            return Response(response, status=status.HTTP_400_BAD_REQUEST)

        try:
            rule_type = request.GET.get('rule_type', '')
            filter_type = request.GET.get('filter_type', '')
            filter_value_list = request.GET.get('filter_values', '').split(',')
            ## sql_injection id: 12 R
            formatted_canc_policy = get_cancellation_policy(request.hotel, rule_type, filter_type, filter_value_list, True)
            response.update({'data': formatted_canc_policy, 'success': True})
            return Response(response, status=status.HTTP_200_OK)

        except Exception, e:
            response['message'] = 'Some Error Occurred'
            api_logger.critical(message="Permission denied: %s" % (str(e)), log_type='ingoibibo',
                                bucket='CancellationRulesAPI',
                                stage='cancellation_policies.views.list')
        return Response(response, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def create(self, request, *args, **kwargs):
        api_logger.info(
            message="API info for create cancellation policy : {}".format(request.__dict__),
            log_type='ingoibibo',
            bucket='CancellationRulesAPI',
            stage='rule.views.create')
        response = {'message': '', 'cancellation_rule': [], 'success': False}
        try:
            ContentPipelineEventHandler().add_attribute(EVENT_NAME, PTCPIngoEvents.onboarding)
            if request.data.get('policies'):
                cancellation_policy = {}
                cancellation_policy['rules'] = request.data['policies']
                related_code = cancellation_policy['rules'][0].get('relatedcode', '')
                related_to = cancellation_policy['rules'][0].get('relatedto', '')
            else:
                cancellation_policy = request.data
                related_code = cancellation_policy.get('relatedcode', '')
                related_to = cancellation_policy.get('relatedto', '')
            segment = request.data.get('segment', None)
            created_cancellation_policy, errors, is_success = create_cancellation_policies([related_code], related_to,
                                                                                           [cancellation_policy],
                                                                                           request.hotel,
                                                                                           request.user, segment)
            if not is_success:
                for error in errors:
                    response['message'] = error['message']
                    if error['error_code'] == 'CR_E500':
                        return Response(response, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                    else:
                        return Response(response, status=status.HTTP_400_BAD_REQUEST)
            else:
                if len(created_cancellation_policy) > 0:
                    response['cancellation_rule'] = created_cancellation_policy[0]
                    from hotels.models import HotelDetail
                    from hotels.models import ListYourHotel
                    from hotels.onboarding_state_machine import OnboardingStateMachine
                    onboarding = True if request.META.get('HTTP_ONBOARDING', '') == 'true' else False
                    source = request.META.get('HTTP_META_DATA_SOURCE', '')
                    if related_to == 'hotel':
                        hotel_obj = HotelDetail.objects.get(hotelcode=related_code)
                        if onboarding and source == settings.HOST_APP_SETTING['INGO_WEB_HEADER'] and hotel_obj:
                            draft_hotel_qs = ListYourHotel.objects.using('default').filter(hotelcode=hotel_obj.hotelcode)
                            if draft_hotel_qs.exists():
                                OnboardingStateMachine.process(draft_hotel_qs.first())
                response['message'] = 'Cancellation rules created successfully'
                response['success'] = True
        except Exception, e:
            response['message'] = 'Exception occurred'
            api_logger.critical(message="Permission denied: %s" % (str(e)), log_type='ingoibibo',
                                bucket='CancellationRulesAPI',
                                stage='rule.views.create')
            return Response(response, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        return Response(response, status=status.HTTP_201_CREATED)

    @list_route(methods=['post'], url_path='delete')
    def change_cancellation_status(self, request, *args, **kwargs):
        response_status = status.HTTP_200_OK
        success = False

        if not request.hotel:
            return Response({'msg': "Invalid hotel"}, status.HTTP_400_BAD_REQUEST)

        try:
            policy_id = request.data.get('id', None)
            policy_data = request.data
            msg, success = deactivate_cancellation_policies(policy_id, [policy_data], request.hotel, request.user)
        except Exception as e:
            api_logger.critical(message="Error: %s" % (str(e)), log_type='ingoibibo', bucket='CancellationRulesAPI',
                                stage='cancellation_policies.change_cancellation_status')
            msg = "Error while deleting cancellation rule"
            response_status = status.HTTP_500_INTERNAL_SERVER_ERROR
        return Response({'msg': msg, 'success': success}, response_status)

    def send_empty_page_response(self):
        response = {}
        response['count'] = 0
        response['data'] = []
        response['next'] = None
        response['prev'] = None
        return Response(response, status=status.HTTP_200_OK)

