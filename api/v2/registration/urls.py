from rest_framework import routers
from django.conf.urls import url, include

from api.v2.registration.views.registration import RegistrationDocumentViewSet

router = routers.DefaultRouter()

router.register(r'documents', RegistrationDocumentViewSet, 'RegistrationDocumentViewSet')
router.register(r'documents/update', RegistrationDocumentViewSet.update, 'RegistrationDocumentViewSet')
router.register(r'documents/delete', RegistrationDocumentViewSet.destroy, 'RegistrationDocumentViewSet')

urlpatterns = [
    url(r'^', include(router.urls)),
]
