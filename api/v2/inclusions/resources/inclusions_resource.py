from hotels.models.basic_models import Inclusions
from api.v2.inclusions.serializers.inclusions_serializers import  InclusionsSerializer
from utils.logger import Logger

api_logger = Logger(logger='inventoryAPILogger')
def get_inclusion_by_id(id):
    response = {'success': False, 'results': []}
    try:
        inclusion = Inclusions.objects.get(pk=id)
        data = InclusionsSerializer(inclusion).data
        response['success'] = True
        response['results'].append(data)
        return response
    except Exception, e:
        api_logger.critical(message='Errors: %s' % (str(e)), log_type='ingoibibo',
                            bucket='AmenitiesAPIv2', stage='amenities.views.Amenities_view')
        response['error'] = 'No Inclusion corresponding to given id'
        response.pop('results', None)
        return response

