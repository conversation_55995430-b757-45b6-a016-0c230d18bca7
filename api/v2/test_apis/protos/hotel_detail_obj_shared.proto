syntax = "proto3";

message ElixirSharedHotelDetail {
  /**
   * Base node for hotel data
   */
  message HotelDetailNode {
    string message_id = 18; // Unique id for each object pushed in the queue. Need to pass with the Acknoledgement.
    float created_time = 20; // Ingo Hotel's created time ( timestamp )
    float updated_time = 21; // Ingo Hotel's last modified time ( timestamp )
    string status = 22; // Hotel status : live, active_but_sold_out, inactive
    string hotel_name = 23; // Hotel name as per the registration in the ingo
    string display_name = 24; // Hotel display text
    string chain_name = 25; // Chain name if hotel is belongs to any chain.
    string chain_id = 26; // chain id registered with ingo server.
    string description = 27; // Hotel's description
    string short_description = 28; // Hotel's Short description
    string property_size = 29; // Total size of the property in terms of L * W
    string hotel_id = 30; // Hotel id as per the ingo
    /*
    *
    {
        “trust”: “Trust”,
        “ihg”: “IHG”,
        “derby”: “Derby”,
        “ingo”: “Ingoibibo”
       }
     */
    string gds_name = 31;
    string lang_iso_code = 34;
    int32 bedroom_count = 32;
    VendorDataNode vendor_data = 1;
    HostDetailNode host_details = 2;
    repeated ImageNode images = 3;
    LocationNode location = 4;
    AttributeNode attributes = 5;
    FinanceLegalAuditNode finance_legal_audit = 6;
    repeated SupplyNode supply = 7;
    ContactDetailNode contact_details = 8;
    PolicyInfoNode policy_info = 9;
    repeated AmenitiyNode amenities = 10;
    repeated UspNode usp = 11;
    repeated RoomDetailNode rooms = 12;
    repeated DocumentNode documents = 13;
    repeated DeltaList delta_list = 14;
    repeated AmenityV2Node amenities_v2 = 15;
    repeated VideoNode videos = 16;
    repeated SpaceDetailNode space_details = 17;
    repeated RoomSpaceMapNode room_space_map = 19;
    repeated string deactivation_reason = 33;
    string source_partner = 35;
    string source_hotelcode = 36;
    repeated HostDetailNodeV2 host_details_v2 = 37;
    repeated CaretakerDetailNode caretaker_details = 38;
    repeated ContactDetailNodeV2 contact_details_v2 = 39;
    bool prewarm_cache = 40;
    PreWarmConfig prewarm_cache_config = 41;
    RegistrationDocumentNode registration_document = 42;
    bool is_self_onboarded = 43;
    repeated ServiceDetailNode inclusions = 44;
  }

  message PreWarmConfig {
    repeated PaxConfig pax = 1;
    repeated  LengthOfStay los_list = 2;
    int32 advance_window = 3;
  }

  message PaxConfig {
    int32 adult_count = 1;
    int32 child_count = 2;
  }

  message LengthOfStay {
    int32 los = 1;
  }

  message VendorDataNode {
    MMTVendorNode mmt = 1;
    GIVendorNode gi = 2;
    CRSNode crs = 3;
    RightStayNode right_stay = 4;
    MMTGCCVendorNode mmt_gcc = 5;
  }

  message MMTVendorNode {
    bool mmt_assured = 1; // true if hotel is mmt assured
    string id = 2; // unique identifier from etl.
    string city_id = 3; // field to store the id of city reffered to mmt side.
    string city = 4; // City name in which hotel is located
    string locality_id = 5; // field to store the id of locality reffered to mmt side.
    string locality = 6; // locality name in which hotel is located
    bool content_only = 7;
    VendorDetailNode vendor_detail = 8;
  }

  message GIVendorNode {
    string id = 1; // unique identifier from vovager.
    bool price_personalization = 2;
    int64 facility_rating = 3;
    string city_id = 4; // city code stored in INGO side.
    string city = 5; // City name in which hotel is located
    string locality_id = 6; // field to store the id of locality reffered to ingo.
    string locality = 7; // locality name in which hotel is located
    bool content_only = 8;
    string locality_source = 9; // source of locality
    VendorDetailNode vendor_detail = 10;
  }

  message MMTGCCVendorNode {
    string id = 1;
    bool price_personalization = 2;
    VendorDetailNode vendor_detail = 3;
  }

  message CRSNode {
    string id = 1; // Thirdparty/Contract Property ID, Eg: NIDA
    string name = 2; // Thirdparty/Contract Property name
  }

  message RightStayNode {
    string id = 1;
    repeated string  activities = 2; // List of activities provided in right stay
    repeated string usps = 3; // List of usps
    repeated string neighbourhoods = 4; // List of neighbourhood places
  }

  message LocationNode {
    float latitude = 1;
    float longitude = 2;
    string city_id = 3;
    string zipcode = 4;
    string address = 5;
    string locality_id = 6;
    string locality = 7;
    string city = 8;
    string state = 9;
    string country = 10;
    string country_code = 11;
    string locus_city_code = 15;
    string locus_city_name = 16;
    float locus_city_lat = 17;
    float locus_city_long = 18;
    string locus_country_code = 19;
    string locus_country_name = 20;
    string ingo_city_id = 21;
    string map_link = 12;
    string directions = 13;
    repeated HotelRouteNode hotel_route = 14;
  }

  message HotelRouteNode {
    string id = 1;
    string type = 2;
    string route_text = 3;
  }

  message AttributeNode {
    int32 no_of_rooms = 10;
    string base_currency = 11;
    string timezone = 12;
    string checkin_time = 13;
    string checkout_time = 14;
    bool twenty_four_hour_check_in = 15;
    int32 max_infant_age = 16;
    int32 max_child_age = 17;
    int32 year_hotel_built = 20;
    int32 no_of_restaurant = 21;
    bool offer_on_extrabed = 22;
    bool extrabed_commissionable = 23;
    repeated string tags = 24;
    bool slot_booking_available = 25;
    bool ta_discounting = 26;
    bool promo_code_discount = 27;
    bool is_chat_enabled = 28;
    bool is_offline_booking_allowed = 29;
    bool go_stay = 30;
    string content_only = 31;
    bool dnd = 32;
    string hotel_category = 33;
    string hotel_type = 34;
    string property_type = 35;
    int32 star_rating = 36;
    int32 no_of_floors = 37;
    repeated int32 special_tag = 38;
    bool duplicate_hotel = 39;
    bool is_gstn_assured = 40;
    bool can_optin_for_gstn_invoice = 41;
    string hostel_type = 42;
    bool is_clean_stay = 43;
    BookingSettingNode booking_settings = 1;
    PahSettingNode pah_settings = 2;
    TaxSettingsNode tax_settings = 3;
    CmSettingNode cm_settings = 4;
    string property_category = 44;
    bool gross_pay = 45;
    bool is_express_checkin = 46;
    bool is_prebook_chat_enabled = 47;
    bool is_test_hotel = 48;
    bool is_blocked_at_funnels = 49;
    bool is_ingo_express = 50;
    bool is_insurance_required = 51;
    string datasource = 52;
    string current_property_view = 53;
    bool hotel_offline_booking_enabled = 54;
    bool offline_booking_blocked_from_admin = 55;
    bool has_enrolled_in_group_booking = 56;
    string host_living_situation = 57;
    bool ask_for_rate_for_group_booking = 58;
    string current_property_view_web = 59;
    bool is_abso = 60;
    bool dayuse_booking_enabled = 61;
    bool display_space_details = 62;
  }

  message BookingSettingNode {
    bool auto_checkin = 1;
    bool name_change_allowed = 2;
    bool date_change_allowed = 3;
    bool booking_auto_confirm = 4;
    string booking_preference = 5;
    bool inventory_block = 6;
    bool allow_lower_commission_offer = 7;
    bool is_rtb_enabled = 8;
    repeated string preapproved_rtb_segments = 9;
  }

  message PahSettingNode {
    bool pah = 1;
    bool pahx = 2;
    bool only_pah = 3;
    int32 pah_inv_threshold = 4;
    bool pah_room_level_inv_exposure = 5;
    bool pah_day_level_inv_exposure = 6;
    bool pah_model = 7;
  }

  message TaxSettingsNode {
    bool tax_on_commission = 1;
    bool tax_pah = 2;
    bool add_extraguest_to_declared_flag = 3;
    bool margin_on_tax = 4;
    bool apply_tax_on_effective_sell_rate = 5;
    bool is_houseboat_moving = 6;
    bool cpp_tax_inc = 7;
    bool cpp_comm_tax_inc = 8;
  }

  message CmSettingNode {
    bool push_net_amt_booking = 1;
  }

  message FinanceLegalAuditNode {
    float first_active_date = 5;
    bool pre_buy = 6;
    bool terms_and_conditions = 7;
    bool invoice_guaranteed = 8;
    bool accept_fraud_terms = 9;
    bool hotel_suspected = 10;
    bool physically_verified = 11;
    float verification_done_on = 12;
    float tnc_accepted_on = 13;
    float last_physically_verified_on = 14;
    string gstn = 15;
    string plb_model = 16;
    bool vcc_payment = 17;
    string vcc_currency = 18;
    string settlement_mode = 19;
    string pann_umber = 20;
    string name_on_pancard = 21;
    string agency_name = 22;
    string agency_address = 23;
    bool is_insurmount_tnc = 24;
    repeated TncDetailNode tnc_details = 25;
    repeated AccountDetailNode account_detail = 1;
    string gst_state_code = 26;
  }

  message TncDetailNode {
    string current_time = 1;
    string hotelier_agreement_url = 2;
    string name = 3;
    string designation = 4;
    string remote_host_ip = 5;
    string mobile_number = 6;
    int64 logged_in_user_id = 7;
    string online = 8;
  }


  message ContactDetailNode {
    repeated string email_list = 1;
    repeated string hotel_phone = 2;
    repeated string hotel_mobile = 3;
    repeated string website_url = 4;
    repeated RoleContactNode role_wise = 5;
    repeated string customercare_phonelist = 6;
  }


  message ContactDetailNodeV2 {
    string id = 1;
    string name = 2;
    bool is_primary = 3;
    string email = 4;
    string whatsapp = 5;
    string mobile = 6;
  }

  message PolicyInfoNode {
    string hotel_policy = 1;
    repeated PolicyRuleNode rules = 2;
    repeated PolicyV2Node rules_v2 = 3;
  }


  message HostDetailNode {
    reserved 6, 9, 11;
    reserved "hobbies", "education", "meal_preference";
    string id = 12;
    string name = 1;
    repeated string email = 2;
    repeated string mobile = 3;
    string about = 4;
    repeated string images = 5;
    repeated string languages = 7;
    string gender = 8;
    float time_since_hosting = 10;
    string first_name = 13;
    string last_name = 14;
    repeated string interests = 15;
    string profession = 16;
    HostAnalyticsDataNode analytics_data = 17;
    bool is_star_host = 18;
    bool is_vaccinated = 19;
    string time_since_hosting_v2 = 20;
    string is_vaccinated_v2 = 21;
  }

  message HostAnalyticsDataNode {
    string response_time = 1;  // will be in hours
    string response_rate = 2;  // will be percentage value
  }

  message ImageNode {
    string id = 7;
    string url = 1;
    string caption = 2;
    int32 order = 3;
    string description = 4;
    int32 height = 5;
    int32 width = 6;
    bool is_active = 8;
    string source = 9;
    repeated string tags = 10;
    string contrast_score = 11;
    string brightness_score = 12;
    string sharpness_score = 13;
    string sharpness_category = 14;
    string contrast_category = 15;
    string brightness_category = 16;
    string final_score = 17;
    string image_action = 18;
    repeated string auto_tags = 19;
    float created_time = 20;
  }

  message VideoNode {
    string id = 8;
    string url = 1;
    string thumbnail_url = 2;
    string caption = 3;
    int32 order = 4;
    string description = 5;
    int32 height = 6;
    int32 width = 7;
    bool is_active = 9;
    string source = 10;
    repeated string tags = 11;
    string video_format = 12;
    repeated ProcessedVideoNode processed_videos = 13;
    VideoMetaDataNode meta_data = 14;
  }

  message ProcessedVideoNode {
    string format = 1;
    string thumbnail_video = 2;
    repeated VideoSubNode videos = 3;
  }

  message VideoSubNode {
    string video_resolution = 1;
    string video_url = 2;
    string image_thumbnail = 3;
  }

  message VideoMetaDataNode {
    string fps = 1;
    string bit_rate = 2;
    string duration = 3;
    string aspect_ratio = 4;
    string pixel_aspect_ratio = 5;
  }

  message AccountDetailNode {
    string pan_number = 1;
    string bank_name = 2;
    string service_tax_num = 3;
    string ifsc = 4;
    string name_on_pancard = 5;
    string acc_name = 6;
    string code = 7;
    string branch_name = 8;
    string branch_code = 9;
    string acc_no = 10;
    string acc_type = 11;
  }

  message RoleContactNode {
    string id = 12;
    string title = 1;
    string name = 2;
    string designation = 3;
    string email = 4;
    string mobile = 5;
    repeated string landline = 6;
    repeated string category = 7;
    bool is_mobile_verified = 10;
    bool is_email_verified = 11;
    repeated AlternetMobileNode alternate_mobile = 8;
    repeated AlternetEmailNode alternate_email = 9;
  }

  message AlternetEmailNode {
    string email = 1;
    bool is_verified = 2;
  }

  message AlternetMobileNode {
    string mobile = 1;
    bool is_verified = 2;
  }

  message AmenitiyNode {
    string id = 1;
    string name = 2;
    string category = 3;
    string sub_category = 4;
    string mmt_code = 5;
  }

  message AmenityV2Attribute {
    string attribute_name = 1;
    repeated string sub_attributes = 2;
  }

  message AmenityV2Node {
    string amenity_id = 1;
    string id = 2;
    string level = 3;
    string main_category = 4;
    repeated string tags = 5;
    string amenity_name = 6;
    repeated AmenityV2Attribute attributes = 7;
  }

  message UspNode {
    string id = 1;
    string name = 2;
    string tag = 3;
    string desc = 4;
  }

  message RatePlanNode {
    string id = 1;
    string parent_id = 2;
    string name = 3;
    string meal_plan = 4;
    float sell_commission = 5;
    bool tax_included = 6;
    int32 cut_off_days = 7;
    repeated string contract_type = 8;
    bool non_refundable = 9;
    bool pah = 10;
    bool only_rateplan_offers = 11;
    int32 mlos = 12;
    repeated ServiceDetailNode inclusions = 13;
    bool is_active = 14;
    LinkedRatePlanNode linked_rateplan = 15;
    string source_rateplancode = 16;
    string description = 17;
    string source_config = 18;
    string long_description = 19;
  }

  message LinkedRatePlanNode {
    string linkage_basis = 1;
    string linkage_type = 2;
    float linkage_amount = 3;
    float extra_guest_linkage_amount = 4;
    bool link_block = 5;
  }

  message RoomDetailNode {
    string id = 10;
    string type = 11;
    string name = 12;
    string parent_id = 13;
    int32 no_of_rooms = 14;
    string size = 15;
    string view = 16;
    string description = 17;
    int32 pah_inventory_limit = 18;
    bool is_bathroom_shared = 19;
    int32 base_adult_occupancy = 20;
    int32 base_child_occupancy = 21;
    int32 max_adult_occupancy = 22;
    int32 max_infant_occupancy = 23;
    int32 max_guest_occupancy = 24;
    int32 max_child_occupancy = 25;
    string mmt_room_code = 28;
    bool is_subroom = 29;
    string room_size_unit = 30;
    int32 no_of_sub_rooms = 31;
    bool is_active = 32;
    string sellable_type = 33;
    string room_dorm_type = 34;
    repeated ImageNode images = 1;
    repeated AmenitiyNode amenities = 2;
    repeated RatePlanNode rateplans = 3;
    repeated UspNode usp = 4;
    repeated BedNode beds = 5;
    repeated BedNode extra_bed = 6;
    repeated AmenityV2Node amenities_v2 = 7;
    repeated VideoNode videos = 8;
    string sub_room_type = 35;
    string source_roomtypecode = 36;
    string source_config = 37;
    float createdon = 38;
    bool is_slot_room = 39;
  }

  message BedNode {
    string id = 1;
    string type = 2;
    int32 count = 3;
  }

  message DocumentNode {
    string url = 1;
    string name = 2;
    string description = 3;
  }

  message SupplyNode {
    string email = 1;
    string mobile = 2;
    string name = 3;
    string category = 4;
  }

  message DeltaList {
    string type = 1;
    string id = 2;
    string source = 3;
  }

  message VendorDetailNode {
    string vendor_code = 1;
    repeated AccountDetailNode account_detail = 2;
    string vat_registration_number = 3;
  }

  message PolicyRuleNode {
    string id = 1;
    string template_id = 2;
    string template_text = 3;
    string template_category = 4;
    repeated AttributeListNode attribute_list = 5;
    string template_order = 6;
  }

  message AttributeListNode {
    string fine_print = 1;
    repeated PolicyAttributeNode attributes = 2;
  }

  message PolicyAttributeNode {
    string label = 1;
    repeated string values = 2;
    string value_type = 3;
    string prefix = 4;
    string postfix = 5;
  }


  message PolicyV2Node {
    string id = 1;
    string template_id = 2;
    string template_text = 3;
    string category_id = 4;
    string category_text = 5;
    string template_order = 7;
    string grammar_text = 8;
    repeated PolicyValuesNode policy_data = 9;
    string parent_id = 10;
    repeated string tags = 11;

  }


  message PolicyValuesNode {
    repeated string value = 1;
    enum ValuesType {
      UNKNOWN = 0;
      TEXT_INPUT = 1;
      TEXT_AREA_INPUT = 2;
      NUMBER_INPUT = 3;
      DATE_INPUT = 4;
      DATE_RANGE_INPUT = 5;
      TIME_INPUT = 6;
      TIME_RANGE_INPUT = 7;
      RADIO = 8;
      CHECKBOX = 9;
      SINGLE_DROPDOWN = 10;
      MULTI_DROPDOWN = 11;
      PHONE_INPUT = 12;
      CURRENCY_INPUT = 13;
      RANGE_INPUT = 14;
      TEXT_WITH_CHOICE = 15;
      NUMBER_WITH_CHOICE = 16;
    }
    ValuesType value_type = 3; //-->enums
    string prefix = 4;
    string postfix = 5;
  }


  message HostDetailNodeV2 {
    reserved 6, 9, 11;
    reserved "hobbies", "education", "meal_preference";
    string id = 12;
    string name = 1;
    repeated string email = 2;
    repeated string mobile = 3;
    string about = 4;
    repeated string images = 5;
    repeated string languages = 7;
    string gender = 8;
    float time_since_hosting = 10;
    string first_name = 13;
    string last_name = 14;
    repeated string interests = 15;
    string profession = 16;
    HostAnalyticsDataNode analytics_data = 17;
    bool is_star_host = 18;
    string hotelier_type_value = 19;
    bool is_vaccinated = 20;
    string time_since_hosting_v2 = 21;
    string is_vaccinated_v2 = 22;
  }


  message CaretakerDetailNode {
    string id = 1;
    string name = 2;
    string mobile = 3;
    string email = 4;
    bool is_fulltime = 5;
    string start_time = 6;
    string end_time = 7;
    repeated string tasks = 8;
    repeated string languages = 9;
    bool is_communicable = 10;
    bool is_vaccinated = 11;
    string profile_image = 12;
    string is_fulltime_v2 = 13;
    string is_vaccinated_v2 = 14;
  }

  message RoomSpaceMapNode {
    string room_code = 1;
    string space_id = 2;
  }

  message SpaceDetailNode {
    string id = 1;
    string space_name = 2;
    string space_type = 3;
    bool is_sellable = 4;
    bool is_active = 5;
    bool is_accessible = 6;
    bool is_shared = 7;
    repeated string not_accessible_reason = 8;
    repeated AmenityV2Node amenities_v2 = 9;
    repeated ImageNode images = 10;
    repeated VideoNode videos = 11;
    repeated PolicyV2Node rules_v2 = 12;
    repeated LinkedSpacesNode linked_spaces = 13;
    SpaceAttributeNode attributes = 14;
  }

  message SpaceAttributeNode {
    GeneralInfoNode general_info = 1;
    SleepingArrangementNode sleeping_arrangement = 2;
    FacilitiesNode facilities = 3;
    SupplyUpKeepNode supply_upkeep = 4;
  }

  message GeneralInfoNode {
    string space_alias = 1;
    string description = 2;
    SpaceSizeNode space_size = 3;
    string floor_level = 4;
    string room_view = 5;
    string bathroom_type = 6;
    string pool_size = 7;
    bool heated_pool = 8;
  }

  message SpaceSizeNode {
    string type = 1;
    string unit = 2;
    string value = 3;
  }
  message SleepingArrangementNode {
    repeated BedNode beds = 1;
    repeated BedNode extra_bed = 2;
    int32 max_guest_occupancy = 3;
    int32 min_guest_occupancy = 4;
  }

  message FacilitiesNode {
    bool has_attached_bathroom = 1;
    bool has_balcony = 2;
    bool is_balcony_furniture_accessible = 3;
  }

  message SupplyUpKeepNode {
  }

  message LinkedSpacesNode {
    string space_id = 1;
    string space_type = 2;
  }

  message RegistrationDocumentNode {
    string id = 1;
    string registration_number = 2;
    float expiry_date = 3;
    string document_url = 4;
    bool is_active = 5;
  }

  message ServiceDetailNode {
    message ServiceConditionNode {
      int32 id = 1;
      int32 stay_weekdays = 2;
      int32 mlos = 3;
      string extra_info = 4;
      bool is_active = 5;
      int32 stay_std = 6;
      int32 stay_end = 7;
      int32 bkg_std = 8;
      int32 bkg_end = 9;
      string stay_blackout_dates = 10;
      int32 segment = 11;
      string modified_on = 12;
      string createdon = 13;
    }

    message ServiceAttributeListItemNode {
      int32 id = 1;
      string label = 2;
    }

    message ServiceAttributeNode {
      string attribute_key = 1;
      bool is_setting = 2;
      bool required = 3;
      string label = 4;
      string label_category = 5;
      int32 attribute_id = 6;
      string setting_key = 7;
      string type = 8;
      repeated ServiceAttributeListItemNode attribute_list = 9;
      float min_value = 10;
      float max_value = 11;
    }

    message ServiceLeafCategoryNode {
      uint32 id = 1;
      repeated ServiceAttributeNode attributes = 2;
      int32 priority_order = 4;
      uint32 created_by = 5;
      string leaf_category_name = 6;
      repeated string tags = 7;
      bool is_active = 8;
      string created_on = 9;
      string modified_on = 10;
      int32 service_category = 11;
      repeated string label_order = 12;
      string image = 13;
      string image_thumb = 14;
      string image_icon = 15;
    }

    message ServiceSourceConfigNode {
      int32 id = 1;
      string name = 2;
      string hotel_vendor = 3;
    }

    int32 id = 1;
    int32 object_id = 2;
    ServiceConditionNode condition = 3;
    map<string, string> service_value = 4;
    map<string, string> selected_attributes = 5;
    string service_mode = 6;
    string pay_mode = 7;
    string status = 8;
    map<string, string> metadata = 9;
    map<string, string> mod_metadata = 10;
    int32 hotelcode = 11;
    bool is_default = 12;
    bool is_mandatory = 13;
    bool avail_subject = 14;
    int32 source_config = 15;
    int32 content_type = 16;
    ServiceLeafCategoryNode leaf_category = 17;
    string modified_by = 18;
    int32 value_type = 19;
    string mod_status = 20;
    string modified_on = 21;
    string createdon = 22;
    bool subject_availability = 23;
    float per_night_adult = 24;
    float default_price = 25;
    float per_night_infant = 26;
    float per_night_child = 27;
    float per_stay_adult = 28;
    float per_night_room = 29;
    float per_stay_infant = 30;
    float per_stay_child = 31;
    float per_stay_room = 32;
  }
}