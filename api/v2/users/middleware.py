from django.http import HttpResponse
from django.core.urlresolvers import resolve, reverse
from hotels.models.user_management import ApiRegistry
from common.constants import UserPermissionConstants
from api.v2.users.service import UserAuthorization<PERSON>elper, HardBlockUserHelper
from rest_framework.response import Response
from django.http import JsonResponse
from rest_framework import status
from utils.logger import Logger
from rest_framework.request import Request
from django.utils.functional import SimpleLazyObject
from django.core.cache import cache
from ingouser.models import User
from django.contrib.auth.middleware import get_user
from rest_framework.authentication import SessionAuthentication, TokenAuthentication, \
    BasicAuthentication
from api.v2.users.resources.services import CacheUtil

import re
import json
import zlib
import traceback

list_of_paths = []
list_of_compiled_regexes = []
logger = Logger(logger="inventoryLogger")
USER_MANAGEMENT_CACHE_KEY_SUFFIX = "user_extranet_permissions"
cache_helper = CacheUtil()
class UserAuthMiddleware(object):

    def __init__(self):

        self.user_auth_helper = UserAuthorizationHelper.get_user_auth_helper()

    def construct_cache_inner_key(self, uri, http_method):
        return "%s_%s" % (uri, http_method)

    def process_view(self, request, view_func, view_args, view_kwargs):

        requested_uri = request.path[1:]
        http_method = request.method
        user_id = request.user.id

        # call Heimdall for authorization.
        try:
            if not user_id:
                authenticated_user = get_user_from_request(request)
                user_id = authenticated_user.id if authenticated_user else None
            if user_id:
                cache_primary_key = "%s_%s" % (
                    user_id, USER_MANAGEMENT_CACHE_KEY_SUFFIX)
                user_permission_object_from_cache = cache_helper.get(
                    cache_primary_key, {})
                cache_inner_key = self.construct_cache_inner_key(
                    requested_uri, http_method)
                if user_permission_object_from_cache:
                    if cache_inner_key in user_permission_object_from_cache:
                        auth_resp = user_permission_object_from_cache[cache_inner_key]
                    else:
                        auth_resp = self.user_auth_helper.check_user_authorized_with_heimdall(
                            user_id, requested_uri, http_method)
                        user_permission_object_from_cache[cache_inner_key] = auth_resp
                        cache_helper.set(cache_primary_key,
                                         user_permission_object_from_cache)
                else:
                    auth_resp = self.user_auth_helper.check_user_authorized_with_heimdall(user_id, requested_uri,
                                                                                          http_method)
                    user_permission_object_from_cache = {}
                    user_permission_object_from_cache[cache_inner_key] = auth_resp
                    cache_helper.set(cache_primary_key,
                                     user_permission_object_from_cache)
                if not auth_resp:
                    logger.debug(
                        message='Permission denied using APIRegistry, user_id: {0} | requested_uri: {1} | http_method: {2}'
                            .format(user_id, requested_uri, http_method),
                        log_type='Ingoibibo', bucket='middleware', stage='user_management_middleware'
                    )
                    return JsonResponse({'status': False, 'data': [], 'message': 'Forbidden'}, status=status.HTTP_403_FORBIDDEN)
        except Exception as e:
            logger.critical(message='Error Contacting Heimdall Server with exception %s' % str(e),
                            log_type='Ingoibibo', bucket='middleware', stage='user_management_middleware ')


class HardBlockUserMiddleware(object):

    def __init__(self):
        self.hard_block_user_helper = HardBlockUserHelper.get_instance()

    def process_view(self, request, view_func, view_args, view_kwargs):

        requested_uri = request.path[1:]
        http_method = request.method
        user_id = request.user.id
        try:
            # Block only extranet calls
            source = self.hard_block_user_helper.get_request_source(request)
            if source != self.hard_block_user_helper.Clients['EXTRANET']:
                return None

            # Dont Block newonboarding API calls
            if self.hard_block_user_helper.is_newonboarding(request):
                return None

            # Dont Hardblock Staff users
            if not user_id:
                user = get_user_from_request(request)
            else:
                user = User.objects.filter(id=user_id).first()
            if not user or user.is_staff:
                return None

            # Block only api calls having hotelcode contained in it.
            # Getting hotel code from request by searching in url path, body and query params.
            hotel_code = self.hard_block_user_helper.find_hotel_code_in_request(
                request)
            if not hotel_code:
                return None

            # check if HotelCode blocked in cache
            if not self.hard_block_user_helper.check_is_user_hard_blocked(hotel_code):
                return None

            #Allow Apis on configurable screens
            api_ids = self.hard_block_user_helper.match_uri_with_api_registry(
                requested_uri, http_method)
            if not self.hard_block_user_helper.is_valid_api_screen(api_ids):
                return JsonResponse({'status': False, 'data': [], 'message': 'Forbidden - Hardblocked User'},
                                        status=status.HTTP_403_FORBIDDEN)
        except Exception as e:
            logger.critical(message='Error Occurred while hard-blocking user, Error: %s Exception:  %s'
                                    % (str(e), repr(traceback.format_exc())),
                            log_type='Ingoibibo', bucket='middleware', stage='user_management_middleware ')


def get_user_from_request(request):
    user = get_user(request)
    if user.is_authenticated():
        return user
    for authentication in [TokenAuthentication, SessionAuthentication, BasicAuthentication]:
        try:
            user = authentication().authenticate(Request(request))
            if user is not None:
                return user[0]
        except:
            pass
    return user

# class OneTimeAPIRegistryMiddleware(object):
#     pass
#
#     # def process_request(self, request):
#     # print "Middleware executed: Now passing the request to views."
#
#
#     def process_view(self, request, view_func, view_args, view_kwargs):
#         global list_of_compiled_regexes, list_of_paths
#         print "URL path being queried is " + request.path
#         # remove starting slash
#         requested_uri = request.path[1:]
#         for index, regex in enumerate(list_of_compiled_regexes):
#             insert_stmt = '''insert into hotels_apiregistry (name, uri, http_methods_supported,permission_type,is_active,createdon) values("{}","{}",'{}',{},1,now());'''
#             p_type = UserPermissionConstants.READ_AND_WRITE if request.method in ["POST",
#                                                                                 "PUT"] else UserPermissionConstants.READ
#             found = False
#             if re.search(regex, requested_uri):
#                 print "request matched with pattern %s, ID: %s" % (list_of_paths[index], index)
#                 print "Checking if the pattern exists in our database"
#                 # This is a one time activity to map all APIs into Database , also would like you to simultaenously generate create statements.
#                 db_objects = ApiRegistry.objects.filter(uri=list_of_paths[index])
#                 for obj in db_objects:
#                     if request.method in obj.get_http_methods_supported():
#                         found = True
#                 if not db_objects or not found:
#                     new_api = ApiRegistry(name=view_func.func_name, uri=list_of_paths[index], is_active=True)
#                     new_api.set_http_methods_supported([request.method])
#
#                     new_api.permission_type = p_type
#                     new_api.save()
#
#
#
#                 #Need to generate SQL Insert statement for the same here.
#                 formatted_statement = insert_stmt.format(view_func.func_name, list_of_paths[index],
#                                                          json.dumps([request.method]), p_type, )
#                 with open('/Users/<USER>/Documents/insert_statements_user_mgmt.txt', 'a+') as the_file:
#                     the_file.write(formatted_statement+'\n')
#
#                 # end comment block
#                 return None
#         formatted_statement = insert_stmt.format(view_func.func_name, " ^"+requested_uri+"$",
#                                                  json.dumps([request.method]), p_type, )
#         print "request did not match with any pattern , review all your patterns present in django urls.py for "+request.path
#         with open('/Users/<USER>/Documents/insert_statements_user_mgmt(review).txt', 'a+') as the_file:
#             the_file.write(formatted_statement + '\n')
#
#
#         # from django.core.urlresolvers import resolve
#         # print "url name is %s"%(resolve(request.path).url_name)
#         # from django.core.urlresolvers import RegexURLPattern, RegexURLResolver
#         # if hasattr(view_func,'suffix') and view_func.suffix:
#         #     view_func_name = view_func.cls
#         # else:
#         #     view_func_name = view_func.func_name
#         # is_api_drf = hasattr(view_func,'cls')
#         # print "url is "
#         #
#         # if is_api_drf:
#         #     if "-" in request.resolver_match.view_name:
#         #         fully_qualified_classname = view_func.cls.__module__ + "." +view_func.cls.__name__ + "."+\
#         #                                     request.resolver_match.view_name
#         #     else:
#         #         http_method = request.method.lower()
#         #         fully_qualified_classname = view_func.cls.__module__ + "." +view_func.cls.__name__ + "." + http_method
#         # else:
#         #     fully_qualified_classname = request.resolver_match.view_name
#         #
#         # return HttpResponse("%s,%s"%(fully_qualified_classname, is_api_drf), http_status.HTTP_200_OK)
#
#
#
#
#
#
#
#         # from django.conf import settings
#         # from django.core.urlresolvers import RegexURLResolver, RegexURLPattern
#         # root_urlconf = __import__(settings.ROOT_URLCONF)  # import root_urlconf module
#         # all_urlpatterns = root_urlconf.urls.urlpatterns  # project's urlpatterns
#         # VIEW_NAMES = []  # maintain a global list
#         #
#         # def get_all_view_names(urlpatterns):
#         #     global VIEW_NAMES
#         #     for pattern in urlpatterns:
#         #         if isinstance(pattern, RegexURLResolver):
#         #             get_all_view_names(pattern.url_patterns)  # call this function recursively
#         #         elif isinstance(pattern, RegexURLPattern):
#         #             view_name = pattern.callback_str  # get the view name
#         #             VIEW_NAMES.append(view_name)  # add the view to the global list
#         #     return VIEW_NAMES
#         #
#         # get_all_view_names(all_urlpatterns)

# class RegexGenerator():
#     def __init__(self):
#         import re
#         from django.core.urlresolvers import get_resolver
#
#         converter = re.compile(r"\?P<.*?>")
#
#         def trim_leading_caret(s):
#             while s.startswith('^'):
#                 s = s[1:]
#             return s
#
#         def recursive_parse(urlpatterns, lst, prefix=None):
#             for pattern in urlpatterns:
#                 path = (prefix or '') + trim_leading_caret(converter.sub("", pattern.regex.pattern))
#                 if hasattr(pattern, "url_patterns"):
#                     recursive_parse(pattern.url_patterns, lst, path)
#                 else:
#                     lst.append('^' + path)
#
#         def generate_paths(urlpatterns):
#             paths = []
#             recursive_parse(urlpatterns, paths)
#             return paths
#
#         from django.conf import settings
#         from django.core.urlresolvers import RegexURLResolver, RegexURLPattern
#         root_urlconf = __import__(settings.ROOT_URLCONF)  # import root_urlconf module
#         all_urlpatterns = root_urlconf.urls.urlpatterns
#         paths = generate_paths(all_urlpatterns)
#         global list_of_compiled_regexes
#         global list_of_paths
#         list_of_paths = paths
#         for path in paths:
#             list_of_compiled_regexes.append(re.compile(path))
