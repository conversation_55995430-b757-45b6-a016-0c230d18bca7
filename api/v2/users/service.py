import grpc
import json
import re
from django.conf import settings
from api.v2.users.proto import auth_pb2_grpc, account_pb2, account_pb2_grpc
from api.v2.users.proto import auth_pb2
from api.v2.users.proto import cache_pb2_grpc
from api.v2.users.proto import cache_pb2
from django.core.cache import cache
import ast
import os

'''
The class is a Singleton class and would be lazily loaded upon First Call Post which it would be reusing the same connection.
'''
ssl_certificate_file = settings.PROJECT_PATH + '/goibibo_inventory/settings/san-aws-ecs-mmt.crt'


class UserAuthorizationHelper():
    instance = None

    def __init__(self):
        # configure the host and the
        # the port to which the client should connect
        # to.
        if UserAuthorizationHelper.instance:
            return UserAuthorizationHelper.instance
        self.host = settings.HEIMDALL_HOST
        self.server_port = settings.HEIMDALL_PORT
        self.metadata = [('x-calling-service', 'Inventory')]

        if os.environ.get("CURR_ENV") in ("prod","qa"):
            # reading certificate file
            with open(ssl_certificate_file, 'rb') as f:
                trusted_certs = f.read()

            # create credentials
            credentials = grpc.ssl_channel_credentials(root_certificates=trusted_certs)

            # instantiate a communication channel
            self.channel = grpc.secure_channel(
                '{}:{}'.format(self.host, self.server_port), credentials=credentials,
                options=(('grpc.enable_http_proxy', 0),
                         ('grpc.client_idle_timeout_ms', 5000)))
        else:
            # instantiate a communication channel
            self.channel = grpc.insecure_channel(
                '{}:{}'.format(self.host, self.server_port), options=(('grpc.enable_http_proxy', 0),))

        # bind the client to the server channel
        self.stub = auth_pb2_grpc.AuthServiceStub(self.channel)

    @staticmethod
    def get_user_auth_helper():
        if not UserAuthorizationHelper.instance:
            UserAuthorizationHelper.instance = UserAuthorizationHelper()
        return UserAuthorizationHelper.instance

    def check_user_authorized_with_heimdall(self, user_id, uri, http_method):
        req = auth_pb2.UserAuthRequest(user_id=str(
            user_id), request_uri=uri, http_method=http_method)
        response = self.stub.UserAuthorization(req, metadata=self.metadata)
        return response.is_user_authorized

    def check_enigma_token_authorized_with_heimdall(self, s2stoken):
        req = auth_pb2.SSOServiceValidatorRequest(service_to_service_auth_token=s2stoken)
        response = self.stub.SSOServiceValidator(req, metadata=self.metadata)
        return response.sso_auth_user if response.is_authenticated else None

    # Brand can be GI/MMT/INGO
    def get_source_of_management(self, brand, brand_uuid):
        req = auth_pb2.SourceOfManagementFromUuidRequest(brand=brand, brand_uuid=brand_uuid)
        response = self.stub.GetSourceOfManagement(req, metadata=self.metadata)
        return response.source_of_management

    # Brand can be GI/MMT/INGO
    def get_auth_user(self, brand, token):
        req = auth_pb2.SSOValidateB2CUserEligibilityRequest(source_auth_token=token, token_source=brand)
        response = self.stub.SSOValidateB2CUserEligibility(req)
        return response.sso_auth_user if response.valid else None


    '''
    sample usage 
    from api.v2.users.service import UserAuthorizationHelper
    x = UserAuthorizationHelper()
    x.check_user_authorized_with_heimdall('1','extranet/initial-data','GET')
    '''


class UserAccountHelper():
    instance = None

    def __init__(self):
        # configure the host and the
        # the port to which the client should connect
        # to.
        if UserAccountHelper.instance:
            return UserAccountHelper.instance
        self.host = settings.HEIMDALL_HOST
        self.server_port = settings.HEIMDALL_PORT
        self.metadata = [('x-calling-service', 'Inventory')]


        if os.environ.get("CURR_ENV") in ("prod"):
            # reading certificate file
            with open(ssl_certificate_file, 'rb') as f:
                trusted_certs = f.read()

            # create credentials
            credentials = grpc.ssl_channel_credentials(root_certificates=trusted_certs)

            # instantiate a communication channel
            self.channel = grpc.secure_channel(
                '{}:{}'.format(self.host, self.server_port), credentials=credentials,
                options=(('grpc.enable_http_proxy', 0),
                         ('grpc.client_idle_timeout_ms', 5000)))
        else:
            # instantiate a communication channel
            self.channel = grpc.insecure_channel(
                '{}:{}'.format(self.host, self.server_port), options=(('grpc.enable_http_proxy', 0),))

        # bind the client to the server channel
        self.stub = account_pb2_grpc.AccountStub(self.channel)

    @staticmethod
    def get_user_account_helper():
        if not UserAccountHelper.instance:
            UserAccountHelper.instance = UserAccountHelper()
        return UserAccountHelper.instance

    def send_verification_email(self, email, full_name):
        req = account_pb2.VerificationEmailRequest(email=email, fullName=full_name)
        response = self.stub.VerificationEmail(req, metadata=self.metadata)
        return

    def save_host_profile(self, request):
        response = self.stub.SaveHostProfile(request, metadata=self.metadata)
        return response

    def save_chain_host_details(self, request):
        response = self.stub.SaveChainHostDetails(request, metadata=self.metadata)
        return response


class CacheRefreshHelper():
    instance = None

    def __init__(self):
        # configure the host and the
        # the port to which the client should connect
        # to.
        if CacheRefreshHelper.instance:
            return CacheRefreshHelper.instance
        self.host = settings.HEIMDALL_HOST
        self.server_port = settings.HEIMDALL_PORT

        # instantiate a communication channel
        self.channel = grpc.insecure_channel(
            '{}:{}'.format(self.host, self.server_port))

        # bind the client to the server channel
        self.stub = cache_pb2_grpc.CacheServiceStub(self.channel)

    @staticmethod
    def get_heimdall_cache_refresh_helper():
        if not CacheRefreshHelper.instance:
            CacheRefreshHelper.instance = CacheRefreshHelper()
            return CacheRefreshHelper.instance

    def refresh_cache_on_heimdall(self):
        req = cache_pb2.RefreshInventoryCacheRequest()
        response = self.stub.RefreshInventoryCache(req)
        return response.success


class HardBlockUserHelper:
    """
    This class is a Singleton Class and will be used for hard blocking user. (Non-Staff and unverified users)
    """

    instance = None

    def __init__(self):
        self.cache_helper = HardBlockUserCacheHelper.get_instance()
        if HardBlockUserHelper.instance:
            return HardBlockUserHelper.instance

    unblocked_screens = {
        "Bookings": 1,
        "Rates": 2
    }

    Clients = {
        "M-WEB": "mweb",
        "EXTRANET": "extranet",
        "IOS": "ios",
        "ANDROID": "android"
    }

    def is_valid_api_screen(self, api_ids):
        if not api_ids:
            return True
        screen_ids = self.match_api_with_screen_mapper(api_ids)
        # checking if screen Booking or Rates & Inventory exist in screen ids list and hotel is domestic then,
        # raise Forbidden exception i.e, Hard-block the Request.
        if self.unblocked_screens['Bookings'] in screen_ids or self.unblocked_screens['Rates'] in screen_ids:
            return True
        return False

    def match_uri_with_api_registry(self, api_uri, http_method):
        api_registry_entries = self.cache_helper.get_api_registry_entries()
        entries = []
        for api_registry_entry in api_registry_entries:
            matched = re.match(api_registry_entry[2], api_uri)
            if matched:
                if http_method.lower() in api_registry_entry[1].split(','):
                    entries.append(api_registry_entry[0])
        return entries

    def match_api_with_screen_mapper(self, api_ids):
        screenmapper_dict = self.cache_helper.get_screen_api_mapper_dict()
        entries = set()
        for api_id in api_ids:
            entries.update(screenmapper_dict[str(api_id)])
        return list(entries)

    def check_is_user_hard_blocked(self, hotel_code):
        data = self.cache_helper.get_unverified_users(hotel_code)
        return data['is_domestic_hotel'] and not data['verified_email_and_mobile_of_any_one_user']

    @staticmethod
    def get_instance():
        if not HardBlockUserHelper.instance:
            HardBlockUserHelper.instance = HardBlockUserHelper()
        return HardBlockUserHelper.instance

    @staticmethod
    def find_hotel_code_in_request(request):
        if not 'application/json' in request.META.get('CONTENT_TYPE', ''):
            return None
        params, body = request.GET, json.loads(request.body or '{}')
        hotel_code = params.get('hotelcode', '') or params.get('hotel_code', '') or body.get('hotelcode', '') or body \
            .get('hotel_code', '') or ''
        if hotel_code:
            return hotel_code
        entries = re.findall("/\d+/", request.path)
        if entries:
            # removing forward slash from start and end of the string for fetching the hotel code.
            return entries[0][1: -1]
        return hotel_code

    @staticmethod
    def is_user_verified(user):
        from hotels.models.user_management import UserProfile
        user_profile = UserProfile.objects.filter(user=user).first()
        if user_profile and user_profile.mobile_verified and user_profile.email_verified:
            return True
        return False

    @staticmethod
    def get_request_source(request):
        headers = request.META
        if not headers:
            return ''
        meta_data = headers.get('HTTP_META_DATA')
        if not meta_data:
            return ''
        meta_dict = ast.literal_eval(meta_data)
        return meta_dict.get('source', '')

    @staticmethod
    def is_newonboarding(request):
        headers = request.META
        return headers.get('HTTP_ONBOARDING', 'false').lower() == 'true'


class HardBlockUserCacheHelper:
    instance = None

    def __init__(self):
        if HardBlockUserCacheHelper.instance:
            return HardBlockUserCacheHelper.instance

    cache_keys = {
        "API_REGISTRY": "api_registry",
        "SCREEN_API_MAPPER": "screen_api_mapper",
        "HARD_BLOCKER_HOTEL_ID": "hard_blocking_user_hotel_id"
    }

    CACHE_TTL = 1 * 24 * 60 * 60  # Cache TTL for 1 day

    def refresh_screenmap_cache(self):
        cache.delete(self.cache_keys['API_REGISTRY'])
        cache.delete(self.cache_keys['SCREEN_API_MAPPER'])

    def invalidate_hotel_ids_cache(self, hotel_detail_ids):
        for hotel_detail_id in hotel_detail_ids:
            cache_key = str(hotel_detail_id) + "-" + \
                        self.cache_keys['HARD_BLOCKER_HOTEL_ID']
            cache.delete(cache_key)

    def get_api_registry_entries(self):
        from hotels.models.user_management import ApiRegistry
        cache_key = self.cache_keys['API_REGISTRY']
        api_registry_entries = json.loads(cache.get(cache_key) or '[]')
        if not api_registry_entries:
            api_registry_entries = list(ApiRegistry.objects.filter(is_active=True).values_list(
                'id', 'http_methods_supported', 'uri'))
            cache.set(cache_key, json.dumps(api_registry_entries),
                      self.CACHE_TTL)
        return api_registry_entries

    def get_screen_api_mapper_dict(self):
        from hotels.models.user_management import ScreenApiMapper
        cache_key = self.cache_keys['SCREEN_API_MAPPER']
        screenmapper_entries = json.loads(cache.get(cache_key) or '{}')
        if not screenmapper_entries:
            screen_api_map = {}
            screenmapper_entries = ScreenApiMapper.objects.filter(is_active=True).values_list('api_id', 'screen_id')
            for screenmapper_entry in screenmapper_entries:
                api_id = screenmapper_entry[0]
                screen_id = screenmapper_entry[1]
                if api_id not in screen_api_map:
                    screen_api_map[api_id] = [screen_id]
                else:
                    screen_api_map[api_id].append(screen_id)
            cache.set(cache_key, json.dumps(screen_api_map),
                      self.CACHE_TTL)

        return screenmapper_entries

    def get_unverified_users(self, hotel_code):
        from hotels.models.helper import get_id_from_code
        from hotels.models.configuration import HotelCodeLength, HotelCodePrefix
        hotel_detail_id = int(get_id_from_code(
            hotel_code, HotelCodeLength, HotelCodePrefix))
        cache_key = str(hotel_detail_id) + "-" + \
                    self.cache_keys['HARD_BLOCKER_HOTEL_ID']
        data = json.loads(cache.get(cache_key) or '{}')
        if data:
            return data
        else:
            data = self.check_any_verified_user_from_db(hotel_detail_id)
            data.update(
                {"is_domestic_hotel": self.is_domestic_hotel(hotel_code)})
            cache.set(cache_key, json.dumps(data), self.CACHE_TTL)
        return data

    @staticmethod
    def is_domestic_hotel(hotel_code):
        from hotels.models.hoteldetail import HotelDetail
        hotel_obj = HotelDetail.objects.filter(hotelcode=hotel_code).first()
        if not hotel_obj:
            return False

        if hotel_obj.city_id:
            country_of_hotel = hotel_obj.city_id.country.countryname.lower() if \
                hotel_obj.city_id and hotel_obj.city_id.country \
                and hotel_obj.city_id.country.countryname else ''
        else:
            country_of_hotel = hotel_obj.cityfk.state.country.countryname.lower() if \
                hotel_obj.cityfk and hotel_obj.cityfk.state and hotel_obj.cityfk.state.country \
                and hotel_obj.cityfk.state.country.countryname else ''
        return country_of_hotel.lower() == 'india'

    @staticmethod
    def check_any_verified_user_from_db(hotel_detail_id):
        from hotels.models import HotelUserLink
        from hotels.models.user_management import UserProfile

        user_ids = HotelUserLink.objects.select_related('user') \
            .filter(hoteldetail_id=hotel_detail_id, user__is_staff=False) \
            .values_list('user_id', flat=True)
        user_profiles = UserProfile.objects.filter(user_id__in=user_ids,
                                                   is_active=True).values('email_verified', 'mobile_verified').all()
        verified_email_and_mobile_of_any_one_user = False
        for user_profile in user_profiles:
            if user_profile['email_verified'] and user_profile['mobile_verified']:
                verified_email_and_mobile_of_any_one_user = True
                break
        cache_data = {"verified_email_and_mobile_of_any_one_user": verified_email_and_mobile_of_any_one_user}
        return cache_data

    @staticmethod
    def get_instance():

        if not HardBlockUserCacheHelper.instance:
            HardBlockUserCacheHelper.instance = HardBlockUserCacheHelper()
        return HardBlockUserCacheHelper.instance
