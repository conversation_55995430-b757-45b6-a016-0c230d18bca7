from utils.logger import Logger
import itertools
import traceback

api_logger = Logger(logger='inventoryAPILogger')
BOOKING_DATE_FORMAT = '%Y-%m-%d'


def review_new_to_extra(reviews):
    try:
        for review in reviews:
            review['unmoderatedReply'] = {}
            review['voyagerId'] = review.get('vendorId', None)
            review['mmt_id'] = review.get('vendorId', None)
            review['featured'] = False
            review['approvedImageCount'] = 0

            if review.get('replies', []):
                for reply in review.get('replies', []):
                    if reply.get('repliedBy', '') == 'hotel':
                        review['rejectComment'] = reply.get('rejectComment', None)
                        review['replyStatus'] = reply.get('replyStatus', None)

            review['reply'] = review.get('replies', [])
            review['hotelReply'] = review.get('replies', [])
            review['status'] = review.get('reviewStatus', None)

            if review.get('totalRating', None):
                review['totalRating'] = review['totalRating'].get('value', 0)
                review['customer_rating'] = review['totalRating']
            else:
                review['totalRating'] = 0
                review['customer_rating'] = 0

            if review['bookingDetails']:
                review['createdon'] = review['bookingDetails'].get('bookedOn', '-')
                review['vendorbookingid'] = review['bookingDetails'].get('vendorBookingId', None)
                review['confirmbookingid'] = review['bookingDetails'].get('confirmbookingid', None)
                review['bookingId'] = review.get('vendorbookingid', None)
                review['genericId'] = review.get('bookingId', None)
                review['bookingDetails']['giBookingId'] = review.get('bookingId', None)

                review['checkout'] = review['bookingDetails'].get('checkout', '-')
                review['checkin'] = review['bookingDetails'].get('checkin', '-')

                review['hotelName'] = review['bookingDetails'].get('hotelName', None)
                review['hotelCity'] = review['bookingDetails'].get('hotelCity', None)
                review['noofrooms'] = review['bookingDetails'].get('noofrooms', None)

            if review['reviewDetails']:
                review['reviewerId'] = review['reviewDetails'].get('reviewerId', None)
                review['firstName'] = review['reviewDetails'].get('firstName', None)
                review['lastName'] = review['reviewDetails'].get('lastName', None)
                review['reviewTitle'] = review['reviewDetails'].get('reviewTitle', None)
                review['reviewContent'] = review['reviewDetails'].get('reviewContent', None)
                review['createdAt'] = review['reviewDetails'].get('createdAt', None)
                review['submittedAt'] = review['reviewDetails'].get('submittedAt', None)

            review['fromBooking'] = review['flags'].get('fromBooking', False) if review.get('flags', None) else False

    except Exception as e:
        api_logger.critical(message='Error: %s  %s' % (str(e), repr(traceback.format_exc())), log_type='ingoibibo',
                            bucket='ReviewsAPI', stage='Reviews.review_new_to_extra')
    return reviews


def mergeResponse(allresponse, response):
    try:
        flat = itertools.chain.from_iterable(allresponse)
        response['results'] = list(flat)
    except Exception as e:
        api_logger.critical(message='Error: %s  %s' % (str(e), repr(traceback.format_exc())), log_type='ingoibibo',
                            bucket='ReviewsAPI', stage='Reviews.mergeResponse')
    return response


def summary_new_to_extra(summary):
    try:
        summary['ingId'] = summary['hotelId']
        summary['hotelRating'] = summary['totalRating']['value'] if summary['totalRating'] else 0
        for rating in summary['ratings']:
            key = rating['keyName']
            value = rating['value']
            summary[key] = value
    except Exception as e:
        api_logger.critical(message='Error: %s  %s' % (str(e), repr(traceback.format_exc())), log_type='ingoibibo',
                            bucket='SummaryAPI', stage='Summary.parser_to_extranet')
    return summary
