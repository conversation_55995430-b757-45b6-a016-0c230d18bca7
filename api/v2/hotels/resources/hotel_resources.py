# -*- coding: utf-8 -*-
import json
import os
import re
import traceback
from datetime import datetime
import hashlib

from django.core.exceptions import PermissionDenied
from ingouser.models import User
from django.contrib.contenttypes.models import ContentType
from rest_framework.exceptions import ValidationError

from api.v2.common.resources.constants import BENEFICIARY_NAME_EMPTY_ERR_MSG
from api.v2.hotels.resources.hotel_data_helper import BANK_ACCOUNT_DETAIL_FIELD_TYPE, CONTACT_DETAIL_FIELD_TYPE, \
    USP_FIELD_TYPE, HOTEL_META_DATA_FIELD_TYPE, HOTEL_MESSAGES, DEFAULT_BDO, VAT_NOC_AGREEMENT_ID, \
    EXTRANET_CUTOFF_DATE_NEW_CP, APP_VERSION_NEW_CP, ALLOWED_APP_BRANDS, ALLOWED_PLATFORMS
from api.v2.statemachine.resources.resources import get_state_machine_es_transitions
from api.v2.validators import validate_list_of_dictionaries, validate_type
from bulk_uploader.common_helpers import validate_checkin_checkout_time
from common.commonhelper import convert_string_to_time, compare_version
from common.models import City
from common.services import ManagerMappingWrapper
from goibibo_inventory import settings
from goibibo_inventory.settings import HOSTPROFILE_REVAMP_CUTOFF_DATE
from hotels import hotelchoice
from hotels.hotelchoice import PROPERTY_ACTIVE_STATES, PROPERTY_STATES, HOMESTAY, SELF_SIGNUP_REFERENCE_TUPLE
from hotels.models import HotelDetail, VendorDetail, ListYourHotel, HotelUserLink
from hotels.models.fc_leads import DraftUserMapping, HotelAgreementMapping
from common.constants import UserLinkConstants, PREBOOK_CHAT_ENABLED, HOTEL
from hotels.models.helper import validate_checkin_end_time, validate_checkin_checkout
from lib.AWSS3Upload import S3Upload, BucketMapper, DOCUMENT_OBJECT_TYPE
from lib.aes_encryption.helpers import hash_value
from utils.logger import Logger
from django.core.exceptions import ObjectDoesNotExist

api_logger = Logger(logger='inventoryAPILogger')

def set_flag_bit_values(hotel_object, flag_bit_values):
    if 'is_houseboat_moving' in flag_bit_values:
        hotel_object.is_houseboat_moving = flag_bit_values.get('is_houseboat_moving')
    if 'pahx' in flag_bit_values:
        hotel_object.pahx = flag_bit_values.get('pahx')
    if 'is_onboarding' in flag_bit_values:
        hotel_object.is_onboarding = flag_bit_values.get('is_onboarding')
    if 'pan_verification_optin' in flag_bit_values:
        hotel_object.pan_verification_optin = flag_bit_values.get('pan_verification_optin')
        hotel_object.gstn_verification_optin = flag_bit_values.get('gstn_verification_optin')
        hotel_object.account_verification_optin = flag_bit_values.get('account_verification_optin')
    if 'is_rm_enabled' in flag_bit_values:
        country = hotel_object.city_id.country.countryname if hotel_object.city_id \
            else hotel_object.cityfk.state.country.countryname
        if country.lower() == 'india':
            hotel_object.rm_enabled = flag_bit_values.get('is_rm_enabled')
    if 'is_static_optin' in flag_bit_values:
        hotel_object.is_static_optin = flag_bit_values.get('is_static_optin')
    if 'is_copy_optin' in flag_bit_values:
        hotel_object.is_copy_optin = flag_bit_values.get('is_copy_optin')
    if 'show_rtb_settings' in flag_bit_values:
        hotel_object.show_rtb_settings = flag_bit_values.get('show_rtb_settings')
    if PREBOOK_CHAT_ENABLED in flag_bit_values:
        if not hotel_object.property_category == HOTEL:
            if HotelUserLink.objects.filter(hoteldetail_id=hotel_object.id, is_host=UserLinkConstants.HOST).exists():
                    hotel_object.is_prebook_chat_enabled = flag_bit_values.get(PREBOOK_CHAT_ENABLED)
            else:
                hotel_object.is_prebook_chat_enabled = False
        else:
            hotel_object.is_prebook_chat_enabled = flag_bit_values.get(PREBOOK_CHAT_ENABLED)
    if 'is_insurance_required' in flag_bit_values:
        hotel_object.is_insurance_required = flag_bit_values.get('is_insurance_required')
    if 'fraud_hotel' in flag_bit_values:
        hotel_object.fraud_hotel = flag_bit_values.get('fraud_hotel')
    if 'is_test_hotel' in flag_bit_values:
        hotel_object.is_test_hotel = flag_bit_values.get('is_test_hotel')
    if 'is_blocked_at_funnels' in flag_bit_values:
        hotel_object.is_blocked_at_funnels = flag_bit_values.get('is_blocked_at_funnels')
    if 'is_single_inventory' in flag_bit_values and hotel_object.is_single_inventory != flag_bit_values.get('is_single_inventory'):
        from bulk_uploader.external_functions import check_for_ics_dl_linkage
        if flag_bit_values['is_single_inventory'] is False:
            if not check_for_ics_dl_linkage(hotel_object.hotelcode):
                raise ValidationError(
                    "This property is currently linked with a duplicate listing through the internal calendar sync. Please deactivate the internal calendar sync to proceed.")

            # Commenting it out since product requirement got changed. now, if DL exist, error should be thrown.
            # child_property = find_child_property(hotel_object.hotelcode)
            # if child_property:
            #     # check if booking exist for child property, if no, break linkage else throw error
            #     if check_for_active_bookings(child_property):
            #         raise ValidationError(
            #             "This property is currently linked with a duplicate listing through the internal calendar sync. Please deactivate the internal calendar sync to proceed.")
            #     else:
            #         # break linkage
            #         data_dict = {'parent_hotel_code': hotel_object.hotelcode, 'child_hotel_code': child_property}
            #         from bulk_uploader.external_functions import dl_unlink_bulk_uploader
            #         dl_unlink_bulk_uploader(data_dict)
            #         success = deactivate_hotel(child_property)
            #
            #     # hotel_obj should be parent property && if DL exist, break linkage
            hotel_object.is_single_inventory = False
        else:
            raise ValidationError("Multi to single inventory conversion is not allowed")

def validate_brand_and_platform(brand, platform):
    if brand.lower() not in ALLOWED_APP_BRANDS or platform.lower() not in ALLOWED_PLATFORMS:
        return False
    return True

def onboarded_to_new_cp(incoming_request_version, meta_data_source, meta_data_brand, meta_data_platform):
    """
        Determines if a new hotel is onboarded with new Cancellation Policy (CP)
            - For INGO and Host app, if current incoming request version is greater than or equal to first allowed version, return True
            - For Extranet, if current date is greater than first allowed cutoff date, return True
    """
    current_time = datetime.now().date()
    
    ## INGO and Host app Request
    if meta_data_source == settings.HOST_APP_SETTING["SUPPLY_APP_HEADER"]:
        if not validate_brand_and_platform(meta_data_brand, meta_data_platform):
            return False
        if compare_version(incoming_request_version, APP_VERSION_NEW_CP[meta_data_brand.lower()][meta_data_platform.lower()]):
            return True

    ## Extranet Request
    elif meta_data_source == settings.HOST_APP_SETTING["INGO_WEB_HEADER"] and current_time > EXTRANET_CUTOFF_DATE_NEW_CP:
        return True
    
    return False

def validate_hotel_related_data(hotel_related_data):
    current_user = hotel_related_data.get('current_user', None)
    validate_usp_details(hotel_related_data.get('usp_details', {}))
    validate_role_wise_contact_data(hotel_related_data.get('role_wise_contact_details', []), current_user)
    validate_vendor_details(hotel_related_data.get('vendor_details', {}), current_user, hotel_related_data.get('agreement_payload', {}))
    validate_hotel_meta_data(hotel_related_data.get('hotel_meta_data', {}))
    validate_pan_number(hotel_related_data.get('pan_number', ''))


def upload_checkin_checkout_details(hotel_code, checkin_time, checkin_end_time, checkout_time):
    try:
        api_logger.info("data : ", hotel_code, checkin_time, checkin_end_time, checkout_time)
        hotel = HotelDetail.objects.filter(hotelcode = hotel_code).first()
        if hotel:
            checkin_range = validate_checkin_end_time(checkin_time, checkin_end_time)
            validate_checkin_checkout(convert_string_to_time(checkin_time), convert_string_to_time(checkout_time), hotel.hoteltype)
            api_logger.info("Checkin range : ", checkin_range)
            if checkin_range :
                checkin_time_obj = datetime.strptime(checkin_time, '%H:%M:%S').time()
                checkin_end_time_obj = datetime.strptime(checkin_end_time, '%H:%M:%S').time()
                checkout_time_obj = datetime.strptime(checkout_time, '%H:%M:%S').time()
                hotel_details = HotelDetail.objects.filter(hotelcode = hotel_code).first()
                hotel_details.checkintime = checkin_time_obj
                hotel_details.checkinendtime = checkin_end_time_obj
                hotel_details.checkouttime = checkout_time_obj
                api_logger.info("Data to save : ", hotel_details.checkintime, hotel_details.checkinendtime, hotel_details.checkouttime)
                hotel_details.save()
        else:
             raise Exception ("Hotel Code not Valid")
    except Exception as e:
        raise Exception ("Error validating checkin and checkout ranges", str(e))


def validate_usp_details(usp_details):
    from api.v2.hotels.serializers.hotel_serializers import USPDetailSerializer, USPDetailUpdateSerializer
    validate_list_of_dictionaries(usp_details, 'usp')
    for usp_detail in usp_details:
        validate_type(USP_FIELD_TYPE, usp_detail)
        blank_to_null_conversion(usp_detail)
        content_type = ContentType.objects.get_for_model(HotelDetail)
        usp_detail.update({'content_type': content_type.id})

        if usp_detail.get('contact_id'):
            usp_serializer = USPDetailUpdateSerializer(data=usp_detail)
        else:
            usp_serializer = USPDetailSerializer(data=usp_detail)

        if not usp_serializer.is_valid():
            raise ValidationError(usp_serializer.errors)


def validate_role_wise_contact_data(role_wise_contact_data, current_user):
    from api.v2.hotels.serializers.hotel_serializers import ContactDetailSerializer, ContactDetailUpdateSerializer
    validate_list_of_dictionaries(role_wise_contact_data, 'role_wise')
    for role_wise in role_wise_contact_data:
        validate_type(CONTACT_DETAIL_FIELD_TYPE, role_wise)
        role_wise.update({'user': current_user})
        blank_to_null_conversion(role_wise)

        if role_wise.get('contact_id'):
            contact_serializer = ContactDetailUpdateSerializer(data=role_wise)
        else:
            contact_serializer = ContactDetailSerializer(data=role_wise)

        if not contact_serializer.is_valid():
            raise ValidationError(contact_serializer.errors)


def validate_vendor_details(vendor_details, current_user, payload):
    if not vendor_details:
        return
    from api.v2.hotels.serializers.hotel_serializers import BankAccountDetailSerializer, \
        BankAccountDetailUpdateSerializer
    validate_type(BANK_ACCOUNT_DETAIL_FIELD_TYPE, vendor_details)
    blank_to_null_conversion(vendor_details)
    content_type = ContentType.objects.get_for_model(VendorDetail)
    vendor_details.update({'content_type': content_type.id, 'user': current_user})

    if vendor_details.get('account_id'):
        bank_account_serializer = BankAccountDetailUpdateSerializer(data=vendor_details)
    elif vendor_details.get('payment_method_code', '').lower() == 'vcc':
        bank_account_serializer = BankAccountDetailUpdateSerializer(data=vendor_details)
    else:
        bank_account_serializer = BankAccountDetailSerializer(data=vendor_details)

    if vendor_details.get('tan_number'):
        if not re.match(r'^[A-Z]{4}[0-9]{5}[A-Z]{1}', vendor_details['tan_number']):
            raise ValidationError({'tan_number' : 'Correct format is - AAAA11111A (4 alphabets 5 numbers 1 Alphabet)'})

    if vendor_details.get('account_number'):
        data = {
            'account': vendor_details['account_number']
        }
        from api.v1.fake_details.service import FakeDetailService
        FakeDetailService.check_exists(data=data, raise_exception=True)

    vat_registration_number = vendor_details.get('vat_registration_number')
    city_id = vendor_details.get('vendor_city_id')
    city_code = vendor_details.get('vendor_city_code')
    agreement_id_list = []
    for value in payload.get('agreement_list', []):
        if not isinstance(value, dict):
            raise ValidationError('Wrong values supplied for agreement id list')
        if value.get('id'):
            agreement_id_list.append(value.get('id'))

    if city_id or city_code:
        if city_id:
            city = City.objects.filter(id=city_id, isactive=True).first()
        else:
            city = City.objects.filter(citycode=city_code, isactive=True).first()
        if city and city.country.iso2 in hotelchoice.VAT_REQUIRED_COUNTRIES_ISO2 and not vat_registration_number and VAT_NOC_AGREEMENT_ID not in agreement_id_list:
            raise ValidationError({'vat_registration_number': 'Required for a UAE hotel'})
        if city and city.country.iso2 != 'IN' and vat_registration_number:
            if len(vendor_details['vat_registration_number']) > 20:
                raise ValidationError({'vat_registration_number': 'Max length can be 20'})
            if not re.match(r'^\w+$', vendor_details['vat_registration_number']):
                raise ValidationError({'vat_registration_number': 'Can contain only alphabets [a-z] and digits [0-9]'})

    if not bank_account_serializer.is_valid():
        custom_error_dict = bank_account_serializer.errors
        custom_error_message = "Validation errors occurred:"
        for field, errors in bank_account_serializer.errors.items():
            custom_error_message += "Field '{}': {} ".format(field, "; ".join(errors))
            custom_error_dict = {"error_msg": custom_error_message}
        raise ValidationError(custom_error_dict)


def validate_hotel_meta_data(hotel_meta_data):
    from api.v2.hotels.serializers.hotel_serializers import HotelMetaDataSerializer
    validate_type(HOTEL_META_DATA_FIELD_TYPE, hotel_meta_data)
    hotel_meta_data_serializer = HotelMetaDataSerializer(data=hotel_meta_data)
    if not hotel_meta_data_serializer.is_valid():
        raise ValidationError(hotel_meta_data_serializer.errors)


def validate_pan_number(pan_number):
    if pan_number:
        data = {
            'pan': pan_number
        }
        from api.v1.fake_details.service import FakeDetailService
        FakeDetailService.check_exists(data=data, raise_exception=True)


def blank_to_null_conversion(data):
    for field in data:
        if isinstance(data[field], basestring) and data[field].strip() == '':
            data[field] = None


class Empty(object):
    """
    Placeholder for unset attributes.
    Cannot use `None`, as that may be a valid value.
    """
    pass


class Transformer(object):
    model_level_fields = set()
    json_to_model_field_map = {}
    exclude_fields = {'tags'}

    def __init__(self):
        self.input_data = {}
        self.output_data = {}

    def set_fields_on_model(self, input_data):
        """
        :param input_data:
        :param output_data:
        :return:
        If field in json is named as in model just set it, else consult map.
        If still it is not there handle in respective handlers.
        """

        for key, val in input_data.items():
            if key in self.exclude_fields:
                continue
            if key in self.model_level_fields:
                self.output_data[key] = input_data[key]
            elif self.json_to_model_field_map.get(key):
                self.output_data[self.json_to_model_field_map[key]] = input_data[key]

    def get_derived_fields(self):
        """
        like content_only ,tnc
        :return:
        """
        pass


class JSONtoHotelMeta(Transformer):
    """
    push_net_amt_booking, content_only flag??
    """
    pass


class JSONtoHoteldata(Transformer):
    model_level_fields = {field.name for field in HotelDetail._meta.fields}
    json_to_model_field_map = {
        'hotel_type': 'hoteltype', 'star_rating': 'starrating', 'no_of_floors': 'nooffloors',
        'no_of_rooms': 'noofroomsinhotel', 'checkin_time': 'checkintime', 'checkout_time': 'checkouttime',
        'year_hotel_built': 'yearhotelbuilt', 'no_of_restaurant': 'noofrestaurent',
        'pah': 'payathotelflag', 'pahx': 'pay_at_hotel_model', 'only_pah': 'only_pay_at_hotel_flag',
        'extrabed_commissionable': 'extrabed_commissionable_flag',
        'tax_on_commission': 'tax_on_commission_flag', 'go_stay': 'go_stay_flag',
        'dnd': 'dnd_flag'}

    def __call__(self, input_data):
        self.input_data = input_data
        self.get_vendor_data()
        self.get_image_data()
        self.get_location_details()
        self.get_attributes()
        self.get_finance_legal_fields()
        return self.output_data

    def get_vendor_data(self):
        vendor_details = self.input_data.pop('vendor', {})
        crs_data = vendor_details.get('crs', {})
        if 'id' in crs_data:
            self.output_data['crs_id'] = crs_data['id']

    def get_image_data(self):
        pass

    def get_location_details(self):
        location_data = self.input_data.pop('location', {})
        self.set_fields_on_model(location_data)

    def get_booking_settings(self, input_data):
        booking_settings = input_data.pop('booking_settings', {})
        self.set_fields_on_model(booking_settings)

    def get_tax_settings(self, input_data):
        tax_settings = input_data.pop('tax_settings', {})
        self.set_fields_on_model(tax_settings)

    def get_pah_settings(self, input_data):
        pah_settings = input_data.pop('pah_settings', {})
        self.set_fields_on_model(pah_settings)

    def get_tag_data(self):
        pass

    def get_channel_manager_settings(self, input_data):
        pass

    def get_attributes(self):
        attributes = self.input_data.pop('attributes', {})
        self.get_booking_settings(attributes)
        self.get_tax_settings(attributes)
        self.get_pah_settings(attributes)
        self.get_channel_manager_settings(attributes)
        self.set_fields_on_model(attributes)

    def get_finance_legal_fields(self):
        pass

    def get_account_detail(self):
        pass

    def get_supply_fields(self):
        pass

    def get_contact_fields(self):
        pass


def get_staff_contact_details(staff_usernames):
    """
    Returns a dictionary with staff designations as keys and user details or None as values.
    Handles None or empty values gracefully for each designation.
    Optimized to use select_related('hoteluser') for efficient related data fetching.
    Filters users by username_hash to ensure correct mapping if usernames have changed.
    """
    from ingouser.models import User
    import hashlib

    # Build a mapping of role to username for non-empty usernames
    role_to_username = {role: uname for role, uname in staff_usernames.items() if uname}
    # Build a mapping of role to username_hash
    role_to_hash = {role: hashlib.sha256(uname.lower().encode()).hexdigest() for role, uname in role_to_username.items()}
    username_hashes = list(role_to_hash.values())

    # Query users by username_hash
    users = User.objects.filter(username_hash__in=username_hashes).select_related('hoteluser')
    # Build user details for each found user, mapping back to the role
    designation_user_details = {}
    for role, expected_hash in role_to_hash.items():
        user = next((u for u in users if u.username_hash == expected_hash), None)
        if user:
            # Temp disable mobile for regional_head - They are getting too many calls from extranet, On PM request
            mobile_number = getattr(getattr(user, "hoteluser", None), "mobile", None) if role != 'regional_head' else None
            designation_user_details[role] = {
                'id': user.id,
                'name': user.get_full_name().strip(),
                'email': user.email,
                'mobile': mobile_number
            }

    # Construct the result, ensuring all expected keys are present
    result = {
        'contractbdo': designation_user_details.get('contractbdo') if staff_usernames.get('contractbdo') else None,
        'contractmanager': designation_user_details.get('contractmanager') if staff_usernames.get('contractmanager') else None,
        'regional_head': designation_user_details.get('regional_head') if staff_usernames.get('regional_head') else None,
    }
    return result


def get_location_meta_data(hotel_data, hotel_meta_data):
    location = hotel_data.pop('location', {})
    if 'map_link' in location:
        map_link = location.pop('map_link', None)
        hotel_meta_data.update({'map_link': map_link})
    if 'directions' in location:
        directions = location.pop('directions', None)
        hotel_meta_data.update({'directions': directions})
    return location, hotel_meta_data


def update_meta_data_custom_info(hotel_meta_data, custom_info):
    if 'map_link' in hotel_meta_data:
        custom_info.update({'map_link': hotel_meta_data.pop('map_link', None)})
    if 'directions' in hotel_meta_data:
        custom_info.update({'directions': hotel_meta_data.pop('directions', None)})
    if 'ingoexpress_onboarding' in hotel_meta_data:
        custom_info.update({'ingoexpress_onboarding': hotel_meta_data.pop('ingoexpress_onboarding', None)})
    if 'blackEnrolledOn' in hotel_meta_data:
        if hotel_meta_data.get('blackEnrolledOn',"") == "" or hotel_meta_data.get('blackEnrolledOn',"") is None:
            custom_info.pop('blackEnrolledOn', None)
        else:
            custom_info.update({'blackEnrolledOn': hotel_meta_data.pop('blackEnrolledOn', '')})

    hotel_meta_data.update({'custom_info': json.dumps(custom_info)})
    return hotel_meta_data


def validate_property_category(data):
    """

    :param data:
    :return: True or False based on validation
    """
    property_type = data.get('hotel_type', None)
    property_category = data.get('property_category', None)
    if not property_type or not property_category:
        return False
    if (hotelchoice.PROPERTY_CATEGORY[1].__contains__(property_category) and property_type in
        [hotelchoice.HOMESTAY_TYPE.__getitem__(i)[0] for i in range(len(hotelchoice.HOMESTAY_TYPE))]) or \
            (hotelchoice.PROPERTY_CATEGORY[0].__contains__(property_category) and
             property_type in [hotelchoice.HOTEL_TYPE.__getitem__(i)[0] for i in range(len(hotelchoice.HOTEL_TYPE))]):
        return True
    return False


def validate_amenities(amenities):
    if not isinstance(amenities, list):
        raise ValidationError({'amenities': HOTEL_MESSAGES.get('AMENITIES_LIST')})

    for amenity in amenities:
        if not isinstance(amenity, int):
            raise ValidationError({'amenities': HOTEL_MESSAGES.get('AMENITIES_LIST_INTEGERS')})


def validate_special_tag(special_tag):
    if not isinstance(special_tag, list):
        raise ValidationError({'special_tag': HOTEL_MESSAGES.get('SPECIAL_TAG')})

    for tag in special_tag:
        if not isinstance(tag, int):
            raise ValidationError({'special_tag': HOTEL_MESSAGES.get('SPECIAL_TAG_INTEGERS')})


def get_list_hotel_data(**kwargs):
    """
    get the list hotel data for the corresponding id
    :param kwargs:
    :return response:
    """
    from api.v2.hotels.serializers.hotel_serializers import ListYourHotelSerializer
    response = {'success': False}
    try:
        pk = kwargs.get('pk')
        user = kwargs.get('user')
        log_data = kwargs.get('log_data', {})
        draft_list = list(DraftUserMapping.objects.filter(user=user).values_list('draft_id', flat=True))
        if not pk:
            # for non staff user adding the onbording draft in get info api (without FCL flow)
            existing_draft_list = list(ListYourHotel.objects.filter(user=user).values_list('id', flat=True)) if not user.is_staff else []
            final_list = list(set(draft_list) | set(existing_draft_list))
            instances = ListYourHotel.objects.filter(status=hotelchoice.LIST_YOUR_HOTEL_STATUS.get('active'), id__in = final_list)
        else:
            pk = long(int(pk))
            if pk not in draft_list:
                raise PermissionDenied("Access Denied")
            instances = ListYourHotel.objects.filter(pk=pk)
        response['data'] = ListYourHotelSerializer(instances, many=True).data

        if pk and instances.exists():
            # Parse the JSON string into a dictionary
            meta_data = json.loads(instances[0].meta_data)
            # change time post sending live -- dummy for now
            response['data'][0]['newHostFlow'] = True if instances[0].createdon.date() > settings.HOSTPROFILE_REVAMP_CUTOFF_DATE else False
            hotelcode = instances[0].hotelcode
            isSingleInventory = meta_data.get('isSingleInventory', None)
            if isSingleInventory is None and hotelcode:
                hotel_obj = HotelDetail.objects.filter(hotelcode=hotelcode).first()
                if hotel_obj:
                    isSingleInventory = hotel_obj.is_single_inventory
                    new_meta_data = response['data'][0].get('meta_data', {})
                    new_meta_data['isSingleInventory'] = isSingleInventory
                    meta_data['isSingleInventory'] = isSingleInventory
                    response['data'][0]['meta_data']=new_meta_data
            is_homestay = meta_data.get('isHomeStayFlow', False)
            if not is_homestay:
                response['data'][0]['isBasicInfoCompleted'] = True if meta_data.get('acceptsBookingSince',
                                                                                                 None) else False
            else:
                from hotels.models.user_management import HostProfile
                host_obj = None
                if user:
                    host_obj = HostProfile.objects.filter(user=user).first()
                response['data'][0]['isBasicInfoCompleted'] = True if host_obj and host_obj.about and meta_data.get('acceptsBookingSince', None) else False

        if response['data']:
            response['success'] = True
    except PermissionDenied as e:
        raise e
    except Exception as e:
        log_data['error']['message'] = str(e)
        log_data['error']['traceback'] = repr(traceback.format_exc())
        api_logger.critical(message='Some Error Occurred Error:- %s \t TraceBack:- %s' %
                                    (str(e), repr(traceback.format_exc())),
                            log_type='ingoibibo',
                            bucket='HotelsAPIv2',
                            stage='api.v2.hotels.resources.hotel_resources.get_list_hotel_data', identifier='{}'.format(log_data))
    return response


def is_validate_name_without_special_character(value):
    pattern = '[^0-9a-zA-Z|:\-/\\\(\)\'\`., &@]'
    if re.search(pattern, value):
        return False
    return True


def validate_text_without_special_character(value):
    pattern = '[^\w\-/\\\(\)\'<>\`.,:\s\!\^%@*#]'
    if re.search(pattern, value):
        return False
    return True


def validate_name_without_html_characters(value):
    pattern = '[&<>"\'/]'
    if re.search(pattern, value):
        return False
    return True


def filters_to_elastic_query(request, search_obj):
    """
    Adds `match` query for text fields and `term` for keywords.
    :param request:
    :param search_obj:
    :return:
    """
    es_text_fields = ["city", "state", "country", "locality"]
    filter_args = {}
    filters = json.loads(request.query_params["filters"])
    for query_filter in filters:
        nested = query_filter.pop('nested', False)
        for key, val in query_filter.items():
            if nested:
                filter_args[nested + "__%s" % key] = val
            else:
                filter_args[key] = val
            if key in es_text_fields:
                search_obj = search_obj.filter("match", **filter_args)
            else:
                search_obj = search_obj.filter("term", **filter_args)
            filter_args = {}
    return search_obj

def get_hotel_snooze_data(hotel):
    from api.v1.hotels.resources.hotel_resources import response_snooze_list
    from hotels.models.hoteldetail import Snooze

    snooze_queryset = Snooze.objects.filter(hotelcode=hotel.hotelcode, is_active=True)
    return response_snooze_list(snooze_queryset)


def get_hotel_room_details(hotel):
    room_query_dict = {
        "hotel": hotel,
        "isactive": True
    }

    from hotels.models import RoomDetail
    room_objs = RoomDetail.objects.filter(**room_query_dict)
    room_ids = room_objs.values_list("id", flat=True)
    room_rate_plans = get_room_rateplans(room_ids)

    result_list = []
    for room in room_objs:
        room_dict = {"room_code": str(room.roomtypecode), "type": room.roomtype, "name": room.roomtypename,
                     "rateplans": room_rate_plans.get(room.id, []),
                     "is_subroom": True if room.parent_id else False, "is_active": room.isactive}
        result_list.append(room_dict)
    return result_list


def get_room_rateplans(room_ids):
    room_rate_plans = {}

    rate_pan_query_dict = {
        "roomtype_id__in": room_ids,
        "isactive": True
    }
    from hotels.models import RatePlan

    rate_plan_objs = RatePlan.objects.filter(**rate_pan_query_dict)

    for rate_plan in rate_plan_objs:
        if rate_plan.roomtype_id not in room_rate_plans:
            room_rate_plans[rate_plan.roomtype_id] = []
        room_rate_plans[rate_plan.roomtype_id].append({
            "rateplan_code": str(rate_plan.rateplancode),
            "name": rate_plan.rateplanname,
            "meal_plan": rate_plan.mealplan,
            "is_active": rate_plan.isactive,
        })
    return room_rate_plans


def get_hotel_location_details(hotel):
    from api.v2.hotels.resources.make_hotel_details import get_locality_state_city_details
    from api.v2.hotels.resources.make_hotel_details import get_hotel_locations_node

    state_city_details = get_locality_state_city_details(hotel)
    kwargs = {
        'hotel_obj': hotel,
        'state_city_details': state_city_details
    }

    return get_hotel_locations_node(**kwargs)

def get_hotel_onboarding_completion_percent(hotel_obj):
    completion_fields = [hotel_obj.location_complete, hotel_obj.rooms_and_spaces_complete,
                         hotel_obj.property_details_complete, hotel_obj.photos_and_videos_complete,
                         hotel_obj.booking_settings_complete, hotel_obj.pricing_availability_complete,
                         hotel_obj.payout_and_compliance_complete]
    total = sum(map(bool, completion_fields)) / float(len(completion_fields))
    return round(total * 100, 2)

def set_onboarding_homestay_settings(hotel_obj):
    if hotel_obj:
        if not hotel_obj.is_property_hostel:
            hotel_obj.is_chat_access = True
            hotel_obj.is_prebook_chat_enabled = True
            hotel_obj.is_chat_enabled = True
        if not hotel_obj.datasource_id or hotel_obj.datasource_id != SELF_SIGNUP_REFERENCE_TUPLE[5]:
            bdm_user = User.objects.filter(id=settings.DEFAULT_AA_BDM_ID)
            if bdm_user.exists():
                hotel_obj.contractbdo = bdm_user.first().username
            else:
                hotel_obj.contractbdo = '<EMAIL>'
            hotel_obj.booking_auto_confirm = True
            zm_user = User.objects.filter(id=settings.DEFAULT_AA_ZM_ID)
            if zm_user.exists():
                hotel_obj.contractmanager = zm_user.first().username
            else:
                hotel_obj.contractmanager = '<EMAIL>'


def agreement_accepted_onboarding_mail_trigger(hotel_object, log_data={'api_specific_identifiers': {}, 'error': {}, 'request_id':''}):
    try:
        agreement_list = HotelAgreementMapping.objects.using('default').filter(hotel_id=hotel_object.id, status="accepted")
        agreement_id_list = []
        agreement_url_list = []
        import urllib
        import datetime
        import zipfile
        folder_name = '/tmp/temp_agreement'
        zipfilename = 'agreements_%s_%s.zip' % (datetime.datetime.now(), hotel_object.hotelcode)
        zipfilelocation = "/tmp/" + zipfilename
        zf = zipfile.ZipFile(zipfilelocation, "w")
        if not os.path.isdir(folder_name):
            os.mkdir(folder_name)
        for agreement_obj in agreement_list:
            agreement_id_list.append(agreement_obj.agreement.id)
            meta_data_content = json.loads(agreement_obj.meta_data)
            agreement_number = agreement_obj.agreement_number
            current_agreement = meta_data_content.get('current agreement', [])
            if current_agreement:
                agreement_url = current_agreement[0].get('agreement url', '')
            else:
                agreement_url = ''
            if agreement_url:
                dt = datetime.datetime.now()
                file_name = "agreement_%s_%s_%s.pdf" %(str(hotel_object.hotelcode),str(agreement_number),dt.strftime("%Y%m%d%H%M%S"))
                filepath = folder_name + "/" + file_name
                urllib.urlretrieve(agreement_url, filepath)
                agreement_url_list.append(agreement_url)
                result_file = open(filepath, 'r')
                fpath = os.path.abspath(result_file.name)
                fdir, fname = os.path.split(fpath)
                zf.write(fpath, fname, compress_type=zipfile.ZIP_DEFLATED)
                result_file.close()
        zf.close()
        with open(zipfilelocation, "r") as result_file:
            s3 = S3Upload()
            s3_response = s3.upload(result_file, params={'bucket_key': settings.IBCDN_BUCKET_KEY},
                                prepend_file_name='agreements/zipfiles/')
            # TODOS3 write will depend upon if read is via attachment or direct url
            # mapper = BucketMapper(DOCUMENT_OBJECT_TYPE)
            # mapper.set_document_type('agreement')
            # prepend_file_name = mapper.get_mandatory_prepend_path() + 'zipfiles/'
            # s3_response = s3.upload(result_file, params={'bucket_key': mapper.get_bucket_key(),},
            #                     prepend_file_name=prepend_file_name)
            if s3_response.get('success', None):
                zipfile_url = s3_response['data']
        if zipfile_url:
            # TODOREAD read url from s3 and save in hotel object. Check if not sent as attachment. We can use param to keep hash which can be object specific
            hotel_object.agreement_zip_url = str(zipfile_url)
            hotel_object.save()
            from communication.tasks import agreement_acceptance_mail
            agreement_acceptance_mail.apply_async(args=(hotel_object, agreement_id_list, agreement_url_list, zipfile_url))

    except Exception as e:
        log_data['error']['message'] = "Error for hotelcode %s agreement zipping file %s" % (hotel_object.hotelcode, str(e))
        log_data['error']['traceback'] = repr(traceback.format_exc())
        api_logger.critical("Error for hotelcode %s agreement zipping file %s Traceback: %s" % (hotel_object.hotelcode,
            str(e), repr(traceback.format_exc())), log_type="ingoibibo",
                            bucket="HotelsAPIv2", stage="api.v2.hotels.resources.hotel_resources.agreement_accepted_onboarding_mail_trigger", identifier='{}'.format(log_data))


def get_hotel_state_details(hotel_object):
    hotel_state_dict = {}
    try:
        transition_data = get_state_machine_es_transitions(hotel_object.id, full=True, asc=False)
        hotel_state_dict['current_state'] = hotel_object.property_states_code
        hotel_state_dict['current_state_text'] = PROPERTY_STATES[hotel_object.property_states_code - 1][1]
        hotel_state_dict['inactive_date'] = None
        if not hotel_object.isactive:
            for data in transition_data:
                if data['_source']['previous_state'] in PROPERTY_ACTIVE_STATES:
                    hotel_state_dict['inactive_date'] = data['_source']['modified_on']
                    break

    except Exception as e:
        api_logger.critical("Error for hotelcode {}, {}, {}".format(hotel_object.hotelcode, e.message, traceback.format_exc()), log_type="ingoibibo",
                            bucket="HotelsAPIv2",
                            stage="api.v2.hotels.resources.hotel_resources.get_hotel_state_details")

    return hotel_state_dict


def get_hotel_onboarding_stage(hotel):
    try:
        draft_hotel_obj = ListYourHotel.objects.only('onboarding_stage').get(hotelcode=hotel.hotelcode)
        return draft_hotel_obj.onboarding_stage or ''
    except ObjectDoesNotExist as e:
        return ''
    except Exception as e:
        api_logger.critical("Error for hotelcode {} agreement zipping file {} Traceback: {}".format(hotel.hotelcode, e.message, traceback.format_exc()), log_type="ingoibibo",
                            bucket="HotelsAPIv2", stage="api.v2.hotels.resources.hotel_resources.get_hotel_onboarding_stage")
        return ''


def get_hotel_id(base_obj):
    from hotels.models import RoomDetail
    from hotels.models.hoteldetail import SpaceDetail

    if isinstance(base_obj, HotelDetail):
        return base_obj.id
    elif isinstance(base_obj, SpaceDetail):
        return base_obj.hotel_id
    elif isinstance(base_obj, RoomDetail):
        return base_obj.hotel_id
    return None


def get_hotel_content_type():
    return ContentType.objects.get_for_model(HotelDetail)


def check_homestay_by_id(hotel_id):
    return is_homestay(HotelDetail.objects.get(id=hotel_id))


def is_homestay(base_obj):
    hotel_id = get_hotel_id(base_obj)
    if not hotel_id:
        return False
    hotel = base_obj
    if not (isinstance(base_obj, HotelDetail)):
        hotel = HotelDetail.objects.only('property_category').get(id=hotel_id)
    return hotel.property_category == HOMESTAY


def set_manager_mapping(hotel_data, city_id, hotel_object, correlation_id='undefined', log_data={'api_specific_identifiers': {}, 'error': {}, 'request_id':''}):
    if not hotel_data.get('is_fcl', False):
        bdm, zm = ManagerMappingWrapper.get_contract_details(city_id=city_id,
                                                             property_category=hotel_object.property_category,
                                                             correlation_id=correlation_id, log_data=log_data)
        if bdm is not None:
            hotel_object.contractbdo = bdm
        if zm is not None:
            hotel_object.contractmanager = zm


def get_changed_fields(current, updated):
    changed_fields = []
    for name, val in current.items():
        if name in updated and val != updated[name]:
            changed_fields.append(name)
    return changed_fields


def get_hotel_supply_leadership_details(hotel):
    """
    Returns a dict with the appropriate hotel_supply_leadership email and name based on hotel status and category.
    - If inactive: only 'email' (<EMAIL>)
    - If active:
        - International hotel & AA/homestay: 'email' (<EMAIL>), 'name'
        - Domestic hotel: 'email' (<EMAIL>), 'name'
        - Domestic AA/homestay: 'email' (<EMAIL>), 'name'
    """
    if not hotel.isactive:
        return {'email': '<EMAIL>'}
    if hasattr(hotel, 'is_international') and callable(
            getattr(hotel, 'is_international')) and hotel.is_international():
        return {'email': '<EMAIL>', 'name': 'Hotel Supply Leadership'}
    if is_homestay(hotel):
        return {'email': '<EMAIL>', 'name': 'Hotel Supply Leadership'}

    return {'email': '<EMAIL>', 'name': 'Hotel Supply Leadership'}
