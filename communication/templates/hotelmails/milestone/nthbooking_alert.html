{% extends 'hotelmails/mail_layout_responsive.html'%}
{%block email_css %}
<title>ingoibibo</title>
<style type="text/css">
/* Client-specific Styles */
            #outlook a {
	padding:0;
} /* Force Outlook to provide a "view in browser" menu link. */
body {
	width:100% !important;
	-webkit-text-size-adjust:100%;
	-ms-text-size-adjust:100%;
	margin:0;
	padding:0;
}
/* Prevent Webkit and Windows Mobile platforms from changing default font sizes, while not breaking desktop design. */
            .ExternalClass {
	width:100%;
} /* Force Hotmail to display emails at full width */
.ExternalClass, .ExternalClass p, .ExternalClass span, .ExternalClass font, .ExternalClass td, .ExternalClass div {
	line-height: 100%;
} /* Force Hotmail to display normal line spacing.  */
#backgroundTable {
	margin:0;
	padding:0;
	width:100% !important;
	line-height: 100% !important;
}
img {
	outline:none;
	text-decoration:none;
	border:none;
	-ms-interpolation-mode: bicubic;
	height:auto;
}
a img {
	border:none;
}
.image_fix {
	display:block;
}
/*p {margin: 0px 0px !important;}*/
            table td {
	border-collapse: collapse;
}
table {
	border-collapse:collapse;
	mso-table-lspace:0pt;
	mso-table-rspace:0pt;
}
a {
	text-decoration:none!important;
}
/*STYLES*/
            table[class=full] {
	width: 100%;
	clear: both;
}
            /*IPAD STYLES*/
            @media only all and (max-width: 640px) {
 a[href^="tel"], a[href^="sms"] {
 text-decoration: none;
 color: #ffffff; /* or whatever your want */
 pointer-events: none;
 cursor: default;
}
 .mobile_link a[href^="tel"], .mobile_link a[href^="sms"] {
 text-decoration: default;
 color: #ffffff !important;
 pointer-events: auto;
 cursor: default;
}
 table[class=devicewidth] {
width: 440px!important;
}
 td[class=devicewidth] {
width: 440px!important;
}
 img[class=devicewidth] {
width: 440px!important;
}
 img[class=banner] {
width: 440px!important;
height:147px!important;
}
 table[class=devicewidthinner] {
width: 420px!important;
}
 table[class=icontext] {
width: 345px!important;
text-align:center!important;
}
 img[class="colimg2"] {
width:420px!important;
height:243px!important;
}
 table[class="emhide"] {
display: none!important;
}
 img[class=responsiveImg] {
 display:inline-block;
 max-width:100% !important;
 width:100% !important;
 height:auto !important;
}
}
            /*IPHONE STYLES*/
            @media only all and (max-width: 480px) {
 a[href^="tel"], a[href^="sms"] {
 text-decoration: none;
 color: #ffffff; /* or whatever your want */
 pointer-events: none;
 cursor: default;
}
 .mobile_link a[href^="tel"], .mobile_link a[href^="sms"] {
 text-decoration: default;
 color: #ffffff !important;
 pointer-events: auto;
 cursor: default;
}
 table[class=devicewidth] {
width: 100%!important;
}
 td[class=devicewidth] {
width: 100%!important;
}
 img[class=devicewidth] {
width: 100%!important;
}
 img[class=banner] {
width: 100%!important;
height:93px!important;
}
 table[class=devicewidthinner] {
width: 100%!important;
}
 table[class=icontext] {
width: 186px!important;
text-align:center!important;
}
 img[class="colimg2"] {
width:260px!important;
height:150px!important;
}
 table[class="emhide"] {
display: none!important;
}
}

.progress {
	overflow: hidden;
	height: 10px;
	margin-bottom: 9px;
	background-color: #ecf0f1;
	border-radius: 4px;
	-webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
	box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
	width: 90%;
}
.progress .progress-bar {
    font-size: 10px;
    line-height: 10px;
}

.progress-bar-info {
    background-color: #3498db;
}

.progress-bar {
    background-color: #2c3e50;
    box-shadow: 0 -1px 0 rgba(0, 0, 0, 0.15) inset;
    color: #ffffff;
    float: left;
    font-size: 13px;
    height: 100%;
    line-height: 21px;
    text-align: center;
    transition: width 0.6s ease 0s;
    width: 0;
}
</style>
{%endblock%}
{%block email_content%}

<table width="100%" bgcolor="#eaeaea" cellpadding="0" cellspacing="0" border="0" id="backgroundTable">
  <tbody>
    <tr>
      <td><table width="630" cellpadding="0" cellspacing="0" border="0" align="center" class="devicewidth" style="background:#f7f7f7; margin:0 auto">
          <tbody>
            <tr>
              <td width="100%"><table width="100%" align="left" cellpadding="0" cellspacing="0" border="0" class="devicewidthinner" style="margin-top:40px; margin-bottom:20px;">
                  <tbody>
                    <tr>
                       <td align="center" valign="top" style="">
                       <p style="padding-bottom:36px;font-family:Arial, Helvetica, sans-serif; font-size:113px; text-align:center; color:#2f68b2;margin-bottom:0; margin-top:0;"> {{bookingcount}}</p>

                       <p style="font-family:Arial, Helvetica, sans-serif; font-size:20px; text-align:center; margin-top:10px; color:#2f68b2;">Bookings</p>

                        </td>
                       </tr>
                       <tr>
                      <td align="center" style="">
                       <p class="contentTxt" style="color:#2f68b2; font-family:Helvetica, arial, sans-serif; font-size:40px; margin-top:0px;  margin-bottom:0px; padding:0;"> Congratulations!</p>

                        </td>
                    </tr>
                  </tbody>
                </table></td>
            </tr>
          </tbody>
        </table></td>
    </tr>
  </tbody>
</table>


<table width="100%" bgcolor="#eaeaea" cellpadding="0" cellspacing="0" border="0" id="backgroundTable">
  <tbody>
    <tr>
      <td><table width="630" cellpadding="0" cellspacing="0" border="0" align="center" class="devicewidth" style="background:#ffffff; margin:0 auto">
          <tbody>
            <tr>
              <td width="100%" style="padding:15px"><table width="100%" align="left" cellpadding="0" cellspacing="0" border="0" class="devicewidthinner">
                  <tbody>
                    <tr>
                      <td align="center">
                              <p style="font-family: Helvetica, arial, sans-serif; font-size: 20px; color: #333333; margin-top:1px; margin-bottom:1px;display:inline-block; padding:0">You have received <span style="color:#2f68b2; font-weight:bolder">{{multiple}}+ bookings </span>for </p>
                        <p class="contentTxt" style="color:#4a4a4a; font-family:Helvetica, arial, sans-serif; font-size:20px;  margin-bottom:0px; padding:0;"> <span style="color:#2f68b2; font-weight:bolder">{{hotelname}}</span> on goibibo</p>
                        </td>
                    </tr>


                  </tbody>
                </table></td>
            </tr>
          </tbody>
        </table></td>
    </tr>
  </tbody>
</table>

<table width="100%" bgcolor="#eaeaea" cellpadding="0" cellspacing="0" border="0" id="backgroundTable">
            <tbody>
                <tr>
                    <td style="">
                        <table width="630" cellpadding="0" cellspacing="0" border="0" align="center" class="devicewidth" bgcolor="#ffffff" style="margin:0 auto" >
                            <tbody>
                                <tr>
									<td width="100%" style="padding:30px 0">
										<table width="100%" cellpadding="0" cellspacing="0" border="0" class="deviceinnerwidth">
											<tbody>
												<tr>
													<td width="100%" style="padding:10px 0">


                                                        														<p style="color:#9b9ba3; text-align:center;font-family:Arial, Helvetica, sans-serif; font-size:15px;margin:0 0 4px 0">Keep up the good work and always do immediate confirmation</p>
                                                                    <table cellspacing="0" cellpadding="0" border="0" align="center">
																			<tbody>
																				<tr>
																					<td style="width: 79%;background-color:#f0741a; padding-top:10px; padding-bottom:10px; text-align:center; padding-left:5px; padding-right:5px; border-radius:7px; -moz-border-radius:7px;"><a style="font-family:Arial, Helvetica, sans-serif; color:#ffffff; background-color:#f0741a; font-size:16px; text-decoration:none;" href="in.goibibo.com/extranet" target="_blank">Go to your account</a></td>
																				</tr>
																			</tbody>
																		</table>


													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
							</tbody>
						</table>
					</td>
				</tr>
			</tbody>
		</table>

{%endblock%}
