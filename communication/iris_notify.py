import json
import traceback
import uuid
import requests
import logging

from proto_utils.dict_to_proto3_to_dict import protobuf_to_dict
from protos.compiled.notify_pb2 import NotifyPayload, Address, DataParams, Tag, whatsapp, english, sms, email, \
    AttachmentInfo, S3
from protos.compiled.iris_whatsapp_optin_pb2 import SetWhatsappOptinRequest, GetWhatsappOptinRequest, \
    GetWhatsappOptingResponse, SetWhatsappOptinResponse
from comm_identifier_python.scrambler_client import Scrambler
from utils.logger import Logger
from django.conf import settings

scrambler_obj = Scrambler()

logger = logging.getLogger("inventoryLogger")
logger_stats = Logger(logger = "inventoryLogger")

WA_DATA_TYPE = "mobile"
WA_CONTENT_TYPE = "whatsapp_content"
GENERIC_TEMPLATE_NAME = "generic_template"


class Notify:
    """
    This class INGO communication with IRIS platform
    """
    def __init__(self):
        self.notify_url = settings.IRIS_CONFIG["NOTIFY_URL"]
        self.optin_url = settings.IRIS_CONFIG["WHATSAPP_OPTIN_URL"]
        self.platform_token = settings.IRIS_CONFIG["PLATFORM_TOKEN_MAPPING"]
        self.MOBILE_DATATYPE = "mobile"

    def __get_commid(self, contact_address, type, country_code = "", action = "encode"):
        """
        encode or decode contact address
        @param contact_address: email/mobile number (type: string)
        @param type: email/mobile (type: string)
        @param country_code: mobile country code
        @param action: encode/decode (type: string)
        @return: string
        """
        try:
            data = [{
                "data": contact_address,
                "action": action,
                "data_type": type
            }]

            if country_code:
                data[0]["country_code"] = country_code

            response = scrambler_obj.generate_data(data)
            logger.info("Notify:__get_commid:{contact_address}, {response}".format(contact_address = contact_address,
                                                                                   response = response))
            return response[0]["result"]
        except Exception as e:
            log_id = str(uuid.uuid1())
            response["message"] = "Error occur (log id: {log_id})".format(log_id = log_id)
            logger.critical("Notify:__get_commid:{contact_address}:{log_id}, {e}".format(
                contact_address = contact_address, log_id = log_id, e = repr(traceback.format_exc())))
        return None

    def sendWhatsapp(self, mobile_number, country_code, namespace, template_name, language_code, language_policy,
                     lob_name = "INGO", message_type = "", notif_type = "", flow_name = "", tags = [], params = []):
        """

        @param mobile_number: mobile number with or without country name if this number with country code then country
        code parm file should be empty string
        @param country_code: country code
        @param namespace: whatsapp template namespace this (dev should ask this value from product)
        @param template_name: whatsapp template name (dev should ask this value from product)
        @param language_code: template language code
        @param language_policy: template language policy
        @param lob_name: lob name
        @param message_type: Type is string and this will help in analysis inside iris dashboard
        @param notif_type: Type is string and this will help in analysis inside iris dashboard
        @param flow_name: Type is string and this will help in analysis inside iris dashboard
        @param tags: Type is string and this will help in analysis inside iris dashboard
        @param params: variable part of template
        """
        response = {
            "status": False,
            "msg_id": "",
            "message": ""
        }
        try:
            notify_payload_obj = NotifyPayload()
            notify_payload_obj.channels.extend([whatsapp])

            address_obj = Address()

            address_obj.comm_id_wa_number = self.__get_commid(contact_address = mobile_number,
                                                              country_code = country_code,
                                                              type = self.MOBILE_DATATYPE)
            notify_payload_obj.address.CopyFrom(address_obj)

            tag_list = []

            for key, value in tags:
                tag = Tag()
                tag.key = key
                tag.value = value

            notify_payload_obj.tags.extend(tag_list)

            data_params_list = []

            data_params_obj = DataParams()
            data_params_obj.content_type = WA_CONTENT_TYPE

            parms_list = []

            for param in params:
                parms_list.append({
                    "default": param
                })

            language_node = dict({
                "policy": language_policy,
                "code": language_code
            })

            hsm_dict = dict()
            hsm_dict["namespace"] = namespace
            hsm_dict["element_name"] = template_name
            hsm_dict["localizable_params"] = parms_list
            hsm_dict["language"] = language_node

            data_params_obj.data_content["hsm"] = json.dumps(hsm_dict)

            data_params_obj.message_identifiers.lob_name = lob_name
            data_params_obj.message_identifiers.flow_name = flow_name
            data_params_obj.message_identifiers.message_type = message_type
            data_params_obj.message_identifiers.notif_type = notif_type

            data_params_list.append(data_params_obj)

            notify_payload_obj.data_params.extend(data_params_list)

            notify_payload_obj.template_name = GENERIC_TEMPLATE_NAME
            notify_payload_obj.language = english

            headers = {
                "Content-type": "application/x-protobuf",
                "Accept": "application/json",
                "token": self.platform_token[lob_name]
            }

            iris_response = requests.post(
                url = self.notify_url,
                data = notify_payload_obj.SerializeToString(),
                headers = headers
            )
            if iris_response.status_code == 200:
                response["msg_id"] = iris_response.json()["message_id"]
                response["status"] = True

            logger.info("Notify:send_whatsapp:{mobile_number}:{iris_response}".format(mobile_number = mobile_number,
                                                                                      iris_response = iris_response))
        except Exception as e:
            log_id = str(uuid.uuid1())
            response["message"] = "Error occur (log id: {log_id})".format(log_id = log_id)
            logger.critical("Notify:send_whatsapp:{mobile_number}:{log_id}, {e}".format(
                mobile_number = mobile_number, log_id = log_id, e = repr(traceback.format_exc())))
        return response

    def sendSms(self, mobile_number, message_content, lob_name = "INGO", country_code = "", message_type = "defaultMsgType",
                notification_type = "DefaultNotificationType", flow_name = "InGoibibo_SMS", tags = dict()):
        """
        Send SMS to through IRIS
        @param mobile_number: Mobile number (type: String)
        @param message_content: Message content (type: string)
        @param lob_name: LOB name (type: string, valid value: MMT/INGO/GI)
        @param country_code: Country code (type: string)
        @param message_type: This is a string type and use in pigeon dashboad for analytics (type: string)
        @param notification_type: notification type (type: string)
        @param flow_name: flow name (type: string)
        @param tags: dict type
        @return: dict type
        """
        response = {
            "status": False,
            "msg_id": "",
            "message": ""
        }
        try:
            notify_payload_obj = NotifyPayload()

            data_params_list = []

            data_params_obj = DataParams()

            data_params_obj.data_content["sms_content"] = message_content
            data_params_obj.message_identifiers.lob_name = lob_name
            data_params_obj.message_identifiers.flow_name = flow_name
            data_params_obj.message_identifiers.notif_type = notification_type
            data_params_obj.message_identifiers.message_type = message_type

            data_params_list.append(data_params_obj)

            notify_payload_obj.channels.extend([sms])

            address_obj = Address()

            if not settings.DEBUG:
                address_obj.comm_id_mobile.append(self.__get_commid(contact_address = mobile_number,
                                                                    country_code = country_code,
                                                                    type = self.MOBILE_DATATYPE))

            notify_payload_obj.address.CopyFrom(address_obj)

            tag_list = []

            for key, value in tags:
                tag = Tag()
                tag.key = key
                tag.value = value

            notify_payload_obj.tags.extend(tag_list)
            notify_payload_obj.data_params.extend(data_params_list)
            notify_payload_obj.template_name = GENERIC_TEMPLATE_NAME
            notify_payload_obj.language = english

            headers = {
                "Content-type": "application/x-protobuf",
                "Accept": "application/json",
                "token": self.platform_token[lob_name]
            }

            iris_response = requests.post(
                url = self.notify_url,
                data = notify_payload_obj.SerializeToString(),
                headers = headers
            )
            if iris_response.status_code == 200:
                response["status"] = True
                response["msg_id"] = iris_response.json()["message_id"]

        except Exception as e:
            log_id = str(uuid.uuid1())
            response["message"] = "Error occur (log id: {log_id})".format(log_id = log_id)
            logger.critical("Notify:sendSms:{mobile_number}:{log_id}, {e}".format(
                mobile_number = mobile_number, log_id = log_id,
                e = repr(traceback.format_exc())))

        return response

    def sendEmail(self, to_emails, from_email, body, subject, lob_name = "INGO", cc_emails = [], bcc_emails = [],
                  message_type = "email", notification_type = "generic", flow_name = "", attachments = [],
                  tags = {}):
        """
        Send email
        @param to_emails: to email id
        @param from_email: from email id
        @param body: email body this can be html code as a string
        @param subject: email subject
        @param lob_name: lob name
        @param cc_emails: cc email id
        @param bcc_emails: bcc email id
        @param message_type: message type
        @param notification_type: notification type
        @param flow_name: flow name
        @param attachments: list of attachment sample value [{"file_name": "abc.pdf",
                                                            "link": "pigeon s3 bucket file name"}]
        @param tags: tag of email key value pair
        @return: dict
        """
        response = {
            "status": False,
            "msg_id": "",
            "message": ""
        }
        try:
            notify_payload_obj = NotifyPayload()
            data_params_list = []
            data_params_obj = DataParams()
            data_params_obj.data_content["body"] = body
            data_params_obj.data_content["subject"] = subject
            data_params_obj.message_identifiers.lob_name = lob_name
            data_params_obj.message_identifiers.message_type = message_type
            data_params_obj.message_identifiers.notif_type = notification_type
            data_params_obj.message_identifiers.flow_name = flow_name
            data_params_list.append(data_params_obj)
            notify_payload_obj.channels.extend([email])

            if attachments:
                attachments_info_obj = AttachmentInfo()
                attachments_info_obj.attachment_source = S3

                for attachment in attachments:
                    attachments_obj = AttachmentInfo.Attachment()

                    file_obj = AttachmentInfo.FileNames()
                    file_obj.email = attachment["filename"]

                    link_obj = AttachmentInfo.Links()
                    link_obj.email = attachment["link"]

                    attachments_obj.links.CopyFrom(link_obj)
                    attachments_obj.file_names.CopyFrom(file_obj)

                    attachments_info_obj.attachments.extend([attachments_obj])

                notify_payload_obj.attachment_info.CopyFrom(attachments_info_obj)

            address_obj = Address()

            for to_email in to_emails:
                address_obj.comm_id_to.extend([self.__get_commid(contact_address = to_email, type = "email")])

            for cc_email in cc_emails:
                address_obj.comm_id_cc.extend([self.__get_commid(contact_address = cc_email, type = "email")])

            for bcc_email in bcc_emails:
                address_obj.comm_id_bcc.extend([self.__get_commid(contact_address = bcc_email, type = "email")])

            address_obj.comm_id_from = self.__get_commid(contact_address = from_email, type = "email")
            notify_payload_obj.address.CopyFrom(address_obj)
            tag_list = []

            for key, value in tags.items():
                tag = Tag()
                tag.key = key
                tag.value = value
                tag_list.append(tag)

            notify_payload_obj.data_params.extend(data_params_list)
            notify_payload_obj.template_name = GENERIC_TEMPLATE_NAME
            notify_payload_obj.language = english

            headers = {
                "Content-type": "application/x-protobuf",
                "Accept": "application/json",
                "token": self.platform_token[lob_name]
            }

            iris_response = requests.post(
                url = self.notify_url,
                data = notify_payload_obj.SerializeToString(),
                headers = headers
            )
            if iris_response.status_code == 200:
                response["status"] = True
                response["msg_id"] = iris_response.json()["message_id"]

        except Exception as e:
            log_id = str(uuid.uuid1())
            response["message"] = "Error occur (log id: {log_id})".format(log_id = log_id)
            logger.critical("Notify:sendEmail:{email_id}:{log_id}, {e}".format(
                email_id = from_email, log_id = log_id, e = repr(traceback.format_exc())))

        return response

    def set_whatsapp_optin_status(self, mobile_number, country = "INDIA", optin_status=True, lob="INGO"):
        """
        opt-in or opt-out whatsapp number
        @param country: Country name of mobiile number
        @param mobile_number: mobile number as string
        @param optin_status: opt in flag
        @param lob: either GI/MMT/INGO
        @return: dict
        """
        response = {
            'status': False,
            'error': ''
        }
        try:
            proto_obj = SetWhatsappOptinRequest()
            proto_obj.mobile = mobile_number
            proto_obj.opt_in = optin_status
            header = {'Content-type': 'application/x-protobuf', 'Accept': 'application/x-protobuf',
                      "token": self.platform_token[lob]}
            iris_response = requests.post(
                url = self.optin_url,
                data = proto_obj.SerializeToString(),
                headers = header
            )
            if iris_response.status_code == 200:
                pb_model_response = SetWhatsappOptinResponse()
                pb_model_response.ParseFromString(iris_response.content)
                response = protobuf_to_dict(pb_model_response)
        except Exception as e:
            log_id = str(uuid.uuid1())
            response["message"] = "Error occur (log id: {log_id})".format(log_id = log_id)
            logger.critical("Notify:whatsapp_optin:{mobile_number}:{log_id}, {e}".format(
                mobile_number = mobile_number, log_id = log_id, e = repr(traceback.format_exc())))
        return response

    def get_whatsapp_optin_status(self, mobile_number, lob="INGO"):
        """
        get current WhatsApp optin status
        @param mobile_number: mobile number
        @param lob: INGO/MMT/GI
        @return: return
        """
        response = {
            'error': '',
            'opt_status': False,
            'status': False
        }
        try:
            proto_obj = GetWhatsappOptinRequest()
            proto_obj.mobile = mobile_number
            header = {'Content-type': 'application/x-protobuf', 'Accept': 'application/x-protobuf',
                      "token": self.platform_token[lob]}
            iris_response = requests.post(
                url = self.optin_url,
                data = proto_obj.SerializeToString(),
                headers = header
            )
            if iris_response.status_code == 200:
                pb_model_response = GetWhatsappOptingResponse()
                pb_model_response.ParseFromString(iris_response.content)
                response = protobuf_to_dict(pb_model_response)
        except Exception as e:
            log_id = str(uuid.uuid1())
            response["message"] = "Error occur (log id: {log_id})".format(log_id = log_id)
            logger.critical("Notify:whatsapp_optin:{mobile_number}:{log_id}, {e}".format(
                mobile_number = mobile_number, log_id = log_id, e = repr(traceback.format_exc())))
        return response