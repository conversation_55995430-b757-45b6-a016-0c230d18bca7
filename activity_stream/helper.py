__author__ = 'keshavagrawal'
from actstream.models import Action
import datetime
from django.conf import settings

from actstream.models import target_stream
from ingouser.models import User

ACTIONS_PER_PAGE = 100


def get_target_activity(target, actor_name=None, filter_action_type=None):

    actions = target_stream(target)
    if not actor_name:
        if not filter_action_type:
            return actions
        else:
            return actions.filter(verb=filter_action_type)

    actors_id = User.objects.get(username=actor_name)
    actions = actions.filter(actor_object_id=actors_id.id)
    if filter_action_type:
        actions.filter(verb=filter_action_type)

    return actions


def create_activity(actor=None, action="changed", target=None, data=None):
    """
    This is a wrapper over django actstream package which takes care of adding
    custom_data in a defined format.
    :param actor:  Actor who is making the changes
    :param action: A text defining the verb/action
    :param target: Object which is being modified
    :param data: A dict {
        'from_state': <value>,
        'to_state': <value>,
    }
    :return: returns Action(actstream.models.Action) object if successful else
    raises exception accordingly.
    """
    if actor and action and target:
        if data and not isinstance(data, dict):
            raise Exception("'data' param should be of dict datatype")

        if not {"from_state", "to_state"} <= set(data):
            raise Exception("'data' param should have mandatory keys - "
                            "'from_state' & 'to_state'")

        action = Action(actor=actor, verb=action, target=target, data=data)
        action.save()

        return action
    else:
        for val in ["actor", "action", "target"]:
            if not vars()[val]:
                raise Exception("%s should have a valid value" % val)
