
[unix_http_server]
file=/dev/shm/supervisor.sock   ; (the path to the socket file)

[supervisord]
logfile=/opt/logs/supervisord.log ; (main log file;default $CWD/supervisord.log)
logfile_maxbytes=50MB        ; (max main logfile bytes b4 rotation;default 50MB)
logfile_backups=10           ; (num of main logfile rotation backups;default 10)
loglevel=info                ; (log level;default info; others: debug,warn,trace)
pidfile=/opt/logs/supervisord.pid ; (supervisord pidfile;default supervisord.pid)
nodaemon=false               ; (start in foreground if true;default false)
minfds=10000                  ; (min. avail startup file descriptors;default 1024)
minprocs=50000                 ; (min. avail process descriptors;default 200)
[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///dev/shm/supervisor.sock ; use a unix:// URL  for a unix socket

[supervisord]
nodaemon=true


# 2 containers, 4 cores each, moderate ram, each container has 8 workers
# total concurrency 16
[program:celery_rates]
command=/usr/local/goibibo/python2.7/bin/python2.7 /usr/local/goibibo/source/goibibo_inventory/manage.py celery worker -E --time-limit=3600 --concurrency=2 -n <EMAIL> --loglevel=INFO --pythonpath=/usr/local/goibibo/source/goibibo_inventory -Ofair -Q rates --logfile=/opt/logs/w1-ingoibibo-rates.log --pidfile=/dev/shm/w1-ingoibibo-rates.pid --app=goibibo_inventory.settings:app
stderr_logfile=/opt/logs/supervisor_rates.err.log
stdout_logfile=/opt/logs/supervisor_rates.out.log
autostart=true
autorestart=true
startretries=3
user=root
stopasgroup=true
killasgroup=true
stopsignal=QUIT


