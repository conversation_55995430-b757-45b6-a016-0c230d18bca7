
[unix_http_server]
file=/dev/shm/supervisor.sock   ; (the path to the socket file)

[supervisord]
logfile=/opt/logs/supervisord.log ; (main log file;default $CWD/supervisord.log)
logfile_maxbytes=50MB        ; (max main logfile bytes b4 rotation;default 50MB)
logfile_backups=10           ; (num of main logfile rotation backups;default 10)
loglevel=info                ; (log level;default info; others: debug,warn,trace)
pidfile=/opt/logs/supervisord.pid ; (supervisord pidfile;default supervisord.pid)
nodaemon=false               ; (start in foreground if true;default false)
minfds=10000                  ; (min. avail startup file descriptors;default 1024)
minprocs=50000                 ; (min. avail process descriptors;default 200)
[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///dev/shm/supervisor.sock ; use a unix:// URL  for a unix socket

[supervisord]
nodaemon=true


    #[program:filebeatapache]
#command=/usr/share/filebeat/bin/filebeat -c /etc/filebeat/apache_access_filebeat.yml -e
#directory=/etc/filebeat/
#stderr_logfile=/opt/logs/fiebeat_apache_error.log
#stdout_logfile=/opt/logs/fiebeat_apache_access.log
#autostart=true
#autorestart=true
#startretries=3

[program:filebeatinventory]
command=/usr/share/filebeat/bin/filebeat  -c /etc/filebeat/ingoibibo_inventory.yml -e
directory=/etc/filebeat/
stderr_logfile=/opt/logs/fiebeat_inventory_error.log
stdout_logfile=/opt/logs/fiebeat_inventory_access.log
autostart=true
autorestart=true
startretries=3


#[program:filebeatapache_new_ES]
#command=/usr/share/filebeat/bin/filebeat  -c /etc/filebeat/apache_access_filebeat_new.yml -e
#directory=/etc/filebeat/
#stderr_logfile=/opt/logs/fiebeat_apache_new_error.log
#stdout_logfile=/opt/logs/fiebeat_apache_new_access.log
#autostart=true
#autorestart=true
#startretries=3

[program:filebeatinventory_new_ES]
command=/usr/share/filebeat/bin/filebeat  -c /etc/filebeat/ingoibibo_inventory_new.yml -e
directory=/etc/filebeat/
stderr_logfile=/opt/logs/fiebeat_inventory_new_error.log
stdout_logfile=/opt/logs/fiebeat_inventory_new_access.log
autostart=true
autorestart=true
startretries=3
#===================================================

#    Filebeat kafka 03

[program:filebeat_ingoibibo]
command=/usr/share/filebeat/bin/filebeat  -c /etc/filebeat/filebeat.yml -e
directory=/etc/filebeat/
stderr_logfile=/opt/logs/filebeat_ingoibibo_error.log
stdout_logfile=/opt/logs/filebeat_ingoibibo_access.log
autostart=true
autorestart=true
startretries=3

[program:filebeat_Kafka]
command=/etc/filebeat_6/filebeat-6.2.4-linux-x86_64/filebeat -c /etc/filebeat_6/filebeat-6.2.4-linux-x86_64/filebeat.yml -e
directory=/etc/filebeat_6/filebeat-6.2.4-linux-x86_64
stderr_logfile=/opt/logs/filebeat_kafka_error.log
stdout_logfile=/opt/logs/filebeat_kafka_access.log
autostart=true
autorestart=true
startretries=3

#==================================================

# filebeat kafka04

[program:filebeat_ingoibibo]
command=/usr/share/filebeat/bin/filebeat  -c /etc/filebeat/filebeat.yml -e
directory=/etc/filebeat/
stderr_logfile=/opt/logs/filebeat_ingoibibo_error.log
stdout_logfile=/opt/logs/filebeat_ingoibibo_access.log
autostart=true
autorestart=true
startretries=3


[program:filebeat_Kafka]
command=/etc/filebeat_6/filebeat-6.2.4-linux-x86_64/filebeat -c /etc/filebeat_6/filebeat-6.2.4-linux-x86_64/filebeat.yml -e
directory=/etc/filebeat_6/filebeat-6.2.4-linux-x86_64
stderr_logfile=/opt/logs/filebeat_kafka_error.log
stdout_logfile=/opt/logs/filebeat_kafka_access.log
autostart=true
autorestart=true
startretries=3


#======================================================

#    filebeat kafka05

[program:filebeat_ingoibibo]
command=/usr/share/filebeat/bin/filebeat  -c /etc/filebeat/filebeat.yml -e
directory=/etc/filebeat/
stderr_logfile=/opt/logs/filebeat_ingoibibo_error.log
stdout_logfile=/opt/logs/filebeat_ingoibibo_access.log
autostart=true
autorestart=true
startretries=3


[program:filebeat_Kafka]
command=/etc/filebeat_6/filebeat-6.2.4-linux-x86_64/filebeat -c /etc/filebeat_6/filebeat-6.2.4-linux-x86_64/filebeat.yml -e
directory=/etc/filebeat_6/filebeat-6.2.4-linux-x86_64
stderr_logfile=/opt/logs/filebeat_kafka_error.log
stdout_logfile=/opt/logs/filebeat_kafka_access.log
autostart=true
autorestart=true
startretries=3

