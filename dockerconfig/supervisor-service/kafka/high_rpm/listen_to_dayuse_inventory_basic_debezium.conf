[unix_http_server]
file=/dev/shm/supervisor.sock   ; (the path to the socket file)

[supervisord]
logfile=/opt/logs/supervisord.log ; (main log file;default $CWD/supervisord.log)
logfile_maxbytes=50MB        ; (max main logfile bytes b4 rotation;default 50MB)
logfile_backups=10           ; (num of main logfile rotation backups;default 10)
loglevel=info                ; (log level;default info; others: debug,warn,trace)
pidfile=/opt/logs/supervisord.pid ; (supervisord pidfile;default supervisord.pid)
nodaemon=false               ; (start in foreground if true;default false)
minfds=10000                  ; (min. avail startup file descriptors;default 1024)
minprocs=50000                 ; (min. avail process descriptors;default 200)
[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///dev/shm/supervisor.sock ; use a unix:// URL  for a unix socket

[supervisord]
nodaemon=true

#topic partitions 8, parallelism 2, min container 1, nax container 3, cores 2, ram 4gb
[program:listen_to_dayuse_inventory_basic_debezium]
command=/usr/local/goibibo/python2.7/bin/python2.7 /usr/local/goibibo/source/goibibo_inventory/manage.py run_kafka_consumer listen_to_dayuse_inventory_basic_debezium
environment=CURR_ENV=prod
environment=APP_TYPE=web
process_name=%(program_name)s_%(process_num)05d
numprocs=2
priority=999 ; the relative start priority (default 999)
autostart=true ; start at supervisord start (default: true)
autorestart=true ; retstart at unexpected quit (default: true)
startsecs=10 ; number of secs prog must stay running (def. 10)
startretries=3 ; max # of serial start failures (default 3)
exitcodes=0,2 ; 'expected' exit codes for process (default 0,2)
stopsignal=TERM ; signal used to kill process (default TERM)
stopwaitsecs=10 ; max num secs to wait before SIGKILL (default 10)
user=root ; setuid to this UNIX account to run the program
log_stdout=true ; if true, log program stdout (default true)
log_stderr=true ; if true, log program stderr (def false)
logfile=/opt/logs/listen_to_dayuse_inventory_basic_debezium.log ; child log path, use NONE for none; default AUTO
logfile_maxbytes=50MB ; max # logfile bytes b4 rotation (default 50MB)
logfile_backups=10 ; # of logfile backups (default 10).log ; child log path, use NONE for none; default AUTO
logfile_maxbytes=50MB ; max # logfile bytes b4 rotation (default 50MB)
logfile_backups=10 ; # of logfile backups (default 10)
