var ing={};var city;function getCookie(a){var e=null;if(document.cookie&&document.cookie!=""){var d=document.cookie.split(";");for(var c=0;c<d.length;c++){var b=jQuery.trim(d[c]);if(b.substring(0,a.length+1)==(a+"=")){e=decodeURIComponent(b.substring(a.length+1));break}}}return e}ing.selfsignup={};ing.selfsignup.tabList=["#addProperty","#addHotelInfo","#addLocation","#addRoomPrice","#photosinfo","#addAmenities","#addPayout"];ing.selfsignup={goibibo_staff_choice:"2",init:function(){$("#subtabs li a").on("click",function(a){sub_nav=($(this).attr("href")).substring(1);ing.selfsignup.property.getSubTabs(sub_nav)});$("#roomtypecode").val("");ing.selfsignup.tabList=["#addProperty","#addHotelInfo","#addLocation","#addRoomPrice","#photosinfo","#addAmenities","#addPayout"];ing.selfsignup.getGeneralDetails();if($("#hotelcode").val()){$("#hotelactiontype").val("add");step=$("#ssu_step").val();if(step){ing.selfsignup.property.subtab=ing.selfsignup.tabList[step].substring(1);newList=ing.selfsignup.tabList.slice(ing.selfsignup.tabList.indexOf("#"+ing.selfsignup.property.subtab)+1);$(newList.join(", ")).attr("class","disabled");$(ing.selfsignup.tabList[step]).click()}}else{ing.selfsignup.property.addNewHotel()}$(function(){$("#loading").spin({}).hide()})},getGeneralDetails:function(){$("#loading").fadeIn();$.ajaxSetup({async:false});$.ajax({type:"GET",url:"/extranet/property/getallgeneraldetails/",data:{},success:this.successGenAjax})},successGenAjax:function(a){dataJson=JSON.parse(a);commonThis=ing.selfsignup;commonThis.allinclusions=dataJson.allinclusions;commonThis.allhoteltypes=dataJson.allhoteltypes;commonThis.allbanknames=dataJson.allbanknames;commonThis.allifsccodes=dataJson.allifsccodes;commonThis.roomallfacilities=dataJson.roomallfacilities;commonThis.roomallfacilities_groupby=_.groupBy(dataJson.roomallfacilities,"amenitysubcat");commonThis.allamenitydetails=_.groupBy(dataJson.allamenitydetails,"amenitysubcat");commonThis.amenities_subcat=dataJson.amenities_subcat;$.ajaxSetup({async:true})},getlocalityList:function(){$.ajax({type:"GET",url:"/extranet/manageproperty/getlocalitylist/",data:{city:$("#id_city").val()},success:this.successLocalityAjax})},createOptionHtml:function(b){var a=_.template($("#optionlist_template").html());return a({optionList:b})},successLocalityAjax:function(a){dataJson=JSON.parse(a);if(dataJson.success){$("#form_"+ing.selfsignup.property.subtab+" #id_localityname").html(ing.selfsignup.createOptionHtml(dataJson.localitylist))}},getStateList:function(){$.ajax({type:"GET",url:"/extranet/manageproperty/getstatelist/",data:{country:$("#id_country").val()},success:this.successState2Ajax})},successState2Ajax:function(a){dataJson=JSON.parse(a);if(dataJson.success){dataJson.statelist.splice(0,0,["","Select state"]);$("#form_"+ing.selfsignup.property.subtab+" #id_state").html(ing.selfsignup.createOptionHtml(dataJson.statelist));$("#form_"+ing.selfsignup.property.subtab+" #id_city").html(ing.selfsignup.createOptionHtml(["","Select City"]));$("#form_"+ing.selfsignup.property.subtab+" #id_localityname").html(ing.selfsignup.createOptionHtml(["","Select City"]))}},getCityList2:function(){$.ajax({type:"GET",url:"/extranet/manageproperty/getcitylist/",data:{state:$("#id_state").val()},success:this.successCity2Ajax})},successCity2Ajax:function(a){dataJson=JSON.parse(a);if(dataJson.success){dataJson.citylist.splice(0,0,["","Select city"]);$("#form_"+ing.selfsignup.property.subtab+" #id_city").html(ing.selfsignup.createOptionHtml(dataJson.citylist));$("#form_"+ing.selfsignup.property.subtab+" #id_localityname").html(ing.selfsignup.createOptionHtml(["","Select locality"]))}},autoSuggestHandler:function(a){validation=$("#form_addProperty").parsley("validate");if(validation==false){return}var g=$("#id_hotelname").val();var j=$("#id_address").val();var e=$("#id_city").val();var i=$("#id_hotelcode").val();var c=$("#id_country").val();var b=$("#id_state").val();var k=$("#id_localityname").val();var h=$("#csrf_token").val();var d=i===""&&c.toUpperCase()!=="INDIA";if(d){var f={address:j,city:e,country:c,state:b,localityname:k,hotelname:g,csrfmiddlewaretoken:h};$.ajax({type:"POST",data:f,url:"/extranet/voyager/autosuggest/",success:function(l){if(l.hotels){var n=_.template($("#ing_selfsignup_autosuggest").html());var m=n({hotels:l.hotels,});$("#myModal").html(m).show();$($(".optradio")[0]).attr("checked",true);$("#autosuggestmodal").modal("show");$("#not_checkbox").on("click",function(o){if($("#not_checkbox").is(":checked")){_.each($(".optradio"),function(q,p){$(q).attr("checked",false)});$("#id_voyagerid").val("")}else{$($(".optradio")[0]).prop("checked",true)}});$(".optradio").click(function(){var o=_.some($(".optradio"),function(p){return $(p).is(":checked")});if(o){$("#not_checkbox").prop("checked",false)}});$("#submit_autosuggest").on("click",function(){var o=$(".optradio").filter(function(){return $(this).is(":checked")});if(o.length!=0){voyagerid=$(o[0]).attr("class").split(" ")[1];$("#autosuggestmodal").modal("hide");$("#myModal").css("display","none");$("#id_voyagerid").val(voyagerid)}$("#autosuggestmodal").modal("hide");$("#myModal").css("display","none");ing.selfsignup.property.submitDetails("#form_addProperty")})}else{ing.selfsignup.property.submitDetails("#form_addProperty")}}})}if(!d){ing.selfsignup.property.submitDetails("#form_addProperty")}}};ing.selfsignup.property={addNewHotel:function(){$("#current_htl_name").val("");$("#hotelcode").val("");$("#rateplancode").val("");ing.selfsignup.property.subtab="addProperty";newList=ing.selfsignup.tabList.slice(ing.selfsignup.tabList.indexOf("#"+ing.selfsignup.property.subtab)+1);$(newList.join(", ")).attr("class","disabled");$("#hotelactiontype").val("add");ing.selfsignup.property.getSubTabs()},getSubTabs:function(sub_nav){$("#property_message").hide();if(sub_nav==undefined||sub_nav==""){sub_nav="addProperty"}if(sub_nav=="addPayout"){$("#addPayout").removeClass("disabled").show();ing.selfsignup.property.switchTab()}if($("#"+sub_nav).hasClass("disabled")){return}ing.selfsignup.property.subtab=sub_nav;eval("this.preAjaxData."+sub_nav+"()");ing.selfsignup.property.getForm()},preAjaxData:{addProperty:function(){this.url="/extranet/selfsignup/";this.successGetFunc="ing.selfsignup.property.successAjaxGetInfo";this.successFunc="ing.selfsignup.property.successAjaxInfo";this.dataArr={}},addHotelInfo:function(){this.url="/extranet/selfsignup/";this.successGetFunc="ing.selfsignup.property.successAjaxGetInfo";this.successFunc="ing.selfsignup.property.successAjaxInfo";this.dataArr={}},addAmenities:function(){this.templatename="#facilityinfo_template1";this.url="/extranet/selfsignup/";this.successFunc="ing.selfsignup.property.successAjaxAmenityInfo";this.successGetFunc="ing.selfsignup.property.successAjaxGetAmenityInfo";this.dataArr={all_amenities:ing.selfsignup.allamenitydetails,hotel_amenities:ing.selfsignup.property.hotelamenitydetails,room_all_amenities:ing.selfsignup.roomallfacilities_groupby,room_amenities:"",all_subcat:ing.selfsignup.amenities_subcat}},addPayout:function(){this.url="/extranet/selfsignup/manageaccounts/";this.successFunc="ing.selfsignup.property.successAjaxPayoutInfo";this.successGetFunc="ing.selfsignup.property.successAjaxGetInfo";this.dataArr={allbanknames:ing.selfsignup.allbanknames,allifsccodes:ing.selfsignup.allifsccodes}},addLocation:function(){this.url="/extranet/selfsignup/";this.successGetFunc="ing.selfsignup.property.successAjaxGetInfo";this.successFunc="ing.selfsignup.property.successAjaxInfo";this.dataArr={}},photosinfo:function(){this.templatename="#photoslist_template";this.url="/extranet/selfsignup/manageproperty/images/getall/";this.successGetFunc="ing.selfsignup.property.successAjaxGetPhotosInfo";this.successFunc="ing.selfsignup.property.successAjaxInfo";this.dataArr={imagelist:ing.selfsignup.property.imagelist,relatedToList:ing.selfsignup.property.relatedToList,hotelcode:$("#hotelcode").val(),mobile:ing.selfsignup.property.mobile}},addRoomPrice:function(){this.url="/extranet/selfsignup/managerooms/";this.successFunc="ing.selfsignup.property.successAjaxInfo";this.successGetFunc="ing.selfsignup.property.successAjaxGetInfo";this.dataArr={}}},displaySteps:function(c){var b=_.template($(this.preAjaxData.templatename).html());var a=b(this.preAjaxData.dataArr);$(c).html(a);$("#fileupload").fileupload({previewMaxWidth:240,previewMaxHeight:200})},selfsignupStaffCodeProcess:function(){try{if($("select[name=datasource_id] option[selected=selected]")[0].value!=ing.selfsignup.goibibo_staff_choice){$(".staff_code").parent().parent().hide()}}catch(a){$(".staff_code").parent().parent().hide()}$("select[name=datasource_id]").on("change",function(){if(this.value==ing.selfsignup.goibibo_staff_choice){$(".staff_code").parent().parent().fadeIn(700);$(".staff_code").attr("data-required","true")}else{$(".staff_code").parent().parent().hide();$(".staff_code").removeAttr("data-required");$(".staff_code").val("")}})},getForm:function(){$("#loading").fadeIn();dataDict={formname:ing.selfsignup.property.subtab,hotelcode:$("#hotelcode").val()};if(ing.selfsignup.property.subtab=="addRoomPrice"){dataDict.roomtypecode=$("#roomtypecode").val()}$.ajax({type:"GET",url:this.preAjaxData.url,data:dataDict,success:eval(this.preAjaxData.successGetFunc)})},successAjaxGetInfo:function(a){respJson=JSON.parse(a);if(respJson.hotelcode!=undefined){$("#hotelcode").val(respJson.hotelcode)}if((!$("#roomtypecode").val())&&(respJson.roomtypecode!=undefined)){$("#roomtypecode").val(respJson.roomtypecode)}if((!$("#rateplancode").val())&&(respJson.rateplancode!=undefined)){$("#rateplancode").val(respJson.rateplancode)}$("#content").html(respJson.renderHtml);if(!respJson.show_error&&$("#hotelactiontype").val()=="add"){$(".errorlist").hide()}var e=$("#id_dialing_prefix").val();if(e!=""&&typeof e!="undefined"&&$(".dialprefix").length==0){ing.selfsignup.property.addDialingPrefix("#id_hotelphone",e);ing.selfsignup.property.addDialingPrefix("#id_hotelmobile",e)}var b=$("#id_localityname");var c=b.length>0;var h=$("#id_country").val();var d=h?h.toLowerCase()!=="india":false;var g=typeof ing.selfsignup.property.showLocalityButton==="undefined";if(c&&d&&g){b.parent().after('<input type="button" class="btn btn-sm btn-warning" onclick="ing.selfsignup.property.showLocalityModal();" id="addloc" value="Add a locality">');var f=$("#id_localityname option").first();if(f.val()===""){f.text("Add a locality now.")}}if($("#id_hoteltype").length){valueSelected=$("#id_hoteltype").val();if(!(valueSelected=="hrtg"||valueSelected=="rst"||valueSelected=="plc"||valueSelected=="htl")){$("#id_starrating").hide();$("label[for='id_starrating']").hide()}}if($("#id_checkintime").length){$("#id_checkintime").addClass("bootstrap-timepicker")}if($("#id_checkouttime").length){$("#id_checkouttime").addClass("bootstrap-timepicker");$("#id_checkouttime, #id_checkintime").timepicker({showMeridian:false})}if($("#id_taxes").val()){$("#tax-field").show()}if($("#id_pernightprice").length){$("#id_pernightprice").blur(function(j){var i=$("#id_pernightprice").val();$("#id_pernightprice").val($.trim(i))})}$("#loading").fadeOut();$("input[type=text], textarea, select, input[type=password]").addClass("form-control");if(ing.selfsignup.property.subtab=="addProperty"){ing.selfsignup.property.selfsignupStaffCodeProcess()}},successAjaxInfo:function(a){try{respJson=JSON.parse(a);if(respJson.success){$("#hotelcode").val(respJson.hotelcode);if(respJson.roomtypecode!=undefined){$("#roomtypecode").val(respJson.roomtypecode)}if(respJson.rateplancode!=undefined){$("#rateplancode").val(respJson.rateplancode)}$(".errorlist").hide();ing.selfsignup.property.switchTab()}}catch(c){$("#content").html(a);if(ing.selfsignup.property.subtab=="addProperty"){ing.selfsignup.property.selfsignupStaffCodeProcess()}if($("#hotelactiontype").val()!="add"){$("#content .skip-btn").hide()}}var b=$("#id_dialing_prefix").val();if(b!=""&&typeof b!="undefined"&&$(".dialprefix").length==0){ing.selfsignup.property.addDialingPrefix("#id_hotelphone",b);ing.selfsignup.property.addDialingPrefix("#id_hotelmobile",b)}$("#content input[type=text], #content textarea, #content select, #content input[type=password]").addClass("form-control");$("#loading").fadeOut()},successAjaxPayoutInfo:function(a){try{var b=$("#property_message");b.text("");b.text("Account details saved successfully");$("#property").scrollTop(0);responseJson=JSON.parse(a);if(responseJson.success){if(b.hasClass("alert-error")){b.removeClass("alert-error")}b.addClass("alert alert-success")}else{b.addClass("alert alert-error")}b.show();window.location="/extranet/"}catch(c){$("#content").html(a);if($("#hotelactiontype").val()!="add"){$("#content .skip-btn").hide()}}$("#content input[type=text], #content textarea, #content select, #content input[type=password]").addClass("form-control");$("#loading").fadeOut()},submitAmenitydetails:function(mode){$("#loading").fadeIn();ajaxThis=ing.selfsignup.property;ajaxThis.preAjaxData.dataArr.roomtypecode="";ing.selfsignup.mode=mode;var hotelcode=$("#hotelcode").val();var code=hotelcode;var relatedcode=$("#update_htl_amnenity select[name=roomtypecode]").val();if(relatedcode){code=relatedcode;ajaxThis.preAjaxData.dataArr.all_amenities=ing.selfsignup.roomallfacilities_groupby;ajaxThis.preAjaxData.dataArr.roomtypecode=code}var output=$.map($("#update_htl_amnenity input:checkbox:checked"),function(n,i){return n.value}).join(",");if(!output){alert("Select atleast one amenity");return}$.ajax({type:"GET",url:"/extranet/property/submitamenitydetails/",data:{hid:$("#hotelcode").val(),idlist:output,relatedcode:code},success:eval(ajaxThis.preAjaxData.successFunc)})},successAjaxAmenityInfo:function(a){ajaxThis=ing.selfsignup.property;responseJson=JSON.parse(a);ajaxThis.preAjaxData.dataArr.hotel_amenities=responseJson.amenities;ajaxThis.displaySteps("#content");if(ing.selfsignup.mode=="Done"){ing.selfsignup.property.submitForApproval()}else{var b=$("#property_message");b.text("");b.text("Amenities added successfully");$("#property").scrollTop(0);window.location="/extranet/";$("#loading").fadeOut()}},successAjaxGetAmenityInfo:function(a){if($('a[href="#uc"]')){$('a[href="#uc"]').trigger("click")}ajaxThis=ing.selfsignup.property;responseJson=JSON.parse(a);ajaxThis.preAjaxData.dataArr.hotel_amenities=responseJson.amenities;ajaxThis.displaySteps("#content");ajaxThis.preAjaxData.dataArr.all_amenities=ing.selfsignup.allamenitydetails;$("#loading").fadeOut()},getAmenity:function(){$("#loading").fadeIn();var prop_msg=$("#property_message");prop_msg.text("").hide();var datadict={};ajaxThis=ing.selfsignup.property;var relatedcode=$("#update_htl_amnenity select[name=roomtypecode]").val();if(relatedcode){datadict.roomtypecode=relatedcode;ajaxThis.preAjaxData.dataArr.all_amenities=ing.selfsignup.roomallfacilities_groupby;ajaxThis.preAjaxData.dataArr.roomtypecode=datadict.roomtypecode;$.ajax({type:"GET",url:"/extranet/selfsignup/managerooms/",data:datadict,success:eval(ajaxThis.successAjaxGetAmenityInfo)})}else{$("#addAmenities").click()}},successAjaxGetPhotosInfo:function(a){var c=ing.selfsignup.property;if(a.response){c.imagelist=a.files;c.relatedToList=a.relatedToList;c.mobile=a.mobile;c.preAjaxData.photosinfo();c.displaySteps("#content")}else{$("#content").html(a.renderHtml)}ing.selfsignup.property.updateImageAttrs();$(".actstat").on("change",ing.selfsignup.property.filterPhotos);c.configureDropzones();var b={header:"ui-icon-circle-arrow-e",activeHeader:"ui-icon-circle-arrow-s"};$("#accordion").accordion({header:".head",heightStyle:"content",icons:b});$(".existing_files").sortable({update:function(){$(".imgalert").hide();$(".imgalert").parent().removeClass("col-md-10").addClass("col-md-9");$("#photo_save").parent().show();$("#photo_save_warning").show()},cancel:".uploadzone",items:"> li"});$(".existing_files li input").bind("mousedown.ui-disableSelection selectstart.ui-disableSelection",function(d){d.stopImmediatePropagation()});$(".existing_files li select").bind("mousedown.ui-disableSelection selectstart.ui-disableSelection",function(d){d.stopImmediatePropagation()});$("#loading").fadeOut()},updateImageAttrs:function(){$("button[name=update_images_attr]").off();$("button[name=update_images_attr]").on("click",function(a){a.preventDefault();x=a.currentTarget;fileid=$(x).attr("fileid");ing.selfsignup.property.updateImage(fileid)})},configureDropzones:function(){$(".dropzone").dropzone({url:"manageproperty/images/new/",maxFilesize:10,dictDefaultMessage:"",paramName:"files",init:function(){this.on("success",function(c,b){var e=b.files[0];var d=e.relatedto;if(e.error||!(Object.keys(e).length)){if(!(Object.keys(e).length)){alert("Invalid file type")}else{alert(e.error)}$("#ul_"+d+" .template-download .dropzone div").remove();$(".dz-preview").remove()}else{var f=_.template($("#single_image").html());var a=f({file:e,relatedToList:e.relatedToList});$("#ul_"+d+" .dropzone").parent().before(a);$("#ul_"+d+" .template-download .dropzone div").remove()}$(".existing_files li input").bind("mousedown.ui-disableSelection selectstart.ui-disableSelection",function(g){g.stopImmediatePropagation()});$(".existing_files li select").bind("mousedown.ui-disableSelection selectstart.ui-disableSelection",function(g){g.stopImmediatePropagation()});ing.selfsignup.property.updateImageAttrs()});this.on("error",function(a){alert("Error uploading image");$(".dz-preview").remove()});this.on("addedfile",function(d,c){$(".dz-preview").css("margin-top","-50%");$(".dz-preview").css("margin-left","8%")})}})},updateImage:function(b){if($("#update_button_"+b).hasClass("disabled")){return}$("#loading").fadeIn();var a=$("#csrf_token").val();$.ajax({type:"POST",url:"/extranet/manageproperty/editimage/",data:{csrfmiddlewaretoken:a,hotelcode:$("#hotelcode").val(),imageid:b,caption:$("#caption_"+b).val(),relatedto:$("#relatedto_"+b).val()},success:ing.selfsignup.property.successImageUpdateInfo})},successImageUpdateInfo:function(c){var b=_.template($("#modal_template").html());var a=b();$("#myModal").html(a);$("#modal_body").html(c.message);$("#heading").html("Message");$("#myModal #submit_button_action").hide();$("#myModal").modal("show");if(c.imageid!=undefined){$("#update_button_"+c.imageid).addClass("disabled")}$("#loading").fadeOut()},submitDetails:function(formId){if($("#id_description").length){patt=new RegExp(/[^\w\-/\\\(\)\'<>\`.,:\s\!\^%@*#]/g);$("#id_description").val($("#id_description").val().replace(patt," "))}if($("#id_hoteltype").length){valueSelected=$("#id_hoteltype").val();if(!(valueSelected=="hrtg"||valueSelected=="rst"||valueSelected=="plc"||valueSelected=="htl")){$("#id_starrating").prop("selectedIndex",-1)}}if($("#id_accno").length){patt=new RegExp(/\W/g);$("#id_accno").val($("#id_accno").val().replace(patt,""));$("#ssu_step").val("6")}validation=$(formId).parsley("validate");if(validation==false){return}$("#loading").fadeIn();postDict=$(formId).serialize();if(ing.selfsignup.property.subtab=="addRoomPrice"){postDict+="&roomtypecode="+$("#roomtypecode").val();postDict+="&rateplancode="+$("#rateplancode").val();ing.selfsignup.roomtypename=$("#id_roomtypename").val()}if(ing.selfsignup.property.subtab=="addLocation"){postDict+="&country="+$("#id_country")[0].value;postDict+="&state="+$("#id_state")[0].value;postDict+="&city="+$("#id_city")[0].value}postDict+="&formname="+ing.selfsignup.property.subtab;postDict+="&hotelcode="+$("#hotelcode").val();$.ajax({type:"POST",url:this.preAjaxData.url,data:postDict,success:eval(this.preAjaxData.successFunc)})},switchTab:function(a){$("#property_message").hide();ajaxThis=ing.selfsignup.property;currenttab=ing.selfsignup.tabList[ing.selfsignup.tabList.indexOf("#"+ajaxThis.subtab)];$("#ssu_step").val(ing.selfsignup.tabList.indexOf("#"+ing.selfsignup.property.subtab)+1);if(a!=undefined&&a=="skip"){$(currenttab).parent().addClass("skipped")}else{if($(currenttab).parent().hasClass("error")){$(currenttab).parent().removeClass("error")}$(currenttab).parent().addClass("success")}previousTabs=ing.selfsignup.tabList.slice(0,ing.selfsignup.tabList.indexOf("#"+ajaxThis.subtab)+1);$(previousTabs.join(", ")).removeClass("disabled").parent();$(currenttab).parent().removeClass("active");nexttab=ing.selfsignup.tabList[ing.selfsignup.tabList.indexOf("#"+ajaxThis.subtab)+1];$(nexttab).show();$(nexttab).parent().attr("class","active");if(nexttab!=="#addPayout"){$(nexttab).removeClass("disabled").click()}},propertyInit:function(){var b=$('#property #subtabs li[class="active"] a')[0];var a=($(b).attr("id"));ajaxThis=ing.selfsignup.property;if(ajaxThis.subtab==undefined){ajaxThis.subtab=a}ajaxThis.getSubTabs(ajaxThis.subtab)},displayAjaxResponseMessage:function(a){if(a.renderHtml!=undefined){$("#docBody").html(a.renderHtml);return}var b=$("#property_message");b.text("");b.text(a.message);$("#property").scrollTop(0);if(a.success){if(b.hasClass("alert-error")){b.removeClass("alert-error")}b.addClass("alert alert-success")}else{b.addClass("alert alert-error")}if($("#hotelactiontype").val()=="edit"||$("#hotelactiontype").val()==""){b.show()}},submitForApproval:function(){$("#loading").fadeIn();dataDict={hotelcode:$("#hotelcode").val()};$.ajax({type:"GET",url:"/extranet/selfsignup/approval/",data:dataDict,success:ing.selfsignup.property.succApprovalForms})},succApprovalForms:function(a){respJson=JSON.parse(a);$("#btn_preview").prop("disabled",false);if(respJson.success){$("#send_for_approval").val("Wait For Hotel Acivation").addClass("disabled");$("#resp_message_success").html(respJson.message)}else{$("#resp_message_error").html(respJson.message)}$("#myModal").html($("#thank_you").html()).show();$("#hotelactiontype").val("edit");$("#loading").fadeOut()},amenitiesTransferFlow:function(b,a){$("button[id=all_amenities]").off();$("button[id=selected_amenities]").off();$("button[id=all_amenities]").on("click",function(c){c.preventDefault();ing.selfsignup.common.SelectMoveRows(b,a)});$("button[id=selected_amenities]").on("click",function(c){c.preventDefault();ing.selfsignup.common.SelectMoveRows(a,b)})},handleDisable:function(a){$("#"+a).removeClass("disabled")},datesInit:function(){$("#datepicker_stayend").datepicker({minDate:0,dateFormat:"yy-mm-dd",onClose:function(b){if(b){var c=new Date();selectedDate2=new Date(b);var a=24*60*60*1000;ing.selfsignup.property.diffDays=Math.ceil((selectedDate2.getTime()-c.getTime())/a);ing.selfsignup.property.setEnddayValue()}}});$("#datepicker_staystart").datepicker({minDate:new Date(),dateFormat:"yy-mm-dd",onClose:function(a){$("#datepicker_stayend").datepicker("option","minDate",a)}});$("#datepicker_staystart,#datepicker_stayend").on("keydown",function(a){a.preventDefault()})},changeIcss:function(c){var a=$(c).attr("class");var b=a=="glyphicon glyphicon-plus"?"glyphicon glyphicon-minus":"glyphicon glyphicon-plus";$(c).removeClass(a);$(c).addClass(b)},getvoyagerdetail:function(){$.ajaxSetup({async:false});$.ajax({type:"GET",url:"/extranet/selfsignup/getvoyagerdetails",data:{hotelcode:$("#hotelcode").val()},success:function(a){resp=JSON.parse(a);if(resp.success){ing.selfsignup.hotel_voyagerid=resp.hotel_voyagerid;ing.selfsignup.city_id=resp.city_id;ing.selfsignup.hotel_name=resp.hotel_name;ing.selfsignup.city=resp.city}}})},preview:function(){ing.selfsignup.property.getvoyagerdetail();var d=new Date();var c=new Date();c.setDate(d.getDate()+1);d=d.toISOString().slice(0,10).replace(/-/g,"");c=c.toISOString().slice(0,10).replace(/-/g,"");var b=ing.selfsignup.property.hotel_name.replace(/ /g,"-");var a=ing.selfsignup.property.city.replace(/ /g,"-");var e="https://www.goibibo.com/hotels/detail/"+b+"-hotel-in-"+a+"-"+ing.extranet.common.hotel_voyagerid+'/?hquery=%7B"ci"%3A"'+d+'"%2C"co"%3A"'+c+'"%7D';window.open(e)},deleteImage:function(b,a){var c=$("#csrf_token").val();$.ajax({url:"manageproperty/images/delete/"+a+"/"+b+"/",type:"POST",data:{csrfmiddlewaretoken:c},success:function(d){}})},addDialingPrefix:function(a,c){var d=$(a);d.unwrap();var b=d.parent();b.addClass("form-inline");d.before('<input type="text" class="dialprefix" readonly class="form-control" value="+91" style="padding:11px;margin:0px 0px 0px 15px;width:103px;">');$(".dialprefix").val(c);d.css("width","243px")},showLocalityModal:function(){var a=_.template($("#locality_template").html());var b=a({city:$("#id_city").val(),state:$("#id_state").val()});$("#myModal").html("");$("#myModal").html(b).show();$("#submit_locality").on("click",ing.selfsignup.property.addLocality)},filterPhotos:function(d){$(".imgalert").hide();var c=$(".actstat").val();if(c!=="all"){$("#reorder-disabled").show();$(".existing_files").sortable({disabled:true})}else{$(".existing_files").sortable({disabled:false})}var b=$(".template-download");var a=".template-download a";if(c!=="all"){a=a+'[isactive="'+c+'"]'}$(b).hide();$(a).parent().show();$(".uploadzone").show()},updateImageOrder:function(){$(".imgalert").hide();var a={};var b=$(".existing_files");b.each(function(){var d=this.id;var e=[];$(this).find("img").each(function(){e.push(this.id)});a[d]=e});var c=$("#csrf_token").val();$.ajax({type:"POST",data:{images:JSON.stringify(a),csrfmiddlewaretoken:c},url:"property/updateimageorder/",success:function(d){if(d.success){$("#save-notification").show()}else{$("#error-notification").show()}$("#photo_save").parent().hide();$(".imgalert").parent().removeClass("col-md-9").addClass("col-md-10")}})},addLocality:function(){localityname=$("#modal_locality").val();cityname=$("#id_city").val();statename=$("#id_state").val();var b={localityname:localityname,city:cityname,state:statename,csrfmiddlewaretoken:$("#csrf_token").val()};var a=$("#locality-form").parsley("validate");if(a){$.ajax({type:"POST",data:b,url:"/extranet/property/addlocality/",success:function(c){if(!c.success){alert("Error adding locality")}else{$("#addLocation").click();ing.selfsignup.property.selected_locality=c.locality;ing.selfsignup.property.showLocalityButton=false}},error:function(d,c){alert("Error adding locality")},complete:function(d,c){$("#myModal").hide()}})}}};