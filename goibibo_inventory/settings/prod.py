from base import *
import os
import sys
import requests
import socket

from aws_secrets import secret_settings
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

env_config = sys.modules[__name__]
secret_settings('prod', env_config)

os.environ['comm_email_salt'] = str(COMM_EMAIL_SALT)
os.environ['comm_mobile_salt'] = str(COMM_MOBILE_SALT)

log_host = socket.gethostname()

HOST = 'in.goibibo.com'
RATES_HOST = 'ingo-phoenix.ecs.mmt'
RATES_PORT = 443
INVENTORY_HOST = 'ingo-phoenix.ecs.mmt'

INVENTORY_PORT = 443

RESERVATION_ENGINE_HOST = 'ingo-reservationengine.ecs.mmt'
RESERVATION_ENGINE_PORT = 8443

FRN_HOST='ingo-frn.ecs.mmt'
FRN_PORT=8443

CAMPAIGN_HOST = 'ingo-hotel-supply-campaign.ecs.mmt'
CAMPAIGN_PORT = 8443

INTERLINK_HOST = "ingo-hotel-supply-interlink-service.ecs.mmt"
INTERLINK_PORT = 8443

HTLCLD_HOST = 'htlcld-supply.ecs.mmt'
HTLCLD_PORT = 8443
HTLCLD_HEIMDALL_HOST = 'htlcld-supply-heimdall.ecs.mmt'
HTLCLD_HEIMDALL_PORT = 8443
HTLCLD_CONTENT_SERVICE_URL = 'htlcld-supply-content.ecs.mmt'
HTLCLD_CONTENT_SERVICE_PORT = '8443'

SITE_ID = 1
SESSION_COOKIE_DOMAIN = '.in.goibibo.com'

ALLOWED_HOSTS = ['.' + HOST, '.' + HOST + '.', '.in.goibibo.com','*']
########## END SITE CONFIGURATION
EC2_PRIVATE_IP = None
try:
    EC2_PRIVATE_IP = requests.get('http://***************/latest/meta-data/local-ipv4', timeout = 0.01).text
except requests.exceptions.RequestException:
    pass
if EC2_PRIVATE_IP:
    ALLOWED_HOSTS.append(EC2_PRIVATE_IP)

SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
HTTPS_SUPPORT = False
########## EMAIL HOST CONFIGURATION
#EMAIL_HOST = "***************"
#EMAIL_HOST = "mailx.goibibo.com"
EMAIL_HOST = "mailx.goibibo.com"
########## END EMAIL HOST CONFIGURATION

########## DEBUG CONFIGURATION
DEBUG = False
TEMPLATE_DEBUG = DEBUG
########## END DEBUG CONFIGURATION

########## MANAGER CONFIGURATION
#ADMINS = (
#    ('Rahul Goyal', '<EMAIL>'),
#    ('Prerna Arya', '<EMAIL>'),
#    ('Amit Barnwal', '<EMAIL>'),
#    ('Vikalp Sahni', '<EMAIL>'),
#    ('Ankit Rastogi', '<EMAIL>'),
#)


ADMINS = (
     # ('Goibibo errors', '<EMAIL>'),
#     ('Goibibo errors', '<EMAIL>'),
)

# This is dummy backend for django to don't do anything with uncaught exceptions
EMAIL_BACKEND = "django.core.mail.backends.dummy.EmailBackend"
# EMAIL_BACKEND = "django.core.mail.backends.filebased.EmailBackend"
# EMAIL_FILE_PATH = "/tmp/django_uncaught_exceptions"

MANAGERS = ADMINS
########## END MANAGER CONFIGURATION

########## Extranet Thin App URL
EXTRANET_CONTAINER = 'http://ingo-extranet.ecs.mmt/'
EXTRANET_ASSETS = 'https://prod-mmt-ingo-extranet.s3.ap-south-1.amazonaws.com/'

#### CASSANDRA CONFIGURATION ################################

#CASSANDRA_CONFIG = {
#    'CLUSTER_NODES' : ['***********', '***********', '***********'],
#    'KEYSPACE_NAME': 'goibibo_inventory'
#}

CASSANDRA_CONFIG = {
    'CLUSTER_NODES' : ['ingo-cass-01.prod.goibibo.com', 'ingo-cass-02.prod.goibibo.com', 'ingo-cass-03.prod.goibibo.com'],
    'KEYSPACE_NAME': 'ingoibibo',
    'DATA_CENTER': 'ingocassmumbai'
}

#########END CASSANDRA CONFIGURATION #########################

########## CACHE CONFIGURATION
REDIS_HOST = "redis-ingo-app.mmt.mmt:6379"

RATE_LIMIT_CACHE = {
    'HOST': REDIS_HOST,
    'OPTIONS': {'DB': 6}
}

CACHES = {
    'default': {
        'BACKEND': 'lib.redis_cache_backend.RedisCache',
        'LOCATION': 'redis-ingo-app.mmt.mmt:6379',
        'KEY_PREFIX': 'ingoibibo',
        'VERSION': 1,
        'OPTIONS': {
            'DB': 3,
            #'PASSWORD': '',
            #'PARSER_CLASS': 'redis.connection.HiredisParser'
        },
    },
    'session_storage': {
        'BACKEND': 'lib.redis_cache_backend.RedisCache',
        'LOCATION': 'redis-ingo-app.mmt.mmt:6379',
        'KEY_PREFIX': 'ingoibibo',
        'KEY_FUNCTION' : 'common.commonhelper.make_cache_key',
        'VERSION': 1,
        'OPTIONS': {
            'DB': 1,
        },
    },
    'redis_audit': {
        'BACKEND': 'lib.redis_cache_backend.RedisCache',
        'LOCATION': 'redis-ingo-app.mmt.mmt:6379',
        'KEY_PREFIX': 'ingoibibo',
        'KEY_FUNCTION': 'common.commonhelper.make_cache_key',
        'VERSION': 1,
        'OPTIONS': {
            'DB': 8,
        },
    },
    'payment_cache': {
        'BACKEND': 'lib.redis_cache_backend.RedisCache',
        'LOCATION': 'redis-ingo-app.mmt.mmt:6379',
        'KEY_PREFIX': 'ingoibibo',
        'KEY_FUNCTION': 'common.commonhelper.make_cache_key',
        'VERSION': 1,
        'OPTIONS': {
            'DB': 4,
        },
    },
    'configkeeper_subscriber': {
         'BACKEND': 'lib.redis_cache_backend.RedisCache',
         'LOCATION': 'redis-ingo-app.mmt.mmt',
         'PORT': 6379,
         'KEY_PREFIX': 'ingoibibo',
         'KEY_FUNCTION': 'common.commonhelper.make_cache_key',
         'VERSION': 1,
         'OPTIONS': {
             'DB': 12,
         },
     },
    'redis_static_audit': {
        'BACKEND': 'lib.redis_cache_backend.RedisCache',
        'LOCATION': 'redis-ingo-app.mmt.mmt',
        'PORT': '6379',
        'KEY_PREFIX': 'ingoibibo',
        'KEY_FUNCTION': 'common.commonhelper.make_cache_key',
        'OPTIONS': {
            'DB': 7,
        },
    },
    'redis_static_content_pipeline': {
        'BACKEND': 'lib.redis_cache_backend.RedisCache',
        'LOCATION': 'redis-ingo-app.mmt.mmt',
        'PORT': '6379',
        'KEY_PREFIX': 'contentpipelinehotel',
        'OPTIONS': {
            'DB': 14,
        },
    }
}

SESSION_CACHE_ALIAS = 'session_storage'

SEARCH_CACHE = {
    'HOST': 'redis-ingo-app.mmt.mmt:6379',
    'OPTIONS': {'DB': 5}
}

CELERY_REDIS_CONNECTION = {
    'HOST': 'redis-ingo-celery.mmt.mmt:6379',
    'OPTIONS': {'DB': 4}
}
########## END CACHE CONFIGURATION
########## MEM CACHE SETTINGS ##########
#MEMCACHE_CONFIG = ['ingoibibo-memcache.vcbrj0.cfg.aps1.cache.amazonaws.com:11211']
########## END CACHE SETTINGS ##########
########## REDIS CELERY CONFIGURATION
BROKER_URL = "redis://redis-ingo-celery.mmt.mmt:6379/4"
CELERY_RESULT_BACKEND = "redis://redis-ingo-celery.mmt.mmt:6379/4"

########## END REDIS CELERY CONFIGURATION
########## REDIS CELERY NON-TXN CONFIGURATION
NON_TXN_BROKER_URL = "redis://redis-ingo-celery.mmt.mmt:6379/4"
NON_TXN_CELERY_RESULT_BACKEND = "redis://redis-ingo-celery.mmt.mmt:6379/4"
########## END REDIS CELERY NON-TXN CONFIGURATION
CELERY_IGNORE_RESULT = True

########## REDIS CELERY POST-BOOKING CONFIGURATION
# POST_BOOKING_BROKER_URL = "redis://redis-ingo-inventory.mmt.mmt:6379/0"
# POST_BOOKING_CELERY_RESULT_BACKEND = "redis://redis-ingo-inventory.mmt.mmt:6379/0"
POST_BOOKING_BROKER_URL = BROKER_URL
POST_BOOKING_CELERY_RESULT_BACKEND = CELERY_RESULT_BACKEND
########## END REDIS CELERY POST-BOOKING CONFIGURATION
INTERNAL_IPS = ('127.0.0.1',)
########## DYNAMIC REDIS CACHE CONFIGURATION
DYNAMIC_REDIS_BROKER_URL = 'redis-ingo-inventory.mmt.mmt:6379'

# A sample logging configuration. The only tangible logging
# performed by this configuration is to send an email to
# the site admins on every HTTP 500 error when DEBUG=False.
# See http://docs.djangoproject.com/en/dev/topics/logging for
# more details on how to customize your logging configuration.
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'travelFormatter': {
            'format': '%(asctime)s\t%(levelname)s\t%(name)s\t%(message)s'
        },
        's3_inventory_formatter': {
            'format': '%(asctime)s %(levelname)s %(name)s %(message)s     ' + log_host
        },
        's3_api_formatter': {
            'format': '%(asctime)s %(levelname)s %(name)s %(message)s     ' + log_host
        },
        's3_extranet_formatter': {
            'format': '%(asctime)s %(levelname)s %(name)s %(message)s     ' + log_host
        },
        's3_stats_formatter': {
            'format': '%(asctime)s %(levelname)s %(name)s %(message)s     ' + log_host
        },
        's3_activity_formatter': {
            'format': '%(asctime)s %(levelname)s %(name)s %(message)s     ' + log_host
        },
        's3_audit_raw_formatter': {
            'format': '%(message)s'
        },
        'changeHistoryFormatter': {
             'format': '%(message)s'
        }
    },
    'filters': {
        'require_debug_false': {
            '()': 'django.utils.log.RequireDebugFalse'
        }
    },
    'handlers': {
        'mail_admins': {
            'level': 'ERROR',
            'filters': ['require_debug_false'],
            'class': 'django.utils.log.AdminEmailHandler'
        },
        'inventoryLoggerHandler': {
            'level': 'INFO',
            'maxBytes': 1048576000,
            'class': 'logging.handlers.RotatingFileHandler',
            'formatter': 's3_inventory_formatter',
            'filename': '/opt/logs/goibiboinventory.log'
        },
        'inventoryAPILoggerHandler': {
            'level': 'INFO',
            'maxBytes': 1048576000,
            'class': 'logging.handlers.RotatingFileHandler',
            'formatter': 's3_api_formatter',
            'filename': '/opt/logs/goibiboinventory_api.log'
        },
        'inventoryStatsLoggerHandler': {
            'level': 'INFO',
            'maxBytes': 1048576000,
            'class': 'logging.handlers.RotatingFileHandler',
            'formatter': 's3_stats_formatter',
            'filename': '/opt/logs/goibiboinventory_stats.log'
        },
        'extranetLoggerHandler': {
            'level': 'INFO',
            'maxBytes': 1048576000,
            'class': 'logging.handlers.RotatingFileHandler',
            'formatter': 's3_extranet_formatter',
            'filename': '/opt/logs/goibiboinventory_extranet.log'
        },
        'inventoryActivityLoggerHandler': {
            'level': 'INFO',
            'maxBytes': 1048576000,
            'class': 'logging.handlers.RotatingFileHandler',
            'formatter': 's3_activity_formatter',
            'filename': '/opt/logs/goibiboinventory_activity.log'
        },
        'changeHistoryLoggerHandler': {
          'level': 'INFO',
          'maxBytes': 1048576000,
          'class': 'logging.handlers.RotatingFileHandler',
          'formatter': 'changeHistoryFormatter',
          'filename': '/opt/logs/goibiboinventory_change_history.json'
        },
        'kafkaLoggerHandler': {
            'level': 'INFO',
            'maxBytes': 1048576000,
            'class': 'logging.handlers.RotatingFileHandler',
            'formatter': 'travelFormatter',
            'filename': '/opt/logs/goibiboinventory_kafka.log'
        },
        'auditRawLoggerHandler': {
            'level': 'INFO',
            'maxBytes': 1048576000,
            'class': 'logging.handlers.RotatingFileHandler',
            'formatter': 's3_audit_raw_formatter',
            'filename': '/opt/logs/goibiboinventory_audit_raw.json'
        }
    },
    'loggers': {
        'django.request': {
            'handlers': ['mail_admins'],
            'level': 'ERROR',
            'propagate': True,
        },
        'inventoryLogger': {
            'handlers':['inventoryLoggerHandler'],
            'level':'DEBUG',
            'propagate':False,
        },
        'inventoryAPILogger':{
            'handlers':['inventoryAPILoggerHandler'],
            'level':'INFO',
            'propagate':False,
        },
        'inventoryStatsLogger':{
            'handlers':['inventoryStatsLoggerHandler'],
            'level':'INFO',
            'propagate':False,
        },
        'extranetLogger':{
            'handlers':['extranetLoggerHandler'],
            'level':'INFO',
            'propagate':False,
        },
        'inventoryActivityLogger': {
            'handlers': ['inventoryActivityLoggerHandler'],
            'level': 'INFO',
            'propagate': False,
        },
        'changeHistoryLogger': {
          'handlers': ['changeHistoryLoggerHandler'],
          'level': 'INFO',
          'propagate': False,
        },
        'auditRawLogger': {
            'handlers': ['auditRawLoggerHandler'],
            'level': 'INFO',
            'propogate': False
        },
        'kafka':{
            'handlers':['kafkaLoggerHandler'],
            'level':'ERROR',
            'propagate':False,
        }
    }
}

######### ADDITIONAL CONFIGURATION
EMAIL_SUBJECT_PREFIX = '[Ingoibibo]'
######### END ADDITIONAL CONFIGURATION

######### HOTELSTORE CONFIGURATION
HOTELSTORE_FLAG = True
HOTELSTORE = {
    'HOST': 'redis-ingo-app.mmt.mmt:6379',
    'OPTIONS': {'DB': 9}
}
HOTELSTORE_SERVER_TEMP = {
    'HOST': '***********:6379',
    'OPTIONS': {'DB': 7}
}
######### END HOTELSTORE CONFIGURATION

######### CORPORATE REDIS CONFIGURATION
CORPORATE_BOOKING = {
    'HOST': 'redis-ingo-app.mmt.mmt:6379',
    'OPTIONS': {'DB': 12}
}
######### END CORPORATE REDIS CONFIGURATION

ACCOUNT_ACTIVATION_DAYS = 7
LOGIN_REDIRECT_URL = '/'

VOYAGER_URL = "http://voyager.goibibo.com/api/v1/"
VOYAGER_INTL_URL = VOYAGER_URL + "vendor_hotels/"
GOATXSITE = 525128
#PREMIUM_CUSTOMER_THRESHOLD = 30
PREMIUM_CUSTOMER_THRESHOLD = 50
GOIBIBO_URL = "http://www.goibibo.com/"
GOIBIBO_SALT = "ingPush@@321"

CONTRACT_MANAGER_EDIT_RIGHTS_USER = ['ujjwal', 'jagminderps','mohit.bharti']

#CONTRACT_MANAGER_EDIT_RIGHTS_USER =  ['snigdha.dixit', 'shilpi.goyal', 'shilpagr', 'vidhyaangel', 'kaptansingh', 'nehatodankar', 'renu.george', 'karishma.vaishnav', 'pankajyaduwanshi','bhagyashree', 'shivani','ujjwal', 'jagminderps', 'mohit.bharti']

REFUND_TO_CARD = False

#PAYMENT_ALERTS_EMAILIDS = ['<EMAIL>', '<EMAIL>']
#PAYMENT_ALERTS_EMAILIDS = ['<EMAIL>']
#HBASE = {'ip':'************','port':20550}

#CHOUCHOU_IP_ADDRESS = 'http://baas.goibibo.com'
CHOUCHOU_IP_ADDRESS = 'http://hotels-community-platform.ecs.mmt'
GOIBIBO_RATING_URL = 'https://www.goibibo.com'

HOTELSTORE_CREDENTIALS = {"url":"http://cyclone.goibibo.com/rest/push_to_hotelstore/", "userid": '"123"', "application": "goIbiboHotel", "hash": '"2837423032023"'}


CELERYD_PREFETCH_MULTIPLIER=1
CELERY_ACKS_LATE=True
BROKER_TRANSPORT_OPTIONS = {'fanout_prefix': True, 'socket_timeout': 15}

####COMMISSION DECREASE ACCESS###
# mention username in list below
#SELL_COMMISSION_USERNAME = ['Shivani', 'bhagyashree', 't.kingsle', 'siddhant.arora']

#FEEDBACK_EMAILIDS = ['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>']

CASSANDRA_SEARCH = False
CASSANDRA_IN_QUERY_RANGE = 1000


HOTEL_CREDIT_ALERT = ['<EMAIL>','<EMAIL>', '<EMAIL>', '<EMAIL>']

GOCASH_SERVER_TYPE='ingoibibo'
LOCALITY_FLAG= False

POS_FLAG=True
GOIBIBO_FLAG=True

ADJUST_PAYATHOTEL_BOOKING=True

#PIGEON = { "url": "https://pigeon.goibibo.com/", "token": "" }
MAILER_API = 'PIGEON'
SMS_API = 'PIGEON'

VENDOR_DONT_SELLS_PAY_AT_HOTEL = ['redbus', 'tboholidays', 'seatseller']

non_txn_celery_app = Celery('goibibo_inventory', broker=NON_TXN_BROKER_URL)
app = Celery('goibibo_inventory', broker=BROKER_URL)
post_booking_celery_app = Celery('post_booking_goibibo_inventory', broker=POST_BOOKING_BROKER_URL)

#app.conf.update(
#    CELERY_RESULT_BACKEND=CELERY_RESULT_BACKEND,
#    CELERY_IMPORTS=CELERY_IMPORTS,
#    CELERY_ROUTES=CELERY_ROUTES,
#    BROKER_TRANSPORT_OPTIONS=BROKER_TRANSPORT_OPTIONS
#)
app.conf.update(
    CELERY_RESULT_BACKEND=CELERY_RESULT_BACKEND,
    CELERY_IMPORTS=CELERY_IMPORTS,
    CELERY_ROUTES=CELERY_ROUTES,
    BROKER_TRANSPORT_OPTIONS=BROKER_TRANSPORT_OPTIONS,
    CELERY_ENABLE_UTC=False,
    CELERY_TIMEZONE="Asia/Calcutta",
    CELERY_IGNORE_RESULT=CELERY_IGNORE_RESULT,
    CELERY_RESULT_EXPIRES = 60
)


non_txn_celery_app.conf.update(
    CELERY_RESULT_BACKEND=NON_TXN_CELERY_RESULT_BACKEND,
    CELERY_IMPORTS=CELERY_IMPORTS,
    BROKER_TRANSPORT_OPTIONS=BROKER_TRANSPORT_OPTIONS,
    CELERY_ROUTES=CELERY_ROUTES,
    CELERY_ENABLE_UTC=False,
    CELERY_TIMEZONE="Asia/Calcutta",
    CELERY_IGNORE_RESULT=CELERY_IGNORE_RESULT,
    CELERY_RESULT_EXPIRES = 60
)
post_booking_celery_app.conf.update(
    CELERY_RESULT_BACKEND=POST_BOOKING_CELERY_RESULT_BACKEND,
    CELERY_IMPORTS=CELERY_IMPORTS,
    CELERY_ROUTES=CELERY_ROUTES,
    BROKER_TRANSPORT_OPTIONS=BROKER_TRANSPORT_OPTIONS,
    CELERY_ENABLE_UTC=False,
    CELERY_TIMEZONE="Asia/Calcutta",
    CELERY_IGNORE_RESULT=CELERY_IGNORE_RESULT,
    CELERY_RESULT_EXPIRES = 60
)

#INGOIBIBO_RANK_REPORT += CATEGORY_TEAM_EMAIL
CSRF_COOKIE_HTTPONLY=True
ADJUSTMENT_PAY_FLAG = True

KAFKA_SERVER_CONF = {
    'servers': {
        'default': {
           'HOST':  ['kafka01.prod.goibibo.com:9092', 'kafka02.prod.goibibo.com:9092', 'kafka03.prod.goibibo.com:9092']
        },
        'mmt_accessible': {
           'HOST': ['kafka04.prod.goibibo.com:9092']
        },
        'mmt': {
            'HOST': ['kafka-ingo.mmt.mmt:9092']
        }
    }
}

KAFKA_TOPIC_MAP = {
    'SEARCH_RESULT': {
        "DISTRIBUTION": "srp_production_distribution",
        "PROCESSING": "srp_production_processing"
    }

}

NSQ_SERVER_CONF = {
    'servers': {
        'nsqd': {
            'HOST': '127.0.0.1'
        },
        'nsqlookupd': {
            'HOST': ['ingoibiboapi01.prod.goibibo.com:4161', 'ingoibiboapi02.prod.goibibo.com:4161', 'ingoibiboapi03.prod.goibibo.com:4161']
        }
    },
    'topics': {
        'city_search_api': 'ingoibibo_city_search_api'
    },
    'max_in_flight': 6
}
VCC_PAYMENT_URL = "https://www.goibibo.com"
VCC_V2_PAYMENT_URL = "https://pay.goibibo.com"
VCC_PAYMENT_SALT = '24280f5fd0'
INGOIBIBO_BASE_URL = 'in.goibibo.com'
ALL_CITIES_DISTRIBUTE = True
ALL_USERNAME_DISTRIBUTE = True
PAYOUT_CELERY_CODE = False

SECRETDEAL_CACHE = {
    'HOST': REDIS_HOST,
    'OPTIONS': {'DB': 2}
}
RECONFIRM_ASSIGNED_ACCESS = ['AditiD', 'pradeepbalayan', 'bhagawan.swaroop', 'Amandeep.KaurCD', 'devendra.tiwari', 'pranjal.mishra', 'IB1417_SaurabhHTL', 'saurabh.bhagat', 'pradeepbalyan' ,'manisha.thukral', 'vikram.mehta', 'Saurabh.Arora', 'rahul.dhawan', 'Mayank.Lawania', 'Leena.Jethwani', 'manish.chauhan', 'pankaj.singh', "pradeepbalayan", "pranjal.mishra", "rahul.dhawan", "saurabh.arora", "saurabh.bhagat", "vikram.mehta"]

#CELERYD_LOG_FORMAT = "%(asctime)s\t%(levelname)s\t%(processName)s\t%(message)s"
#CELERYD_TASK_LOG_FORMAT = "%(asctime)s\t%(levelname)s\t%(processName)s\t%(task_id)s\t%(task_name)s\t%(message)s"
CELERYD_LOG_FORMAT = "%(asctime)s\t%(levelname)s\t%(processName)s\t%(message)s"
CELERYD_TASK_LOG_FORMAT = "%(asctime)s\t%(levelname)s\t%(processName)s\t[%(task_id)s\t%(task_name)s]\t%(message)s"

BOOKING_VENDOR_ID_MMT = 24

MMT_CREDIT_CARD_SERVICE_CERTIFICATE_PATH = '/etc/pki/ca-trust/source/anchors/star.mmt.go.crt'

MMT_PAYMENT_SERVICE = {
    "URL"           : "https://payments.mmt.go/payments-webservices-5-2/rest/paymentService/getCardDetails",
    "CONTENT-TYPE"  : "application/json",
    "USER"          : "Hotel",
    "PASSWORD"      : "" ,
    "HEADER-KEY"    : "HOTELS_CUC" ,
    "FORWARDED-FOR" : "10.30.1.118"
}
MMT_IMAGE_API="http://internal-ImagePort-Ingo-1504913348.ap-south-1.elb.amazonaws.com:8080/image-evaluation-engine/compareForSimilarity"
RECONFIRM_ASSIGNED_ACCESS = ["AditiD", "Amandeep.KaurCD", "bhagawan.swaroop", "devendra.tiwari", "IB1417_SaurabhHTL", "leena.jethwani", "manish.chauhan", "manisha.thukral", "mayank.lawania", "pankaj.singh", "pradeepbalayan", "pranjal.mishra", "rahul.dhawan", "saurabh.arora", "saurabh.bhagat", "vikram.mehta"]


URL_FOR_GETTING_REVIEW_SCORE_FROM_VOAYGER = "https://voyager.goibibo.com/api/v1/hotels/get_hotels_data/?params={ \"id_list\":[\"%s\"],\"id_type\":\"_id\",\"fields\":[\"extra.gir_data\"]}"

CORPORATE_GET_HOTEL_MAPPING_URL = "http://corp-bs.mmt.go:8080/cbs/apis/v1/org/list/"
CORPORATE_POST_RATEPLAN_MAPPING_URL = "http://corp-bs.mmt.go:8080/cbs/apis/v1/ratesmapping/refresh"
CORPORATE_CLIENT_ID = "INGO"

INGO_VOY_COMMUNICATION_SETTINGS = {
     "bucket_name": "ingovoyager-communication",
     "kinesis_stream": {
           "data": "Ingo-Voyager_communications",
           "ack": "Ingo-Voyager_ACK"
     },
     "region_name": "ap-south-1"
 }

FETCH_RANK_API = 'http://internal-flywheel-internal-elb-824728425.ap-south-1.elb.amazonaws.com/augur/api/rank'

AP_TASK_QUEUE_CONFIG = {
    'KEY'       : 'ap_tasks',
    'BATCH_SIZE': 1000,
    'HOST'      : 'redis-ingo-app.mmt.mmt',
    'PORT'      : 6379,
    'DB'        : 11,
    'KAFKA_TIMEOUT_SECONDS': 10,
    'KAFKA_TOPIC_FULLDAY_CACHE_EVENT': 'cache_events',
    'KAFKA_TOPIC_DAYUSE_CACHE_EVENT': 'dayuse_cache_events',
    'KAFKA_TOPIC_SOLDOUT_CACHE_EVENT': 'cache_events_soldout'
}

# This config is for the task which basically captures data from redis and pushes it to Kafka consumed by GDS
ARI_TO_GDS_UPDATE_TASK_REDIS_CONFIG = {
    'PRICE_CHANGE_NORMAL_KEY':'ap_tasks_pn',
    'PRICE_CHANGE_PRIORITY_KEY':'ap_tasks_pp',
    'SOLDOUT_NORMAL_KEY':'ap_tasks_sn',
    'SOLDOUT_PRIORITY_KEY':'ap_tasks_sp',
    'HOST': 'redis-ingo-app.mmt.mmt',
    'PORT': 6379,
    'DB': 11
}

ARI_TO_GDS_UPDATE_TASK_QUEUE_CONFIG = {
    'BATCH_SIZE': 1000,
    'KAFKA_TIMEOUT_SECONDS': 10,
    'KAFKA_TOPIC_PRICE_CHANGE_CACHE_EVENT_NORMAL': 'normal_cache_raw_events',
    'KAFKA_TOPIC_SOLDOUT_CACHE_EVENT_NORMAL': 'normal_cache_raw_events_soldout',
    'KAFKA_TOPIC_PRICE_CHANGE_CACHE_EVENT_PRIORITY': 'priority_cache_raw_events',
    'KAFKA_TOPIC_SOLDOUT_CACHE_EVENT_PRIORITY': 'priority_cache_raw_events_soldout'
}

WRITE_CHANGE_HISTORY_TO_DB = False
MMT_NAV_COMMON_URL = "http://mojobusinessrestapi.mmt.mmt/v1/common"
MOJO_CONSOLIDATED_VENDOR_V3_URL = "http://core-mdm-resource.ecs.mmt/mdm/v1/vendor"

### Third Party Vendors Working as InGoibibo support Team
OFFICIAL_EMAIL_DOMAINS += ["radicalminds.net", "radmintec.com", "ienergizer.in", "isonbpo.com", "igt.in", "bis.co.in", "partners.goibibo.com"]

SHERLOCK_BASE_URL = "http://datain.goibibo.com/"
ANALYTICS_API_BASE_URL = SHERLOCK_BASE_URL
HSA_API_BASE_URL = "http://ingo-hotel-supply-analytics-api.ecs.mmt/"

ANALYTICS_DYNAMIC_API_BASE_URL = "http://ingo-hotel-supply-analytics-api.ecs.mmt/"

ANALYTICS_DOCUMENT_API_BASE_URL = "http://ingo-hotel-supply-analytics-api.ecs.mmt"

ONELOGIN_LOGIN_URL = "https://sso.go-mmt.com"
### ONELOGIN Settings Start ###
ONELOGIN_CONNECTOR_ID = 2278644
ONELOGIN_ISSUER_URL = "https://app.onelogin.com/saml/metadata/975530a9-cbb7-4cb7-b6a1-e0af63ba940b"
ONELOGIN_X509_CERT = "MIIEvTCCA6WgAwIBAgIUdy+Y6QGLf8Azr8QOwFN+LMqyp04wDQYJKoZIhvcNAQELBQAwgY0xCzAJBgNVBAYTAklOMRAwDgYDVQQIDAdIYXJ5YW5hMRAwDgYDVQQHDAdHdXJnYW9uMSEwHwYDVQQKDBhNYWtlbXl0cmlwIEluZGlhIFB2dCBMdGQxFTATBgNVBAsMDE9uZUxvZ2luIElkUDEgMB4GA1UEAwwXT25lTG9naW4gQWNjb3VudCAxMTQ5MDUwHhcNMjIwODIzMDQ0OTAzWhcNMjcwODIzMDQ0OTAzWjCBjTELMAkGA1UEBhMCSU4xEDAOBgNVBAgMB0hhcnlhbmExEDAOBgNVBAcMB0d1cmdhb24xITAfBgNVBAoMGE1ha2VteXRyaXAgSW5kaWEgUHZ0IEx0ZDEVMBMGA1UECwwMT25lTG9naW4gSWRQMSAwHgYDVQQDDBdPbmVMb2dpbiBBY2NvdW50IDExNDkwNTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALn78wnmYxgdS3rJ3WusseE0x+ou/JEnuT6EP48gV4TmseIan/08z6KHskk2UzqEyiTU74tCVXLpEPaRJHdWZbOUfYo/el54Pp7XyoX2wywZeWJfJOgb4OqcU7PKRoQkGUbpjo1iAI8nAPM6btblup7lL8XPDCd75/zwUKwEo2KTQO1PwJ+E+uPoX0jM7Atj0WwYCFe6bw63y/JZHmv7m1IbiSQMRL7tp4QjvdSeKwdCakdDVbTsSFzGBg07xfGCK8vCL0hJ6XDJXGCZGDwddDKqG+nJ4QfR6litLq1Jbu+32DsEi0EbtDsg6cX3h0VxMemH6Zuxah5evcoNig6dOakCAwEAAaOCAREwggENMAwGA1UdEwEB/wQCMAAwHQYDVR0OBBYEFOY+lcpWhQtMi0MyqIb57XRW6V8+MIHNBgNVHSMEgcUwgcKAFOY+lcpWhQtMi0MyqIb57XRW6V8+oYGTpIGQMIGNMQswCQYDVQQGEwJJTjEQMA4GA1UECAwHSGFyeWFuYTEQMA4GA1UEBwwHR3VyZ2FvbjEhMB8GA1UECgwYTWFrZW15dHJpcCBJbmRpYSBQdnQgTHRkMRUwEwYDVQQLDAxPbmVMb2dpbiBJZFAxIDAeBgNVBAMMF09uZUxvZ2luIEFjY291bnQgMTE0OTA1ghR3L5jpAYt/wDOvxA7AU34syrKnTjAOBgNVHQ8BAf8EBAMCB4AwDQYJKoZIhvcNAQELBQADggEBAK0pLd1jjJUiSvWYs3HEgzTOGGZuZXSLDtgyNfRgRKHQeYkNOEl1FYlBTfHjMzgvbmjkbsis9tD9LbvBluATQaGPp0dP60SLj1mbtJ1w9LrjE23a3HxruCUCZ6NiocxNh+4DEDtWngiTB2vl0hv/cxv0fXXqyE7m/CYQ55TNh95oWiL+8GCubwefSkVefLwPSmNSnGgdfp8u4X1t78fjJMFxU2vMAy4OTAaIJPxtHbPMUWyZK+RW1xfMZYMdVIm+6R+3WgETWjGtP7r09LpOcWMOoUH+wyZJByUSpDuEHv0EZJada301R1l38DUhSinNUwSuqUFJpWiLFLApsZnAqBI="
SP_METADATA_URL = "https://in.goibibo.com/saml/metadata/" # from saml_service_provider.urls
SP_LOGIN_URL = "https://in.goibibo.com/saml/initiate-login/" # from saml_service_provider.urls
SP_LOGOUT_URL = "https://in.goibibo.com/logout/"
SAML_CREATE_USER = False
### ONELOGIN Settings End ###

MAX_HOTEL_PER_CELERY = 500
ENABLE_CELERY_QUEUE_LIMIT = 30
MID_OFFICE_FRN_BOOKING_URL = "http://mojobusinessrestapi.mmt.mmt/v1/hotel/convertToPLBBooking"
MID_OFFICE_ADT_ENTRY_URL = "http://core-finance-businessrestapps.ecs.mmt/v1/mofinance/svp/nav/apis/hotelPLBData"
MID_OFFICE_ADT_ENTRY_STATUS_URL = "http://core-finance-businessrestapps.ecs.mmt/v1/mofinance/svp/nav/apis/getAdjustmentDetails"
INTERNATIONAL_PLB_FLAG = True

# Possible values of PRICING_SERVICE =  SEARCH_API/GRPC, AVAILABILITY_SERVICE = GOKU_API/GRPC
PRICING_SERVICE = "SEARCH_API"
AVAILABILITY_SERVICE = "GOKU_API"
INGO_SEARCH_TRANS_API = "http://ingo-goku-trans.ecs.mmt/api/specific_search_api"
INGO_CHECK_AVAILABILITY_TRANS_API = "http://ingo-goku-trans.ecs.mmt/api/check-availability"
INGO_QC_SPECIFIC_SEARCH_API = "http://ingo-goku-gi.ecs.mmt/api/specific_search_api"
INGO_QC_CITY_SEARCH_API = "http://ingo-goku-gi.ecs.mmt/api/city_search_api"


from celery.signals import before_task_publish
@before_task_publish.connect
def before_task_publish_signal_patch(*args, **kwargs):
    try:
        from celery._state import get_current_worker_task
        from lib.helpers import audit_redis_connection
        from lib.request_middleware import get_audit_user
        task = get_current_worker_task()
        task_id = ''
        if kwargs:
            task_id = kwargs['body']['id'] if kwargs.get('body', None) else task_id
        audit_data = None
        if task:
            audit_data = audit_redis_connection.get(task.request.id)
        if not audit_data:
            # this is to fetch user from kwargs of a called method
            audit_data = fetch_audit_user(kwargs)
        if not audit_data:
            # this is to assign a default cron user for cron being called
            audit_data = get_audit_user()
        audit_redis_connection.set(task_id, audit_data, timeout=CELERY_DEFAULT_TIMEOUT)
    except:
        pass


# Kafka sandesh pipeline
SANDESH_KAFKA_SERVER_CONF = {
    'servers': {
        'generic_contact_detail_data_push': {
            'HOST': ['kafka03.prod.goibibo.com:9092'],
            'TOPIC': 'generic_contact_detail_static_data',
            'GROUP': 'ingo_contact'
        }
    }
}

DYNAMIC_HUB_KAFKA_SERVER_CONF = {
    'servers': {
        'change_request_ticket_creation_push': {
            'HOST': ['kafka-ingo.mmt.mmt:9092'],
            'TOPIC': 'ingo_changerequest_ticket_creation',
            'GROUP': 'ingo_dynamic_hub'
        },

        'change_request_ticket_updation_push': {
            'HOST': ['kafka-ingo.mmt.mmt:9092'],
            'TOPIC': 'ingo_changerequest_ticket_updation',
            'GROUP': 'ingo_dynamic_hub'
        },
    }
}


# Kafka content pipeline and state machine configurations
STATIC_CONTENT_PIPELINE_KAFKA_SERVER_CONF = {
    'servers': {
        'hotel_static_data_push': {
            'HOST': ['kafka04.prod.goibibo.com:9092'],
            'TOPIC': 'ingo_hotel_static_data',
            'GROUP': 'ingo_content'
        },
        'hotel_static_analytics_data_push': {
            'HOST': ['kafka-ingo.mmt.mmt:9092'],
            'TOPIC': 'ingo_hotel_static_analytics_data',
            'GROUP': 'ingo_content'
        },
        'hotel_static_data_ack': {
            'HOST': ['kafka04.prod.goibibo.com:9092'],
            'TOPIC': 'ingo_hotel_static_data_ack',
            'GROUP': 'ingo_content'
        },
        'state_machine_data_publish': {
            'HOST': ['kafka01.prod.goibibo.com:9092'],
            'TOPIC': 'ingo_hotel_state_machine_data'
        },
        'hotel_static_lingo_data_push': {
            'HOST': ['kafka04.prod.goibibo.com:9092'],
            'TOPIC': 'ingo_hotel_data_arabic',
            'GROUP': 'ingo_content'
        },
        'hotel_static_lingo_data_ack': {
            'HOST': ['kafka04.prod.goibibo.com:9092'],
            'TOPIC': 'ingo_hotel_data_arabic_ack',
            'GROUP': 'ingo_content'
        }
    }
}

PICASSO_IMAGE_PACKET_PIPELINE_KAFKA_SERVER_CONF={
        'picasso_image_packet_server': {
            'HOST': ['kafka-ingo.mmt.mmt:9092'],
            'TOPIC': 'ingo_picasso_image_async_processing',
            'GROUP': 'picasso_image_processing_group'
        }
}

PICASSO_BULK_IMAGE_PACKET_PIPELINE_KAFKA_SERVER_CONF = {
    'picasso_image_bulk_upload': {
        'HOST': ['kafka-ingo.mmt.mmt:9092'],
        'TOPIC': 'PicassoBulkMediaUpload',
    }
}

AEROSPIKE_CONFIG = {
    'namespace': 'ingommt',
    'config': {
        'hosts': [('ingo-aero-1.prod.goibibo.com', 3000)]
    }
}

CAL_SYNC_CONFIG = {
    'cal_sync_set': 'rm_cal_dates'
}

POLYGLOT_KAFKA_SERVER_CONF = {
    'servers': {
        'polyglot_data_push': {
            'HOST': ["kafka-hotels-c2.mmt.mmt:9092"],
            'TOPIC': 'polyglot_cms_request',
            'GROUP': 'ingo_polyglot'
        },
        'polyglot_data_ack': {
            'HOST': ["kafka-hotels-c2.mmt.mmt:9092"],
            'TOPIC': 'polyglot_ingo_cms_response',
            'GROUP': 'ingo_polyglot'
        }
    }
}
INGO_SEARCH_MULTILINGUAL_KAFKA_SERVER_CONF = {
    'servers': {
        'ingo_search_multilingual': {
            'HOST': ["kafka-ingo.mmt.mmt:9092"],
            'TOPIC': 'ingo_search_multilingual_events',
            'GROUP': 'ingo_search_multilingual_events'
        }
    }        
}

DATA_SCIENCE_KAFKA_SERVER_CONFIG = {
    'servers': {
        'icrunch_hotel_demand': {
            'HOST': ['kafka07.prod.goibibo.com:9092',
                     'kafka08.prod.goibibo.com:9092',
                     'kafka09.prod.goibibo.com:9092'],
            'TOPIC': 'icrunch_hotel_raw_data',
            'GROUP': 'ingo_hotel_demand'
        },
    }
}

MODERATION_PANEL_KAFKA_SERVER_CONF = {
    'servers': {
        'moderation_panel_input': {
            'HOST': ['kafka04.prod.goibibo.com:9092'],
            'TOPIC': 'ingo_moderation_panel_input',
            'GROUP': 'ingo_moderation_panel'
        },
        'moderation_panel_output': {
            'HOST': ['kafka04.prod.goibibo.com:9092'],
            'TOPIC': 'ingo_moderation_panel_output',
            'GROUP': 'ingo_moderation_panel'
        }
    }
}

IMAGE_AUTO_MODERATION_KAFKA_SERVER_CONF = {
    'servers': {
        'push_image_for_auto_moderation': {
            'HOST': ['kafka-ingo.mmt.mmt:9092'],
            'TOPIC': 'push_image_for_auto_moderation',
            'GROUP': 'ingo_content'
        },
        'pull_image_from_auto_moderation': {
            'HOST': ['kafka-ingo.mmt.mmt:9092'],
            'TOPIC': 'pull_image_from_auto_moderation',
            'GROUP': 'ingo_content'
        }
    }
}

AUDIT_LOG_API_URL = 'https://datain.goibibo.com/api/v1/audit/history/'

# Goibibo GIA config

GIA_KAFKA_SERVER_CONF = {
    'servers': {
        'default': {
            'HOST': ['kafka01.prod.goibibo.com:9092'],
            'TOPIC': 'gia_user_messages',
            'GROUP': 'ingo_gia'
        },
    }
}

GUEST_CHAT_KAFKA_SERVER_CONF = {
    "servers": {
        "default": {
            "HOST": ["kafka01.prod.goibibo.com:9092"],
            "TOPIC": "ingo_guest_chat_user_messages",
            "GROUP": "ingo_guest_chat"
        }
    }
}

RTB_EVENT_KAFKA_SERVER_CONF = {
    "TOPIC": "ingo_rtb_event_push",
    "GROUP": "ingo_rtb_event_push"
}

ANALYTICS_API_URL = "https://inanalytics.goibibo.com"

ETL_API = {
    'default': {
        'HOST': 'http://ingoetl.mmt.go',
        'URL': '/etl'
    },
    'create': {
        'HOST': 'http://htl-elixir.mmt.go',
        'URL': '/elixir'
    }
}

MMT_BKG_STAT_UPD_URL = 'http://hotelapi-booking.mmt.go/HotelsSOA/hotels/manageBookings/v1.0/updateBookingStatus'

INSIGHTS_KAFKA_SERVER_CONF = {
    'servers': {
        'default': {
            'HOST': ['*************:9092'],
            'TOPIC': '55',
            'GROUP_ID': 'giRead',
            'TEMPLATE_ID': '10091',
            'SCHEMA_URL': 'http://*************:8080/mdm/schema-repo/{topic_id}/id/{template_id}'
        }
    }
}

DEBEZIUM_KAFKA_SERVER_CONF = {
     'servers': {
         'default': {
             'HOST': ['kafka01.prod.goibibo.com:9092', 'kafka02.prod.goibibo.com:9092', 'kafka03.prod.goibibo.com:9092'],
             'CONSUMER_GROUP': {
                 'hotel_detail': 'debezium_consumer_hotel_detail',
                 'hotel_detail_wizard': 'debezium_consumer_hotel_detail_wizard',
                 'roomdetail': 'debezium_consumer_room_detail',
                 'rateplan': 'debezium_consumer_rateplan',
                 'dayuseroominfo':'debezium_consumer_dayuse_room_info_stg',
                 'gst_rules': 'debezium_consumer_gst_rules',
                 'inventory_manager': 'debezium_consumer_inventory_manager',
                 'offer_condition': 'debezium_consumer_offer_condition',
                 'offer_value': 'debezium_consumer_offer_value',
                 'pah_blackout': 'debezium_consumer_pah_blackout',
                 'free_cancellation': 'debezium_consumer_free_cancellation',
                 'hotel_cancellationrule': 'debezium_consumer_hotel_cancellation_rule',
                 'hotel_tax': 'debezium_consumer_hotel_tax',
                 'blackout_date': 'debezium_consumer_blackout_date',
                 'hotelrates_basic': 'debezium_consumer_hotelrates_basic',
                 'hotelrates_enriched': 'debezium_consumer_hotelrates_enriched',
                 'hotelrates_extended': 'debezium_consumer_hotelrates_extended',
                 'hotelinventory_basic': 'debezium_consumer_hotelinventory_basic',
                 'hotelrates_basic_synxis': 'debezium_consumer_hotelrates_basic_synxis',
                 'hotelrates_basic_los_synxis': 'debezium_consumer_hotelrates_basic_los_synxis',
                 'hotelrates_extended_synxis': 'debezium_consumer_hotelrates_extended_synxis',
                 'hotelrates_extended_los_synxis': 'debezium_consumer_hotelrates_extended_los_synxis',
                 'hotels_restrictions': 'debezium_consumer_restrictions',
                 'hotelrates_basic_los': 'debezium_consumer_hotelrates_basic_los',
                 'hotelrates_enriched_los': 'debezium_consumer_hotelrates_enriched_los',
                 'hotelrates_extended_los': 'debezium_consumer_hotelrates_extended_los',
                 'hotels_static_copy_ari': 'debezium_consumer_hotels_static_copy_ari',
                 'hotel_nexus_partner_mapping': 'debezium_consumer_hotel_nexus_partner_mapping',
                 'room_nexus_partner_mapping': 'debezium_consumer_room_nexus_partner_mapping',
                 'rateplan_nexus_partner_mapping': 'debezium_consumer_rateplan_nexus_partner_mapping',
                 'hotelinventory_basic_los': 'debezium_consumer_hotelinventory_basic_los',
                 'hotelrates_basic_derby': 'debezium_consumer_hotelrates_basic_derby',
                 'hotelrates_basic_los_derby': 'debezium_consumer_hotelrates_basic_los_derby',
                 'hotelrates_extended_derby': 'debezium_consumer_hotelrates_extended_derby',
                 'hotelrates_extended_los_derby': 'debezium_consumer_hotelrates_extended_los_derby',
                 'hotelinventory_basic_linked_room_update':'debezium_consumer_hotelinventory_basic_linked_room_update',
                 'hotel_booking': 'debezium_consumer_hotel_booking',
                 'dayuse_inventory_basic': 'debezium_consumer_dayuse_inventory_basic',
                 'hotelrates_basic_child_age_range_derby':'debezium_consumer_hotels_hotelrates_basic_child_age_range_derby',
                 'hotelrates_basic_los_child_age_range_derby':'debezium_consumer_hotels_hotelrates_basic_los_child_age_range_derby',
                 'goku_cache_invalidate': 'debezium_consumer_goku_cache_invalidation',
                 'ari_consumer_soldout_event_normal':'ari_consumer_soldout_event_normal',
                 'ari_consumer_price_change_event_normal':'ari_consumer_price_change_event_normal',
                 'ari_consumer_soldout_event_priority':'ari_consumer_soldout_event_priority',
                 'ari_consumer_price_change_event_priority':'ari_consumer_price_change_event_priority'
             }
         }
     }
}

HOTELSTORE_KAFKA_SERVER_CONF = {
     'servers': {
         'default': {
             'HOST': ['************:9092'],
             'TOPIC': 'hotelstore'
         }
     }
}

MMT_DOWNLOAD_ADVANCE_PAYMENT = "http://hotel-admin.mmt.go/reports/rs/extranetRestService/payments/downloadPaymentAdvance?infotype=%s&searchData=%s"
MMT_FETCH_ADVANCE_PAYMENT = "http://hotel-admin.mmt.go/reports/rs/extranetRestService/payments/getAdvancePayment?searchData=%s"
MMT_BKG_VOUCHER_URL = 'http://mojobusinessrestapi.mmt.mmt/v1/hotel/previewInGoHotelVoucher'
MMT_NAV_BOOKING_URL = "http://navwebservices.mmt.com/MMT.MidOfficeServices/api/VendorService/PostAllBookings"
MMT_NAV_GET_VENDOR_URL = "http://core-finance-businessrestapps.ecs.mmt/v1/mofinance/svp/client/apis/getvendor"

IMAGE_MODERATION_BUCKET_KEY = "ingo_image_auto_moderation_communication"

INGO_IMAGE_AUTO_MODERATION_COMMUNICATION_SETTINGS = {
    'bucket_name': IMAGE_MODERATION_BUCKET_KEY,
    'kinesis_stream': {
        'push_stream_name': 'process_image_moderation',
        'pull_stream_name': 'complete_image_moderation',
        'run_count': 100,
        'call_delay': 0.5
    },
    'region_name': 'ap-south-1'
}

REDSHIFT = {
    'HOST': 'https://inredshift.goibibo.com'
}

ROOM_MATCHING_API = {
    "URL": "/mapping/RoomMappingService",
    "HOST": "http://***********:8080",
    "ACCEPTABLE_CONFIDENCE": 40
}

MMT_SOA_FETCH_BKG_URL = 'http://hotelapi-booking.mmt.go/HotelsSOA/hotels/book/v2.0/bookingDetails'
TOTEM_SERVICE_URL = "http://totem.goibibo.com/totem/engine/v1/cronus"
CLEARTAX_ENDPOINT = 'https://gst.cleartax.in/api/v0.1'


INGO_DATASCIENCE_HOST = "http://ingo-datascience-api.ecs.mmt"


MO_AUTHID = "AUTOMATION"
MO_USERID = "AUTOMATION"


MMT_COMPANY_CODE = "MMTINDIA"
GI_COMPANY_CODE = "GOIBIBO"

MO_BASE_URL = "http://core-finance-businessrestapps.ecs.mmt"

MO_API = {
    "booking_id_payment_detail": "/v1/mofinance/svp/client/apis/fetchPnrInvoiceDetail",
    "bookings_ids_payment_details": "/v1/mofinance/svp/client/apis/fetchPnrInvoiceDetailList",
    "vendor_booking_id_payment_detail": "/v1/mofinance/svp/client/apis/fetchBookingInvoiceDetail",
    "booking_id_payment_summary": "/v1/mofinance/svp/client/apis/fetchBookingSummary",
    "hotel_payment_summary": "/v1/mofinance/svp/client/apis/fetchHotelPaymentSummary",
    "vendor_payment_summary": "/v1/mofinance/svp/client/apis/fetchVendorPaymentSummary",
}

IS_MO_ENABLE = True
BOOKING_REPORT_TRIGGER = 400
IS_SANDESH_ENABLE = False
SANDESH_ENDPOINT = 'http://ingo-sandesh.ecs.mmt/'
TRANSACTIONAL_API_ENDPOINT = '/api/v1/sandesh-transaction/'

WATSON_WEBAPI_HOST_ENDPOINT = "http://watson-webapi.goibibo.com"

GOKU_CANCELLATION = {
    "POLICY": "http://ingo-goku-trans.ecs.mmt/api/cancellation/policy",
    "RULE": "http://ingo-goku-gi.ecs.mmt/api/cancellation/rule",
    "CHARGES": "http://ingo-goku-trans.ecs.mmt/api/cancellation/charges"
}

MO_HOTEL_PAYMENT_BASE_URL = "http://core-finance-businessrestapps.ecs.mmt"

MO_HOTEL_PAYMENT_MAPPING = {
    "SUMMARY": "/v1/mofinance/svp/client/apis/fetchHotelPaymentSummary",
    "DETAIL": "/v1/mofinance/svp/client/apis/paymentReferenceDetailV2",
    "DETAIL_BOTH_BANK": "/v1/mofinance/dashobard/client/apis/bothpaymentDashboardBankRefnoDetails/"
}

IS_MO_HOTEL_ENABLE = True

PUSH_TO_STATE_MACHINE_FLAG = False

NEXUS_BASE_URL = "http://ingo-nexus-partner.ecs.mmt"

CK_HOST_URL = "http://ingo-configkeeper.ecs.mmt"
CK_SERVICE = "ingo-datascience-v4"
CK_CATEGORY = "dynamic-pricing"

QUALITY_SCORE_KAFKA_SERVER_CONF = {
    'servers': {
        'default': {
            'HOST': ['kafka01.prod.goibibo.com:9092'],
            'TOPIC': 'ingoibibo.quality_score_events',
            'GROUP': 'ingo-qs'
        },
    }
}

TOTAL_QUALITY_SCORE_KAFKA_SERVER_CONF = {
    'servers': {
        'default': {
            'HOST': ['kafka01.prod.goibibo.com:9092'],
            'TOPIC': 'ingoibibo.quality_score_total_events',
            'GROUP': 'ingo-total-qs'
        },
    }
}

ANALYTICS_BOOKING_URL = 'http://datain.goibibo.com/api/v1/documents?pipeline=Transaction&' \
                        'filters={"hotelId": ["%s"], "vendorId": ["Goibibo", "MakeMyTrip"], "confirmStatus": [2, 4]}' \
                        '&group_by=["checkin"]&count=100&sort={"field": "checkin", "order": "desc"}' \
                        '&range={"field": "checkin", "start_date": "%s"}&format=json' \
                        '&fields=["bookingId","hotelId","vendorId","checkin"]'

FEATURE_FLAGS = {
    'skip_room_medias_in_hotel': True
}

# elastic configuration starts

HOTEL_STATE_MACHINE_ES_INDEX = 'statemachine'
HOTEL_STATE_MACHINE_ES_TYPE = 'data'
HOTEL_CONTENT_DATA_ES_INDEX = 'content'
HOTEL_CONTENT_DATA_ES_TYPE = 'data'
HOTEL_CONTENT_SCORE_DATA_ES_INDEX = 'contentscore'
HOTEL_CONTENT_SCORE_DATA_ES_INDEX_V2 = 'contentscore_v2'
HOTEL_CONTENT_SCORE_DATA_ES_TYPE = 'score'
HOTEL_QUALITY_SCORE_ES_INDEX = 'qsTotalScore'
HOTEL_QUALITY_SCORE_DATA_ES_TYPE = 'qs_total_score'
HOTEL_QUALITY_TRANSITION_INDEX = 'qsTransition'
HOTEL_QUALITY_TRANSITION_DATA_ES_TYPE = 'qs_transition'
HOTEL_VISITOR_FEATURE_ES_INDEX = 'featureVisitor'
HOTEL_VISITOR_FEATURE_DATA_ES_TYPE = 'feature_visitor'
DEMAND_HEAT_MAP_ES_INDEX = 'demand_heat_map'
TRANSACTION_INDEX = 'transactions'

STATIC_DATA_ELASTIC_SEARCH = {
    'host': 'vpc-m-ingo-static-es-vocnfnvhtmp2epjy3g6oytaoii.ap-south-1.es.amazonaws.com',
    'protocol': 'https',
    'port': 443
}

ES_CLIENT_STATIC = ES_CLIENT = Elasticsearch(
    [STATIC_DATA_ELASTIC_SEARCH['host']],
    http_auth=(),
    scheme=STATIC_DATA_ELASTIC_SEARCH['protocol'],
    port=STATIC_DATA_ELASTIC_SEARCH['port']
)

DYNAMIC_DATA_ELASTIC_SEARCH = {
    'host': 'vpc-m-ingo-analytics-es-f3mrnoo3zzueud3mcagvzdkjeq.ap-south-1.es.amazonaws.com',
    'protocol': 'https',
    'port': 443
}

ES_CLIENT_DYNAMIC = Elasticsearch(
    [DYNAMIC_DATA_ELASTIC_SEARCH['host']],
    http_auth=(),
    scheme=DYNAMIC_DATA_ELASTIC_SEARCH['protocol'],
    port=DYNAMIC_DATA_ELASTIC_SEARCH['port']
)

TRANSACTIONS_DATA_ELASTIC_SEARCH = {
    'host': 'vpc-m-ingo-transcation-es-lqmvcj2q5skigtrs43w5yry4aa.ap-south-1.es.amazonaws.com',
    'protocol': 'https',
    'port': 443
}

ES_CLIENT_TRANSACTIONS = Elasticsearch(
    [TRANSACTIONS_DATA_ELASTIC_SEARCH['host']],
    http_auth=(),
    scheme=TRANSACTIONS_DATA_ELASTIC_SEARCH['protocol'],
    port=TRANSACTIONS_DATA_ELASTIC_SEARCH['port']
)


ES_PRICING_INDEX = 'pricing'

# elastic configuration ends

DCH_BOOKING_LIST_URL = "https://in.goibibo.com/api/v2/booking/direct-connect-bookings"
GI_BOOKING_DETAIL_URL = "https://www.goibibo.com/hotels/searchbookingv3/?p_id={booking_id}"

# Video API
VIDEO_API_CONFIG = {
    "S3_BUCKET": VIDEOS_BUCKET_KEY,
    "S3_REGION": "ap-south-1",
    "VIDEO_API_TRACK_TYPE": 'Video',
    "VIDEO_API_TRACK_TYPE_GENERAL": 'General',
    "VIDEO_API_S3_FOLDER": 'video',
    "VIDEO_MODERATION_FLAG": True,
    "CDN_DOMAIN": "https://ingommt-videos.goibibo.com/",
    "S3_DOMAIN": "https://ingommt-videos.s3.amazonaws.com/",
    "META_CONFIG": {
        "SIZE_MIN": 4.0,  # in MB
        "SIZE_MAX": 60.0,  # in MB
        "MIN_WIDTH": 240,  # in pixel
        "MIN_HEIGHT": 240,  # in pixel
        "MAX_WIDTH": 1080,  # in pixel
        "MAX_HEIGHT": 1080,  # in pixel
        "ASPECT_RATIO": [1, 1.6, 1.8],  # in range of ~0.05
        "ASPECT_RATIO_MIN": 1.0,  # in range of ~0.05
        "ASPECT_RATIO_MAX": 2.2,  # in range of ~0.05
        "MAX_BIT_RATE": 700,  # 700K
        "MAX_FPS": 30,
        "MAX_DURATION": 120,  # in sec,
        "DELTA_ASPECT_RATIO": 0.05,
    },
    "VIDEO_SERVICE_CLIENT": "ingo",
    "VIDEO_SERVICE_SUPPORTED_FORMATS": ['mp4', 'webm', 'hls'],
    "VIDEO_SERVICE_SUPPORTED_RESOLUTIONS": ['p320', 'p480', 'p720']
}

# Express checkin Key
EXPRESS_CHECKIN_URL = 'http://express-checkin.hotelsimply.com'

IMAGE_API_CONFIG = {
    'S3_BUCKET': IBCDN_BUCKET_KEY,
    "MIN_HEIGHT": 700,  # in pixel
    "MIN_WIDTH": 800,  # in pixel
    "MAX_WIDTH": 8000,
    "MAX_HEIGHT": 8000
}
IMG_SRC_KEYS = ['oyo', 'makemytrip', 'goibibo', 'voyager', 'apiv2', 'extranet', 'spot']
HEIMDALL_HOST = "ingo-heimdall.ecs.mmt"
HEIMDALL_PORT = "8443"
MIDDLEWARE_CLASSES += ('api.v2.users.middleware.UserAuthMiddleware',)
MIDDLEWARE_CLASSES += ('api.v2.users.middleware.HardBlockUserMiddleware',)


#change history constants
CHANGE_HISTORY_ATHENA_DB = 'ingo_e'
CHANGE_HISTORY_S3_BUCKET = 'change-history-log-athena-query-result-set'
CHANGE_HISTORY_S3_BUCKET_PATH = 'result-data'
CHANGE_HISTORY_ATHENA_TABLE_TYPE = dict(modifiedon_high='change_history_modifiedon_high_athena',
                                        modifiedon_normal='change_history_modifiedon_normal_athena',
                                        staydate_high='change_history_staydate_high_athena',
                                        staydate_normal='change_history_staydate_normal_athena')
CHANGE_HISTORY_CHANGE_OBJECT_DICT = {'inventory': 'NULL', 'rates': 'NOT NULL'}
CHANGE_HISTORY_BUCKET_LIST = ['normal', 'high']
CHANGE_HISTORY_QUERY_TYPE_LIST = ['modifiedon', 'staydate']

#Athena connection and poll constants
ATHENA_AWS_REGION = 'ap-south-1'
ATHENA_POLL_STATUS_TIMEOUT = 600
ATHENA_CON_MAX_RETRIES = 3
S3_CON_MAX_RETRIES = 3

MYRA_CREATE_SESSION_URL = "http://core-myra-bot.ecs.mmt/usermessagehandler/api/v1/session"
GIA_CREATE_SESSION_URL = "http://core-gia-bot.ecs.mmt/usermessagehandler/api/v1/session"

MYRA_UPDATE_SESSION_URL = "http://core-myra-bot.ecs.mmt/usermessagehandler/api/v1/sessionUpdate"
GIA_UPDATE_SESSION_URL = "http://core-gia-bot.ecs.mmt/usermessagehandler/api/v1/sessionUpdate"

MYRA_BLOCK_USER_URL = "http://core-myra-bot.ecs.mmt/usermessagehandler/api/v1/blockUser"
GIA_BLOCK_USER_URL = "http://core-gia-bot.ecs.mmt/usermessagehandler/api/v1/blockUser"

MYRA_CHECK_USER_BLOCK_STATUS_URL = "http://core-myra-bot.ecs.mmt/usermessagehandler/api/v1/checkUserStatus"
GIA_CHECK_USER_BLOCK_STATUS_URL = "http://core-gia-bot.ecs.mmt/usermessagehandler/api/v1/checkUserStatus"

MYRA_SEND_UPDATE_URL = "http://core-myra-bot.ecs.mmt/usermessagehandler/api/v1/host/{id}/updatePermission"

# Multilingual Flag
TEMP_TASK_FLAG = False  # TEMP_TASK_FLAG, until task is available in celery
POLYGLOT_SYNC_FLAG = False # polyglot api will call if this flag is true, set it False for kafka flow
GCC_PIPELINE_SYNC = False # for testing push_to_gcc in bulk, otherwise set it False for async calling to GCC pipeline
PUSH_GCC_PIPELINE_FLAG = True # allow to stop pushing on GCC pipeline
INGO_UNIT_FLOW = False # this flag is for testing independant ingo system with lingo update

# allow new GST match logic
ALLOW_NEW_GST_MATCH = True

AEROSPIKE_PROMO_CONFIG = {
    'namespace': 'dynamic',
    'set': 'promo_cache',
    'config': {
        'hosts': [('ingo-aero-1.prod.goibibo.com', 3000)],
    }
}

NEXUS_CONNECTOR_RTB_ACTION_UPDATE = 'http://hotels-nexus-connector-supplier-book.ecs.mmt/api/webhook/booker/rtb/v1/guest-initiated'

GST_FLOW_V2 = True
CREATE_ITB_INVITE_URL = 'http://hotels-nexus-connector-supplier-book.ecs.mmt/api/webhook/orchestrator/rtb/v1/host-initiated'

NEXUS_SERVICES_KAFKA_SERVER_CONF = {
    'servers':{
        'nexus_missing_static_data': {
            'HOST': ['kafka-ingo.mmt.mmt:9092'],
            'TOPIC': 'nexus_partner_cm_content',
            'GROUP': 'nexus_missing_entity_consumer'
        },
    }
}

HOTELS_SERVICES_DEBEZIUM_TOPIC = {
    'HOST': ["kafka-ingo.mmt.mmt:9092"],
    'TOPIC': 'debezium.goibibo_inventory.hotels_services'
}

# Host App Migration Related Config
HOST_APP_MIGRATION_MONGO_CONFIG = {
    'database': 'host_app_migration_changes',
    'username': '',
    'password': '',
    'host': 'mongodb://ingoibibomongo01.prod.goibibo.com:27017/goibibo_inventory',
    'alias': 'migration'
}


# Host App Migration Related Config
APPLE_USER_DELETION = {
    'database': 'user_deletion',
    'username': '',
    'password': '',
    'host': 'mongodb://gocashmongo.pp.goibibo.dev:27017/goibibo_inventory',
    'alias': 'deletion'
}


# Ingo Hotel Partners Connection (Synxis, Derby etc.)
INGO_SOURCE_PARTNER_MONGO_CONFIG = {
    'database': 'source_partner_hotel_data',
    'username': '',
    'password': '',
    'host': 'mongodb://ingoibibomongo01.prod.goibibo.com:27017/goibibo_inventory',
    'alias': 'default'
}

INGO_ACK_MONGO_CONFIG = {
    'database': 'ack_data',
    'username': '',
    'password': '',
    'host': 'mongodb://ingoibibomongo01.prod.goibibo.com:27017/goibibo_inventory',
    'alias': 'goibibo_inventory_ack_data'
}

ACK_SERVICE_KAFKA_SERVER_CONF = {
    'servers': {
        'ack_flow_packet': {
            'HOST': ['kafka01.prod.goibibo.com:9092', 'kafka02.prod.goibibo.com:9092', 'kafka03.prod.goibibo.com:9092', 'kafka04.prod.goibibo.com:9092'],
            # 'HOST': ["*************:9092"],
            'TOPIC': 'ingo_ack_flow_data',
            'GROUP': 'ingo_ack_flow'
        }
    }
}

INGO_PARTNERS_CONFIG_MANAGER_CONFIG = {
    'cache_key': 'ingo_partners_config_keeper',
    'service': "ingoibibo",
    'category': ["config_manager"]
}

AUTO_QC_APPROVER_USERNAME = '<EMAIL>'
NANONETS_USERNAME = '<EMAIL>'


DSD_CONFIG = {
    "dsd_redis_cache_enabled" : False
}

PROMO_GENIE_CONFIG = {
    "promo_genie_enabled": True,
    "host": "ingo-promo-genie.ecs.mmt",
    "port": 8443
}

CAMPAIGN_SERVICE_CONFIG = {
    "campaign_service_enabled": True,
    "host": "ingo-hotel-supply-campaign.ecs.mmt",
    "port": 8443
}

CONTENT_SERVICE_CONFIG = {
    "is_secure_connection": True,
    "content_service_enabled": True,
    "host": "hotel-supply-content-service.ecs.mmt",
    "port": 8443
}

INCLUSION_SERVICE_CONFIG = {
    "inclusion_service_enabled": True,
    "host": "supply-inclusions.ecs.mmt",
    "port": 8443
}

# set to True when GI-MMT merger is done
GI_MMT_MERGER_FLAG = True


ALTACCO_BRAND_USER_KAFKA_SERVER_CONF = {
    'servers': {
        'mmt_user_kafka': {
            'HOST': ['kafka-common.mmt.mmt:9092'],
            'TOPIC': ['UsrSvcG_CHN', 'UsrSvcG_MUM', 'UsrSvc_DEL_MUM'],
            'GROUP': 'ingo_altacco',
            'SESSION_TIMEOUT_IN_MS': 6000,
            'POLLING_PERIOD': 0.1,
            'sasl_conf': {
                'SECURITY_PROTOCOL': 'SASL_PLAINTEXT',
                'KERBEROS_KEYTAB': '/usr/local/goibibo/source/goibibo_inventory/woof_kafka/conf/hotel_web.keytab',
                'MECHANISMS': 'GSSAPI',
                'KERBEROS_PRINCIPAL': '<EMAIL>',
                'KERBEROS_KINIT_CMD': 'kinit <EMAIL> -k -t /usr/local/goibibo/source/goibibo_inventory/woof_kafka/conf/hotel_web.keytab',
                'KERBEROS_SERVICE_NAME': 'kafka'
            }
        },
        'gi_user_kafka': {
            'HOST': [''],
            'TOPIC': '',
            'GROUP': '',
        }
    }
}

ELIXIR_KAFKA_SERVER_CONF = {
    'servers': {
        'mmt_elixir_kafka': {
            'HOST': ['kafka-hotels.mmt.mmt:9092'],
            'TOPIC': ['ingo_ingest_queue', 'onboarding_and_update_event_ingo_altacco', 'onboarding_event_ingo_hotel',
                      'update_event_ingo_hotel', 'bulk_update_event_ingo_hotel', 'bulk_event_ingo_altacco',
                      'hostapp_event_ingo', 'ingo_non_eng_ingest_queue'],
            'GROUP': 'ingo_elixir_static_data_consumer',
            'SESSION_TIMEOUT_IN_MS': 6000,
            'POLLING_PERIOD': 1,
            'sasl_conf': {
                'SECURITY_PROTOCOL': 'SASL_PLAINTEXT',
                'KERBEROS_KEYTAB': '/usr/local/goibibo/source/goibibo_inventory/woof_kafka/conf/hotel_web.keytab',
                'MECHANISMS': 'GSSAPI',
                'KERBEROS_PRINCIPAL': '<EMAIL>',
                'KERBEROS_KINIT_CMD': 'kinit <EMAIL> -k -t /usr/local/goibibo/source/goibibo_inventory/woof_kafka/conf/hotel_web.keytab',
                'KERBEROS_SERVICE_NAME': 'kafka'
            }
        }
    }
}

CSV_FILES_CONFIG = {
    "S3_BUCKET": PROD_BUCKET_KEY, 
    "S3_REGION": "ap-south-1",
    "S3_DOMAIN": "https://gos3.ibcdn.com/",
    "S3_DIR": 'csv-reports',
    "S3_EXPIRE_TIME": 60 * 5,
    "APPLY_SIGNED_URL": True
}

CORPORATE_GSTN_MOJO_KAFKA_TOPICS = {
    'servers': {
        'tax_invoice_info_push': {
            'HOST': ['kafka04.prod.goibibo.com:9092'],
            'TOPIC': 'tax_invoice_info',
        },
        'penalty_info_push': {
            'HOST':['kafka04.prod.goibibo.com:9092'],
            'TOPIC': 'corporate_booking_penalty_info',
        }
    }
}

MOJO_BOOKING_CANCELATION_SERVICE_KAFKA_SERVER_CONF = {
    'servers': {
        'booking_confirm_push': {
            'HOST': ['kafka-ingo.mmt.mmt:9092'],
            'TOPIC': 'mof_hotels_booking_confirm_push',
            'GROUP': 'mof_hotels_booking_confirm_push_group'
        },
        'booking_cancel_push': {
            'HOST': ['kafka-ingo.mmt.mmt:9092'],
            'TOPIC': 'mof_hotels_booking_cancel_push',
            'GROUP': 'mof_hotels_booking_cancel_push_group'
        }
    }
}

CREATE_PREBUY_CONFIG_RESELLER = {
    'servers': {
        'create_prebuy_config': {
            'HOST': ['kafka-htlcld.mmt.mmt:9092'],
            'TOPIC': 'HTLCLD_order_create',
            'GROUP': 'HTLCLD_order_create_group'
        }
    }
}

ONBOARDING_SERVICES_CONFIG = {
    'cache_key': 'black_only_service_templates_config',
    'service': "onboarding",
    'category': ["services"],
    'cache_ttl':   60 * 60 * 24 * 5 #5 days
}

ORCH_URL = 'http://hcs-uds.ecs.mmt/orch/unified/user/get/details'
ORCH_AUTH = 'DFjgkUbgDFCvebc'

from project_settings_configkeeper import add_or_override_project_setting_from_configkeeper,handle_ap_days_to_push_val
# NOTE: THIS FUNCTION SHOULD ALWAYS BE CALLED POST LOADING CONFIGS FROM AWS, TO AVOID OVERRIDES.
add_or_override_project_setting_from_configkeeper(env_config, CK_HOST_URL)

# NOTE: THIS FUNCTION SHOULD SET THE FINAL VALUE OF THE VARIABLE, AFTER VALUES FROM CONFIG KEEPER ARE LOADED.
env_config.AP_DAYS_TO_PUSH = handle_ap_days_to_push_val(env_config)

AIRFLOW_SERVER_CONF = {
    'server': {
        'HOST': "http://ingo-airflow-workflow.ecs.mmt/",
        'PORT': "",
    }
}

AIRFLOW_API_AUTH_KEY = ""

MY_DESK_URL = "http://oaov.mmt.mmt"
