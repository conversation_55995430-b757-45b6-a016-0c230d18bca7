import os
import json
import base64


import boto3
from botocore.exceptions import Client<PERSON><PERSON>r


def fetch_db_secrets(client, db_secrets_map):
    db_secrets = {}
    for key, value in db_secrets_map.items():
        try:
            kwargs = {'SecretId': value}
            response = client.get_secret_value(**kwargs)
            db_secrets[key] = json.loads(response['SecretString'])
        except Exception as e:
            print("Error fetching secret for key: {}, value: {}, error: {}".format(key, value, e))
            raise e
    return db_secrets

def udpate_db_credentials(secret_json, db_secrets_map):
    if not db_secrets_map:
        return secret_json
    if 'DATABASES' in secret_json:
        for key, db_config in secret_json['DATABASES'].items():
            if key not in ('inventory_stats-slave', 'slave'):
                continue
            db_config['HOST'] = 'mysql-ingo-static-proxysql.mmt.mmt'
            if os.environ.get('APP_TYPE') == 'trans-api' and os.environ.get('CURR_ENV') == "prod" and 'slave' in key:
                db_config['HOST'] = 'mysql-ingo-trans-slave.mmt.mmt'
            db_config['PORT'] = '3306'
            db_config['USER'] = db_secrets_map.get('mysql_goibibo_inventory_slave', {}).get('username', '')
            db_config['PASSWORD'] = db_secrets_map.get('mysql_goibibo_inventory_slave', {}).get('password', '')
    return secret_json

def get_secret(env, region_name='ap-south-1'):
    '''
    In this sample we only handle the specific exceptions for the 'GetSecretValue' API.
    See https://docs.aws.amazon.com/secretsmanager/latest/apireference/API_GetSecretValue.html
    :param env: dev, pp, prodpp, prod, we store secrets as plain text against secret key
    :param region_name: ap-south-1
    :return:
    '''
    secret_mapping = {
        'dev': 'dev/ingoibibo/web/secrets',
        'prodpp': '/alpha/ingo/goibibo-inventory/secrets',
        'prod': 'prod/ingoibibo/web/secrets',
        'pp': 'pp/ingoibibo/web/secrets',
        'base': 'ingoibibo/web/base',
        'qa': 'prodpp/ingoibibo/web/secrets'
    }
    prodpp_env_secret_paths = {
		"mysql_goibibo_inventory_slave":  "/alpha/ingo/mysqldb/inventory/goibibo_inventory/ingo_inventory_stg_slave/credentials",
		"mysql_goibibo_inventory_master": "/alpha/ingo/mysqldb/inventory/goibibo_inventory/ingo_inventory_stg_master/credentials",
	}
    prod_env_secret_paths = {
		"mysql_goibibo_inventory_slave":  "/prod/ingo/mysqldb/inventory/goibibo_inventory/ingo_inventory_slave/credentials"
        # uncomment below when write credentials are available
		# "mysql_goibibo_inventory_master": "/prod/ingo/mysqldb/inventory/goibibo_inventory/ingo_inventory_master/credentials",
	}
    secret = ''
    db_secrets_map = {}
    try:
        secret_name = secret_mapping.get(env, 'base')
        kwargs = {'SecretId': secret_name}
        if env == 'dev':
            AWS_ACCESS_KEY_ID = os.getenv('AWS_ACCESS_KEY_ID', None)
            AWS_SECRET_ACCESS_KEY = os.getenv('AWS_SECRET_ACCESS_KEY', None)
            AWS_SESSION_TOKEN = os.getenv('AWS_SESSION_TOKEN', None)
            client = boto3.client(service_name='secretsmanager', aws_access_key_id=AWS_ACCESS_KEY_ID,
                                  aws_secret_access_key=AWS_SECRET_ACCESS_KEY, aws_session_token=AWS_SESSION_TOKEN,
                                  region_name=region_name)
        else:
            session = boto3.session.Session()
            client = session.client(
                service_name='secretsmanager',
                region_name=region_name
            )

        get_secret_value_response = client.get_secret_value(**kwargs)
        # Decrypts secret using the associated KMS CMK.
        # Depending on whether the secret is a string or binary, one of these fields will be populated.
        if 'SecretString' in get_secret_value_response:
            secret = get_secret_value_response['SecretString']
        else:
            secret = base64.b64decode(get_secret_value_response['SecretBinary'])

        if not secret:
            raise Exception('Not able to fetch access from secret manager, please check connection')
        if env == 'prod':
            db_secrets_map = fetch_db_secrets(client, prod_env_secret_paths)
        elif env == 'prodpp':
            db_secrets_map = fetch_db_secrets(client, prodpp_env_secret_paths)

    except ClientError as e:
        if e.response['Error']['Code'] == 'DecryptionFailureException':
            # Secrets Manager can't decrypt the protected secret text using the provided KMS key.
            # Deal with the exception here, and/or rethrow at your discretion.
            raise e
        elif e.response['Error']['Code'] == 'InternalServiceErrorException':
            # An error occurred on the server side.
            # Deal with the exception here, and/or rethrow at your discretion.
            raise e
        elif e.response['Error']['Code'] == 'InvalidParameterException':
            # You provided an invalid value for a parameter.
            # Deal with the exception here, and/or rethrow at your discretion.
            raise e
        elif e.response['Error']['Code'] == 'InvalidRequestException':
            # You provided a parameter value that is not valid for the current state of the resource.
            # Deal with the exception here, and/or rethrow at your discretion.
            raise e
        elif e.response['Error']['Code'] == 'ResourceNotFoundException':
            # We can't find the resource that you asked for.
            # Deal with the exception here, and/or rethrow at your discretion.
            raise e
    secret_json = json.loads(secret)
    udpate_db_credentials(secret_json, db_secrets_map)
    return secret_json


def secret_settings(env, module, retry_count=0):
    try:
        secret_keys = get_secret(env)
        for key in secret_keys:
            try:
                config = secret_keys[key]
                setattr(module, key, config)
            except Exception as e:
                print('exception occurred while getting key {} from secret_keys for env: {} error: {}'.format(key, env, str(e)))
                raise e
        print('secrets loaded successfully')
    except Exception as e:
        print('exception occured while secret loading {} count {}'.format(str(e), str(retry_count)))
        if retry_count < 3:
            retry_count += 1
            secret_settings(env, module, retry_count)
        else:
            raise e