INGO_MODEL_TO_LINGO_RELATED_DATA_MAPPING = {
            "HotelDetail" : {
                'lingo_key': 'htl_ht_dtl', 'doc': 'hotels', 'code': '01', 'tags': ['ingo_hotel']
            },
            "HotelMetaData" : {
                'lingo_key': 'htl_ht_mdt', 'doc': 'hotels', 'code': '02', 'tags': ['ingo_hotel'],
            },
            "RoomDetail" : {
                'lingo_key': 'htl_rm_dtl', 'doc': 'hotels', 'code': '03', 'tags': ['ingo_hotel']
            },
            "RatePlan" : {
                'lingo_key': 'htl_rp_dtl', 'doc': 'hotels', 'code': '04', 'tags': ['ingo_hotel']
            },
            "Services" : {
                'lingo_key': 'htl_svc', 'doc': 'hotels', 'code': '05', 'tags': ['ingo_hotel'],
            },
            "USPDetail" : {
                'lingo_key': 'htl_usp_dtl', 'doc': 'hotels', 'code': '09', 'tags': ['ingo_hotel']
            },
            "HotelPolicyMappingV2" : {
                'lingo_key': 'htl_plc_mpv2', 'doc': 'hotels', 'code': '06', 'tags': ['ingo_hotel']
            },
            "AmenitiesV2" : {
                'lingo_key': 'cmn_amntv2', 'doc': 'templates', 'code': '54', 'tags': ['ingo_templates']
            },
            "ServicesTemplate" : {
                'lingo_key': 'htl_svc_tmpl', 'doc': 'templates', 'code': '51', 'tags': ['ingo_templates']
            },
            "ServicesCategory" : {
                'lingo_key': 'htl_svc_ctgry', 'doc': 'templates', 'code': '56', 'tags': ['ingo_templates']
            },
            "PolicyCategory" : {
                'lingo_key': 'htl_plc_ctgry', 'doc': 'templates', 'code': '52', 'tags':['ingo_templates']
            },
            "PolicyTemplateV2" : {
                'lingo_key': 'htl_plc_tmplv2', 'doc': 'templates', 'code': '53', 'tags': ['ingo_templates']
            },
            "HotelChain":{
                'lingo_key': 'htl_ht_chn', 'doc': 'glossary', 'code': '60', 'tags': ['ingo_gls']
            },
            'CommonInterests':{
                'lingo_key': 'usr_intrst', 'doc': 'glossary', 'code': '61', 'tags': ['ingo_gls']
            },
            'CommonProfession':{
                'lingo_key': 'usr_prfsn', 'doc': 'glossary', 'code': '62', 'tags': ['ingo_gls']
            },
            'CommonLanguages':{
                'lingo_key': 'usr_lang', 'doc': 'glossary', 'code': '63', 'tags': ['ingo_gls']
            },
            'User':{
                'lingo_key': 'cmn_usr', 'doc': 'users', 'code': '80', 'tags': ['ingo_usr']
            },
            'HostProfile':{
                'lingo_key': 'usr_hst_prfl', 'doc': 'users', 'code': '81', 'tags': ['ingo_usr']
            }
}

#this is replaced logicall by INGO_MODEL_TO_LINGO_MAPPING
HOTEL_MODELS_LIST = ['HotelDetail', 'RoomDetail', 'Services', 'RatePlan', 'USPDetail', 'HotelPolicyMappingV2', 'User', 'HostProfile']

HOTELS_DEFAULT_BIN = ['htl_ht_dtl', 'htl_ht_mdt', 'htl_rm_dtl', 'htl_rp_dtl', 'htl_svc', 'htl_usp_dtl', 'htl_plc_mpv2']

TEMPLATES_DEFAULT_BIN = ['type', 'type_id', 'doc_dtl']

CHOICES_DEFAULT_BIN = ['type', 'doc_dtl']

USERS_DEFAULT_BIN = ['cmn_usr', 'usr_hst_prfl']

# tns_ltr_req is user for transliteration to make sure that characters are in ascii
LINGO_FIELDS_TO_MODEL_FIELD_MAPPING_PER_MODEL = {
            "HotelDetail": {
                "hotelname": {'field': "hotelname", 'tns_ltr_req': True, 'moderation_required': True},
                "displayname": {'field': "displayname", 'tns_ltr_req': True, 'moderation_required': True},
                "description": {'field': "description", 'tns_ltr_req': False},
                'property_size': {'field': "property_size", 'tns_ltr_req': False},
                'address': {'field': "address", 'tns_ltr_req': False, 'moderation_required': True},
            },
            "HotelMetaData" : {
                "landmark": {'field': "landmark", 'tns_ltr_req': False},
                "agency_name": {'field': "agency_name", 'tns_ltr_req': True},
                "agency_address": {'field': "agency_address", 'tns_ltr_req': False}
            },
            "Services": {
                "metadata__full_grammar": {'field': "metadata", 'tns_ltr_req': False},
                "metadata__short_description": {'field': "metadata", 'tns_ltr_req': False},
                "metadata__category_name":{'field': "metadata", 'tns_ltr_req': False},
                "metadata__leaf_name": {'field': "metadata", 'tns_ltr_req': False}
            },
            "RoomDetail": {
                "roomtypename": {'field': "roomtypename", 'tns_ltr_req': False, 'moderation_required': True},
                "description": {'field': "description", 'tns_ltr_req': False}
            },
            "RatePlan": {
                "rateplanname": {'field': 'rateplanname', 'tns_ltr_req': False},
                "description": {'field': 'description', 'tns_ltr_req': False},
                "long_description": {'field': 'long_description', 'tns_ltr_req': False}
            },
            "USPDetail": {
                "usp_text": {'field': 'usp_text', 'tns_ltr_req': False}
            },
            "HotelPolicyMappingV2": {
                "grammar": {'field': 'grammar', 'tns_ltr_req': False}
            },
            "ServicesTemplate":{
                "leaf_category_name" : {'field': "leaf_category_name", 'tns_ltr_req': False}
            },
            "ServicesCategory":{
                "name" : {'field': 'name', 'tns_ltr_req': False}
            },
            "PolicyCategory":{
                "name": {'field': 'name', 'tns_ltr_req': False},
                "description": {'field': 'description', 'tns_ltr_req': False}
            },
            "PolicyTemplateV2":{
                "template_text": {'field': 'template_text', 'tns_ltr_req': False}
            },
            'AmenitiesV2':{
                "main_category": {'field': 'main_category', 'tns_ltr_req': False},
                "amenity_name": {'field': 'amenity_name', 'tns_ltr_req': False},
                "template": {'field': 'template', 'tns_ltr_req': False, 'is_template':True}
            },
            "HotelChain":{
                "chainname": {'field': 'chainname', 'tns_ltr_req': True}
            },
            "User":{
                "first_name": {'field': 'first_name', 'tns_ltr_req': True},
                "last_name": {'field': 'last_name', 'tns_ltr_req': True}
            },
            "HostProfile":{
                "about": {'field': 'about', 'tns_ltr_req': False},
                "gender": {'field': 'gender', 'tns_ltr_req': False},
            },
            "CommonInterests":{
                "name": {'field': 'name', 'tns_ltr_req': False},
                "description": {'field': 'description', 'tns_ltr_req': False},
            },
            "CommonProfession":{
                "name": {'field': 'name', 'tns_ltr_req': False},
                "description": {'field': 'description', 'tns_ltr_req': False},
            },
            "CommonLanguages":{
                "label": {'field': 'label', 'tns_ltr_req': False}
            }
}

INGO_MODEL_TO_APP_MAPPING = {
            'HotelDetail': 'hotels',
            'Services':'hotels',
            'RoomDetail': 'hotels',
            'AmenitiesV2': 'common',
            'User': 'ingouser',
            'HostProfile': 'hotels'
}

CMS_KEY_CONST = "__"

CMS_KEY_SEPARATOR = "__"
LINGO_NESTED_JSON_KEY_SEPARATOR = "__"


HOTEL_LINGO_DOCUMENT = "hotels"
TEMPLATE_DOCUMENT = "templates"
GLOSSARY_DOCUMENT = "glossary"
CHOICE_DOCUMENT = "choices"
USER_DOCUMENT = "users"
HOTEL_CHOICE_DOCUMENT = "hotel_choice"
COMMON_CHOICE_DOCUMENT = "common_choice"

INGO_METADATA = "HotelMetaData"
INGO_RATEPLAN = "RatePlan"
INGO_HOTEL_DETAIL = "HotelDetail"
INGO_USP_DETAIL = "USPDetail"
INGO_ROOM_DETAIL = "RoomDetail"
INGO_HOTEL_POLICY_MAPPING_V2 = "HotelPolicyMappingV2"
INGO_COMMON_AMENITIES_V2 = "AmenitiesV2"
INGO_HOTEL_CHAIN = "HotelChain"
INGO_HOST_PROFILE = "HostProfile"
INGO_USER = "User"
INGO_COMMON_INTERESTS = "CommonInterests"
INGO_COMMON_PROFESSION = "CommonProfession"
INGO_BASIC_SERVICES = "Services"
INGO_BASIC_SERVICES_TEMPLATE = "ServicesTemplate"
INGO_BASIC_SERVICES_CATEGORY = "ServicesCategory"
INGO_POLICY_CATEGORY = "PolicyCategory"
INGO_POLICY_TEMPLATE_V2 = "PolicyTemplateV2"

MAKE_HOTEL_DETAILS_OBJECT_CHOICES = {
    "hotel_choice": ["BookingPreferenceDict", "BED_CHOICES", "BedType", "extra_bed_choices"],
    "common_choice": []
}

LIST_TUPLE_CHOICES = ["BedType", "extra_bed_choices"]
HASH_TUPLE_CHOICES = []
TRANS_ALLOWED_LANG_DICT = {
    1: 'ara',
    2: 'hin'
}
TRANSLATION_ALLOWED_LANG = TRANS_ALLOWED_LANG_DICT.values()

INGO_TABLE = "ingo_table"
CHILD_NODES = "child_nodes"
NEED_TRANSLATION = "need_translation"
PACKET_TRANSLATION_REQUIRED_FIELDS = "resp_fields"
CATEGORY="category"
PACKET_FIELD_TO_LINGO_FIELD_MAP = "field_to_col_map"

INGO_HOTEL_DOCUMENTS = [INGO_METADATA, INGO_USP_DETAIL, INGO_ROOM_DETAIL, INGO_RATEPLAN, INGO_HOTEL_POLICY_MAPPING_V2]
INGO_TEMPLATE_DOCUMENTS = [INGO_COMMON_AMENITIES_V2]
INGO_GLOSSARY_DOCUMENTS = [INGO_HOTEL_CHAIN, INGO_COMMON_INTERESTS, INGO_COMMON_PROFESSION]
INGO_USER_DOCUMENTS = [INGO_HOST_PROFILE]

DOC_DTL = "doc_dtl"
HOTEL_CHOICE_PREFIX = "htl__"
SUB_ATTRIBUTES = "sub_attributes"
TEMPLATE = "template"
ATTRIBUTE_NAME = "attribute_name"

PK = "pk"

LINGO_QUERY = {
    HOTEL_LINGO_DOCUMENT: {
        INGO_HOTEL_DETAIL: None
    },
    TEMPLATE_DOCUMENT: {},
    GLOSSARY_DOCUMENT: {},
    USER_DOCUMENT: {
        INGO_USER: None,
        INGO_HOST_PROFILE: None
    }
}

LINGO_DOCUMENTS = [HOTEL_LINGO_DOCUMENT, TEMPLATE_DOCUMENT, GLOSSARY_DOCUMENT, USER_DOCUMENT]

LINGO_DOC_TABLES = {HOTEL_LINGO_DOCUMENT: INGO_HOTEL_DOCUMENTS, TEMPLATE_DOCUMENT: INGO_TEMPLATE_DOCUMENTS,
          GLOSSARY_DOCUMENT: INGO_GLOSSARY_DOCUMENTS, USER_DOCUMENT: INGO_USER_DOCUMENTS}

# need to revisit the constants
HUNDRED_FLOAT = 100.0
ZERO_FLOAT = 0.0
ZERO = 0
ONE = 1
ROUND_OF_TWO = 2


MANDATORY_TRANSLATED_FIELD_PER_MODEL={
    'RoomDetail':['roomtypename', 'description'],
    'RatePlan':['rateplanname'],
    'HotelDetail':['hotelname','displayname', 'address', 'property_size', 'description', 'chainname'],
}

TRANSLATION_FIELDS_PER_MODEL={
'Hotel':["hotelname", "displayname", "chainname", "address", "property_size", "description"],
'Room':["roomtypename", "description"],
'RatePlan':["rateplanname"],
'Policy':["grammar"],
'Amenities':["amenity_name"],
'USPDetail':["usp_text"]
}

MODERATION_FIELD_KEY = "moderation_required"

PERCENTAGE_THRESHOLD=60.0