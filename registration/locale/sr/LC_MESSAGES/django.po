# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: django-registration trunk\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2008-04-05 13:51+0200\n"
"PO-Revision-Date: 2008-04-05 14:00+0100\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2;\n"
"X-Poedit-Language: Serbian\n"
"X-Poedit-Country: YUGOSLAVIA\n"

#: forms.py:38
msgid "username"
msgstr "korisničko ime"

#: forms.py:41
msgid "email address"
msgstr "email adresa"

#: forms.py:43
msgid "password"
msgstr "šifra"

#: forms.py:45
msgid "password (again)"
msgstr "šifra (ponovo)"

#: forms.py:54
msgid "Usernames can only contain letters, numbers and underscores"
msgstr "Korisničko ime može da se sastoji samo od slova, brojeva i donje crte (\"_\")"

#: forms.py:59
msgid "This username is already taken. Please choose another."
msgstr "Korisničko ime je već zauzeto. Izaberite drugo."

#: forms.py:71
msgid "You must type the same password each time"
msgstr "Unete šifre se ne slažu"

#: forms.py:100
msgid "I have read and agree to the Terms of Service"
msgstr "Pročitao sam i slažem se sa uslovima korišćenja"

#: forms.py:109
msgid "You must agree to the terms to register"
msgstr "Morate se složiti sa uslovima korišćenja da bi ste se registrovali"

#: forms.py:128
msgid "This email address is already in use. Please supply a different email address."
msgstr "Ova e-mail adresa je već u upotrebi. Morate koristiti drugu e-mail adresu."

#: forms.py:153
msgid "Registration using free email addresses is prohibited. Please supply a different email address."
msgstr "Registracija korišćenjem besplatnig e-mail adresa je zabranjena. Morate uneti drugu e-mail adresu."

#: models.py:188
msgid "user"
msgstr "korisnik"

#: models.py:189
msgid "activation key"
msgstr "aktivacioni ključ"

#: models.py:194
msgid "registration profile"
msgstr "registracioni profil"

#: models.py:195
msgid "registration profiles"
msgstr "registracioni profili"

