{% extends "admin/base_site.html" %}
{% load i18n admin_static %}
{% load admin_shortcuts_tags %}
{% load custom_tags %}

{% block extrastyle %}<style type="text/css">{% admin_shortcuts_css %}</style>
{{ block.super }}<link rel="stylesheet" type="text/css" href="{% static "admin/css/dashboard.css" %}" />
<link href="/static/images/favicon.ico?v=2" type="image/ico" rel="shortcut icon">
<link href="/static/images/favicon.ico?v=2" type="image/ico" rel="icon">
{% endblock %}

{% block coltype %}colMS{% endblock %}
{% block extrahead %}
	{{ block.super }}
	{% admin_shortcuts_js %}
{% endblock %}
{% block bodyclass %}dashboard{% endblock %}

{% block breadcrumbs %}
	<div class="admin_shortcuts">
		{% admin_shortcuts %}
	</div>
{% endblock %}

{% block content %}
<div id="content-main">

{% if app_list %}
{% app_order %}
    {% for app in app_list %}
        <div class="module">
        <table summary="{% blocktrans with name=app.name %}Models available in the {{ name }} application.{% endblocktrans %}">
        <caption><a href="{{ app.app_url }}" class="section">{% blocktrans with name=app.name %}{{ name }}{% endblocktrans %}</a></caption>
        {% for model in app.models %}
            <tr>
            {% if model.admin_url %}
                <th scope="row"><a href="{{ model.admin_url }}">{{ model.name }}</a></th>
            {% else %}
                <th scope="row">{{ model.name }}</th>
            {% endif %}

            {% if model.add_url %}
                <td><a href="{{ model.add_url }}" class="addlink">{% trans 'Add' %}</a></td>
            {% else %}
                <td>&nbsp;</td>
            {% endif %}
            {% if model.admin_url %}
                <td><a href="{{ model.admin_url }}" class="changelink">{% trans 'Change' %}</a></td>
            {% else %}
                <td>&nbsp;</td>
            {% endif %}
            </tr>
            {% ifequal model.admin_url "/admin/hotels/bookingpushstatushistory/" %}
                <tr>
                    <th scope="row"><a href="/admin/hotels/cm/bookings/">New Booking Push Status</a></th>
                    <td>&nbsp;</td>
                    <td><a href="{{ model.admin_url }}" class="changelink">{% trans 'Change' %}</a></td>
                </tr>
            {% endifequal %}
        {% endfor %}

        </table>
        </div>
    {% endfor %}
{% else %}
    <p>{% trans "You don't have permission to edit anything." %}</p>
{% endif %}
</div>
{% endblock %}

{% block sidebar %}
	{%comment%}
<div id="content-related">
    <div class="module" id="recent-actions-module">
        <h2>{% trans 'Recent Actions' %}</h2>
        <h3>{% trans 'My Actions' %}</h3>
            {% load log %}
            {% get_admin_log 10 as admin_log for_user user %}
            {% if not admin_log %}
            <p>{% trans 'None available' %}</p>
            {% else %}
            <ul class="actionlist">
            {% for entry in admin_log %}
            <li class="{% if entry.is_addition %}addlink{% endif %}{% if entry.is_change %}changelink{% endif %}{% if entry.is_deletion %}deletelink{% endif %}">
                {% if entry.is_deletion or not entry.get_admin_url %}
                    {{ entry.object_repr }}
                {% else %}
                    <a href="{{ entry.get_admin_url }}">{{ entry.object_repr }}</a>
                {% endif %}
                <br/>
                {% if entry.content_type %}
                    <span class="mini quiet">{% filter capfirst %}{% trans entry.content_type.name %}{% endfilter %}</span>
                {% else %}
                    <span class="mini quiet">{% trans 'Unknown content' %}</span>
                {% endif %}
            </li>
            {% endfor %}
            </ul>
            {% endif %}
    </div>
</div>
{%endcomment%}
{% endblock %}
