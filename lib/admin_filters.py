import datetime

from django.contrib.admin.filters import <PERSON><PERSON><PERSON><PERSON> as DjangoListFilter
from django.contrib.admin.options import IncorrectLookupParameters
from django.contrib.admin.utils import prepare_lookup_value
from django.core.exceptions import ImproperlyConfigured, ValidationError
from django.utils import timezone
from django.utils.translation import ugettext_lazy as _


class ListFilter(DjangoListFilter):
    def __init__(self, request, params, model, model_admin):
        super(ListFilter, self).__init__(request, params, model, model_admin)
        for p in self.expected_parameters():
            if p in params:
                value = params.pop(p)
                self.used_parameters[p] = prepare_lookup_value(p, value)

    def queryset(self, request, queryset):
        try:
            return queryset.filter(**self.used_parameters)
        except ValidationError as e:
            raise IncorrectLookupParameters(e)


class BooleanFilter(ListFilter):
    parameter_name = None
    null_support = True
    exact_support = True

    def __init__(self, request, params, model, model_admin):
        if not (self.null_support or self.exact_support):
            raise ImproperlyConfigured(
                "The list filter '%s' does not specify a 'null_support' or 'exact_support'." % self.__class__.__name__
            )

        if self.exact_support:
            self.lookup_kwarg = '%s__exact' % self.parameter_name
            self.lookup_val = request.GET.get(self.lookup_kwarg, None)

        if self.null_support:
            self.lookup_kwarg2 = '%s__isnull' % self.parameter_name
            self.lookup_val2 = request.GET.get(self.lookup_kwarg2, None)

        super(BooleanFilter, self).__init__(request, params, model, model_admin)

    def has_output(self):
        return True

    def expected_parameters(self):
        li = []
        if self.exact_support:
            li.append(self.lookup_kwarg)
        if self.null_support:
            li.append(self.lookup_kwarg2)
        return li

    @staticmethod
    def exact_choices():
        return (
            ('1', _('True')),
            ('0', _('False'))
        )

    @staticmethod
    def null_choices():
        return (
            ('1', _('True')),
            ('0', _('False'))
        )

    def choices(self, cl):
        dc = {}
        if self.exact_support:
            dc[self.lookup_kwarg] = None
        if self.null_support:
            dc[self.lookup_kwarg2] = None
        yield {
            'selected': getattr(self, 'lookup_val', None) is None and getattr(self, 'lookup_val2', None) is None,
            'query_string': cl.get_query_string(dc),
            'display': _('All'),
        }
        if self.exact_support:
            for lookup, title in self.exact_choices():
                yield {
                    'selected': self.lookup_val == lookup and not self.lookup_val2,
                    'query_string': cl.get_query_string({
                        self.lookup_kwarg: lookup,
                    }, [self.lookup_kwarg2] if self.null_support else None),
                    'display': title,
                }
        if self.null_support:
            for lookup, title in self.null_choices():
                yield {
                    'selected': self.lookup_val2 == lookup,
                    'query_string': cl.get_query_string({
                        self.lookup_kwarg2: lookup,
                    }, [self.lookup_kwarg] if self.exact_support else None),
                    'display': title,
                }


class DateTimeFilter(ListFilter):
    parameter_name = None
    datetime_field = True   # True if datetime field. False if date field.

    def __init__(self, request, params, model, model_admin):
        self.field_generic = '%s__' % self.parameter_name
        self.date_params = dict((k, v) for k, v in params.items()
                                if k.startswith(self.field_generic))

        now = timezone.now()
        # When time zone support is enabled, convert "now" to the user's time
        # zone so Django's definition of "Today" matches what the user expects.
        if timezone.is_aware(now):
            now = timezone.localtime(now)

        if self.datetime_field:
            today = now.replace(hour=0, minute=0, second=0, microsecond=0)
        else:       # field is a DateField
            today = now.date()
        self.today = today

        tomorrow = today + datetime.timedelta(days=1)
        self.tomorrow = tomorrow

        if today.month == 12:
            next_month = today.replace(year=today.year + 1, month=1, day=1)
        else:
            next_month = today.replace(month=today.month + 1, day=1)
        self.next_month = next_month

        next_year = today.replace(year=today.year + 1, month=1, day=1)
        self.next_year = next_year

        self.lookup_kwarg_since = '%s__gte' % self.parameter_name
        self.lookup_kwarg_until = '%s__lt' % self.parameter_name

        super(DateTimeFilter, self).__init__(request, params, model, model_admin)

    def get_links(self):
        return [
            (_('Any date'), {}),
            (_('Today'), {
                self.lookup_kwarg_since: str(self.today),
                self.lookup_kwarg_until: str(self.tomorrow),
            }),
            (_('Past 7 days'), {
                self.lookup_kwarg_since: str(self.today - datetime.timedelta(days=7)),
                self.lookup_kwarg_until: str(self.tomorrow),
            }),
            (_('This month'), {
                self.lookup_kwarg_since: str(self.today.replace(day=1)),
                self.lookup_kwarg_until: str(self.next_month),
            }),
            (_('This year'), {
                self.lookup_kwarg_since: str(self.today.replace(month=1, day=1)),
                self.lookup_kwarg_until: str(self.next_year),
            }),
        ]

    def has_output(self):
        return True

    def expected_parameters(self):
        return [self.lookup_kwarg_since, self.lookup_kwarg_until]

    def choices(self, cl):
        for title, param_dict in self.get_links():
            yield {
                'selected': self.date_params == param_dict,
                'query_string': cl.get_query_string(
                    param_dict, [self.field_generic]),
                'display': title,
            }
