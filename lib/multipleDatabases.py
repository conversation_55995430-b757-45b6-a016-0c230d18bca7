import random


class StatsDBRouter(object):
    """
    A router to control all database operations on models in the
    stats application.
    """

    def db_for_read(self, model, **hints):
        """
        Attempts to read auth models go to auth_db.
        """
        if model._meta.app_label in ['hotelstore']:
            return 'stats'
        return None

    def db_for_write(self, model, **hints):
        """
        Attempts to write auth models go to auth_db.
        """
        if model._meta.app_label in ['hotelstore']:
            return 'stats'
        return None

    def allow_relation(self, obj1, obj2, **hints):
        """
        Allow relations if a model in the auth app is involved.
        """
        if obj1._meta.app_label in ['hotelstore'] or \
                        obj2._meta.app_label in ['hotelstore']:
            return True
        return None

    def allow_syncdb(self, db, model):
        """
        Make sure the auth app only appears in the 'stats_db'
        database.
        """
        if db == 'stats':
            return model._meta.app_label in ['hotelstore']
        elif model._meta.app_label in ['hotelstore']:
            return False
        return None


class MasterSlaveRouter(object):
    def db_for_read(self, model, **hints):
        """
        Reads go to a randomly-chosen slave.
        """
        database = getattr(model, '_DATABASE', None)
        if database:
            return '%s-slave' % database
        return 'slave'

    def db_for_write(self, model, **hints):
        """
        Writes always go to master.
        """
        database = getattr(model, '_DATABASE', None)
        if database:
            return '%s-master' % database
        return 'default'

    def allow_relation(self, obj1, obj2, **hints):
        """
        Relations between objects are allowed if both objects are
        in the master/slave pool.
        """
        db_list = ('default', 'slave', 'secondary-master', 'secondary-slave', 'ingo-analytics-slave')
        if obj1._state.db in db_list and obj2._state.db in db_list:
            return True
        return None

    def allow_syncdb(self, db, model):
        """
        All non-stats models end up in this pool.
        """
        return True


class MasterSlaveRouterOld(object):
    """A router that sets up a simple master/slave configuration"""

    def db_for_read(self, model, **hints):
        "Point all read operations to a random slave"
        if (model._meta.app_label == 'stats'):
            return 'stats'
        else:
            return random.choice(['slave'])

    def db_for_write(self, model, **hints):
        "Point all write operations to the master"
        if (model._meta.app_label == 'stats'):
            return 'stats'
        else:
            return 'default'

    def allow_relation(self, obj1, obj2, **hints):
        '''
        Relations between objects are allowed if both objects are
        in the master/slave pool.
        '''
        db_list = ('default', 'slave')
        if obj1._state.db in db_list and obj2._state.db in db_list:
            return True
        return None

    def allow_syncdb(self, db, model):
        "Explicitly put all models on all databases."
        if db == 'stats':
            return model._meta.app_label == 'stats'
        elif (model._meta.app_label == 'stats'):
            return False
        else:
            return True