from enum import Enum

LOCALITY_SOURCE_VOYAGER = 'voy'
AAJ_KA_BHAO = 'Last Minute Rate'
CUTOFF_FORMAT_REQUIRED = 'cutoff_format_required'
ROOM_RATE_PLAN_ADDED = 'Auto room-rateplan added'

DISCOUNT_COUPON = 'discount_coupon'
CUG_OFFER = 'cug'

PID_TASK_IDLE_TIME_THRESHOLD = 60 * 30

HOTEL = 'hotel'
ROOM='room'
RATEPLAN='rateplan'
HOMESTAY = 'homestay'
EXTERNAL_CAL = 'external_cal'
CREATE_LINKING = 'create_linking'
ICAL_CREATED = 'ical_created'
ICAL = 'ical'
PREBOOK_CHAT_ENABLED = "is_prebook_chat_enabled"
CHAT_ENABLED = "is_chat_enabled"

PENDING_WAIVER_FLAG_BIT1 = 3
ACCEPTED_WAIVER_FLAG_BIT1 = 5

PREBUY_TAX_EXCLUSIVE = 1
PREBUY_TAX_INCLUSIVE = 2

#this is a defualt value,but dont use this as source of truth
TCS_DEDUCTION_PERCENTAGE = 1
TCS_DEDUCTION_MIN_SELL_PRICE = 1000

OYO_CHAINNAME_ID = 897

VERSION_THRESHOLD = "9.10.0(100950)"
PLATFORM_COMMISSION_TAX_PERCENTAGE = 18
MULTIROOM_MERGING_TIME = 720

UNICODE_REMOVAL_REGEX = r'[^\x00-\x7F]'

STARTING_SELLING_PRICE_REGEX = r'^\d+$'

# BankDetails model, list of ids of popular banks
# HDFC Bank Ltd = 82
# State Bank of India = 178
# ICICI Bank = 86
# Axis Bank = 16
# Punjab National Bank = 156
# Bank of Baroda = 22
# Union Bank of India = 258
# Canara Bank = 42
# Kotak Mahindra Bank = 118
POPULAR_BANKS_LIST = [82, 178, 86, 16, 156, 22, 258, 42, 118]
HOTEL_ALREADY_CREATED = 'Hotel is already created!'
HOTEL_DOES_NOT_EXISTS = 'Hotel does not exists!'
RESPONSE_FROM_MOJO = {
    'Data already present for this Hotel': HOTEL_ALREADY_CREATED,
    'Hotel is already created!': HOTEL_ALREADY_CREATED,
    'Hotel not present': HOTEL_DOES_NOT_EXISTS,
    'Hotel does not exists!': HOTEL_DOES_NOT_EXISTS
}

# platform constants for GDS even push
PLATFORM_FULLDAY = "gommt"
PLATFORM_HOTEL_TRAVEL = "hotel_travel"
PLATFORM_DAYUSE = "dayuse"

class LocalityStatus:
    PENDING = 1
    APPROVED = 2
    REJECTED = 3


class UserPermissionConstants:
    READ = 0
    READ_AND_WRITE = 1
    ADMIN = 0
    USER = 1
    CHANNEL_MANAGER = 2
    STAFF_USER = 3
    ROLE_ADMIN = "Admin"
    ROLE_USER = "User"
    OWNER = "Owner"
    HOTEL_MANAGER = "Hotel Manager"
    FRONT_DESK_MANAGER = "Front Desk Manager"
    CENTRAL_RESERVATION_MANAGER = "Central Reservation Manager"
    HOTEL_CONTENT_MANAGER = "Hotel Content Manager"
    RATES_AND_INVENTORY_MANAGER = "Rates & Inventory Manager"
    ACCOUNT_AND_PAYMENT_MANAGER = "Account & Payments Manager"
    USER_GROUP_ID = 1
    REVENUE_MANAGER_GROUP_ID = 2
    CHANNEL_MANAGER_GROUP_ID = 3
    PMS_GROUP_ID = 4
    DEFAULT = 0
    RESELLER_AND_GOMMT = 1
    HOTELTRAVEL = 2


class ClientConstants:
    EXTRANET = {'key': 'Extranet', 'value': 0}
    ANDROID = {'key': 'Android', 'value': 1}
    IOS = {'key': 'IOS', 'value': 2}
    ALTACCO = {'key': 'AltAcco', 'value': 3}

    types = (EXTRANET, ANDROID, IOS, ALTACCO)


class UserLinkConstants:
    HOTELIER, HOTELIER_CONST = 0, "Hotelier"
    HOST, HOST_CONST = 1, "Host"
    COHOST, COHOST_CONST = 2, "CoHost"

    hotelier_types = {
        HOTELIER_CONST: HOTELIER,
        HOST_CONST: HOST,
        COHOST_CONST: COHOST
    }

    reverse_hotelier_types = {
        HOTELIER: HOTELIER_CONST,
        HOST: HOST_CONST,
        COHOST: COHOST_CONST
    }

    all_hosts = [HOST, COHOST]


class LegalEntityTypeConstants:
    NA, NA_CONST = 0, "NA"
    HOTELTRAVEL, HOTELTRAVEL_CONST = 1, "HotelCloud"

    le_types = {
        HOTELTRAVEL_CONST: HOTELTRAVEL,
        NA_CONST: NA
    }

    rev_le_types = {
        HOTELTRAVEL: HOTELTRAVEL_CONST,
        NA: NA_CONST
    }


RESELLER_AGREEMENT_MAPPING = {
    LegalEntityTypeConstants.HOTELTRAVEL: [431, ]
}

# For Hotel property
HOTEL_COMMISSION_DICT = {
    'domestic': {
        'independent': {
            'pay_now': [15, 20, 20],
            'pay_at_hotel': [15, 18, 18],
        },
        'chain': {
            'pay_now': [10, 30, 10],
            'pay_at_hotel': [10, 30, 10],
        }
    },
    'international': {
        'independent': {
            'pay_now': [5, 20, 15],
            'pay_at_hotel': [5, 20, 15],
        },
        'chain': {
            'pay_now': [5, 20, 15],
            'pay_at_hotel': [5, 20, 15],
        }
    }
}

HOMESTAY_CHAIN_COMMISSION_VALUES = {
    'pay_now': [15, 18, 18],
    'pay_at_hotel': [16, 16, 16]
}

GROUP_BOOKING_DEFAULT_COMMSSION = 5

# For Current implementation, commission values for chain and non-chain category are same. (For Homestay property)
HOMESTAY_COMMISSION_DICT = {
    'domestic': {
        'independent': HOMESTAY_CHAIN_COMMISSION_VALUES,
        'chain': HOMESTAY_CHAIN_COMMISSION_VALUES
    },
    'international': {
        'independent': HOMESTAY_CHAIN_COMMISSION_VALUES,
        'chain': HOMESTAY_CHAIN_COMMISSION_VALUES
    }
}

# Array indicates the minimum, maximum and default commission values corresponding to property category.
COMMISSION_DICT = {
    "hotel": HOTEL_COMMISSION_DICT,
    "homestay": HOMESTAY_COMMISSION_DICT
}
COMMISSION_LIST_FOR_FAB_TREEBO = [25, 30, 25]
MIDNIGHT_CHECKIN_END_TIME = '05:59:59'

DOCUMENT_FILE_SIZE_LIMIT = 10485760
MB_SIZE = 1024 * 1024
ALLOWED_INVOICE_FORMATS = ['pdf', 'png', 'jpeg', 'jpg']

GSTN_PENALTY_BOOKING_COUNT_THRESHOLD = 2
GSTN_UPLOAD_CUTOFF_DAYS_POST_CHECKOUT = 14
GSTN_UPLOAD_REMINDER_DAYS_LIST = [0, 2, 6]
GSTN_PENALTY_REVERSE_UPLOAD_COUNT_THRESHOLD = 5

GSTN_SANDESH_EVENT_ID = 'SEVT00008'
GSTN_SANDESH_EVENT_ID_HOTELWISE = 'SEVT00021'
GSTN_INVOICE_REJECTION_SANDESH_EVENT_ID = 'SEVT00157'
GSTN_INVOICE_NN_TIMEOUT = 'SEVT00170'

PENALTY_STATUS_DICT = {
    0: "NA",
    1: "OPEN",
    2: "HOLD",
    3: "RELEASE",
}

PENALTY_SUB_STATUS_DICT = {
    "Both": 0,
    "Gst": 1,
    "Booking": 2
}

# GST payment status
CB_STATUS_NA = 0
CB_STATUS_OPEN = 1
CB_STATUS_HOLD = 2
CB_STATUS_RELEASED = 3

# GST payment sub-status
CB_SUB_STATUS_BOTH = 0
CB_SUB_STATUS_GST = 1
CB_SUB_STATUS_NA = 2

# GST payment type
BOOKING = "Booking"
GST = "Gst"
BOTH = "Both"

# Vendor names
MMT = 'MakeMyTrip'
GI = 'Goibibo'
HOTELTRAVEL = 'HotelCloud'


# DCB No Show Thresold values
DCB_NOSHOW_THRESOLD_POST_CHECKOUT = 7

CONTRACTTYPE = [
    ('b2c', 'B2C Sell'),
    ('b_2_b', 'B2B Sell'),
    ('corporate', 'Mybiz'),
    ('bundled', 'BUNDLED'),
    ('hbc', 'HBC'),
    ('corporate_rfp', 'Corporate RFP'),
    ('mobile', 'Mobile'),
    ('gcc', 'GCC POS'),
    ('b2a', 'My Partner'),
    ('IN-POS', 'India POS'),
    ('grp', 'Group'),
    ('mypartner_rfp', 'MyPartner RFP')
]

ADDITIONALCONTRACTTYPE = [
    ('loggedin', 'loggedin'),
    ('MMT BLACK', 'MMT BLACK'),
    ('MMT BLACK1', 'MMT BLACK1'),
    ('MMT BLACK2', 'MMT BLACK2'),
    ('dlo', 'Desktop Logged In'),
    ('hotel_travel', 'Hotel Travel'),
]

BUNDLED = "bundled"

CORPORATERFP_PAH_CONTRACTTYPE = 'corporate_rfp'

CORPORATERFP_PAH_CONTRACTTYPE_ERROR = 'Rateplan can be Pay at Hotel, If contracttype is corporate_rfp only'

# previous version for inclusion
PREVIOUS_INCLUSION_DATA_VERSION = '1'
WAIVER_THRESOLD_POST_CHECKOUT = 2


class OfferStatusEnumeration(Enum):
    Active = 1
    InActive = 2
    Expired = 3
    All = 4


SEGMENTSINSEGMENTCODE = 5

OVERWRITTEN_RATE_SEGEMENT_IDS = ['1134', '1136', '1137', '1167', '1168']

RATESEGMENTMAPPING = {
    "1134" : "black",
    "1136" : "black1",
    "1137" : "black2",
    "1167" : "black1",
    "1168" : "black2"
}

BLACK_SEGMENTS =["black","black1","black2"]

PROMOTIONSEGMENTREVERSEMAPPING = {
    "mob": "mobile",
    "cor": "corporate",
    "log": "loggedin",
    "inp": "IN-POS",
    "b2c": "b2c",
    "b2b": "b_2_b",
    "bun": "bundled",
}

RATESEGMENTREVERSEMAPPING = {
    "bun": "bundled",
    "cor": "corporate",
    "hbc": "hbc",
    "rfp": "corporate_rfp",
    "b2c": "b2c",
    "b2b": "b_2_b",
    "gcc": "gcc",
    "b2a": "b2a",
    "mob": "mobile",
    "log": "loggedin",
    "blk": "MMT BLACK",
    "bk1": "MMT BLACK1",
    "bk2": "MMT BLACK2",
    "dlo": "dlo",
    "inp": "IN-POS",
    "grp": "grp",
    "mnr": "mypartner_rfp",
    "htr": "hotel_travel",
    "mpc": "mypartner_cug"
}

CUGSEGMENTREVERSEMAPPING = {
    "blk": "MMT BLACK",
    "mob": "MOBILE",
    "cor": "corporate",
    "inp": "GEOGRAPHY",
    "bk1": "MMT BLACK1",
    "bk2": "MMT BLACK2",
    "gcc": "gcc",
    "b2a": "b2a",
    "hol": "Holidays",
    "af1": "Affiliate",
    "msl": "MMT SELCT",
    "ms1": "MMT SELCT1",
    "ms2": "MMT SELCT2",
    "vis": "vistarafly",
    "mem": "member"
}

WAIVER_MESSAGE_MAP = {
    "medical emergency": {"id": 1, "label": "medical"},
    "death of guest/family member": {"id": 2, "label": "death"},
    "natural calamity": {"id": 3, "label": "calamity"},
    "hotel agreed for cancellation": {"id": 4, "label": "cancellation agreed by hotel"},
    "flight got cancelled": {"id": 5, "label": "flight booking cancellation"},
    "other": {"id": 6, "label": "others"},
}

# expiring soon threshold value
PROMO_EXPIRING_SOON_THRESHOLD = 5

END_DATE_FOR_DATELESS_OFFER = "2099-12-31 23:59:59"

CONTENTTYPE_MODEL_RELATED_TO_MAP = {
    "hoteldetail": "Hotel",
    "roomdetail": "Room",
    "rateplan": "Rateplan"
}

CONFIRM_STATUS_MAP = {
    "pending": 1,
    "confirmed": 2,
    "crm": 3,
    "reconfirmed": 4,
    "rejected": 5,
    "reject_service_issue": 6,
    "cancelled": 7,
    "part_cancelled": 8,
    "cancelconfirm": 9,
    "amended": 10,
    "noshow": 11,
    "noshow_staff": 12,
    "customer_reject": 13,
    "aborted": 14,
    "alternate": 15,
    "alternate_cancelled": 16
}

LANG_CODE_MAP = {
    'ara': True,
    'hin': True,
}

RTB_REQUEST_VALIDITY = 24  # 24 hour
ITB_REQUEST_VALIDITY = 48  # 24 hour
RTB_EVENT_PUSH_TOPIC = 'ingo_rtb_event_push'
MYBIZ_RTB_REQUEST_VALIDITY = 24
MYPARTNER_RTB_REQUEST_VALIDITY = 1

# RTB Multiroom merge status
INTERIM = 'interim'
PREINTERIM = 'preinterim'
INTPICKED = 'intpicked'
PICKED = 'picked'

REASON_STATUS_TYPE = {
    'confirmed': 'confirmed',
    'reject': 'reject'
}

REASON_REJECT_TYPE = {
    'snooze': 'snooze',
    'ingo_express': 'ingo_express',
    'other': 'other'
}

class BlockedPermissions:
    READ_WRITE = {
        "name": "No Access",
        "key": "READ-WRITE",
        "value": 0,
    }
    WRITE = {
        "name": "Can Read",
        "key": "WRITE",
        "value": 1,
    }

    types = [READ_WRITE, WRITE]


INVITATION_ACTIONS = {
    "accept": {
        "name": "Accept",
        "value": "accept",
    },
    "reject": {
        "name": "Reject",
        "value": "decline",
    }
}

MAX_CARETAKER_COUNT = 3

HEIMDALL_INGO_SOM = ["INGO", ""]

MAX_INVITATION_COUNT = 10
INVITATION_EXPIRATION_DAYS = 10

# Discussed for myra chat
HOST_COHOST_ADDED_EVENT_TYPE = 0
COHOST_WITHDRAW_EVENT_TYPE = 1
COHOST_RIGHTS_UPDATE_EVENT_TYPE = 2
APPLE_DELETE_USER_EVENT_TYPE = 3

BOOKING_LIST_SORT_BY_OPTIONS = {
    'checkin': 'checkin',
    'checkout': 'checkout',
    'booking': 'booking'
}

BOOKING_API_TYPE = {
    'booking_list': 'booking_list',
    'booking_count': 'booking_count'
}

REJECT_STATUS_MAIL_SANDESH_EVENT_ID = "SEVT00111"

INGO_EXPRESS_ONBOARD_SANDESH_EVENT_ID = "SEVT00156"

CHAIN_IDS_FOR_NULL_STND_PRCHS_VENDR = [897]
CHAIN_IDS_FOR_WITH_NO_PAH = [975, 976]

INTERRUPT_MO_PUSH_CORPORATE_BOOKING_WITH_MANUAL_INTERVENTION = True
CORPORATE_BOOKINGMAX_DAYS_IN_GSTN_OCR_VERIFIED_STATE = 3
PENALTY_FLOW_RELEASE_DATE = '2022-02-02'
GSTN_INVOICE_DAILY_REPORT_INTERNAL_MAIL_IDS = ['<EMAIL>', '<EMAIL>']

GSTN_INVOICE_DAILY_REPORT_INTERNAL_MAIL_IDS_CC = ['<EMAIL>', '<EMAIL>']

CORPORATE_GSTN_REPORTING_INTERNAL_MAIL_IDS = ['<EMAIL>', '<EMAIL>']
CORPORATE_GSTN_REPORTING_INTERNAL_MAIL_IDS_CC = ['<EMAIL>', '<EMAIL>',
                                                 '<EMAIL>', '<EMAIL>',
                                                 '<EMAIL>', '<EMAIL>',
                                                 '<EMAIL>', '<EMAIL>',
                                                 '<EMAIL>', '<EMAIL>',
                                                 '<EMAIL>', '<EMAIL>','<EMAIL>']

DAYUSE_PRICE_PERCENTAGES = {
    "3": {
        "suggested": 45,
        "min": 0,
        "max": 70
    },
    "6": {
        "suggested": 60,
        "min": 0,
        "max": 75
    },
    "9": {
        "suggested": 75,
        "min": 0,
        "max": 85
    }
}

DAYUSE_PRICE_PERCENTAGES_FOR_LINKAGE = {
    "3": {
        "suggested": 45,
        "min": 0,
        "max": 70
    },
    "6": {
        "suggested": 60,
        "min": 0,
        "max": 75
    },
    "9": {
        "suggested": 75,
        "min": 0,
        "max": 85
    }
}

BDO_USER_TYPE_LIST = ['contract_bdo', 'contractmanager', 'regional_head']
DAYUSE_LINKED_RATE_TYPE = "linked"
DAYUSE_FIXED_RATE_TYPE = "basic"


def combine_add_and_add_line_2(add_1, add_2):
    if add_1:
        if add_2:
            return str(add_1.encode('utf-8')) + ', ' + str(add_2.encode('utf-8'))
        else:
            return str(add_1.encode('utf-8'))
    elif add_2:
        return str(add_2.encode('utf-8'))
    else:
        return None


class ProgramType(Enum):
    GoStays = 0
    Advantage = 1

    @staticmethod
    def list():
        return list(map(lambda c: c.value, ProgramType))


ContentTypeIdForHotel = 27
ContentTypeIdForRoom = 30
ContentTypeIdForRatePlan = 31
ContentTypeIdForLocality = 15
ContentTypeIdForCity = 14

API_CLIENTS = {
    ('ingo_web', 'Extranet'),
    ('host_app', 'HostApp'),
    ('ingo_app', 'IngoApp'),
    ('api', 'API')
}

THIRD_PARTY_API = [
    ('adhar_otp_generate', 'Aadhar Generate OTP'),
    ('adhar_otp_submit', 'Aadhar Submit OTP'),
    ('driving_license_id', 'Driving License'),
    ('ocr_license', 'OCR License'),
    ('ocr_voter', 'OCR Voter')
]

THIRD_PARTY_ERRORS_EMAILS_TO = ['<EMAIL>', '<EMAIL>', '<EMAIL>']
THIRD_PARTY_ERRORS_EMAILS_CC = ['<EMAIL>']
doc_streaming_url = "https://{}/api/v3/stream/{}"
document_prefix = "hoteldocuments/tnc/"

RESELLER_SUB_VENDOR_CODE = 'INGORHT'

GDS_CACHE_CLEAR_MAX_RANGE = 365     # Days

TICKET_STATUS_OPEN = 'OPEN'
TICKET_STATUS_CLOSED = 'CLOSED'

TICKET_STATUSES = {
    TICKET_STATUS_OPEN: "OPEN",
    TICKET_STATUS_CLOSED: "CLOSED"
}

GSTN_CRON_USER = "<EMAIL>"

UpgradeTypeRoom = "ROOM"
UpgradeTypeMeal = "MEAL"
UPGRADABLE_BOOKING = "is_upgradable_booking"

BLACK_ENROLL_ACTION = "enroll"
BLACK_WITHDRAW_ACTION = "withdraw"
BLACK_UPDATE_ACTION = "update"


BLACK_MIN_DIFFERENCE_ALLOWED = 5
BLACK_MIN_DURATION=30 # 30 days
BLACK_ENROLLMENT_ACTIONS = [BLACK_ENROLL_ACTION, BLACK_WITHDRAW_ACTION, BLACK_UPDATE_ACTION]

ALLOWED_BLACK_SEGMENTS = ["bk1","bk2"]
UPGRADE_TYPE_KEY = "upgradeType"
ROOM_UPGRADE_GRAMMAR_TEXT = "Complimentary Guaranteed Room Upgrade is available"
MEAL_UPGRADE_GRAMMAR_TEXT = "Complimentary Guaranteed Meal Upgrade is available"
ROOM_UPGRADE_FROM = "fromRoom"
ROOM_UPGRADED_TO = "toRoom"
MEAL_UPGRADED_FROM = "fromMeal"
MEAL_UPGRADED_TO = "toMeal"

INDIAN_TIMEZONE = 'Asia/Kolkata'
LOCAL_TIMEZONE = 'Asia/Calcutta'

DEFAULT_LOG_IDENTIFIER_KEY_DICT = {
    "default": ["hotelcode", "hotel_code", "roomcode", "room_code", "roomtypecode", "room_type_code", "rateplancode",
                "rate_plan_code"],
    "provisional_booking": ["sellerbookingid", "hotelcode", "roomcode", "rateplancode", "is_rtb", "segment_code",
                            "sales_category", "booking_vendor_name", "offline_booking", "parent_vendor_booking_id",
                            "brand", "checkintime", "rateplan_duration"],
    "confirm_booking": ["probookingid", "sellerbookingid", "nr_flag", "old_booking_id", "is_frn_eligible",
                        "parent_vendor_booking_id","old_booking_info"],
    "hotel_cancel": ["bookingid", "override_cancel_charges", "override_cancel_charges_percentage"],
    "interim_booking": ["probookingid"]
}

RESELLER_BOOKING_ID = "reseller_booking_id"
RESELLER_TARIFF_ID = "reseller_tariff_id"
PREBUY_ORDER_ID = "prebuyOrderId"
REFERENCE_RATE_INFO = "referenceRateInfo"
HOTELCLOUD_VENDOR_BOOKING_ID = "hotelCloudVendorBookingId"
HOTELCLOUD_PARENT_VENDOR_BOOKING_ID = "hotelCloudParentVendorBookingId"
HOTELCLOUD_CONFIRM_BOOKING_ID = "hotelCloudConfirmBookingId"
HOTELCLOUD_CANCEL_BOOKING_ID = "hotelCloudCancelBookingId"
REFERENCE_BASE_RATE = "baseRate"
REFERENCE_BASE_RATE_DAYWISE = "baseRateDayWise"
REFERENCE_RATE_KEY = "rateKey"
REFERENCE_OFFER_CODES = "offerCodes"
REFERENCE_HCP_CODE = "hcpCode"
REFERENCE_COMMISSION_RATE = "commissionRate"
REFERENCE_SELL_RATE = "totalSellRate"
PARENT_OCCUPANCY_LIST = "parentOccupancyList"
BOOKING_META_DATA = "bookingMetaData"
GUARANTEED_INCLUSIONS = "guaranteedInclusions"
SPOTLIGHT = "SPOTLIGHT"

B2C_REFERENCE_RATE_INFO = "b2cReferenceRateInfo"
B2C_REFERENCE_ORIGINAL_RATE_DAYWISE= "originalRateDayWise"
B2C_REFERENCE_COMMISSION_RATE_DAYWISE= "commissionDayWise"
B2C_REFERENCE_TAX_DAYWISE= "taxDayWise"
B2C_REF_TOTAL_CHARGES= "totalCharges"


MAX_LENGTH_OF_BOOKING_NAME_IN_ENCRYPTION = 105
POST_FIX_OF_BOOKING_NAME_IN_ENCRYPTION = "..."

EVENT_NAME = "event_name"
ANALYTICS_PACKET = "analytics_packet"
AGENTS = "Agents"
CORPORATE = "Corporate"
MY_BIZ = "MyBiz"
MY_PARTNER = "MyPartner"
MY_BIZ_CAMEL_CASE = "myBiz"
MY_PARTNER_CAMEL_CASE = "myPartner"


HOST_PROFILE_MODEL_NAME = "HostProfile"

CATEGORY_MAP = {
    AGENTS: CORPORATE
}

SALES_CHANNEL_PASCAL_TO_CAMEL_CASE_MAP = {
    MY_BIZ: MY_BIZ_CAMEL_CASE,
    MY_PARTNER: MY_PARTNER_CAMEL_CASE
}

# Voucher related Constants
VOUCHER_BOOKING = "BOOKING"
VOUCHER_CANCELLATION = "CANCELLATION"
INGO_CLIENT_FOR_VOUCHER = "INGO"


# Constants for Derby pull to push migration
RESTART_NEXUS_PARTNER_CONTAINER = "restartNexusPartnerContainer"
UPDATE_HOTEL_DATA_SOURCE_ID_TO_CONTRACTED = "updateHotelDataSourceToContracted"
PROCESS_BEFORE_DERBY_DOORWAY_INACTIVE = "processBeforeDerbyDoorwayInactive"
LIVE_PROCESS_AFTER_DJANGO_UPLOAD = "liveProcessAfterDjangoUpload"


DERBY_MIGRATION_ACTION_TYPES = [RESTART_NEXUS_PARTNER_CONTAINER,UPDATE_HOTEL_DATA_SOURCE_ID_TO_CONTRACTED,PROCESS_BEFORE_DERBY_DOORWAY_INACTIVE,LIVE_PROCESS_AFTER_DJANGO_UPLOAD ]
FBP_OFFER_CATEGORY = "fbp"  ## Offer Category name for FBP promotions
