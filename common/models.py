import json
import socket
import os
import hashlib

from PIL import Image as pil_image
from django.conf import settings
from ingouser.models import Use<PERSON><PERSON><PERSON><PERSON>, User
from django.db.models import Manager
from django.contrib.contenttypes.fields import GenericF<PERSON>ign<PERSON><PERSON>, GenericRelation
from django.contrib.contenttypes.models import ContentType
from django.core.cache import cache
from django.core.exceptions import ObjectDoesNotExist, ValidationError
from django.core.urlresolvers import reverse
from django.db import models
from django.utils.translation import ugettext_lazy as _
from image_cropping.fields import ImageRatioField, ImageCropField
from json_field import <PERSON><PERSON><PERSON><PERSON>
from timezone_field import <PERSON>ZoneField
import traceback

from api.v1.templates.resources.resources import construct_template_code
from api.v2.campaigns.resources.constants import CM_DISC_TYPE, CM_MAPPING_STATUS, CM_TYPE, CM_DISCOUNT_TYPE, CM_LEVEL, \
    DAYWISE_CONFIG_META_DATA_FIELDS, CAMPAIGN_ENTITY_MAPPING_FLAG_ONE_DICT
from common import commonchoice
from common.campaign_manager.campaign_metadata_config import populate_meta_fields_to_cmp_master, \
    CAMPAIGN_RELATED_META_FIELDS, TYPE_MAPPINGS_CAMPAIGN_RELATED_META_FIELDS
from common.commonchoice import ContactTypes, FakeDetailTypes, FakeDetailStatus
from common.campaign_manager.model_signals import execute_handler_function, push_notification, \
    push_campaign_related_info
from common.commonhelper import validate_manager_mapping_object
from common.constants import CONTRACTTYPE, ADDITIONALCONTRACTTYPE, POPULAR_BANKS_LIST, API_CLIENTS, THIRD_PARTY_API
from common.locus import Locus
from common.methods import CommonMethods
from hotels import hotelchoice
from hotels.hotelchoice import PROPERTY_CATEGORY
from hotels.post_save_connector import push_static_content
from hotels.push_video_service import push_video_to_processing_service
from lib.MDBStorage import MDBStorage
from lib.aes_encryption.helpers import calculate_max_length
from lib.check_permission import has_permission
from lib.current_user.models import CurrentUserField
from lib.fields import MultiSelectField, MultiEmailField
from lib.aes_encryption.signals import register_model_signal
from lib.validators import *
from utils.logger import Logger
from lib.models import Model as INGOModel
from hotels.models.flagbit_converter import populate_flag_info_to_obj, compute_flag_info_for_obj, \
    populate_json_fields_to_obj
from hotels.DuplicateShell.decorators import skip_post_save_signal
from django.core.cache import cache, caches
from django.db.models import Q
from ingo_partners.constants.common_constants import TEST_CITY_NAME, TEST_LOCUS_CODE
from ingouser.models import User
PAYMENT_CACHE = caches['payment_cache']

#### Synxis phase 2 ####
from hotels.models.source_config import SourceConfig

#### Synxis phase 2 ####
logger_stats = Logger(logger="inventoryLogger")
hostname = socket.gethostname()
inventory_logger = Logger(logger='inventoryLogger')


# Create your models here.
class ItemNote(models.Model):
    notes = models.TextField(_('Note'))
    user = CurrentUserField(verbose_name=_('User'))
    content_type = models.ForeignKey(ContentType, verbose_name=_('Related Object Type'))
    object_id = models.PositiveIntegerField(db_index=True)
    content_object = GenericForeignKey('content_type', 'object_id')
    createdon = models.DateTimeField(_('Created On'), auto_now_add=True, db_index=True)
    modifiedon = models.DateTimeField(_('Modified On'), auto_now=True, db_index=True)

    def __unicode__(self):
        return str(self.createdon)

    class Meta:
        verbose_name_plural = _('Notes')
        verbose_name = _('Note')


class HistoryItem(models.Model):
    note = models.TextField()
    is_success = models.BooleanField(default=False)
    user = CurrentUserField()
    content_type = models.ForeignKey(ContentType)
    object_id = models.PositiveIntegerField(db_index=True)
    content_object = GenericForeignKey('content_type', 'object_id')
    createdon = models.DateTimeField(auto_now_add=True, db_index=True)
    modifiedon = models.DateTimeField(auto_now=True, db_index=True)

    def __unicode__(self):
        return str(self.createdon)


class UserLoginHistory(models.Model):
    user = CurrentUserField()
    createdon = models.DateTimeField(auto_now_add=True, db_index=True)
    modifiedon = models.DateTimeField(auto_now=True, db_index=True)
    details = models.TextField()

    def __unicode__(self):
        return str(self.createdon)


class UserActivity(models.Model):
    activity_date = models.DateField(db_index=True)
    days = models.PositiveIntegerField()
    activity_type = models.CharField(_('Activity Type'), max_length=10, db_index=True,
                                     choices=commonchoice.UserActivityType)
    users = models.PositiveIntegerField()
    hotels = models.PositiveIntegerField()
    cm_hotels = models.PositiveIntegerField()
    createdon = models.DateTimeField(auto_now_add=True, db_index=True)

    class Meta:
        unique_together = ('activity_date', 'days', 'activity_type')

    def __unicode__(self):
        return str(self.createdon)


class VideoLink(models.Model):
    isactive = models.BooleanField(_('Active Flag'), default=True)
    content_type = models.ForeignKey(ContentType)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')
    videoid = models.CharField(_('Video Id'), max_length=250, null=True)
    videolink = models.CharField(_('Embed Video Code'), max_length=500, null=True, blank=True)
    videosource = models.CharField(_('Video Source'), max_length=250, null=True)
    title = models.CharField(_('Video Title'), max_length=200, null=True)
    description = models.TextField(_('Video Description'), null=True, blank=True)
    user = CurrentUserField()
    createdon = models.DateTimeField(_('Added On'), auto_now_add=True)
    modifiedon = models.DateTimeField(_('Modified On'), auto_now=True)

    def __unicode__(self):
        return self.title

    class Meta:
        verbose_name = _('Video Link')
        verbose_name_plural = _('Video Links')


class Image(models.Model):
    APPROVED = 'approved'
    REJECTED = 'rejected'
    PENDING = 'pending'
    MODERATION_STATUS = (
        (APPROVED, 'Approved'),
        (REJECTED, 'Rejected'),
        (PENDING, 'Pending')
    )

    IMAGE_CLEAN_STAY_TAGS = ['Safety and Hygiene']
    IMAGE_FOOD_CONTENT_TAGS = ['Restaurant/cafe', 'Food', 'Food Menu', 'Beverage Menu']

    # with ref to http://jira.mmt.mmt/browse/INGO-6734
    SUPER_PREMIUM_IMAGE_TAGS = ['Experiences', 'Signature Amenity']

    isactive = models.BooleanField(_('Active Flag'), default=False)
    content_type = models.ForeignKey(ContentType)
    watermark = MultiSelectField(_('WaterMark Type'), max_length=10, null=True, blank=True,
                                 choices=commonchoice.IMAGE_FOR)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')
    image = ImageCropField(verbose_name=_('Actual Image'), upload_to="mdb", null=True, storage=MDBStorage(),
                           max_length=1000, help_text=_('Image size must be less than 12 MB.'))
    cropping_rect = ImageRatioField('image', '639x426', allow_fullsize=True, size_warning=True,
                                    verbose_name=_('Wide Image Cropping'))
    cropping_sqr = ImageRatioField('image', '350x350', allow_fullsize=True, size_warning=True,
                                   verbose_name=_('Square Image Cropping'))
    caption = models.CharField(_('Image Caption'), max_length=200, null=True, blank=True)
    description = models.TextField(_('Image Description'), null=True, blank=True)
    img_src = models.CharField(_('Image Source'), max_length=25, null=True)
    img_order = models.PositiveSmallIntegerField(_('Image Order'), null=True, help_text="Set '0' for Main Image.")
    image_rect = models.FileField(verbose_name=_('Wide Image'), upload_to="mdb", null=True, blank=True,
                                  storage=MDBStorage(), max_length=1000)
    image_sqr = models.FileField(verbose_name=_('Square Image'), upload_to="mdb", null=True, blank=True,
                                 storage=MDBStorage(), max_length=1000)
    image_thumb = models.FileField(verbose_name=_('Thumbnail'), upload_to="mdb", null=True, blank=True,
                                   storage=MDBStorage(), max_length=1000)
    user = CurrentUserField()
    createdon = models.DateTimeField(_('Added On'), auto_now_add=True)
    modifiedon = models.DateTimeField(_('Modified On'), auto_now=True)
    width = models.PositiveSmallIntegerField(_('Image Width'), null=True)
    height = models.PositiveSmallIntegerField(_('Image Height'), null=True)
    status = models.CharField(_('Approval status'), max_length=10, null=True, db_index=True,
                              choices=MODERATION_STATUS, default=PENDING,
                              help_text='Make sure you are authorised to moderate the image \
                                        and you have verified the image before marking it \
                                        as Rejected or Approved')
    reject_reason = models.CharField(_('Reject Reason'), max_length=200, null=True,
                                     blank=True, help_text='Please mention a reason if you are \
                                     rejecting the image. A rejected image will be automatically \
                                     deactivated.', choices=commonchoice.IMAGE_REJECT_REASON)

    mmt_img_id = None
    tags = models.CharField(_('User Tags'), max_length=200, null=True)
    auto_tags = models.CharField(_('Auto-moderation Tags'), max_length=200, null=True)
    is_auto_moderated = models.BooleanField(_('Auto Moderation Flag'), default=False)

    image_hash = models.CharField(max_length=50)
    image_scores = JSONField(decoder_kwargs={'cls': json.JSONDecoder})
    vendor_img_id = models.BigIntegerField(null=True, blank=True)
    image_size = models.CharField(_('Size'), max_length=200, null=True, blank=True)  # Convert to Floating

    ### Synxis phase 2 ####
    source_config = models.ForeignKey(SourceConfig, null=True, blank=True, db_column='source_config')

    ### Synxis phase 2 ####

    def __init__(self, *args, **kwargs):
        super(Image, self).__init__(*args, **kwargs)
        self._status = self.status

    def save(self, *args, **kwargs):
        if str(self.image) == 'Error : Internal Error':
            raise Exception('Internal Error occurred while uploading image')

        #checking if width and height of image are NULL
        if not self.height or not self.width:
            inventory_logger.error("Width and Height for image are null (pre-save) for image_id: %s",self.id)
        if self.pk:
            if kwargs.get('using', None):
                db_obj = Image.objects.using('default').get(id=self.pk)
            else:
                db_obj = Image.objects.get(id=self.pk)
            from hotels.models import HotelDetail
            hotel_content_type = ContentType.objects.get_for_model(HotelDetail)


            if self.status != db_obj.status and self.status == self.APPROVED and self.isactive:
                from api.v2.hotels.resources.make_hotel_details import SPACEDETAIL_CONTENT_TYPE
                generic_image_objs_list = GenericImageMapping.objects.filter(image_id=db_obj.id,
                                                                             content_type_id=SPACEDETAIL_CONTENT_TYPE,
                                                                             is_active=True).values_list("object_id",
                                                                                                         flat=True)
                if len(generic_image_objs_list) > 0:
                    from hotels.models.hoteldetail import SpaceDetail
                    from common.commonchoice import SPACE_CHOICES
                    from hotels.models import HotelDetail, HotelMetaData
                    from hotels.models.roomdetail import GenericRoomMapping, RoomDetail
                    space_objs = SpaceDetail.objects.filter(id__in=generic_image_objs_list,
                                                            status=SPACE_CHOICES.SPACE_STATUS_DRAFT, is_active=True)
                    if space_objs.count() > 0:
                        meta_data_obj = HotelMetaData.objects.get(hotel_id=space_objs[0].hotel.id)
                        hotel_entire = True if meta_data_obj.property_type_meta == "entire" else False
                    for space_obj in space_objs:
                        space_obj.status = SPACE_CHOICES.SPACE_STATUS_PUBLISHED
                        space_obj.save()
                        from api.v2.amenities.resources.amenities_resources import create_or_update_mapped_property_amenity
                        create_or_update_mapped_property_amenity(space_obj)
                        if not hotel_entire:
                            mapped_room = GenericRoomMapping.objects.filter(object_id=space_obj.id,
                                                                            content_type_id=SPACEDETAIL_CONTENT_TYPE)
                            if mapped_room.count() > 0:
                                mapped_room_ins = mapped_room[0].room
                                mapped_room_ins.isactive = True
                                mapped_room_ins.save()
                        else:
                            mapped_room_ins = RoomDetail.objects.filter(hotel_id=space_obj.hotel.id).first()
                            if not mapped_room_ins.isactive:
                                mapped_room_ins.isactive = True
                                mapped_room_ins.save()

                # upgrade to new layout of hostweb
                if self.content_type_id == hotel_content_type.id:
                    hotel_object = self.content_object
                    from scripts.hostapp_migration.migration_settings import allowed_hw_migration_hotel_types
                    if hotel_object.hoteltype in allowed_hw_migration_hotel_types and not hotel_object.image_new_layout:
                        from hotels.tasks import set_upgradation_flag
                        set_upgradation_flag.apply_async(args=(hotel_object.id, "Image Approval"), countdown=300)

        return super(Image, self).save(*args, **kwargs)

    def clean(self):
        if self.image:
            from hotels.models.user_management import HostProfile
            if not self.content_type == ContentType.objects.get_for_model(HostProfile):
                hotel_image = pil_image.open(self.image)
                (hotel_image_width, hotel_image_height) = hotel_image.size
                dimension_ratio = float(hotel_image_width) / float(hotel_image_height)
                if dimension_ratio < 1 or dimension_ratio > 2.2:
                    raise ValidationError(_('Invalid Image Dimensions'))

    def __unicode__(self):
        return self.image.name

    def url(self):
        from django.conf import settings
        return settings.CDN_DOMAIN + str(self.image)

    def thumb_url(self):
        from django.conf import settings
        return settings.CDN_DOMAIN + str(self.image_thumb)

    def thumb_url_resized(self):
        return 'https://cdn1.goibibo.com/voy_ing/t_srp/' + str(self.image)

    def thumb_square_url_resized(self):
        return 'https://cdn1.goibibo.com/voy_ing/t_g/' + str(self.image)

    def thumb_rect_url_resized(self):
        return 'https://cdn1.goibibo.com/voy_ing/t_g/' + str(self.image)

    def content_link(self):
        try:
            if self.content_object.admin_url():
                return '<a target="__blank" href="' + str(self.content_object.admin_url()) + '">Image Link</a>'
            else:
                return '<a target="__blank" href="../../r/' + str(self.content_type.id) + '/' + str(
                    self.object_id) + '/">Image Link</a>'
        except:
            return None

    content_link.short_description = 'Link (related to)'
    content_link.admin_order_field = 'content_type'
    content_link.allow_tags = True

    def image_link(self):
        try:
            return '<a target="_blank" href="' + self.url() + '"><img src="' + self.url() + '" height="100" /></a>'
        except:
            return None

    image_link.short_description = 'Image'
    image_link.admin_order_field = 'content_type'
    image_link.allow_tags = True

    def related_to(self):
        return str(self.content_type) + ': ' + str(self.content_object)

    related_to.short_description = 'Image (related to)'
    related_to.admin_order_field = 'content_type'

    def get_absolute_url(self):
        return reverse('admin:common_image_change', args=(self.id,))

    def get_hotel(self):
        from hotels.models import HotelDetail, RoomDetail
        hotel_content_type = ContentType.objects.get_for_model(HotelDetail)
        roomdetail_content_type = ContentType.objects.get_for_model(RoomDetail)

        if self.content_type_id == hotel_content_type.id:
            return self.content_object
        elif self.content_type_id == roomdetail_content_type.id:
            return self.content_object.hotel
        else:
            # Image might be related to Vendor or some other model
            return None

    def get_hotel_ids_by_object(self):
        result = self.get_hotel()
        return [result.hotelcode] if result else []


@skip_post_save_signal
def update_img_onvoyager(sender, **kwargs):
    """auto push image changes records to voyager if user will click on save button"""
    try:
        from hotels.tasks import push_hotel_to_voyager
        from hotels.models import HotelDetail, RoomDetail
        from hotels.methods import HotelMethods
        image_model = kwargs.get('instance')
        if image_model.content_type.name == 'Host Profile':
            return
        object_id = image_model.object_id
        model_name = image_model.content_type.name
        if image_model and kwargs.get('created') and has_permission(image_model.user, 'custom_moderate_image_mmt'):
            image_model.status = 'approved'
            image_model.isactive = True
            approve_image = Image.objects.filter(id=image_model.id)
            approve_image.update(status='approved', isactive=True)
            hotel_methods = HotelMethods()
            hotel_methods.updateLogMsg(image_model.user, image_model, 'Auto approved')
        if settings.VOYAGER_FLAG and not settings.DEBUG:
            hotelobj = ''
            if model_name == "Hotel Detail":
                hotelobj = HotelDetail.objects.filter(id=object_id).using('default')[0]
            elif model_name == "Room Detail":
                hotelobj = RoomDetail.objects.filter(id=object_id).using('default')[0].hotel
            if hotelobj:
                push_hotel_to_voyager.apply_async(args=(hotelobj, "update_img_onvoyager",), )
    except Exception as e:
        logger_stats.error(str(e), log_type="hotel_image", bucket="voyager_update", stage="update image on voyager")


@skip_post_save_signal
def image_auto_moderation(sender, **kwargs):
    try:
        image_object = kwargs.get('instance')
        is_newly_created = kwargs.get('created', False)
        push_image_to_auto_moderation(image_object, is_newly_created)
    except Exception as e:
        logger_stats.error(str(e), log_type="hotel_image", bucket="image_auto_moderation",
                           stage="image auto moderation")


def push_image_to_auto_moderation(image_object, is_newly_created):
    from common.task import push_image_to_moderation_task, push_image_to_kafka_moderation_task
    from ingo_partners.resources.configure import get_config_data
    from hotels.models import helper as HotelHelper
    from hotels.models import RoomDetail
    from hotels.models import configuration as HotelConf

    configs_dict = get_config_data(value_key=True)

    # IC-914: Skipping Image Auto Moderation for below tagged images.
    if image_object.status == 'pending' and is_newly_created and (image_object.is_auto_moderated is False) \
            and settings.IMAGE_AUTO_MODERATION_FLAG and (
            image_object.source_config_id == configs_dict['ingo'] or image_object.source_config_id is None):
        caretaker_content_type = ContentType.objects.get_for_model(Caretaker)

        if image_object.tags and any(tag in image_object.tags for tag in (Image.IMAGE_CLEAN_STAY_TAGS +
                                                                          Image.IMAGE_FOOD_CONTENT_TAGS)):
            logger_stats.info(
                "Image auto moderation skipped for image id {}, newly_created: {}, is_auto_moderated: {}".format(
                    image_object.id, is_newly_created, image_object.is_auto_moderated),
                log_type="hotel_image", bucket="image_auto_moderation", stage="image auto moderation")
            push_hotel_image_to_gmp(image_object)
        elif image_object.content_type and image_object.content_type.name == 'Host Profile':
            from hotels.push_moderation_data_to_kafka import push_image_to_gmp_moderation
            push_image_to_gmp_moderation('image_moderation', image_object)
        elif image_object.content_type and image_object.content_type_id == caretaker_content_type.id:
            from hotels.push_moderation_data_to_kafka import push_caretaker_image_to_gmp_moderation
            push_caretaker_image_to_gmp_moderation('image_moderation', image_object)
        else:
            # push_image_to_moderation_task.apply_async(args=(image_object, 'image_auto_moderation',), countdown=10)
            #push_image_to_kafka_moderation_task.apply_async(args=(image_object, 'image_auto_moderation',),countdown=10)
            if not image_object.is_auto_moderated:
                push_images_to_picasso(image_object)
            else:
                logger_stats.info(
                    message="Image already moderated, skipping Picasso push for image id %s" % image_object.id,
                    log_type="hotel_image",
                    bucket="image_auto_moderation",
                    stage="push_images_to_picasso"
                )

    else:
        logger_stats.info(
            "Image auto moderation skipped for image id {}, newly_created: {}, is_auto_moderated: {}, source_config: {}".format(
                image_object.id, is_newly_created, image_object.is_auto_moderated, image_object.source_config_id),
            log_type="hotel_image", bucket="image_auto_moderation", stage="image auto moderation")


def push_images_to_picasso(image_object):
    try:
        from hotels.picassoService.picasso_service_client import PicassoServiceClient
        picasso_service_client = PicassoServiceClient()

        # Get all hotel_ids from image_object

        hotel_id = image_object.get_hotel().id

        if not hotel_id:
            raise Exception("No hotel IDs found for image object")

        request_data = {hotel_id: [image_object.id]}

        log_data = {
            'api_specific_identifiers': {
                'hotel_ids': hotel_id,
                'image_ids': [image_object.id],
                'message': 'Pushing images to Picasso for moderation'
            },
            'error': {},
            'request_id': ''
        }

        logger_stats.info(
            message='Request data being sent to Picasso: %s' % request_data,
            log_type="hotel_image",
            bucket="image_auto_moderation",
            stage="push_images_to_picasso",
            identifier="%s" % log_data
        )

        response_msg = picasso_service_client.push_images_to_picasso_for_auto_moderation(request_data)

        if not response_msg.success:
            error_detail = response_msg.errorDetail

            logger_stats.error(
                message="Image push to picasso failed for image id %s with error: %s" % (image_object.id, error_detail),
                log_type="hotel_image",
                bucket="image_auto_moderation",
                stage="push_images_to_picasso",
                identifier="%s" % error_detail
            )

        log_data['api_specific_identifiers']['response'] = response_msg

        logger_stats.info(
            message='Response data from Picasso: %s' % response_msg,
            log_type="hotel_image",
            bucket="image_auto_moderation",
            stage="push_images_to_picasso",
            identifier="%s" % log_data
        )

    except Exception as e:
        error_data = {
            'api_specific_identifiers': {
                'image_id': image_object.id,
                'hotel_ids': hotel_id if 'hotel_ids' in locals() else None,
                'message': 'Failed to push image to Picasso'
            },
            'error': {
                'error_message': str(e),
                'traceback': repr(traceback.format_exc())
            },
            'request_id': ''
        }

        logger_stats.error(
            message="Image push to picasso failed for image id %s with exception: %s" % (image_object.id, str(e)),
            log_type="hotel_image",
            bucket="image_auto_moderation",
            stage="push_images_to_picasso",
            identifier="%s" % error_data
        )


def push_hotel_image_to_gmp(image_object):
    try:
        from hotels.models.hoteldetail import HotelDetail, SpaceDetail
        from hotels.models.roomdetail import RoomDetail
        from hotels.models import helper as hotel_helper
        from hotels.models import RoomDetail
        from hotels.models import configuration as hotel_conf

        hotel_content_type = ContentType.objects.get_for_model(HotelDetail)
        room_content_type = ContentType.objects.get_for_model(RoomDetail)

        # Excluding hotel & Room for GMP properties
        hotel_code_include = settings.GMP_EXTRA_CONFIGS['IMAGE_ENHANCED_MODERATION']['MODERATION_HOTEL_LIST']
        hotel_ids_include = [hotel_helper.get_id_from_code(hotelcode, hotel_conf.HotelCodeLength,
                                                           hotel_conf.HotelCodePrefix) for hotelcode in
                             hotel_code_include]
        room_ids_include = list(
            RoomDetail.objects.filter(hotel_id__in=hotel_ids_include).values_list('id', flat=True))

        hotel = None
        # find the image associated hotels type
        if image_object.content_type_id == hotel_content_type.id:
            hotel = HotelDetail.objects.get(id=image_object.object_id)
        elif image_object.content_type_id == room_content_type.id:
            hotel = RoomDetail.objects.select_related('hotel').get(id=image_object.object_id).hotel

        if image_object.content_type and (
                # hotel image for a hotel listed in MODERATION_HOTEL_LIST
                (
                        image_object.content_type_id == hotel_content_type.id and image_object.object_id in hotel_ids_include) or
                # room image for a hotel listed in MODERATION_HOTEL_LIST
                (image_object.content_type_id == room_content_type.id and image_object.object_id in room_ids_include) or
                # hotel or room image for a hotel with type listed in MODERATION_HOTEL_TYPES
                (hotel is not None and hotel.hoteltype in settings.GMP_EXTRA_CONFIGS['IMAGE_ENHANCED_MODERATION'][
                    'MODERATION_HOTEL_TYPES'])
        ):
            from hotels.tasks import push_hotel_image_to_gmp_task
            push_hotel_image_to_gmp_task.apply_async(args=(image_object.id,), countdown=10)
        else:
            logger_stats.info(
                "Image GMP moderation push skipped for image id {}, hotel_type: {}, image_content_type: {}".format(
                    image_object.id, hotel.hoteltype if hotel else "no hotel", image_object.content_type_id),
                log_type="hotel_image", bucket="image_auto_moderation", stage="gmp push")
    except Exception as e:
        logger_stats.info(
            "Image GMP moderation push failed for image id {} with exception: {}".format(image_object.id, str(e)),
            log_type="hotel_image", bucket="image_auto_moderation", stage="gmp push")


@skip_post_save_signal
def create_image_rec_sqr_thumbnails(sender=None, **kwargs):
    try:
        from common.task import crop_image_to_different_sizes
        image_object = kwargs.get('instance')
        is_newly_created = kwargs.get('created', False)
        if is_newly_created:
            crop_image_to_different_sizes.apply_async(args=(image_object.id, 'create_image_rec_sqr_thumbnails',),
                                                      countdown=10)
    except Exception as e:
        logger_stats.error(str(e), log_type="hotel_image", bucket="create_image_rec_sqr_thumbnails",
                           stage="crop image creation")


@skip_post_save_signal
def send_communications(sender, instance, created, **kwargs):
    try:
        # If image status is changed from pending to approve/rejected then we will send communications through Sandesh
        if instance._status != Image.PENDING or instance.status not in [Image.APPROVED, Image.REJECTED]:
            return

        from hotels.models.user_management import HostProfile
        host_profile_content_type = ContentType.objects.get_for_model(HostProfile)
        if instance.content_type == host_profile_content_type:
            user = HostProfile.objects.get(pk=instance.object_id).user

            if instance.status == Image.APPROVED:
                from communication.tasks import send_user_image_accepted_notification
                send_user_image_accepted_notification.apply_async(args=(user.get_full_name(), user.email))

            if instance.status == Image.REJECTED:
                from communication.tasks import send_user_image_rejected_notification
                send_user_image_rejected_notification.apply_async(
                    args=(user.get_full_name(), user.email, instance.reject_reason))

        caretaker_content_type = ContentType.objects.get_for_model(Caretaker)
        if instance.content_type == caretaker_content_type:
            caretaker = Caretaker.objects.get(pk=instance.object_id)
            if instance.status == Image.APPROVED:
                from communication.tasks import send_caretaker_image_accepted_notification
                send_caretaker_image_accepted_notification.apply_async(args=(caretaker.name, caretaker.object_id))
            if instance.status == Image.REJECTED:
                from communication.tasks import send_caretaker_image_rejected_notification
                send_caretaker_image_rejected_notification.apply_async(
                    args=(caretaker.name, caretaker.object_id, instance.reject_reason))

    except Exception as e:
        logger_stats.error(str(e), log_type="hotel_image", bucket="send_communications", stage="send-communications")


if settings.VOYAGER_OLD_STATIC_CONTENT_PIPELINE_FLAG:
    models.signals.post_save.connect(update_img_onvoyager, Image)

models.signals.post_save.connect(image_auto_moderation, Image)
models.signals.post_save.connect(create_image_rec_sqr_thumbnails, Image)
models.signals.post_save.connect(send_communications, Image)


class Video(models.Model):
    APPROVED = 'approved'
    REJECTED = 'rejected'
    PENDING = 'pending'
    MODERATION_STATUS = (
        (APPROVED, 'Approved'),
        (REJECTED, 'Rejected'),
        (PENDING, 'Pending')
    )

    is_active = models.BooleanField(_('Active Flag'), default=False)
    content_type = models.ForeignKey(ContentType)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')
    source_config = models.ForeignKey(SourceConfig, null=True, blank=True, db_column='source_config')
    video_url = models.TextField(_('Video URL'))
    video_thumbnail = models.TextField(_('Video thumbnail URL'), null=True)
    width = models.PositiveSmallIntegerField(_('Video Width'), null=True)
    height = models.PositiveSmallIntegerField(_('Video Height'), null=True)
    meta_data = JSONField(null=True, blank=True)
    status = models.CharField(_('Approval status'), max_length=10, null=True, db_index=True,
                              choices=MODERATION_STATUS, default=PENDING,
                              help_text='Make sure you are authorised to moderate the videos \
                                        and you have verified the videos before marking it \
                                        as Rejected or Approved')
    reject_reason = models.CharField(_('Reject Reason'), max_length=500, null=True,
                                     blank=True, help_text='Please mention a reason if you are \
                                     rejecting the videos. A rejected videos will be automatically \
                                     deactivated.', choices=commonchoice.IMAGE_REJECT_REASON)
    video_size = models.CharField(_('Size'), max_length=200, null=True)  # Convert to Floating
    tags = models.CharField(_('Tags'), max_length=200, null=True, blank=True)
    caption = models.CharField(_('Video Caption'), max_length=200, null=True, blank=True)
    description = models.TextField(_('Video Description'), null=True, blank=True)
    is_auto_moderated = models.BooleanField(_('Auto Moderation Flag'), default=False)
    video_format = models.CharField(_('Video Format'), max_length=100, null=True)
    video_source = models.CharField(_('Video Source'), max_length=25, null=True)
    video_source_id = models.CharField(_('Video Source Id'), max_length=100, null=True)
    video_order = models.PositiveSmallIntegerField(_('Video Order'), null=True, help_text="Set '0' for Main Image.")
    user = CurrentUserField()
    created_on = models.DateTimeField(_('Added On'), auto_now_add=True)
    modified_on = models.DateTimeField(_('Modified On'), auto_now=True)

    def url(self):
        return settings.VIDEO_API_CONFIG['CDN_DOMAIN'] + self.video_url

    def thumbnail(self):
        return settings.VIDEO_API_CONFIG['CDN_DOMAIN'] + self.video_thumbnail

    def get_hotel(self):
        from hotels.models import HotelDetail, RoomDetail
        hotel_content_type = ContentType.objects.get_for_model(HotelDetail)
        roomdetail_content_type = ContentType.objects.get_for_model(RoomDetail)

        if self.content_type_id == hotel_content_type.id:
            return self.content_object
        elif self.content_type_id == roomdetail_content_type.id:
            return self.content_object.hotel
        else:
            # Image might be related to Vendor or some other model
            return None

    def get_hotel_ids_by_object(self):
        result = self.get_hotel()
        return [result.hotelcode] if result else []


@skip_post_save_signal
def video_moderation(sender=None, **kwargs):
    try:
        #from hotels.push_moderation_data_to_kafka import push_video_to_moderation
        video_object = kwargs.get('instance')
        is_newly_created = kwargs.get('created', False)

        if video_object.status == 'pending' and is_newly_created and not video_object.is_auto_moderated and \
                settings.VIDEO_API_CONFIG['VIDEO_MODERATION_FLAG']:
            #push_video_to_moderation('video_moderation', video_object)
            push_video_to_picasso_for_moderation(video_object)
        else:
            logger_stats.info("Video moderation skipped for video id " + str(video_object.id),
                              log_type="hotel_video", bucket="video_moderation", stage="video moderation")

    except Exception, e:
        logger_stats.error(str(e), log_type="hotel_video", bucket="video_moderation",
                           stage="video moderation")

def push_video_to_picasso_for_moderation(video_object):
    try:
        from hotels.picassoService.picasso_service_client import PicassoServiceClient
        from api.v2.users.hosts.helper import get_hotel_id_from_code
        picasso_service_client = PicassoServiceClient()

        # Get all hotel_ids from image_object
        hotel_id = video_object.get_hotel().id

        if not hotel_id:
            raise Exception("No hotel IDs found for image object")

        request_data = {hotel_id: [video_object.id]}

        log_data = {
            'api_specific_identifiers': {
                'hotel_ids': hotel_id,
                'image_ids': [video_object.id],
                'message': 'Pushing videos to Picasso for gmp moderation'
            },
            'error': {},
            'request_id': ''
        }

        logger_stats.info(
            message='Request data being sent to Picasso: %s' % request_data,
            log_type="hotel_video",
            bucket="video_moderation",
            stage="push_video_to_picasso_for_moderation",
            identifier="%s" % log_data
        )
        response_msg = picasso_service_client.push_videos_to_picasso_for_gmp(request_data)

        hotel_response = response_msg.get(hotel_id, {})

        if not hotel_response.get('success', False):
            error_msg = hotel_response.get('message', 'Unknown error')
            logger_stats.error(
                message="Video push to picasso failed for video id %s with error: %s" % (
                    video_object.id, error_msg),
                log_type="hotel_video",
                bucket="video_moderation",
                stage="push_video_to_picasso_for_moderation",
                identifier="%s" % error_msg
            )
            return

        log_data['api_specific_identifiers']['response'] = response_msg

        logger_stats.info(
            message='Response data from Picasso: %s' % response_msg,
            log_type="hotel_video",
            bucket="video_moderation",
            stage="push_video_to_picasso_for_moderation",
            identifier="%s" % log_data
        )

    except Exception as e:
        error_data = {
            'api_specific_identifiers': {
                'video_id': video_object.id,
                'hotel_id': hotel_id if 'hotel_id' in locals() else None,
                'message': 'Failed to push video to Picasso'
            },
            'error': {
                'error_message': str(e),
                'traceback': repr(traceback.format_exc())
            },
            'request_id': ''
        }

        logger_stats.error(
            message="Video push to picasso failed for image id %s with exception: %s" % (video_object.id, str(e)),
            log_type="hotel_video",
            bucket="video_moderation",
            stage="push_video_to_picasso_for_moderation",
            identifier="%s" % error_data
        )

@skip_post_save_signal
def update_video_on_voyager(sender, **kwargs):
    """auto push video changes records to voyager if user will click on save button"""
    try:
        from hotels.tasks import push_hotel_to_voyager
        from hotels.models import HotelDetail, RoomDetail
        from hotels.methods import HotelMethods
        video_model = kwargs.get('instance')
        object_id = video_model.object_id
        model_name = video_model.content_type.name

        if settings.VOYAGER_FLAG and not settings.DEBUG:
            hotelobj = ''
            if model_name == "Hotel Detail":
                hotelobj = HotelDetail.objects.filter(id=object_id).using('default')[0]
            elif model_name == "Room Detail":
                hotelobj = RoomDetail.objects.filter(id=object_id).using('default')[0].hotel
            if hotelobj:
                push_hotel_to_voyager.apply_async(args=(hotelobj, "update_video_on_voyager",), )
    except Exception, e:
        logger_stats.error(str(e), log_type="hotel_video", bucket="voyager_update", stage="update video on voyager")


@skip_post_save_signal
def send_video_processing(sender=None, **kwargs):
    try:
        video_object = kwargs.get('instance')
        is_newly_created = kwargs.get('created', False)

        if is_newly_created:
            logger_stats.info("Pushing video for processing, video id {}, newly_created: {}".format(video_object.id,
                                                                                                    is_newly_created),
                              log_type="hotel_video", bucket="send_video_processing", stage="send_video_processing")
            push_video_to_processing_service('video_processing_service', video_object)
        else:
            logger_stats.info(
                "Video processing skipped for video id {}, newly_created: {}".format(video_object.id, is_newly_created),
                log_type="hotel_video", bucket="send_video_processing", stage="send_video_processing")

    except Exception, e:
        logger_stats.error(str(e), log_type="hotel_video", bucket="send_video_processing",
                           stage="send_video_processing")


models.signals.post_save.connect(video_moderation, Video)
models.signals.post_save.connect(send_video_processing, Video)
if settings.VOYAGER_OLD_STATIC_CONTENT_PIPELINE_FLAG:
    models.signals.post_save.connect(update_video_on_voyager, Video)


# To Store Hotel related documents

class HotelDocument(models.Model):
    document = models.FileField(_('Hotel Related Document'), upload_to="mdb", null=True, blank=True,
                                storage=MDBStorage(), max_length=1000, )
    document_name = models.CharField(_('Name of the document'), max_length=100)
    document_type = models.CharField(_('Type of the document'), max_length=50)
    document_description = models.TextField(_('Document Descripton'), null=True, blank=True)
    last_modified_by = models.ForeignKey(User, null=True)
    content_type = models.ForeignKey(ContentType)
    legal_doc_type = models.CharField(_('Legal document'), max_length=50, null=True, blank=True,
                                      choices=hotelchoice.legal_hotel_doc)
    object_id = models.PositiveIntegerField()
    ocr_flag = models.BooleanField(_('OCR Flag'), default=False)
    source = models.CharField(_('Image Source'), default=False, max_length=10)
    content_object = GenericForeignKey('content_type', 'object_id')
    is_verified = models.BooleanField(_('Verified Flag'), default=False)
    isactive = models.BooleanField(_('Active Flag'), default=False)
    createdon = models.DateTimeField(_('Added On'), auto_now_add=True)
    modifiedon = models.DateTimeField(_('Modified On'), auto_now=True)

    def save(self, *args, **kwargs):
        if self.document.name == 'Error : Internal Error':
            raise Exception('Internal Error occurred while uploading document, please try again after some time.')
        from communication.common_comms.communications import sendMail
        if not self.last_modified_by.is_staff and self.content_type.model == 'hoteldetail' and not self.pk:
            subject = '%s, %s has added a NEW Document' % (self.content_object.hotelname, self.content_object.city_id.cityname)
            message = 'HotelCode: <b>%s</b> <br> HotelName: <b>%s</b><br> ' \
                      'City: <b>%s</b><br> Document Name: <b>%s</b><br> added by <b>%s</b>' \
                      % (self.content_object.hotelcode, self.content_object.hotelname, self.content_object.city_id.cityname,
                         self.document_name, self.last_modified_by.username)
            receiver = settings.HOTEL_DOCUMENTS_ALERT
            sendMail('', '', message, subject, '', receiver, [], [])
        super(HotelDocument, self).save(*args, **kwargs)


class Country(models.Model):
    countryname = models.CharField('Name of Country', max_length=100)
    countryname2 = models.CharField('Alternative Names', max_length=100, null=True, blank=True)
    voyagercode = models.CharField('Voyager Code', max_length=50, db_index=True, unique=True, blank=True)
    iso2 = models.CharField(_('ISO2'), max_length=2, null=True, blank=True)
    iso3 = models.CharField(_('ISO3'), max_length=3, null=True, blank=True)
    flag_emoji = models.CharField(max_length=20, verbose_name='Country Flag Unicode Emoji')
    continent = models.CharField(_('Continent'), max_length=25, null=True, choices=commonchoice.Continent)
    latitude = models.FloatField(_('Geo-Location (Latitude/Longitude)'))
    longitude = models.FloatField()
    isactive = models.BooleanField(_('Active Flag'), default=True)
    user = CurrentUserField()
    createdon = models.DateTimeField(_('Added On'), auto_now_add=True)
    modifiedon = models.DateTimeField(_('Modified On'), auto_now=True)
    dialing_prefix = models.CharField(_('Dialing prefix'), null=True, blank=True,
                                      max_length=5)
    mmt_cntry_id = models.CharField('MMT Country Code', max_length=5)
    locus_code = models.CharField(null=True, blank=True, max_length=50)

    class Meta:
        verbose_name_plural = 'Countries'

    def __unicode__(self):
        return self.countryname.title()


class State(models.Model):
    statename = models.CharField('Name of State', max_length=100)
    statename2 = models.CharField('Alternative Names', max_length=100, null=True, blank=True)
    statecode = models.CharField('State Name Code', max_length=5, null=True, blank=True)
    voyagercode = models.CharField('Voyager Code', max_length=50, db_index=True, unique=True, blank=True)
    country = models.ForeignKey(Country, related_name='state', verbose_name=_('Country'))
    latitude = models.FloatField(_('Geo-Location'))
    longitude = models.FloatField()
    isactive = models.BooleanField(_('Active Flag'), default=True)
    user = CurrentUserField()
    createdon = models.DateTimeField(_('Added On'), auto_now_add=True)
    modifiedon = models.DateTimeField(_('Modified On'), auto_now=True)
    locus_code = models.CharField(null=True, blank=True, max_length=50)

    def __unicode__(self):
        return self.statename.title()


class Region(models.Model):
    regionname = models.CharField('Name of Region', max_length=100)
    regionname2 = models.CharField('Alternative Names', max_length=100, null=True, blank=True)
    country = models.ForeignKey(Country, verbose_name=_('Country'))
    latitude = models.FloatField(_('Geo-Location'), null=True, blank=True)
    longitude = models.FloatField(null=True, blank=True)
    isactive = models.BooleanField(_('Active Flag'), default=True)
    user = CurrentUserField()
    createdon = models.DateTimeField(_('Added On'), auto_now_add=True)
    modifiedon = models.DateTimeField(_('Modified On'), auto_now=True)

    def __unicode__(self):
        return self.regionname.title()


class City(models.Model):
    cityname = models.CharField('Name of City', max_length=100)
    cityname2 = models.CharField('Alternative Names', max_length=250, null=True, blank=True)
    citycode = models.CharField('Voyager cityCode', max_length=50, blank=True, db_index=True, unique=True)
    state = models.ForeignKey(State, null=True, blank=True, verbose_name=_('State'),
                              help_text='This field is populated automatically.')
    country = models.ForeignKey(Country, verbose_name=_('Country'))
    region = models.ForeignKey(Region, null=True, blank=True, verbose_name=_('Region'))
    latitude = models.FloatField(null=True, blank=True)
    longitude = models.FloatField(null=True, blank=True)
    isactive = models.BooleanField(_('Active Flag'), default=True)
    addlocality = models.BooleanField(_('Add Locality Flag'), default=True)
    hotelnearbycity = models.ManyToManyField("self", verbose_name=_("Nearby HotelCity"), null=True, blank=True,
                                             related_name='hotelnearbycity', db_table='common_cityhotelnearbycity')
    user = CurrentUserField()
    createdon = models.DateTimeField(_('Added On'), auto_now_add=True)
    modifiedon = models.DateTimeField(_('Modified On'), auto_now=True)
    timezone_1 = TimeZoneField(verbose_name=_('Timezone'), null=True, blank=True)
    mmt_city_id = models.CharField('MMT CityId', max_length=20, null=True, blank=True)
    mmt_city_code = models.CharField('MMT CityCode', max_length=20, null=True, blank=True)
    is_display_rks = models.BooleanField('Display Rank Simulator Extranet', default=True)
    locus_code = models.CharField(null=True, blank=True, unique=True, max_length=50)

    class Meta:
        verbose_name_plural = 'Cities'

    def __unicode__(self):
        return self.cityname.title()

    @staticmethod
    def test_city_obj():
        return City.objects.filter(Q(cityname=TEST_CITY_NAME) | Q(locus_code=TEST_LOCUS_CODE)).first()

    def save(self, *args, **kwargs):
        from hotels.models import HotelDetail
        db_obj = None
        if self.id:
            try:
                db_obj = City.objects.using("default").get(id=self.id, isactive=True)
            except Exception, e:
                inventory_logger.critical(message="Exception %s occured while fetching city with id %s " % (
                    str(e), self.id), log_type="ingoibibo", bucket="common.models", stage="save")
        super(City, self).save(*args, **kwargs)
        if db_obj and db_obj.cityname != self.cityname:
            HotelDetail.objects.filter(city_id=self.id).update(city=self.cityname)


class Locality(models.Model):
    localityname = models.CharField('Name of Location', max_length=100)
    localityname2 = models.CharField('Alternative Names', max_length=100, null=True, blank=True)
    voyagerid = models.CharField(_('Voyager Id'), db_index=True, max_length=50, null=True, unique=True)
    city = models.ForeignKey(City, null=True, verbose_name=_('City'), limit_choices_to={'addlocality': 1})
    latitude = models.FloatField(null=True, blank=True)
    longitude = models.FloatField(null=True, blank=True)
    locality_source = models.CharField(_('Source'), max_length=10, null=True, blank=True)
    locality_status = models.IntegerField(_('Status'), choices=commonchoice.LOCALITY_STATUS,
                                          default=commonchoice.LocalityStatus.PENDING, null=True, blank=True)
    isactive = models.BooleanField(_('Active Flag'), default=True)
    user = CurrentUserField()
    createdon = models.DateTimeField(_('Added On'), auto_now_add=True)
    modifiedon = models.DateTimeField(_('Modified On'), auto_now=True)
    locus_code = models.CharField(null=True, blank=True, max_length=50)

    class Meta:
        verbose_name_plural = 'Localities'

    def __unicode__(self):
        return self.localityname.title() + ', ' + self.city.cityname.title()


class Landmark(models.Model):
    landmarkname = models.CharField('Landmark\'s Name', max_length=100, db_index=True, validators=[validate_name])
    displayname = models.CharField('Display Name', max_length=50, db_index=True, validators=[validate_name])
    city = models.ForeignKey(City, db_index=True, null=True, blank=True, verbose_name=_('City'))
    latitude = models.FloatField(null=True, blank=True)
    longitude = models.FloatField(null=True, blank=True)
    desc = models.TextField(_('Short Description'), null=True, blank=True, validators=[validate_text])
    desclong = models.TextField(_('Description'), null=True, blank=True, validators=[validate_text])
    ticketflag = models.BooleanField(_('Ticket Needed'), default=False)
    ticketprice = models.CharField(_('Ticket Price'), max_length=25, null=True, blank=True)
    timings = models.CharField(_('Visit Timings'), max_length=255, null=True, blank=True)
    landmarktype = models.CharField(_('Landmark Type'), max_length=50, null=True, choices=commonchoice.LandmarkType)
    isactive = models.BooleanField(_('Active Flag'), default=True)
    user = CurrentUserField()
    createdon = models.DateTimeField(_('Added On'), auto_now_add=True)
    modifiedon = models.DateTimeField(_('Modified On'), auto_now=True)
    notes = GenericRelation(ItemNote, related_query_name='item_note')

    def get_absolute_url(self):
        return reverse('admin:common_landmark_change', args=(self.id,))

    def __unicode__(self):
        if self.displayname:
            firstname = self.displayname.title()
        else:
            firstname = self.landmarkname.title() + ' not display name '
        if self.city:
            return firstname + ', ' + self.city.cityname.title()
        else:
            return firstname


class Vendor(models.Model):
    PII_CLASS_CONFIG = {
        'column_length': {
            "accno": 30,
            "accname": 255,
            "pan_number": 15,
            "mobile": 30
        },
        'iv_length': 16,  # AES block size
        'padding': 16,  # Padding to make the data length multiple of AES block size
    }
    code = models.CharField(_('Vendor Code'), unique=True, db_index=True, max_length=50,
                            help_text='Please assign a code to each vendor.You can use vendor name with no spaces.')
    name = models.CharField(_('Name'), max_length=100, db_index=True)
    city = models.ForeignKey(City, db_index=True, verbose_name=_('City'))
    address = models.TextField(_('Address'))
    mobile = models.CharField(_('Mobile'), max_length=calculate_max_length(PII_CLASS_CONFIG, "mobile"), null=True, db_index=True)
    email = models.EmailField(_('Email'), null=True, db_index=True)
    product = MultiSelectField(_('Vendor Type'), max_length=500, choices=commonchoice.PRODUCT_CHOICES)
    isactive = models.BooleanField(_('Activation Flag'), default=True)
    domainname = models.CharField(_('Domain Name'), max_length=100, null=True, blank=True)
    misc = models.TextField(_('Other Info'), null=True, blank=True)
    contracttype = MultiSelectField(_('Contract Type'), max_length=50, choices=CONTRACTTYPE + ADDITIONALCONTRACTTYPE)
    accno = models.CharField(_('Account no.'), max_length=calculate_max_length(PII_CLASS_CONFIG, "accno"), null=True, blank=True)
    ifsc = models.CharField(_('IFSC code'), max_length=20, null=True, blank=True)
    accname = models.CharField(_('Account Holders Name'), max_length=calculate_max_length(PII_CLASS_CONFIG, "accname"), null=True, blank=True)
    branchname = models.CharField(_('Branch Name'), max_length=100, null=True, blank=True)
    branchcode = models.CharField(_('Branch Code'), max_length=30, null=True, blank=True)
    bankname = models.CharField(_('Bank Name'), max_length=100, null=True, blank=True)
    bankcode = models.CharField(_('Bank Code'), max_length=30, null=True, blank=True)
    pan_number = models.CharField(_('Pan Number'), max_length=calculate_max_length(PII_CLASS_CONFIG, "pan_number"), null=True, blank=True)
    gstin = models.CharField(_('GST Number'), max_length=22, null=True, blank=True)
    cin = models.CharField(_('CIN Number'), max_length=40, null=True, blank=True)
    trn = models.CharField(_('TRN Number'), max_length=20, null=True, blank=True)
    logo = models.CharField(_('Image Logo'), max_length=200, null=True, blank=True)
    adminuser = models.OneToOneField(User, related_name='adminvendor', verbose_name=_('Admin User'))
    user = CurrentUserField()
    createdon = models.DateTimeField(_('Added On'), auto_now_add=True)
    modifiedon = models.DateTimeField(_('Modified On'), auto_now=True)
    notes = GenericRelation(ItemNote, related_query_name='item_note')
    netratemarkuptype = models.CharField(_('Nett Rate Markup Type'), max_length=50,
                                         choices=commonchoice.NetRateMarkUpType, default='percentage')
    netratemarkupvalue = models.FloatField(_('Nett Rate Markup Value'), default=0)
    credit_alert_emails = MultiEmailField(_('Credit Alert Email Lists.'), null=True,
                                          help_text='''<b> separate two emails with comma", "</b>''')
    credit_alert_amount = models.IntegerField(_('Credit Alert Amount'), default=100000)
    image = models.ForeignKey(Image, null=True, blank=True)
    encryptionDetails = JSONField(_('Encryption Details'), null=True, blank=True)

    class Meta:
        permissions = (('allownettrate', 'Allow Net Rate'),)

    def __unicode__(self):
        return str(self.name)

    # write a static get_by_id function: check cache first and than db
    # write to push to kakfa on saving in of the below fields
    # contracttype, netratemarkuptype, netratemarkupvalue
    # override save

    def save(self, *args, **kwargs):
        try:
            db_obj = Vendor.objects.using('default').get(id=self.id)
        except Exception, e:
            db_obj = None
        super(Vendor, self).save(*args, **kwargs)

    def update_cache(self, update_type):
        push_dict = self.create_cache_dict()
        topic = 'ingoibibo_cache_update'
        message = json.dumps(push_dict)
        from common.commonhelper import push_message_to_woof, cache_update_log_format
        push_message_to_woof(topic, message, 0, True)
        cache_update_log_format('producer', update_type, push_dict['typ'], push_dict['code'], False, '', '')

    def is_update_cache(self, db_obj):
        is_update = False
        if not db_obj:
            if self.isactive:
                is_update = True
        else:
            is_update = self.isactive != db_obj.isactive \
                        or self.contracttype != db_obj.contracttype \
                        or self.netratemarkuptype != db_obj.netratemarkuptype \
                        or self.netratemarkupvalue != db_obj.netratemarkupvalue \
                        or self.code != db_obj.code \
                        or self.image != db_obj.image
        return is_update

    def create_cache_dict(self):
        vendor_dict = {}
        vendor_dict['id'] = int(self.id)
        vendor_dict['act'] = self.isactive
        vendor_dict['ct'] = self.contracttype
        vendor_dict['pdt'] = self.product
        vendor_dict['nrmt'] = self.netratemarkuptype
        vendor_dict['mrmv'] = self.netratemarkupvalue
        vendor_dict['code'] = str(self.code)
        vendor_dict['img'] = str(self.image.image) if self.image else ''
        vendor_dict['typ'] = 'vendor'
        return vendor_dict

    @staticmethod
    def get_object_by_id(vendor_id):
        pass

    MMT_VENDORS = ["makemytrip", "mmtgcc"]

register_model_signal(Vendor)
class CustomUser(User):
    """
    customizing the user to add vendor group
    """
    vendorgroup = models.ForeignKey(Vendor)
    objects = UserManager()


class MMStats(models.Model):
    user_id = models.PositiveIntegerField(db_index=True)
    cvid = models.CharField('City Name Code', max_length=50, db_index=True)
    createdon = models.DateTimeField(auto_now_add=True, db_index=True)
    applyon = models.DateField(db_index=True)
    days = models.PositiveSmallIntegerField()
    # parameters = models.TextField()
    hotels = models.PositiveIntegerField(default=0)  # total
    conversion_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0)  # avg
    constant_availability = models.DecimalField(max_digits=5, decimal_places=2, default=0)  # avg
    constant_rates = models.DecimalField(max_digits=5, decimal_places=2, default=0)  # avg
    bounces = models.PositiveSmallIntegerField(default=0)  # total
    cancellation_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0.0)  # avg
    sales_tx = models.PositiveSmallIntegerField(default=0)  # total
    sales_rn = models.PositiveIntegerField(default=0)  # total
    sales_gmv = models.PositiveIntegerField(default=0)  # total
    sales_margins = models.DecimalField(max_digits=4, decimal_places=2, default=0.0)  # avg
    page_loads = models.PositiveIntegerField(default=0)  # avg
    offers = models.DecimalField(max_digits=3, decimal_places=1, default=0)  # avg
    rooms = models.DecimalField(max_digits=3, decimal_places=1, default=0.0)  # avg
    rateplans = models.DecimalField(max_digits=3, decimal_places=1, default=0.0)  # avg
    go_rating = models.PositiveSmallIntegerField(default=0)  # hotels
    ta_rating = models.PositiveSmallIntegerField(default=0)  # hotels
    login = models.PositiveIntegerField(default=0)  # hotels
    total_updates = models.PositiveIntegerField(default=0)  # avg
    user_updates = models.PositiveIntegerField(default=0)  # avg
    content_score = models.DecimalField(max_digits=5, decimal_places=2, default=0)  # avg
    images = models.PositiveIntegerField(default=0)  # avg
    images_high_res = models.PositiveIntegerField(default=0)  # avg
    images_low_res = models.PositiveIntegerField(default=0)  # avg
    cs_photo_score = models.DecimalField(max_digits=5, decimal_places=2, default=0.0)  # avg
    cs_hotel_score = models.DecimalField(max_digits=5, decimal_places=2, default=0.0)  # avg
    cs_room_score = models.DecimalField(max_digits=5, decimal_places=2, default=0.0)  # avg
    cs_location_score = models.DecimalField(max_digits=5, decimal_places=2, default=0.0)  # avg
    cs_contact_score = models.DecimalField(max_digits=5, decimal_places=2, default=0.0)  # avg
    cs_bank_score = models.DecimalField(max_digits=5, decimal_places=2, default=0.0)  # avg
    cs_policies_score = models.DecimalField(max_digits=5, decimal_places=2, default=0.0)  # avg
    new_active_hotels = models.PositiveSmallIntegerField(default=0)  # total
    pah_hotels = models.PositiveSmallIntegerField(default=0)  # total
    sales_confirmed = models.PositiveSmallIntegerField(default=0)  # total
    free_cancel = models.DecimalField(max_digits=4, decimal_places=1, blank=True, null=True)  # avg

    class Meta:
        unique_together = ('user_id', 'cvid', 'applyon', 'days')

    def __unicode__(self):
        return self.cvid


class MailRecords(models.Model):
    content_type = models.ForeignKey(ContentType)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')
    to = models.CharField('To Email', max_length=100, null=True, blank=True)
    cc = models.CharField('CC', max_length=500, null=True, blank=True)
    bcc = models.CharField('BCC', max_length=100, null=True, blank=True)
    frm = models.CharField('From Email', max_length=30, null=True, blank=True)
    tempid = models.CharField('Template', max_length=10, null=True, blank=True, db_index=True)
    pigeonid = models.CharField('Pigeon Response Id', max_length=50, null=True, blank=True)
    status = models.CharField('Mail Status', max_length=30, null=True, blank=True, default='waiting')
    stresponse = models.CharField('Status Response', max_length=500, null=True, blank=True, db_index=True)
    createdon = models.DateTimeField(_('Added On'), auto_now_add=True, db_index=True)
    modifiedon = models.DateTimeField(_('Modified On'), auto_now=True)
    irismessageid = models.CharField('IRIS message id', max_length=100, null=True, blank=True)
    bookingid = models.CharField('Booking id', max_length=30, null=True, blank=True)

    class Meta:
        verbose_name = _('Mail Records')
        verbose_name_plural = _('Mail Records')

    def __unicode__(self):
        return "%s" % (self.id)

    def mail_label(obj):
        if obj.tempid:
            if commonchoice.TEMPLATE_DETAILS.get(obj.tempid):
                label = commonchoice.TEMPLATE_DETAILS[obj.tempid][0]
            else:
                label = "Please add in TEMPLATE DETAILS DICT"
            return ("%s" % (label))

    mail_label.short_description = 'Mail Label'


class PropertyCategory(models.Model):
    property_label = models.CharField('Property Label', max_length=100)
    propertytype_code = models.CharField('Property Label', max_length=20)
    short_description = models.CharField('Short Description', max_length=1024)
    property_category = models.CharField('Property Label', max_length=50)
    seq_order = models.PositiveIntegerField()
    category_level = models.CharField('Category level', max_length=50)
    img_url = models.CharField('Property Label', max_length=1024)
    is_active = models.BooleanField(_('Active Flag'), default=True)

    class Meta:
        app_label = 'common'
        verbose_name_plural = 'Hotels Category List'


class BankDetails(models.Model):
    bankname = models.CharField('Bank Name', max_length=250)
    transfercode = models.CharField('IFSC/SWIFT/BIC CODE', max_length=11,
                                    unique=True,
                                    help_text="Length should be between 4 to 8 character")
    bankdetails = models.CharField('Additional Bank Details', max_length=300,
                                   null=True, blank=True)
    country = models.ForeignKey(Country, related_name='bankdetails',
                                verbose_name=_('Country'))
    createdon = models.DateTimeField(_('Added On'), auto_now_add=True)
    modifiedon = models.DateTimeField(_('Modified On'), auto_now=True)

    class Meta:
        unique_together = ('bankname', 'country')
        verbose_name = _('Bank Details')
        verbose_name_plural = _('Bank Details')

    def __unicode__(self):
        return "%s" % (self.id)

    @staticmethod
    def update_bank_details_cache():
        cache_time = 60 * 60 * 24 * 10
        bank_details = BankDetails.objects.all().using('default')
        try:
            bankname_list = []
            popular_bankname_map = {}
            popular_bankname_list = []
            ifsc_to_bankname_dict = {}
            bankname_to_ifsc_dict = {}
            for bankobj in bank_details:
                bankname_list.append((bankobj.bankname, bankobj.bankname))
                ifsc_to_bankname_dict[bankobj.transfercode] = bankobj.bankname
                bankname_to_ifsc_dict[bankobj.bankname] = bankobj.transfercode
                if bankobj.id in POPULAR_BANKS_LIST:
                    popular_bankname_map[bankobj.id] = bankobj.bankname
            for bankobj_id in POPULAR_BANKS_LIST:
                if bankobj_id in popular_bankname_map:
                    popular_bankname_list.append((popular_bankname_map[bankobj_id], popular_bankname_map[bankobj_id]))
            cache.set("bank_name_list", bankname_list, cache_time)
            cache.set("popular_bank_name_list", popular_bankname_list, cache_time)
            cache.set("ifsc_to_bankname_dict", ifsc_to_bankname_dict, cache_time)
            cache.set("bankname_to_ifsc_dict", bankname_to_ifsc_dict, cache_time)
        except Exception, e:
            logger_stats.error(str(e), log_type="common_models", bucket="inventory",
                               stage="update_bank_details_cache")

    def save(self, *args, **kwargs):
        super(BankDetails, self).save(*args, **kwargs)
        self.update_bank_details_cache()


def get_bank_names():
    """
    Get bank name list of DB already stored in cache
    """
    bank_names = cache.get("bank_name_list", None)
    if not bank_names:
        BankDetails.update_bank_details_cache()
        cached_data = cache.get("bank_name_list", None)
        bank_names = cached_data
    return bank_names


def get_popular_bank_names():
    """
    Get popular bank name list of DB already stored in cache
    """
    bank_names = cache.get("popular_bank_name_list", None)
    if not bank_names:
        BankDetails.update_bank_details_cache()
        cached_data = cache.get("popular_bank_name_list", None)
        bank_names = cached_data
    return bank_names


def get_city_name():
    """
    :return: list of all active cities
    """
    city_list = cache.get("city_name_list", None)
    if not city_list:
        cache_time = 60 * 60 * 24 * 10
        city_list = City.objects.filter(isactive=True).values_list('cityname', flat=True)
        cache.set("city_name_list", city_list, cache_time)
        cached_data = cache.get("city_name_list", None)
        city_list = cached_data
    return city_list


def get_country_list():
    """
        :return: list of all active country
        """
    country_name_list = cache.get("country_name_list", None)
    if not country_name_list:
        cache_time = 60 * 60 * 24 * 10
        city_list = Country.objects.filter(isactive=True).values_list('countryname', flat=True)
        cache.set("country_name_list", city_list, cache_time)
        cached_data = cache.get("country_name_list", None)
        country_name_list = cached_data
    return country_name_list


def get_ifsc_to_bankname_dict():
    """
    Get bank ifsc dict of DB already stored in cache
    """
    ifsc_to_bankname_dict = cache.get("ifsc_to_bankname_dict", None)
    if not ifsc_to_bankname_dict:
        BankDetails.update_bank_details_cache()
        ifsc_to_bankname_dict = cache.get("ifsc_to_bankname_dict", None)
    return ifsc_to_bankname_dict


def get_bankname_to_ifsc_dict():
    """
    Get Bank Name as a key with ifsc value
    """
    bankname_to_ifsc_dict = cache.get("bankname_to_ifsc_dict", None)
    if not bankname_to_ifsc_dict:
        BankDetails.update_bank_details_cache()
        bankname_to_ifsc_dict = cache.get("bankname_to_ifsc_dict", None)
    return bankname_to_ifsc_dict


def add_state(sender, **kwargs):
    """
    :param sender:
    :param kwargs:
    :return:
    City has already been created but state is not there. Call voyager with citycode
    and find the hierarchy and then create it in ingoibibo.
    """
    from communication.common_comms.communications import sendMail
    saved = False
    city = kwargs.get('instance')
    voyager_id = city.citycode
    if kwargs.get('created'):
        try:
            city_response = CommonMethods.get_node_by_id(voyager_id)
            parents = city_response.get('parents')
            if parents:
                state_vcode = parents.get('8') or (city_response.get('_id') if
                                                   city_response.get('type') == 13 else None) or \
                              parents.get('9') or parents.get('12')
                state_response = CommonMethods.get_node_by_id(state_vcode)
                state_to_be_created = state_response.get('_id')
                if state_to_be_created:
                    alias = state_response.get('aliases')[0] if \
                        state_response.get('aliases') else ''
                    country_vcode = state_response.get('parents').get('12') or \
                                    parents.get('12')
                    latitude = state_response.get('location').get('lat')
                    longitude = state_response.get('location').get('long')
                    country = Country.objects.get(voyagercode=country_vcode, isactive=True)
                    state, created = State.objects.get_or_create(voyagercode=state_response['_id'], isactive=True,
                                                                 defaults={'statename': state_response['name'],
                                                                           'statename2': alias,
                                                                           'country': country, 'latitude': latitude,
                                                                           'longitude': longitude, 'user': city.user})
                    setattr(city, 'state', state)
                    city.save()
                    saved = True
        except Exception as e:
            logger_stats.critical(message='Failed populating state for %s reason %s' % (city, e),
                                  log_type='ingoibibo', bucket='create_city', stage='city.add_state')
    if not saved and kwargs.get('created'):
        sendMail('', '', voyager_id, 'State could not be popultaed', '', settings.TECH_GURGOAN_EMAIL, [], [])


@skip_post_save_signal
def push_to_etl(sender, **kwargs):
    from hotels.tasks import send_image_etl_task, send_dedup_image_etl_task
    from hotels.models import HotelDetail
    from hotels.models import RoomDetail

    hotel_content_type = ContentType.objects.get_for_model(HotelDetail)
    room_content_type = ContentType.objects.get_for_model(RoomDetail)
    image_obj = kwargs.get('instance')
    if image_obj.content_type and image_obj.content_type.name == 'Host Profile':
        return
    hotel_obj = image_obj.get_hotel()
    mmt_id = hotel_obj.mmt_id
    allow_event = True

    if mmt_id:
        from hotels.admin.helper import block_events_based_on_hoteldataport
        allow_event = block_events_based_on_hoteldataport(mmt_id)

    if allow_event:
        if image_obj.content_type == hotel_content_type or image_obj.content_type == room_content_type:
            if mmt_id and image_obj.status == image_obj.APPROVED:
                if image_obj.mmt_img_id:
                    from hotels.hoteldataporting import create_mmt_ingo_image_mapping
                    create_mmt_ingo_image_mapping(image_obj.id, image_obj.mmt_img_id)
                    send_dedup_image_etl_task.apply_async(args=('Update', image_obj.id, image_obj.mmt_img_id),
                                                          countdown=2 * settings.ETL_DELAY)
                else:
                    send_image_etl_task.apply_async(args=('Update', image_obj.id), countdown=2 * settings.ETL_DELAY)
    else:
        logger_stats.info(message='Push to etl for image blocked: %s' % image_obj,
                          log_type='ingoibibo', bucket='etl',
                          stage='models.push_to_etl')


def add_state_country(sender, **kwargs):
    try:
        city = kwargs.get('instance')
        if kwargs.get('created'):
            latitude = city.latitude
            longitude = city.longitude
            locus_obj = Locus(latitude, longitude)
            locus_obj.fetch_data()
            city.country = get_country(locus_obj.country) if locus_obj.country else None
            city.state = get_state(locus_obj.state, city.country) if locus_obj.state else None
            if locus_obj.country.get('name').lower() == "india":
                if not locus_obj.state or not locus_obj.state.get('name'):
                    raise Exception
            city.save()
    except Exception as e:
        logger_stats.critical(message='Failed populating state for %s reason %s' % (city, e),
                              log_type='ingoibibo', bucket='create_city', stage='city.add_state_country')


def fetch_ingo_city_obj(locus_obj):
    try:
        if not locus_obj.city:
            raise Exception("Locus Obj does not have city.")
        try:
            city_obj = City.objects.get(locus_code=locus_obj.city.get('id'), isactive=True)
            if not (city_obj.cityname.lower() == locus_obj.city.get('name','').lower()):
                city_obj.cityname = locus_obj.city.get('name','')
                city_obj.save()
            return city_obj
        except ObjectDoesNotExist as e:
            if locus_obj.country and locus_obj.country.get('name','').lower() == "india":
                if not locus_obj.state or not locus_obj.state.get('name',''):
                    raise Exception
            city_obj = create_city(locus_obj.city)
            return city_obj
    except Exception as e:
        raise e

def fetch_ingo_locality_obj(locus_primary_area, city_obj):
    try:
        try:
            locality_obj = Locality.objects.get(locus_code=locus_primary_area.get('id'), isactive=True)
            if not (locality_obj.localityname.lower() == locus_primary_area.get('name','').lower()):
                locality_obj.localityname = locus_primary_area.get('name','')
                locality_obj.save()
            return locality_obj
        except ObjectDoesNotExist as e:
            locality_obj = create_locality(locus_primary_area, city_obj)
            return locality_obj
    except Exception as e:
        raise e


def get_country(locus_country_obj):
    try:
        country_obj = Country.objects.get(locus_code=locus_country_obj.get('id'), isactive=True)
        if not (country_obj.countryname.lower() == locus_country_obj.get('name','').lower()):
            country_obj.countryname = locus_country_obj.get('name','')
            country_obj.save()
        return country_obj

    except ObjectDoesNotExist as e:
        country_data = {
            "countryname": locus_country_obj.get('name', ''),
            "locus_code": locus_country_obj.get('id', ''),
            "latitude": locus_country_obj.get('coordinates')[1]
            if len(locus_country_obj.get('coordinates', [])) > 1 else None,
            "longitude": locus_country_obj.get('coordinates')[0]
            if len(locus_country_obj.get('coordinates', [])) > 1 else None
        }
        country_obj = Country.objects.create(**country_data)
        return country_obj
    except Exception as e:
        raise e


def get_state(locus_state_obj, country_obj):
    try:
        state_obj = State.objects.get(locus_code=locus_state_obj.get('id'), isactive=True)
        if not (state_obj.statename.lower() == locus_state_obj.get('name','').lower()):
            state_obj.statename = locus_state_obj.get('name','')
            state_obj.save()
        return state_obj
    except ObjectDoesNotExist as e:
        state_data = {
            "statename": locus_state_obj.get('name', ''),
            "locus_code": locus_state_obj.get('id', ''),
            "latitude": locus_state_obj.get('coordinates')[1]
            if len(locus_state_obj.get('coordinates', [])) > 1 else None,
            "longitude": locus_state_obj.get('coordinates')[0]
            if len(locus_state_obj.get('coordinates', [])) > 1 else None,
            "country": country_obj
        }
        state_obj = State.objects.create(**state_data)
        return state_obj
    except Exception as e:
        raise e


def create_city(locus_city_obj):
    try:
        city_data = {
            "cityname": locus_city_obj.get('name', ''),
            "locus_code": locus_city_obj.get('id', ''),
            "latitude": locus_city_obj.get('coordinates')[1]
            if len(locus_city_obj.get('coordinates', [])) > 1 else None,
            "longitude": locus_city_obj.get('coordinates')[0]
            if len(locus_city_obj.get('coordinates', [])) > 1 else None,
        }
        city_obj = City.objects.create(**city_data)
        return city_obj
    except Exception as e:
        raise e


def create_locality(locus_locality_obj, city_obj):
    try:
        city_data = {
            "localityname": locus_locality_obj.get('name', ''),
            "locus_code": locus_locality_obj.get('id', ''),
            "latitude": locus_locality_obj.get('coordinates')[1]
            if len(locus_locality_obj.get('coordinates', [])) > 1 else None,
            "longitude": locus_locality_obj.get('coordinates')[0]
            if len(locus_locality_obj.get('coordinates', [])) > 1 else None,
            "city": city_obj
        }
        locality_obj = Locality.objects.create(**city_data)
        return locality_obj
    except Exception as e:
        raise e


if settings.ETL_OLD_STATIC_CONTENT_PIPELINE_FLAG:
    models.signals.post_save.connect(push_to_etl, Image)

# models.signals.post_save.connect(add_state, City)

models.signals.post_save.connect(add_state_country, City)

models.signals.post_save.connect(push_static_content, Image)

models.signals.post_save.connect(push_static_content, Video)


# 21/02/2013
# alter table tablename change oldname newname varchar (10) ;
# alter table common_image change is_active isactive bool not null default 1;
# alter table common_landmark add column isactive bool not null default 1 after `landmarktype`;
# 28/01/2014
# alter table common_City add column addlocality bool not null default 1 after `isactive`;
# 26/03/2014
# ALTER TABLE common_vendor ADD netratemarkuptype VARCHAR(50) NULL
# ALTER TABLE common_vendor ADD netratemarkupvalue FLOAT DEFAULT 0
# 22/08/2014
# alter table common_vendor add column credit_alert_emails  varchar(1000) null after netratemarkupvalue;
# alter table common_vendor add column credit_alert_amount integer DEFAULT 100000 after credit_alert_emails;
# 25/09/2014
# alter table common_mmstats add column new_active_hotels smallint(5) unsigned default 0;
# 30/09/2014
# alter table common_mmstats add column pah_hotels smallint(5) unsigned default 0;
# alter table common_mmstats add column sales_confirmed smallint(5) unsigned default 0;
# alter table common_mmstats add column free_cancel decimal(4,1) default null;
# update common_incomingpayment set creditfor='vendor';
# alter table common_image add column watermark varchar(10) default Null after content_type_id;
# 15/04/2015
# add column `payment_mode` varchar (10) after `payment_id`,
# add column bank_reference varchar(20) after `payment_mode`;


class Survey(models.Model):
    name = models.CharField(max_length=255)
    description = models.TextField()
    is_active = models.BooleanField(default=False)
    periodic_window = models.IntegerField()
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    survey_type = models.CharField(max_length=100)
    survey_subtype = models.CharField(max_length=100, default='default')

    class Meta:
        db_table = 'common_survey'
        unique_together = ['survey_type', 'survey_subtype']

    def __unicode__(self):
        return u'Survey Id {}: {}'.format(self.id, self.name)


class SurveyQuestion(models.Model):
    is_active = models.BooleanField(default=True)
    answer_type = models.CharField(choices=commonchoice.SURVEY_QUESTION_TYPES, max_length=255)
    question_text = models.TextField()
    order = models.IntegerField()
    has_child = models.BooleanField(default=False)
    options = JSONField(null=True, blank=True)
    option_identifier = JSONField(null=True, blank=True)
    is_mandatory = models.BooleanField(default=False)
    parent = models.ForeignKey('self', blank=True, null=True)
    survey = models.ForeignKey(Survey, null=False)

    class Meta:
        db_table = 'common_survey_question'

    def __unicode__(self):
        return u'Question Id {}: {}'.format(self.id, self.question_text)


class LocusCity(INGOModel):
    locus_city_code = models.CharField(max_length=45, null=False, blank=False, unique=True)
    locus_city_name = models.CharField(max_length=100, null=False, blank=False)
    locus_city_lat = models.FloatField(null=False, blank=False)
    locus_city_long = models.FloatField(null=False, blank=False)
    locus_state_code = models.CharField(max_length=45)
    locus_state_name = models.CharField(max_length=100)
    locus_state_lat = models.FloatField()
    locus_state_long = models.FloatField()
    locus_country_code = models.CharField(max_length=45)
    locus_country_name = models.CharField(max_length=100)
    locus_country_lat = models.FloatField()
    locus_country_long = models.FloatField()

    class Meta:
        db_table = 'common_locus_city'


class GenericImageMapping(INGOModel):
    content_type = models.ForeignKey(ContentType)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')
    image = models.ForeignKey(Image)
    is_active = models.BooleanField()
    createdon = models.DateTimeField(auto_now_add=True)
    modifiedon = models.DateTimeField(auto_now=True)
    last_modified_user = CurrentUserField()
    order = models.IntegerField()

    class Meta:
        db_table = 'generic_image_mapping'


class GenericVideoMapping(INGOModel):
    content_type = models.ForeignKey(ContentType)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')
    video = models.ForeignKey(Video)
    is_active = models.BooleanField()
    createdon = models.DateTimeField(auto_now_add=True)
    modifiedon = models.DateTimeField(auto_now=True)
    last_modified_user = CurrentUserField()
    order = models.IntegerField()

    class Meta:
        db_table = 'generic_video_mapping'


class Templates(INGOModel):
    title = models.CharField(max_length=50, null=False, blank=False, verbose_name="Title")
    template_code = models.CharField(max_length=15, null=False, blank=False, verbose_name="Template Code")
    description = models.TextField(null=False, blank=False, verbose_name="Description")
    content_type = models.ForeignKey(ContentType, verbose_name="Content Type ID")
    segment_id = models.IntegerField(null=False, choices=hotelchoice.COMMON_TEMPLATE_SEGMENTS, verbose_name="Segments")
    category = models.CharField(max_length=25, null=False, blank=False, choices=hotelchoice.COMMON_TEMPLATE_CATEGORY,
                                verbose_name="Category Group")
    priority = models.PositiveSmallIntegerField(null=False, default=0)  # Lower priority -> Higher precedence
    is_active = models.BooleanField(null=False, default=True, verbose_name="Active Flag")
    fields = JSONField(null=False, blank=False, verbose_name="Template Fields Data")
    meta_data = JSONField(default={}, verbose_name="Meta Data")
    start_date = models.DateField(verbose_name="Start Date")
    end_date = models.DateField(verbose_name="End Date")
    created_by = models.ForeignKey(User, null=False, verbose_name="Created By")
    created_on = models.DateTimeField(auto_now_add=True, null=False, verbose_name="Created On")
    modified_on = models.DateTimeField(auto_now=True, null=False, verbose_name="Last Modified On")

    class Meta:
        app_label = "common"
        verbose_name = "Common Template"
        verbose_name_plural = "Common Templates"

    def __str__(self):
        return self.template_code

    def save(self, *args, **kwargs):
        if not self.template_code:
            super(Templates, self).save(*args, **kwargs)
            self.template_code = construct_template_code(self.id, self.content_type)
        super(Templates, self).save()

    def admin_url(self):
        return reverse("admin:common_templates", args=(self.id,))


class FieldDataModeration(INGOModel):
    APPROVED = 'approved'
    REJECTED = 'rejected'
    PENDING = 'pending'
    MODERATION_STATUS = (
        (APPROVED, 'Approved'),
        (REJECTED, 'Rejected'),
        (PENDING, 'Pending')
    )
    content_type = models.ForeignKey(ContentType, verbose_name="Content Type ID", db_index=True)
    object_id = models.PositiveIntegerField(db_index=True)
    content_object = GenericForeignKey('content_type', 'object_id')
    field_name = models.CharField(max_length=50, null=False, blank=False, choices=commonchoice.MODERATION_FIELDS,
                                  db_index=True)
    field_data = models.CharField(max_length=5000)
    moderation_status = models.CharField(_('Approval status'), max_length=45, null=True, db_index=True,
                                         choices=MODERATION_STATUS, default=PENDING)
    is_active = models.BooleanField(null=False, default=True, verbose_name="Active Flag")
    reject_reason = models.CharField(_('Reject Reason'), max_length=1000, null=True, blank=True)
    createdon = models.DateTimeField(auto_now_add=True)
    modifiedon = models.DateTimeField(auto_now=True)
    last_modified_user = models.ForeignKey(User, db_column='last_modified_user')

    class Meta:
        db_table = 'field_data_moderation'


def field_data_moderation(**kwargs):
    try:
        from hotels.push_moderation_data_to_kafka import push_field_data_to_moderation
        _object = kwargs.get('instance')
        is_newly_created = kwargs.get('created', False)

        if _object.moderation_status == 'pending' and is_newly_created:
            logger_stats.info("Field moderation pushed for data id " + str(_object.id),
                              log_type="FieldDataModeration", bucket="field_moderation", stage="FieldDataModeration")
            push_field_data_to_moderation(_object)
        else:
            logger_stats.info("Field moderation skipped for data id " + str(_object.id),
                              log_type="FieldDataModeration", bucket="field_moderation", stage="FieldDataModeration")

    except Exception as e:
        logger_stats.error(str(e), log_type="FieldDataModeration", bucket="field_moderation",
                           stage="FieldDataModeration")


models.signals.post_save.connect(field_data_moderation, FieldDataModeration)

def create_draft_for_moderation(base_obj, field_name, field_data, user):
    try:
        content_type_obj = ContentType.objects.get_for_model(base_obj)
        try:
            already_exists = FieldDataModeration.objects.get(content_type=content_type_obj, object_id=base_obj.id,
                                                             is_active=True, moderation_status='pending')
            return False, "One record for {field_name} is already present in the Pending state and waiting for" \
                          " Moderation.".format(field_name=field_name)
        except ObjectDoesNotExist as e:
            field_mod_obj = FieldDataModeration.objects.create(content_object=base_obj, field_name=field_name,
                                                               field_data=field_data, last_modified_user=user)
            return True, "Record created successfully."
    except Exception as e:
        return False, repr(e)


class TemplateMapping(INGOModel):
    template_code = models.CharField(max_length=15, null=False, blank=False)
    content_type = models.ForeignKey(ContentType, db_index=True)
    object_id = models.PositiveIntegerField(db_index=True)
    content_object = GenericForeignKey('content_type', 'object_id')
    snapshot = JSONField(default={})
    hotelcode = models.CharField(max_length=15)
    created_on = models.DateTimeField(auto_now_add=True, null=False, verbose_name="Created On")
    modified_on = models.DateTimeField(auto_now=True, null=False, verbose_name="Last Modified On")


class Caretaker(INGOModel):
    PII_CLASS_CONFIG = {
        'column_length': {
            'name': 255,
        },
        'iv_length': 16,  # AES block size
        'padding': 16,  # Padding to make the data length multiple of AES block size
    }
    name = models.CharField(max_length=1144)
    is_fulltime = models.NullBooleanField()
    start_time = models.TimeField(null=True)
    end_time = models.TimeField(null=True)
    role = models.PositiveSmallIntegerField(choices=commonchoice.CARETAKER_ROLE, default=0)
    tasks = JSONField(null=True)
    languages = JSONField(null=True)
    is_communicable = models.BooleanField(default=False)
    is_vaccinated = models.NullBooleanField()
    image = GenericRelation(Image)

    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')

    is_active = models.BooleanField(default=True)
    created_on = models.DateTimeField(auto_now_add=True)
    modified_on = models.DateTimeField(auto_now=True)

    generic_contacts = GenericRelation('hotels.GenericContactDetail', related_query_name='caretaker')
    encryptionDetails = JSONField(null=True, blank=True, default=None)

    class Meta:
        db_table = 'common_caretaker'

    def __unicode__(self):
        return u'Caretaker Id {0}: {1}'.format(self.id, self.name)

    @property
    def mobile(self):
        return self.generic_contacts.filter(is_active=True, contact_type=ContactTypes.MOBILE).first()

    @property
    def email(self):
        return self.generic_contacts.filter(is_active=True, contact_type=ContactTypes.EMAIL).first()

    def get_hotel(self):
        from hotels.models import HotelDetail
        hotel_content_type = ContentType.objects.get_for_model(HotelDetail)

        if self.content_type_id == hotel_content_type.id:
            return self.content_object

    def get_hotel_ids_by_object(self):
        result = self.get_hotel()
        return [result.hotelcode] if result else []

register_model_signal(Caretaker)

models.signals.post_save.connect(push_static_content, Caretaker)

class CampaignMaster(INGOModel):

    def validate_image_extension(value):
        ext = os.path.splitext(value.name)[1]  # [0] returns path+filename
        valid_extensions = ['.png', '.jpg']
        if not ext.lower() in valid_extensions:
            raise ValidationError(u'Unsupported file extension. Please upload png or jpg file.')

    def validate_agreement_id(self, value):
        from hotels.models import AgreementMaster
        try:
            AgreementMaster.objects.get(id=value)
        except AgreementMaster.DoesNotExist:
            raise ValidationError('The given Agreement ID is not valid. Please update with an existing '
                                  'agreement id or create a new agreement master')

    campaign_name = models.CharField(max_length=50, verbose_name=_('Campaign Name'))
    campaign_desc = models.TextField(max_length=500, verbose_name=_('Campaign Description'))
    status = models.BooleanField(_('Active Flag'), default=True)
    modified_by = models.ForeignKey(User, null=True, verbose_name=_('Modified By'),
                                    related_name='campaign_master_modified_by')
    created_on = models.DateTimeField(auto_created=True, auto_now_add=True)
    modified_on = models.DateTimeField(auto_now=True)

    cmp_long_desc = models.TextField(max_length=500, verbose_name=_('Campaign Benefit'))
    campaign_type = models.PositiveIntegerField(verbose_name=_('Campaign Type'), choices=CM_TYPE)
    campaign_std = models.DateTimeField(verbose_name=_('Campaign Start Date'))
    campaign_end = models.DateTimeField(verbose_name=_('Campaign End Date'))
    stay_std = models.DateTimeField(verbose_name=_('Stay Start Date'))
    stay_end = models.DateTimeField(verbose_name=_('Stay End Date'))
    bkg_std = models.DateTimeField(verbose_name=_('Booking Start Date'))
    bkg_end = models.DateTimeField(verbose_name=_('Booking End Date'))
    agreement_id = models.IntegerField(verbose_name=_('Agreement ID'))
    video_url = models.URLField(_('Campaign Video URL'))
    max_black_out_days = models.IntegerField(verbose_name=_('Max Black Out Days Allowed'))
    cmp_image = models.FileField(_('Logo Upload'), upload_to="mdb", null=True, blank=True,
                                 storage=MDBStorage(), max_length=1000,
                                 help_text='''logo upload''',
                                 validators=[validate_image_extension])
    cmp_document = models.FileField(_('Campaign Document'), upload_to="mdb", null=True, blank=True,
                                    storage=MDBStorage(), max_length=1000,
                                    help_text='''campaign document upload''',
                                    validators=[])
    campaign_level = models.PositiveIntegerField(verbose_name=_('Discount Level'), choices=CM_LEVEL)
    discount_value = models.PositiveIntegerField(verbose_name=_('Discount Value'))
    discount_type = models.PositiveIntegerField(verbose_name=_('Discount Type'),
                                                choices=CM_DISCOUNT_TYPE, default=CM_DISCOUNT_TYPE[0][0])
    checkin_blackout_dates = models.TextField(max_length=500, verbose_name=_('Cheeckin Blackout Dates'))
    bkg_blackout_dates = models.TextField(max_length=500, verbose_name=_('Booking Blackout Dates'))
    metadata = JSONField(default={}, verbose_name=_('Campaign Master MetaData'), null=True, blank=True)
    init_call_done = False

    def __unicode__(self):
        return self.campaign_name

    def __init__(self, *args, **kwargs):
        self.init_call_done = False
        super(CampaignMaster, self).__init__(*args, **kwargs)
        if args or 'metadata' in kwargs:
            metadata = self.metadata
            populate_meta_fields_to_cmp_master(self, metadata)
        self.init_call_done = True

    def __getattr__(self, item):
        if item in CAMPAIGN_RELATED_META_FIELDS:
            return self.metadata.get(item, None) if self.metadata else None
        else:
            return super(CampaignMaster, self).__getattribute__(item)

    def __setattr__(self, key, value):
        if self.init_call_done:
            if key in ['metadata']:
                return
            if key in CAMPAIGN_RELATED_META_FIELDS:
                req_static_type = TYPE_MAPPINGS_CAMPAIGN_RELATED_META_FIELDS[key]
                try:
                    if value is not None:
                        value = req_static_type(value)
                    updated_data = self.metadata if self.metadata else {}
                    updated_data.update({key: value})
                    super(CampaignMaster, self).__setattr__('metadata', updated_data)
                except ValueError:
                    inventory_logger.error(
                        "Error saving: CampaignMaster | The key - {} accepts {} data type only".format(
                            key, req_static_type))
                    raise ValueError("Error saving: CampaignMaster | The key - {} accepts {} data type only".format(
                        key, req_static_type))
        super(CampaignMaster, self).__setattr__(key, value)

    class Meta:
        db_table = 'common_campaign_master'


class CampaignEntityMapping(INGOModel):
    campaign = models.ForeignKey(CampaignMaster, db_index=True, verbose_name=_('Campaign Id'))
    content_type = models.ForeignKey(ContentType, db_index=True)
    object_id = models.IntegerField(verbose_name=_('Entity ID'))
    hotelId = models.CharField(verbose_name=_('MMT Hotel ID'), max_length=20)
    entity_refs = models.CharField(verbose_name=_('Entity Reference Code'), max_length=50)
    status = models.IntegerField(choices=CM_MAPPING_STATUS, default=0)
    modified_by = models.ForeignKey(User, null=True, verbose_name=_('Modified by'), related_name='Modified By')
    modified_on = models.DateTimeField(auto_now=True, db_index=True)
    created_on = models.DateTimeField(auto_now_add=True)
    # metadata - Used to store values required for Campaign Business
    metadata = JSONField(default={}, verbose_name=_('Campaign Entity Meta Data'))
    # related_json_data - Used to store internal values for auditing, analytics purpose.
    related_json_data = JSONField(default={}, verbose_name=_('Audit Related JSON Data'))
    flag_bits_1 = models.PositiveIntegerField(default=0, null=False)
    init_call_done = False

    class Meta:
        db_table = 'common_campaign_entity_mapping'
        unique_together = ('campaign', 'content_type', 'object_id')

    def __init__(self, *args, **kwargs):
        self.init_call_done = False
        super(CampaignEntityMapping, self).__init__(*args, **kwargs)
        if args or 'flag_bits_1' in kwargs:
            flag_bits_1 = self.flag_bits_1
            populate_flag_info_to_obj(self, flag_bits_1, CAMPAIGN_ENTITY_MAPPING_FLAG_ONE_DICT)
        self.init_call_done = True

    def __setattr__(self, key, value):
        if self.init_call_done:
            if key in ['flag_bits_1']:
                return
            if key in CAMPAIGN_ENTITY_MAPPING_FLAG_ONE_DICT:
                flag_bits_1_val = compute_flag_info_for_obj(self.flag_bits_1, key, value,
                                                            CAMPAIGN_ENTITY_MAPPING_FLAG_ONE_DICT)
                super(CampaignEntityMapping, self).__setattr__('flag_bits_1', flag_bits_1_val)
        super(CampaignEntityMapping, self).__setattr__(key, value)

    def __getattr__(self, item):
        if item in CAMPAIGN_ENTITY_MAPPING_FLAG_ONE_DICT:
            return self.flag_bits_1 & CAMPAIGN_ENTITY_MAPPING_FLAG_ONE_DICT[item] == \
                   CAMPAIGN_ENTITY_MAPPING_FLAG_ONE_DICT[item]
        else:
            return super(CampaignEntityMapping, self).__getattribute__(item)

    def get_hotel_ids_by_object(self):
        from hotels.models import HotelDetail
        hotel_content_type = ContentType.objects.get_for_model(HotelDetail)
        if self.content_type.id == hotel_content_type.id:
            hobj = HotelDetail.objects.get(id=self.object_id)
            return [hobj.hotelcode]
        else:
            return []


models.signals.post_save.connect(execute_handler_function, CampaignEntityMapping)
models.signals.post_save.connect(push_notification, CampaignEntityMapping)
models.signals.post_save.connect(push_static_content, CampaignEntityMapping)


class CampaignRelatedInfo(INGOModel):
    campaign = models.ForeignKey(CampaignMaster, db_index=True, verbose_name=_('Campaign Id'))
    content_type = models.ForeignKey(ContentType)
    object_id = models.PositiveIntegerField()
    metadata = JSONField(default={}, verbose_name="campaign_meta_data")
    created_on = models.DateTimeField(auto_now_add=True)
    modified_on = models.DateTimeField(auto_now=True)
    modified_by = models.ForeignKey(User, null=True, verbose_name=_('Modified By'),
                                    related_name='campaign_info_modified_by')
    isactive = models.BooleanField(verbose_name=_('Active Flag'), default=True)

    class Meta:
        db_table = 'common_campaign_related_info'
        unique_together = ('campaign', 'content_type', 'object_id')


# models.signals.post_save.connect(push_campaign_related_info, CampaignRelatedInfo)


class CampaignEntityDaywiseConfig(INGOModel):
    campaign_mapping_id = models.PositiveIntegerField(_('Campaign Entity Mapping Id'), max_length=11, null=False,
                                                      blank=False)
    participation_date = models.IntegerField(_('Participation Date'), blank=False, null=False)
    checkin_blackout_dates = models.CharField(_('Checkin Blackout Dates'), max_length=1000, null=True, blank=True)
    is_active = models.BooleanField(_('Active Flag'), default=False)
    meta_data = JSONField(null=True, blank=True)
    created_on = models.DateTimeField(_('Added On'), auto_now_add=True, db_index=True)
    modified_on = models.DateTimeField(_('Modified On'), auto_now=True, db_index=True)
    init_call_done = False

    class Meta:
        db_table = 'common_campaign_entity_daywise_config'

    def __unicode__(self):
        return u'%s-%s' % (self.participation_date, self.campaign_mapping_id)

    def __init__(self, *args, **kwargs):
        self.init_call_done = False
        super(CampaignEntityDaywiseConfig, self).__init__(*args, **kwargs)
        if args or 'meta_data' in kwargs:
            meta_data = self.meta_data
            populate_json_fields_to_obj(self, meta_data, DAYWISE_CONFIG_META_DATA_FIELDS)

        self.init_call_done = True

    def __getattr__(self, item):
        if item in DAYWISE_CONFIG_META_DATA_FIELDS:
            return self.meta_data.get(item, None) if self.meta_data else None
        else:
            return super(CampaignEntityDaywiseConfig, self).__getattribute__(item)

    def __setattr__(self, key, value):
        if self.init_call_done:
            if key in ['meta_data']:
                return
            if key in DAYWISE_CONFIG_META_DATA_FIELDS:
                updated_data = self.meta_data if self.meta_data else {}
                updated_data.update({key: value})
                super(CampaignEntityDaywiseConfig, self).__setattr__('meta_data', updated_data)
        super(CampaignEntityDaywiseConfig, self).__setattr__(key, value)

class ProgramEntityMapping(models.Model):
    NEVER_ENROLLED = 0
    ACTIVE = 1
    INACTIVE = 2
    DISABLED = 3
    STATUS_CHOICES = [
        (NEVER_ENROLLED, 'Never Enrolled'),
        (ACTIVE, 'Active'),
        (INACTIVE, 'Inactive'),
        (DISABLED, 'Disabled')
    ]

    programId = models.BigIntegerField(db_column='programId')
    hotelId = models.CharField(max_length=255, db_column='hotelId')
    status = models.IntegerField(choices=STATUS_CHOICES, db_column='status')
    riskStatus = models.CharField(max_length=255, null=True, blank=True, db_column='riskStatus')
    programAttributes = JSONField(null=True, blank=True, db_column='programAttributes')
    defaultAttributes = JSONField(null=True, blank=True, db_column='defaultAttributes')
    programEndAt = models.DateTimeField(null=True, blank=True, db_column='programEndAt')
    lastEligibilityCheckAt = models.DateTimeField(null=True, blank=True, db_column='lastEligibilityCheckAt')
    nextEligibilityCheckAt = models.DateTimeField(null=True, blank=True, db_column='nextEligibilityCheckAt')
    createdAt = models.DateTimeField(_('Added On'), auto_now_add=True, db_index=True)
    modifiedAt = models.DateTimeField(_('Modified On'), auto_now=True, db_index=True)
    createdBy = models.ForeignKey(User, null=True, related_name='programmapping_createdBy', db_column='createdBy')
    modifiedBy = models.ForeignKey(User, null=True, related_name='programmapping_modifiedBy', db_column='modifiedBy')
    enrolledAt = models.DateTimeField(null=True, blank=True, db_column='enrolledAt')

    class Meta:
        db_table = 'program_entity_mapping'


class ProgramMaster(models.Model):
    type = models.CharField(max_length=255, null=True, blank=True, db_column='type')
    name = models.CharField(max_length=255, null=True, blank=True, db_column='name')
    status = models.CharField(max_length=255, null=True, blank=True, db_column='status')
    createdAt = models.DateTimeField(_('Added On'), auto_now_add=True, db_index=True)
    modifiedAt = models.DateTimeField(_('Modified On'), auto_now=True, db_index=True)
    createdBy = models.ForeignKey(User, null=True, related_name='program_createdBy', db_column='createdBy')
    modifiedBy = models.ForeignKey(User, null=True, related_name='program_modifiedBy', db_column='modifiedBy')

    class Meta:
        db_table = 'program_master'

class FakeDetailManager(Manager):

    def get(self, *args, **kwargs):
        if 'value' in kwargs:
            mobile = kwargs.pop('value')
            kwargs['value_hash'] = hashlib.sha256(mobile.encode()).hexdigest() if mobile else ""
        return super(FakeDetailManager, self).get(*args, **kwargs)

    def filter(self, *args, **kwargs):
        if 'value' in kwargs:
            mobile = kwargs.pop('value')
            kwargs['value_hash'] = hashlib.sha256(mobile.encode()).hexdigest() if mobile else ""
        elif 'value__in' in kwargs:
            mobiles = kwargs.pop('value__in')
            kwargs['value_hash__in'] = [hashlib.sha256(mobile.encode()).hexdigest() for mobile in mobiles]
        return super(FakeDetailManager, self).filter(*args, **kwargs)

class FakeDetail(INGOModel):
    PII_CLASS_CONFIG = {
        'column_length': {
            'value': 1024,
        },
        'iv_length': 16,  # AES block size
        'padding': 16,  # Padding to make the data length multiple of AES block size
    }
    type = models.IntegerField(choices=FakeDetailTypes.choices)
    value = models.CharField(null=False, max_length=1024)
    status = models.IntegerField(choices=FakeDetailStatus.choices)

    created_by = models.ForeignKey(User, null=False, related_name="fake_detail_created_by")
    modified_by = models.ForeignKey(User, null=False, related_name="fake_detail_modified_by")

    created_on = models.DateTimeField(auto_now_add=True)
    modified_on = models.DateTimeField(auto_now=True)
    encryptionDetails = JSONField(blank=True, null=True)
    value_hash = models.CharField(max_length=255, null=True, blank=True)
    value_backup = models.CharField(max_length=1024, null=True, blank=True)

    objects = FakeDetailManager()

    class Meta:
        db_table = 'common_fake_detail'
        unique_together = ('type', 'value')

    def __unicode__(self):
        return u'Fake Detail Id {0}: {1} - {2}'.format(self.id, self.type, self.value)

register_model_signal(FakeDetail)

class FakeDetailMapping(INGOModel):
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')

    fake_detail = models.ForeignKey(FakeDetail, null=False)
    is_active = models.BooleanField(default=True)

    created_by = models.ForeignKey(User, null=False, related_name="fake_detail_mapping_created_by")
    modified_by = models.ForeignKey(User, null=False, related_name="fake_detail_mapping_modified_by")

    created_on = models.DateTimeField(auto_now_add=True)
    modified_on = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'common_fake_detail_mapping'
        unique_together = ('content_type', 'object_id', 'fake_detail')

    def __unicode__(self):
        return u'Fake Detail Mapping Id {0}: {1} - {2} | {3}'.format(
            self.id, self.fake_detail_id, self.content_type_id, self.object_id)

class ThirdPartyErrors(INGOModel):
    api_name =  models.CharField(null=False, max_length=25, choices=THIRD_PARTY_API)
    error_code = models.CharField(_('Error Code'), max_length=100, null=True, blank=True)
    error_message = models.CharField(_('Error Message'), max_length=1000, null=True, blank=True)
    translated_message = models.CharField(_('Translated Message'), max_length=1000, null=True, blank=True)
    client_code = models.CharField(null=False, max_length=25, choices=API_CLIENTS)
    status_code = models.PositiveIntegerField(null=False, max_length=3)
    created_on = models.DateTimeField(_('Added On'), auto_now_add=True, db_index=True)
    created_by = models.ForeignKey(User, related_name='third_party_errors_created_by')
    modified_by = models.ForeignKey(User, related_name='third_party_errors_modified_by')
    modified_on = models.DateTimeField(_('Modified On'), auto_now=True, db_index=True)
    metadata = JSONField(default={}, verbose_name="thirdpartyerrors")
    isactive = models.BooleanField(default=False)

    class Meta:
        app_label = 'common'
        verbose_name = _('Third Party Errors')
        verbose_name_plural = _('Third Party Errors')

    def __unicode__(self):
        return u'%s' % (self.pk)

    def clean(self):
        from api.v2.common.resources.helper import get_redis_key_for_translated_error_message, ERROR_TRANSLATION_CACHE_EXPIRTY_TIME
        tpe_obj = ThirdPartyErrors.objects.filter(api_name=self.api_name, status_code=self.status_code, error_code=self.error_code,
                                                   client_code=self.client_code, error_message=self.error_message)

        redis_key, cache_data = get_redis_key_for_translated_error_message(self.api_name, self.status_code,
                                                                           self.error_code, self.client_code,
                                                                           self.error_message)
        if tpe_obj and not self.pk:
            raise ValidationError("Third Party Error Already exists for this api name, status code, error_code, "
                                      "error_message and client")
        if self.isactive:
            PAYMENT_CACHE.set(redis_key, self.translated_message, ERROR_TRANSLATION_CACHE_EXPIRTY_TIME)
        else:
            PAYMENT_CACHE.delete(redis_key)


class ManagerMapping(INGOModel):
    contract_bdo = models.ForeignKey(User, verbose_name=_('BDM'),related_name='contract_bdo')
    contract_manager = models.ForeignKey(User, verbose_name=_('ZM'), related_name='contract_manager')
    city = models.ForeignKey(City)
    property_category = models.CharField(max_length=50, choices=PROPERTY_CATEGORY)
    added_by = CurrentUserField()
    valid_till = models.DateTimeField()
    is_active = models.BooleanField(_('Active Flag'), default=True)
    created_on = models.DateTimeField(_('Created On'), auto_now_add=True)

    class Meta:
        app_label = 'common'
        db_table = 'common_bdm_mapping'
        verbose_name = _('City Based Manager Mapping')
        verbose_name_plural = _('City Based Manager Mapping')
        unique_together = ('city', 'property_category', 'is_active')

    def clean(self):
        super(ManagerMapping, self).clean()
        # this will work with model form save validation
        validate_manager_mapping_object(self)

    def save(self, *args, **kwargs):
        # direct save on model requires this
        validate_manager_mapping_object(self)
        super(ManagerMapping, self).save(*args, **kwargs)
        if self.id:
            from common.services import ManagerMappingWrapper
            ManagerMappingWrapper.update_entity_cache(self)

