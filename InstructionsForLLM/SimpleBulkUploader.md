# Bulk Uploader Flow: Screen API Assignment

## ✅ Proto Operation Details

**Operation Name:** Screen API Assignment

**Required Fields:** ScreenId,APIId,Action

**Validation Rules:**
- All fields are mandatory
- Action must be either "assign" or "unassign"

**gRPC Call:** Use the appropriate gRPC method to assign/unassign roles

## 🔧 Code Implementation Steps

### Step 1: Add Config Entry

**File:** `bulk_uploader/config.py`

**Action:**
- Add a new config entry at the end of the list
- Use the next available integer ID
- Fields like  `operation`, `model`, `upload_restricted_time`, `max_line_count` should match the previous config entry
- Ensure necessary imports are included (refer to PermissionAssignment imports)
- For Auth_group give acess to these roles 'ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT'. 

**📄 Example Code:**

```python
{
    'id': 124,  # Next available ID
    'name': 'Assign Permission To User Or Group',
    'handle': 'permission_asnmt',
    'auth_group': ['ROLE_INGOMMT_DEV_LEVEL_2', 'ROLE_INGOMMT_PROD_SUPPORT'],
    'sample_file': 'https://s3-ap-south-1.amazonaws.com/ingoibibo-staging/bulk-uploader/0-2025-05-14_2025-05-14T16:29:26_8078c878-30b2-11f0-af26-0242ac140104.csv',
    'operation': 'add',
    'model': RatePlan,
    'validators': [validate_permission_assignment],
    'fields': [
        'ContentType',  # USER or GROUP
        'ContentID',    # User ID or Group ID
        'Permission',   # PermissionId
        'Action'        # ADD or REMOVE
    ],
    'upload_restricted_time': 0,
    'save': save_permission_assignment,
    'max_line_count': 1000
},
```

### Step 2: Add Validation Function

**Location:** `hotel_cloud_service/bulk_uploader_validator/`

**Action:**
- Create a new validation file (e.g., `validate_screen_api_assignment.py`)
- Follow the structure of `validate_permission_assignment.py`
- Validate:
  - All required fields are present
  - Action is either "assign" or "unassign"


**📄 Example Code:**

```python
from django.core.exceptions import ValidationError

def validate_permission_assignment(kwargs):
    mandatory_fields = [
        'ContentType',
        'ContentID',
        'Permission',
        'Action'
    ]
    for field in kwargs.keys():
        if field in mandatory_fields and kwargs.get(field, '') == '':
            raise ValidationError("Field {} is mandatory".format(field))

    valid_content_types = ['USER', 'GROUP']
    content_type = kwargs.get('ContentType', '').upper()
    if content_type not in valid_content_types:
        raise ValidationError("ContentType must be one of {}".format(valid_content_types))

    valid_actions = ['ASSIGN', 'UNASSIGN']
    action = kwargs.get('Action', '').upper()
    if action not in valid_actions:
        raise ValidationError("Action must be one of {}".format(valid_actions))

    return True
```

### Step 3: Add Save Function

**File:** `bulk_uploader/external_functions3.py`

**Action:**
- **Add a new method save_api_registry_upsert similar to `save_screen_api_assignment`**
- **Do not modify existing code**
- Use proper loggers (Python 2.7 style). 
- from utils.logger import Logger # New import
api_logger = Logger(logger='inventoryAPILogger'). import this from bulk_uploader/external_functions.py in a similar way
- Ensure necessary imports for `format_payload_for_screen_api_assignment`
- action has to be "add" or "remove"

**📄 Example Code:**

```python
def save_permission_assignment(data_dict):
    try:
        from hotel_cloud_service.grpc_hotel_cloud_heimdall_client import HotelCloudHeimdallClient
        import uuid

        correlation_key = str(uuid.uuid4())
        action = data_dict.get('Action', '').upper()
        content = data_dict.get('ContentType', '').upper()
        content_id = data_dict.get('ContentID', '')
        permission = data_dict.get('Permission', '')

        if action not in ['ASSIGN', 'UNASSIGN']:
            return [], "Invalid action: %s. Must be 'ASSIGN' or 'UNASSIGN'." % action

        if content not in ['USER', 'GROUP']:
            return [], "Invalid entity type: %s. Must be 'USER' or 'GROUP'." % content

        api_logger.info(
            message="Bulk uploader triggered for permission %s to %s with correlation_key: %s" % (
                action.lower(), content.lower(), correlation_key
            ),
            log_type='ingoibibo',
            bucket='bulk_uploader',
            stage='bulk_uploader.external_functions.save_permission_assignment'
        )

        payload = format_payload_for_permission_assignment(data_dict, correlation_key)
        htlcld_service = HotelCloudHeimdallClient()
        response = htlcld_service.manage_permission_assignment(payload, data_dict)

        error_obj = response.get('error')
        if error_obj:
            error_obj_code = error_obj.get('code', '')
            if error_obj_code:
                return [], "Failed, errorCode: %s, message: %s" % (error_obj_code, error_obj.get('message', ''))
        return [], "Success"

    except Exception as e:
        message = 'Exception occurred while %sing permission: %s' % (
            data_dict.get("Action", "").lower(), str(e)
        )
        api_logger.critical(
            message=message,
            log_type='ingoibibo',
            bucket='bulk_uploader',
            stage='bulk_uploader.external_functions.save_permission_assignment'
        )
        return [], "Error occurred: %s" % str(e)
```

### Step 4: Add Payload Formatting Function

**File:** `bulk_uploader/common_helpers.py`

**Action:**
- Add a new method format_payload_for_api_registry_upsert at the end of the file
- Follow the pattern of `format_payload_for_screen_api_assignment`
- how request should look like: `{"api":999,"isActive":false,"screenId":3}`
- if action is assign, set isActive = true, on unassign set it to false

**📄 Example Code:**

```python
def format_payload_for_permission_assignment(data_dict, correlation_key):
    content_type = data_dict.get('ContentType', '').upper()
    action = data_dict.get('Action', '').upper()

    if content_type == 'USER':
        user_ids = [uid.strip() for uid in data_dict.get('ContentID', '').split(',')]
        payload = {
            "permissionId": int(data_dict.get('Permission')),
            "userIds": user_ids,
            "action": action,
            "correlationKey": correlation_key
        }
    elif content_type == 'GROUP':
        permission_ids = [int(pid.strip()) for pid in data_dict.get('Permission', '').split(',')]
        payload = {
            "groupId": int(data_dict.get('ContentID')),
            "permissionIds": permission_ids,
            "action": action,
            "correlationKey": correlation_key
        }

    return payload
```



## Proto Compilation Instructions

### Step 5: Create Virtual Environment

```bash
python3 -m venv my_grpc_env
```

**Activate manually:**

```bash
source my_grpc_env/bin/activate
```

### Step 6: Install grpcio-tools

```bash
./my_grpc_env/bin/python3 -m pip install grpcio-tools
```

### Step 7: Compile .proto File

**Assume:**
- File: `your_file_name.proto`
- Directory: `path/to/your/proto_directory/`

```bash
./my_grpc_env/bin/python3 -m grpc_tools.protoc \
  -Ipath/to/your/proto_directory/ \
  --python_out=path/to/your/proto_directory/ \
  --grpc_python_out=path/to/your/proto_directory/ \
  your_file_name.proto
```

This generates `your_file_name_pb2.py` and `your_file_name_pb2_grpc.py` in the same directory.

### Step 8: Add gRPC Method in Client

**File:** `grpc_hotel_cloud_heimdall_client.py`

**Action:**
- Add a new method similar to `manage_permission_assignment` named manage_screen_api_assignment
- DO NOT TOUCH EXISTING CODE
- Use appropriate proto messages and service stub
-  call this rpc -> rpc UpsertScreenApiMapper(ScreenApiMapperRequest) returns (ScreenApiMapperResponse) {};
- Ensure proper imports are in place (refer to how permission assignment is handled)
- use these as metadata for request: metadata = (('userid', userid), ('requestid', requestid), ('clientip', '0.0.0.0'),
                        ('source', 'ingoadmin'), ('language', 'eng'), ('country', 'in'),
                        ('platform', 'admin'), ('x-calling-service', 'ingo-web'))

**📄 Example Code:**

```python
def manage_permission_assignment(self, payload, data_dict):
    response = {}
    try:
        userid = data_dict.get('user_id', '0')
        json_request = json.dumps(payload)
        inventory_logger.info(
            message='ManagePermissionAssignment request hit: %s' % str(json_request),
            log_type='ingoibibo', bucket='bulk_uploader', stage='manage_permission_assignment'
        )

        metadata = (("userid", userid), ("source", "ingoadmin"), ("language", "eng"), ("country", "in"), ("platform", "admin"), ("x-calling-service", "ingo-web"))

        if data_dict.get('ContentType', '').upper() == 'USER':
            proto_request = Parse(json_request, AssignPermissionToUserRequest())
            resp_proto = self.permission_service_stub.AssignPermissionToUser(proto_request, metadata=metadata)
        else:
            proto_request = Parse(json_request, AssignPermissionToGroupRequest())
            resp_proto = self.permission_service_stub.AssignPermissionToGroup(proto_request, metadata=metadata)

        response = protobuf_to_dict(resp_proto)
        inventory_logger.info(
            message='ManagePermissionAssignment response received: %s' % str(response),
            log_type='ingoibibo', bucket='bulk_uploader', stage='manage_permission_assignment'
        )

    except Exception as e:
        api_logger.critical(message='ManagePermissionAssignment got failed with Exception {}, correlation: {}'.format(str(e), payload.get('correlationKey')),
                           log_type='ingoibibo', bucket='bulk_uploader', stage='manage_permission_assignment')
        raise e

    return response
```
---

