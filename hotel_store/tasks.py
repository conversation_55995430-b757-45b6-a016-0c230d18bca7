import datetime
import time
import uuid
import logging

from celery.utils.log import get_task_logger
from django.db.models.query import Q
from django.db.models import F
from django.conf import settings

from goibibo_inventory.settings import app
from hotels.models import HotelOfferCondition
from hotel_store.update_store import UpdateHotelStore
from lib.request_middleware import set_dummy_audit_data
from hotel_store.update_store import push_vendor_data
from utils.logger import Logger

CUTOFF_HOTEL_ONCE = 100
HOTELS_UPDATE_THRESHOLD = 1
HOTELS_UPDATE_THRESHOLD_API = 5

logger_stats = Logger(logger="inventoryStatsLogger")
logger = logging.getLogger("inventoryLogger")
celery_logger = get_task_logger(__name__)
update_hotelstore_object = UpdateHotelStore()


@app.task(name="ingoibibo.update_hotel_store_v2")
def update_hotel_store():
    """this cron is to update hotelstore after each 10 minutes
    */10 * * * * /usr/bin/python /usr/local/apache2/htdocs/goibibo_inventory/current/run.py
    hotelstore.cron update_hotel_store >> /tmp/update_hotel_store.log
    """
    updates_list = update_hotelstore_object.get_hotelstore_updates_list()
    if updates_list:
        unique_id = format(uuid.uuid4())
        keys_count = len(updates_list)
        logger.info('%s\t%s\t%s\t%s\t%s\t%s' % ('hotelstore.task', 'update_hotel_store', unique_id, 'started ', '',
                                                keys_count))
        merged_updates = update_hotelstore_object.merge_hotelstore_update_keys(updates_list)
        keys_merged = len(merged_updates)
        logger.info('%s\t%s\t%s\t%s\t%s\t%s' % ('hotelstore.task', 'update_hotel_store', unique_id, 'keys reduction ',
                                                keys_count, keys_merged))
        logger_stats.info(message="%s\t%s\t%s\t%s\t%s" % (
                unique_id, '', '', '', ''),
                log_type='ingoibibo', bucket='hotelstore', stage='cron')

        vendor_tasks_count = 0

        for count in xrange(0, keys_merged, HOTELS_UPDATE_THRESHOLD_API):
            push_vendor_data(merged_updates[count:count + HOTELS_UPDATE_THRESHOLD_API], unique_id)
            vendor_tasks_count = vendor_tasks_count + 1

        logger.info('%s\t%s\t%s\t%s\t%s' % (
            'hotelstore.task', 'update_hotel_store', unique_id, 'vendor tasks created', vendor_tasks_count))


@app.task(name="ingoibibo.update_hotel_store_with_offers_daily_v2")
def update_hotel_store_with_offers_daily():
    """this cron will be used to update hotelstore data daily for different
    conditions of hotel offers.

    05 00 * * * /usr/bin/python /usr/local/apache2/htdocs/goibibo_inventory/current/run.py
    hotelstore.cron update_hotel_store_with_offers_daily >> /tmp/update_hotel_store.log
    """
    try:
        set_dummy_audit_data(username='cron',
                             firstname='update_hotel_store_with_offers_daily')
        uhs = UpdateHotelStore()
        today = datetime.datetime.combine(datetime.datetime.today(), datetime.time(0, 0))
        yesterday = today - datetime.timedelta(days=1)
        offerList = HotelOfferCondition.objects.filter(isactive=True)

        # early bird and last minute offers
        # These get applied on checkin dates, and checkins do not
        # have variable time in checkin, so needs to be runs once a day
        ebOfferList = offerList.filter(bookingdatestart__lte=today, bookingdateend__gte=today) \
            .filter(Q(earlybirdmax__isnull=False) | Q(earlybirdmin__isnull=False))
        for ebOffer in ebOfferList:
            if ebOffer.is_valid_today():
                uhs.update_hotel_store_offer(ebOffer, event_data=None)
        time.sleep(60)
        # booking blackout dates
        # needs to run once a day, blackout is date and not datetime timestamp
        blackOutOfferList = offerList.filter(bookingdatestart__lte=today, bookingdateend__gte=today) \
            .filter(bookingblackoutdates__isnull=False)
        today_date = today.date()
        yesterday_date = yesterday.date()
        for blackOutOffer in blackOutOfferList:
            black_out_dates = blackOutOffer.get_booking_blackout_dates()
            if not ((today_date in black_out_dates and
                     yesterday_date in black_out_dates) or (
                            today_date not in black_out_dates and
                            yesterday_date not in black_out_dates)):
                uhs.update_hotel_store_offer(blackOutOffer, event_data=None)
    except Exception, e:
        logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % \
                        ('hotelstore.task', 'update_hotel_store_with_offers_daily',
                         '', '', datetime.date.today(), str(e)))


@app.task(name="update_hotel_store_with_offers_every_three_hour_v2")
def update_hotel_store_with_offers_every_three_hour():
    uhs = UpdateHotelStore()
    """this cron will be used to update hotelstore data every 3 hours for different
    conditions of hotel offers.

    05 */3 * * * /usr/bin/python /usr/local/apache2/htdocs/goibibo_inventory/current/run.py
    hotelstore.update_hotel_store_with_offers_every_three_hour >> /tmp/update_hotel_store.log
    """
    try:
        offerList = HotelOfferCondition.objects.filter(isactive=True)
        now = datetime.datetime.now()

        # New offers getting applied - Offer should have a delta between
        # push_to_hotelstore_timestamp and booking date start
        newOfferList = offerList.filter(bookingdatestart__lte=now, bookingdateend__gte=now).filter(
            Q(bookingdatestart__gt=F('push_to_hotelstore_timestamp')) | Q(push_to_hotelstore_timestamp=None))
        for newOffer in newOfferList:
            if newOffer.is_valid_today():
                uhs.update_hotel_store_offer(newOffer, event_data=None)
        time.sleep(60)

        # Expired offer
        # Offer should have a delta between push_to_hotelstore_timestamp
        # and booking date end
        expOfferList = offerList.filter(bookingdateend__lte=now,
            bookingdateend__gt=F('push_to_hotelstore_timestamp'))
        for expOffer in expOfferList:
            uhs.update_hotel_store_offer(expOffer, event_data=None)
        time.sleep(60)

    except Exception, e:
        logger.critical('%s\t%s\t%s\t%s\t%s\t%s' % \
                        ('hotelstore.task', 'update_hotel_store_with_offers_every_three_hour',
                         '', '', datetime.date.today(), str(e)))