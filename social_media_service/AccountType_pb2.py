# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: AccountType.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='AccountType.proto',
  package='social.media.accounttype',
  syntax='proto3',
  serialized_pb=_b('\n\x11\x41\x63\x63ountType.proto\x12\x18social.media.accounttype*\x18\n\x0b\x41\x63\x63ountType\x12\t\n\x05HOTEL\x10\x00\x42+\n\'com.mmt.hotels.social.media.accounttypeP\x01\x62\x06proto3')
)

_ACCOUNTTYPE = _descriptor.EnumDescriptor(
  name='AccountType',
  full_name='social.media.accounttype.AccountType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='HOTEL', index=0, number=0,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=47,
  serialized_end=71,
)
_sym_db.RegisterEnumDescriptor(_ACCOUNTTYPE)

AccountType = enum_type_wrapper.EnumTypeWrapper(_ACCOUNTTYPE)
HOTEL = 0


DESCRIPTOR.enum_types_by_name['AccountType'] = _ACCOUNTTYPE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('\n\'com.mmt.hotels.social.media.accounttypeP\001'))
# @@protoc_insertion_point(module_scope)
