# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: SocialMediaAccountUpdate.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


import SocialMediaPlatform_pb2 as SocialMediaPlatform__pb2
import AccountType_pb2 as AccountType__pb2
import ErrorDetails_pb2 as ErrorDetails__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='SocialMediaAccountUpdate.proto',
  package='social.media.accountupdate',
  syntax='proto3',
  serialized_pb=_b('\n\x1eSocialMediaAccountUpdate.proto\x12\x1asocial.media.accountupdate\x1a\x19SocialMediaPlatform.proto\x1a\x11\x41\x63\x63ountType.proto\x1a\x12\x45rrorDetails.proto\"\xe4\x01\n\x14\x41\x63\x63ountUpdateRequest\x12G\n\x08platform\x18\x01 \x01(\x0e\x32\x35.social.media.socialmediaplatform.SocialMediaPlatform\x12;\n\x0c\x61\x63\x63ount_type\x18\x02 \x01(\x0e\x32%.social.media.accounttype.AccountType\x12\x12\n\naccount_id\x18\x03 \x03(\t\x12\x32\n\x06\x61\x63tion\x18\x04 \x01(\x0e\x32\".social.media.accountupdate.Action\"`\n\x15\x41\x63\x63ountUpdateResponse\x12\x0e\n\x06status\x18\x01 \x01(\t\x12\x37\n\x06\x65rrors\x18\x02 \x03(\x0b\x32\'.social.media.errordetails.ErrorDetails*0\n\x06\x41\x63tion\x12\t\n\x05\x42LOCK\x10\x00\x12\x0b\n\x07UNBLOCK\x10\x01\x12\x0e\n\nDISCONNECT\x10\x02\x32\x9b\x01\n\x18SocialMediaAccountUpdate\x12\x7f\n\x18UpdateSocialMediaAccount\x12\x30.social.media.accountupdate.AccountUpdateRequest\x1a\x31.social.media.accountupdate.AccountUpdateResponseB-\n)com.mmt.hotels.social.media.accountupdateP\x01\x62\x06proto3')
  ,
  dependencies=[SocialMediaPlatform__pb2.DESCRIPTOR,AccountType__pb2.DESCRIPTOR,ErrorDetails__pb2.DESCRIPTOR,])

_ACTION = _descriptor.EnumDescriptor(
  name='Action',
  full_name='social.media.accountupdate.Action',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='BLOCK', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='UNBLOCK', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='DISCONNECT', index=2, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=457,
  serialized_end=505,
)
_sym_db.RegisterEnumDescriptor(_ACTION)

Action = enum_type_wrapper.EnumTypeWrapper(_ACTION)
BLOCK = 0
UNBLOCK = 1
DISCONNECT = 2



_ACCOUNTUPDATEREQUEST = _descriptor.Descriptor(
  name='AccountUpdateRequest',
  full_name='social.media.accountupdate.AccountUpdateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='platform', full_name='social.media.accountupdate.AccountUpdateRequest.platform', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='account_type', full_name='social.media.accountupdate.AccountUpdateRequest.account_type', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='account_id', full_name='social.media.accountupdate.AccountUpdateRequest.account_id', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='action', full_name='social.media.accountupdate.AccountUpdateRequest.action', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=129,
  serialized_end=357,
)


_ACCOUNTUPDATERESPONSE = _descriptor.Descriptor(
  name='AccountUpdateResponse',
  full_name='social.media.accountupdate.AccountUpdateResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='social.media.accountupdate.AccountUpdateResponse.status', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
    _descriptor.FieldDescriptor(
      name='errors', full_name='social.media.accountupdate.AccountUpdateResponse.errors', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None, file=DESCRIPTOR),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=359,
  serialized_end=455,
)

_ACCOUNTUPDATEREQUEST.fields_by_name['platform'].enum_type = SocialMediaPlatform__pb2._SOCIALMEDIAPLATFORM
_ACCOUNTUPDATEREQUEST.fields_by_name['account_type'].enum_type = AccountType__pb2._ACCOUNTTYPE
_ACCOUNTUPDATEREQUEST.fields_by_name['action'].enum_type = _ACTION
_ACCOUNTUPDATERESPONSE.fields_by_name['errors'].message_type = ErrorDetails__pb2._ERRORDETAILS
DESCRIPTOR.message_types_by_name['AccountUpdateRequest'] = _ACCOUNTUPDATEREQUEST
DESCRIPTOR.message_types_by_name['AccountUpdateResponse'] = _ACCOUNTUPDATERESPONSE
DESCRIPTOR.enum_types_by_name['Action'] = _ACTION
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

AccountUpdateRequest = _reflection.GeneratedProtocolMessageType('AccountUpdateRequest', (_message.Message,), dict(
  DESCRIPTOR = _ACCOUNTUPDATEREQUEST,
  __module__ = 'SocialMediaAccountUpdate_pb2'
  # @@protoc_insertion_point(class_scope:social.media.accountupdate.AccountUpdateRequest)
  ))
_sym_db.RegisterMessage(AccountUpdateRequest)

AccountUpdateResponse = _reflection.GeneratedProtocolMessageType('AccountUpdateResponse', (_message.Message,), dict(
  DESCRIPTOR = _ACCOUNTUPDATERESPONSE,
  __module__ = 'SocialMediaAccountUpdate_pb2'
  # @@protoc_insertion_point(class_scope:social.media.accountupdate.AccountUpdateResponse)
  ))
_sym_db.RegisterMessage(AccountUpdateResponse)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('\n)com.mmt.hotels.social.media.accountupdateP\001'))

_SOCIALMEDIAACCOUNTUPDATE = _descriptor.ServiceDescriptor(
  name='SocialMediaAccountUpdate',
  full_name='social.media.accountupdate.SocialMediaAccountUpdate',
  file=DESCRIPTOR,
  index=0,
  options=None,
  serialized_start=508,
  serialized_end=663,
  methods=[
  _descriptor.MethodDescriptor(
    name='UpdateSocialMediaAccount',
    full_name='social.media.accountupdate.SocialMediaAccountUpdate.UpdateSocialMediaAccount',
    index=0,
    containing_service=None,
    input_type=_ACCOUNTUPDATEREQUEST,
    output_type=_ACCOUNTUPDATERESPONSE,
    options=None,
  ),
])
_sym_db.RegisterServiceDescriptor(_SOCIALMEDIAACCOUNTUPDATE)

DESCRIPTOR.services_by_name['SocialMediaAccountUpdate'] = _SOCIALMEDIAACCOUNTUPDATE

# @@protoc_insertion_point(module_scope)
