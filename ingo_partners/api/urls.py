from django.conf.urls import url, include
from rest_framework import routers

from ingo_partners.api.views.views import source_merger, RuleEngineViewSet, HotelMongoDataViewSet

router = routers.DefaultRouter()

router.register(r'ruleengine', RuleEngineViewSet, 'ruleengine')
router.register(r'hotelmongodata', HotelMongoDataViewSet, 'hotelmongodata')

urlpatterns = [
    url(r'^source_merger/$', source_merger, name='source_merger'),
    url(r'^', include(router.urls)),
]
